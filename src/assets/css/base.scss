html {
  font: 14px/1 -apple-system, "PingFang SC", "Helvetica Neue", STHeiti, "Microsoft Yahei", <PERSON><PERSON><PERSON>, Simsun, sans-serif;
  color: #1F2329;
  min-width: 1440px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: #507F99;
}
$color-primary: #1F57B3;
$color-txt: #6A727D;
$color-success: #099E7A;
/// color|1|Functional Color|1
$color-warning: #FF7B2F;
/// color|1|Functional Color|1
$color-danger: #FF0000;


ul,
li,
ol {
  list-style: none;
}
i {
  font-style: normal;
}

table,
th,
td,
select,
img,
input,
object {
  vertical-align: middle;
}
img {
  border: none;
}
[v-cloak] {
  display: none;
}
a {
  text-decoration: none;
  outline: none;
  color: #262626;
}
a:hover {
  color: #1F57B3 !important;
  text-decoration: none;
}
a.md-btn {
  display: inline-block;
  min-width: 74px;
  height: 34px;
  border: solid 1px #1F57B3;
  color: #1F57B3;
  line-height: 32px;
  text-align: center;
  box-sizing: border-box;
  padding: 0 18px;
}
.fl {
  float: left;
}
.fr {
  float: right;
}
.cb {
  clear: both;
}
.cbo {
  clear: both;
  overflow: hidden;
}
.clearfloat {
  zoom: 1;
}
.clearfloat:after {
  display: block;
  clear: both;
  content: "";
  visibility: hidden;
  height: 0;
}
.overHide {
  overflow: hidden;
}
.dn {
  display: none;
}
// input[type=text],
// input[type=password],
// select,
// textarea {
//   box-sizing: border-box;
//   border: solid 1px #DDDFE6;
//   outline: none;
// }
// input[type=text]:hover,
// input[type=password]:hover,
// select:hover,
// textarea:hover {
//   border: 1px solid #DDDFE6;
//   outline: none;
// }
// input[type=text]:focus,
// input[type=password]:focus,
// select:focus,
// textarea:focus {
//   border: 1px solid #1F2329;
//   outline: none;
// }
input,
textarea {
  box-sizing: border-box;
  margin: 0;
  border-radius: 0;
  -webkit-appearance: none;
  font: 14px/1 "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
  color: #1F2329;
  outline: none;
}
textarea {
  resize: none;
  -webkit-appearance: none;
}
input[type="button"] {
  cursor: pointer;
  border: none;
  outline: none;
}
button {
  outline: none;
}
::-webkit-input-placeholder {
  color: #BFBFBF;
}
:-moz-placeholder {
  color: #BFBFBF;
}
::-moz-placeholder {
  /* Mozilla Firefox 19+ */
  color: #BFBFBF;
}
:-ms-input-placeholder {
  color: #1F2329;
}
select {
  padding-left: 10px;
  color: #282624;
}
.td-lt {
  text-decoration: line-through;
}

//弹性布局
.flex-box {
  display: flex;
}
.flex-column {
  flex-direction: column;
}
.align-item-center {
  align-items: center;
}
.flex-item {
  flex: 1;
}
.h100p{
  height: 100%;
}

/* 文字溢出 */
.txtEllipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.txtEllipsis2 {
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}


.title-public{
  height: 22px;
  font-size: 16px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 600;
  color: #1F2329;
  line-height: 22px;
  letter-spacing: 1px;
}





.mt16{
  margin-top: 16px;
}
.mb4{
  margin-bottom: 4px;
}



// table

::v-deep .el-table{
  td{
    .cell:empty::before{
      content:'--';
      color:gray;
    }
  }
  
  
  // &.noempty{
  //   :empty::before{
  //   	content: initial;
  //   	color:gray;
  //   }
  // }
  
  
  th.el-table__cell{
    background: #E0E8EB;
    padding: 8px 0;
    color: #6A727D;
  }
  td.el-table__cell{
    padding: 8px 0;
  }
  .el-button + .el-button{
    margin: 0;
  }
}

::v-deep .el-drawer{
  width: 650px !important;
  .el-drawer__header{
    font-size: 16px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    color: #2A264F;
    text-align: center;
  }
  .drawer-box{
    .drawer-footer{
      padding: 16px;
      text-align: right;
      .el-button{
        margin-left: 16px;
        padding: 0 35px;
        height: 40px;
      }
    }
  }
}

::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 8px;
  height: 8px;
  border-radius: 4px;
}
::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius: 4px;
  background: #C7C7C7;
}
::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0);
  border-radius: 4px;
  background: #E1E1E1;
}

.scroll-div{
  height: 100%;
  overflow-y: auto;
}


::v-deep .el-button{
  max-height: 40px;
}

.el-select-dropdown{
  max-width: 600px;
}