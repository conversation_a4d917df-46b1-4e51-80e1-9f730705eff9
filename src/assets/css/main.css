* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html,
body,
#app,
.wrapper {
    width: 100%;
    height: 100%;
    overflow: hidden;
}

body {
    font-family: 'PingFang SC', "Helvetica Neue", Helvetica, "microsoft yahei", arial, STHeiTi, sans-serif;
}

a {
    text-decoration: none
}
/* 针对Chrome, Safari等WebKit浏览器 */
::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

/* 针对Firefox */
input[type="number"] {
    -moz-appearance: textfield;
}

/* 去掉百度地图logo--begin */
.BMap_cpyCtrl {
    display: none;
}
.anchorBL {
    display: none;
}
/* 去掉百度地图logo--end */


.flex-row {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
}
.flex-row-start {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
}

.flex-col {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
}
.hidden-notification {
    display: none !important;
}
.show-notification {
    display: block !important;
}