export default [{
  path: "/qualityControl",
  component: () => import ("@/pages/index.vue"),
  redirect: '/qualityControl/experimentBoard',
  children: [{
      path: "panelList",
      component: () => import ("@/pages/qualityControl/panelList"),
      name: "panelList",
      meta: {
        title: "原材料试验台账",
        keepAlive: true,
      }
    },{
      path: "concretePanelList",
      component: () => import ("@/pages/qualityControl/concretePanelList"),
      name: "concretePanelList",
      meta: {
        title: "混凝土试验台账",
        keepAlive: true,
      }
    },{
      path: "experimentBoard",
      component: () => import ("@/pages/qualityControl/experimentBoard"),
      name: "experimentBoard",
      meta: {
        title: "混凝土看板",
        fixed: true,
        keepAlive: true,
      }
    },{
      path: "materialBoard",
      component: () => import ("@/pages/qualityControl/materialBoard"),
      name: "materialBoard",
      meta: {
        title: "原材料看板",
        fixed: true,
        keepAlive: true,
      }
    },{
      path: "panelListDetail",
      component: () => import ("@/pages/qualityControl/components/panelListDetail"),
      name: "panelListDetail",
      meta: {
        title: "任务详情",
        keepAlive: false,
      }
    },
    {
      path: "purchaseListDetail",
      component: () => import ("@/pages/qualityControl/components/purchaseListDetail"),
      name: "purchaseListDetail",
      meta: {
        title: "采购单详情",
        keepAlive: true,
      }
    },
    {
      path: "taskList",
      component: () => import ("@/pages/qualityControl/taskList"),
      name: "taskList",
      meta: {
        title: "生产任务",
        keepAlive: true,
      }
    },
    {
      path: "addMaterList",
      component: () => import ("@/pages/qualityControl/addMaterList"),
      name: "addMaterList",
      meta: {
        title: "增补资料记录",
        keepAlive: true,
      }
    },
    {
      path: "taskDetail",
      component: () => import ("@/pages/qualityControl/taskDetail"),
      name: "taskDetail",
      meta: {
        title: "任务单详情",
        keepAlive: true,
      }
    },
    
    {
      path: "moistureContentMgt",
      component: () => import ("@/pages/qualityControl/moistureContentMgt"),
      name: "moistureContentMgt",
      meta: {
        title: "含水率管理",
        keepAlive: true,
      }
    },
    {
      path: "projectDetail",
      component: () => import ("@/pages/qualityControl/detailPage/projectDetail.vue"),
      name: "projectDetail",
      meta: {
        title: "详情",
        keepAlive: true,
      }
    },
    
  ]
}];

