export default [{
  path: "/systemMgt",
  component: () => import ("@/pages/index.vue"),
  redirect: '/systemMgt/paramsConfig',
  children: [
    {
      path: "paramsConfig",
      component: () => import ("@/pages/systemMgt/paramsConfig"),
      name: "paramsConfig",
      meta: {
        title: "批检/快检参数配置",
        keepAlive: true,
      }
    },
    {
      path: "testProjectDesc",
      component: () => import ("@/pages/systemMgt/testProjectDesc"),
      name: "testProjectDesc",
      meta: {
        title: "试验说明配置",
        keepAlive: true,
      }
    },
    {
      path: "equipmentConfig",
      component: () => import ("@/pages/systemMgt/equipmentConfig"),
      name: "equipmentConfig",
      meta: {
        title: "设备管理",
        keepAlive: true,
      }
    },
    {
      path: "humitureMgt",
      component: () => import ("@/pages/systemMgt/humitureMgt"),
      name: "humitureMgt",
      meta: {
        title: "温湿度管理",
        keepAlive: true,
      }
    },
    {
      path: "userMgt",
      component: () => import ("@/pages/systemMgt/userMgt"),
      name: "userMgt",
      meta: {
        title: "用户管理",
        keepAlive: true,
      }
    },
    {
      path: "userDetail",
      component: () => import ("@/pages/systemMgt/userDetail"),
      name: "userDetail",
      meta: {
        title: "用户详情",
        keepAlive: true,
      }
    },
    {
      path: "regionalismConfig",
      component: () => import ("@/pages/systemMgt/regionalismConfig"),
      name: "regionalismConfig",
      meta: {
        title: "工程区域划分",
        keepAlive: true,
      }
    },
    {
      path: "specConfig",
      component: () => import ("@/pages/systemMgt/specConfig"),
      name: "specConfig",
      meta: {
        title: "材料及规格设置",
        keepAlive: false,
      }
    },
    {
      path: "materialsConfig",
      component: () => import ("@/pages/systemMgt/materialsConfig"),
      name: "materialsConfig",
      meta: {
        title: "物料规则设置",
        keepAlive: false,
      }
    },
    {
      path: "experimentGenConfig",
      component: () => import ("@/pages/systemMgt/experimentGenConfig"),
      name: "experimentGenConfig",
      meta: {
        title: "生成委托规则设置",
        keepAlive: false,
      }
    },
    {
      path: "supplierList",
      component: () => import ("@/pages/supplierMgt/supplierList"),
      name: "supplierList",
      meta: {
        title: "供应商管理",
        keepAlive: true,
      }
    },
    {
      path: "materialsList",
      component: () => import ("@/pages/supplierMgt/materialsList"),
      name: "materialsList",
      meta: {
        title: "物料管理",
        keepAlive: true,
      }
    },
    {
      path: "tcWaybillList",
      component: () => import ("@/pages/systemMgt/tcWaybillList"),
      name: "tcWaybillList",
      meta: {
        title: "运单管理",
        keepAlive: true,
      }
    },
    {
      path: "supplierDetail",
      component: () => import ("@/pages/supplierMgt/supplierDetail"),
      name: "supplierDetail",
      meta: {
        title: "供应商详情",
        keepAlive: false,
      }
    },
    {
      path: "systemMgt",
      component: () => import ("@/pages/systemMgt/systemMgt"),
      name: "systemMgt",
      meta: {
        title: "预警设置",
        keepAlive: true,
      }
    },
    {
      path: "standardizingRecord",
      component: () => import ("@/pages/systemMgt/standardizingRecord"),
      name: "standardizingRecord",
      meta: {
        title: "设备校准记录",
        keepAlive: true,
      }
    },
    {
      path: "equipmentDetial",
      component: () => import ("@/pages/systemMgt/equipmentDetial"),
      name: "equipmentDetial",
      meta: {
        title: "设备详情",
        keepAlive: false,
      }
    },
    {
      path: "experimentTested",
      component: () => import ("@/pages/systemMgt/experimentTested"),
      name: "experimentTested",
      meta: {
        title: "受检台账",
        keepAlive: true,
      }
    },
    {
      path: "experimentOuter",
      component: () => import ("@/pages/systemMgt/experimentOuter"),
      name: "experimentOuter",
      meta: {
        title: "外委托",
        keepAlive: true,
      }
    },
    {
      path: "erpCustomerList",
      component: () => import ("@/pages/systemMgt/erpCustomer"),
      name: "erpCustomer",
      meta: {
        title: "客户管理",
        keepAlive: true,
      }
    },
    {
      path: "companyMgt",
      component: () => import ("@/pages/systemMgt/companyMgt"),
      name: "companyMgt",
      meta: {
        title: "企业管理",
        keepAlive: true,
      }
    },
    {
      path: "pushMgt",
      component: () => import ("@/pages/systemMgt/pushMgt"),
      name: "pushMgt",
      meta: {
        title: "推送管理",
        keepAlive: false,
      }
    },
  ]
}];

