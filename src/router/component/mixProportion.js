/*
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-05-24 23:40:19
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2024-12-08 14:52:30
 * @FilePath: /quality_center_web/src/router/component/mixProportion.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
export default [{
    path: "/mixProportion",
    component: () => import ("@/pages/index.vue"),
    redirect: '/mixProportion/list',
    children: [
      {
        path: "list",
        component: () => import ("@/pages/mixProportion/mixProportionList"),
        name: "mixProportionPage",
        meta: {
            title: "基准配合比",
            keepAlive: true,
        }
      },
      {
        path: "detail",
        component: () => import ("@/pages/mixProportion/mixProportionDetail"),
        name: "mixProportionDetail",
        meta: {
            title: "配合比详情",
            keepAlive: true,
        }
      },
      {
        path: "add",
        component: () => import ("@/pages/mixProportion/addMixProportion"),
        name: "addMixProportion",
        meta: {
            title: "新增配合比",
            keepAlive: true,
        }
      },
      {
        path: "openAppraisal",
        component: () => import ("@/pages/mixProportion/openAppraisal"),
        name: "openAppraisal",
        meta: {
            title: "开盘鉴定资料",
            keepAlive: true,
        }
      },
    ]
}];
  
  