export default [{
  path: "/engineeringService",
  component: () => import ("@/pages/index.vue"),
  redirect: '/engineeringService/workOrderList',
  children: [
    {
      path: "workOrderList",
      component: () => import ("@/pages/engineeringService/workOrderList"),
      name: "workOrderList",
      meta: {
        title: "工单列表",
        keepAlive: true,
      }
    },
    {
      path: "workOrderDetial",
      component: () => import ("@/pages/engineeringService/workOrderDetial"),
      name: "workOrderDetial",
      meta: {
        title: "工单详情",
        keepAlive: true,
      }
    },
    {
      path: "engineeringList",
      component: () => import ("@/pages/engineeringService/engineeringList"),
      name: "engineeringList",
      meta: {
        title: "工程列表",
        keepAlive: true,
      }
    },
    {
      path: "engineeringDetial",
      component: () => import ("@/pages/engineeringService/engineeringDetial"),
      name: "engineeringDetial",
      meta: {
        title: "工程详情",
        keepAlive: true,
      }
    },
    
    
  ]
  
}];

