/*
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-01-31 22:56:11
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2024-11-11 23:06:17
 * @FilePath: /quality_center_web/src/router/component/index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
export default [
    // 登录
    {
        path: '/',
        component: () => import ('@/pages/NewLoginPage.vue'),
        name: '',
        meta: {
          title: "登录",
        },
    },
    {
        path: '/login',
        component: () => import ('@/pages/NewLoginPage.vue'),
        name: '',
        meta: {
          title: "登录",
        },
    },
    {
      path: "/printRawContent",
      component: () => import ("@/pages/print/printRawContent"),
      name: "printRawContent",
      meta: {
        title: "打印",
        keepAlive: false,
      }
    },
    {
      path: "/printHntContent",
      component: () => import ("@/pages/print/printHntContent"),
      name: "printHntContent",
      meta: {
        title: "打印",
        keepAlive: false,
      }
    },
    {
      path: "/printTaskContent",
      component: () => import ("@/pages/print/printTaskContent"),
      name: "printTaskContent",
      meta: {
        title: "打印",
        keepAlive: false,
      }
    },
    {
      path: '/printEquipmentList',
      component: () => import ("@/pages/print/printEquipmentList"),
      name: "printEquipmentList",
      meta: {
        title: "打印",
        keepAlive: false,
      }
    },
    {
      path: '/printAppraisalCycle',
      component: () => import ("@/pages/print/printAppraisalCycle"),
      name: "printAppraisalCycle",
      meta: {
        title: "打印",
        keepAlive: false,
      }
    },
    {
      path: '/printConfirmTable',
      component: () => import ("@/pages/print/printConfirmTable"),
      name: "printConfirmTable",
      meta: {
        title: "打印",
        keepAlive: false,
      }
    },
    {
      path: '/printMixContent',
      component: () => import ("@/pages/print/printMixContent"),
      name: "printMixContent",
      meta: {
        title: "打印",
        keepAlive: false,
      }
    },
    
    // 404页面
    {
        path: '/404',
        component: () => import ('@/components/404.vue'),
        name: '',
        meta: {
          title: "404",
        },
        hidden: true
    },
    // 首页
    // {
    //     path: '/',
    //     component: () => import ('@/components/Home.vue'),
    //     name: '',
    //     iconPath: 'icon_home',  //图标
    //     leaf: true,  // 只有一个节点
    //     children: [
    //         { path: '/firstpage', component: () => import ('@/pages/FirstPage.vue'), name: '首页' }
    //     ]
    // },

    // 未找到则转到Home
    {
        path: '*',
        component: () => import ('@/components/404.vue'),
        hidden: true,
        meta: {
          title: "404",
        },
        redirect: { path: '/404' }
    }
];

