import moment from 'moment'
const format = 'YYYY-MM-DD'
const monthDayFormat = 'MM-DD'
const yearMonthFormat = 'YYYY-MM'

// window.moment = moment

moment.locale('zh-cn')
export default  {
  firstMonthDay: function (endTime) {
    return moment(endTime).startOf('month').format(format);
  },
  endMonthDay: function (endTime) {
    return moment(endTime).endOf('month').format(format);
  },

  yesterday: function () {
    return moment(Date.now() - 86400000).format(format)
  },
  beforeYesterday: function () {
    return moment(Date.now() - 86400000 * 2).format(format)
  },
  today: function () {
    return moment().format(format)
  },
  todayMonth: function () {
    return moment().format(yearMonthFormat)
  },
  // 半年之前
  halfYear() {
    const diff = 86400000 * 180
    return moment(Date.now() - diff).format(format)
  },
  // 一周之前
  aWeekAgo() {
    const diff = 86400000 * 7
    return moment(Date.now() - diff).format(format)
  },
  // 15天之前
  twoWeekAgo() {
    const diff = 86400000 * 15
    return moment(Date.now() - diff).format(format)
  },

  // X天前
  severalDaysAgo(x) {
    const diff = 86400000 * x
    return moment(Date.now() - diff).format(format)
  },

//年起始
  yearStart(){
    return moment().startOf('year').format(format)
  },
  yearStartMM(){
    return moment().startOf('year').format(yearMonthFormat)
  },
  yearEnd() {
    return moment().endOf('year').format(format)
  },

  //周起始
  getWeekRange(year, weekNumber) {
    console.log(year, weekNumber)
    // 使用moment.js来计算每年的第一天和最后一天
    var firstDayOfYear = moment(year, "YYYY").startOf('year');
    var lastDayOfYear = moment(year, "YYYY").endOf('year');

    // 计算指定周的起止日期
    var firstDayOfWeek = firstDayOfYear.clone().add(weekNumber - 1, 'weeks').startOf('week');
    var lastDayOfWeek = firstDayOfWeek.clone().endOf('week');

    // 返回起止日期字符串
    return {
      start: firstDayOfWeek.format("YYYY-MM-DD"),
      end: lastDayOfWeek.format("YYYY-MM-DD")
    };
  },


  weeks(startDate, endDate) {
    const diff = Math.ceil((moment(endDate).unix() - moment(startDate).unix()) / 86400) + 7
    const res = {}
    let i = 0
    while (i < diff) {
      const m = moment(startDate)
      m.weekday(0)

      const year = m.year() // 年
      const week = m.week() // 第几周
      const weekStart = m.startOf('week').format(monthDayFormat)
      const weekEnd = m.endOf('week').format(monthDayFormat)

      startDate = moment(m.unix() * 1000 + 86400000).format(format)
      const key = `${year}${week}`
      res[key] = res[key] || {}
      res[key] = {
        weekStart,
        weekEnd,
        year,
        week
      }

      i += 7
    }

    return res
  }
}
