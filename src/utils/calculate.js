// 加
function add(a, b) {
  if (a === '' || b === '') {
    return '';
  }

  let c = 0, d = 0, factor;
  try {
    c = a.toString().split(".")[1].length; // 获取 a 的小数位数
  } catch {}
  try {
    d = b.toString().split(".")[1].length; // 获取 b 的小数位数
  } catch {}

  // 使用最大小数位数的倍数来转换成整数进行加法
  factor = Math.pow(10, Math.max(c, d));
  return (a * factor + b * factor) / factor;
}
// 减
function sub(a, b) {
  if (a === '' || b === '') {
    return '';
  }

  let c = 0, d = 0, factor;
  try {
    c = a.toString().split(".")[1].length; // 获取 a 的小数位数
  } catch {}
  try {
    d = b.toString().split(".")[1].length; // 获取 b 的小数位数
  } catch {}

  // 使用最大小数位数的倍数来转换成整数进行减法
  factor = Math.pow(10, Math.max(c, d));
  return ((a * factor - b * factor) / factor).toFixed(Math.max(c, d));
}
// 乘
function mul(a, b) {
  if (a === '' || b === '') {
    return '';
  }

  let c = 0, d = 0;

  try {
    c = a.toString().split(".")[1].length; // 获取 a 的小数位数
  } catch {}
  try {
    d = b.toString().split(".")[1].length; // 获取 b 的小数位数
  } catch {}

  // 计算因子，用于调整小数位到整数运算
  const factor = Math.pow(10, c + d);

  // 将 a 和 b 转换为整数后相乘，再除以因子
  return (Number(a.toString().replace(".", "")) * Number(b.toString().replace(".", ""))) / factor;
}
// 除
function div(a, b) {
  if (a === '' || b === '') {
    return '';
  }

  var e = 0, f = 0;
  try {
    e = a.toString().split(".")[1].length; // 获取 a 的小数位数
  } catch {}
  try {
    f = b.toString().split(".")[1].length; // 获取 b 的小数位数
  } catch {}

  // 转换为整数再进行除法，并调整小数位
  var c = Number(a.toString().replace(".", ""));
  var d = Number(b.toString().replace(".", ""));
  return (c / d) * Math.pow(10, f - e);
}
// 四舍五入到指定的小数位数1
function roundToDecimalPlace(number, decimalPlaces) {
  if(number === ''){
    return '';
  }
  const factor = Math.pow(10, decimalPlaces);
  return numDecimal(Math.round(parseFloat(number) * factor) / factor, decimalPlaces);
}

//公式计算
function calcEquation(arr, decimalPlaces = 0, isXY) {//isXY 'N', 'Y' 是否修约
  //arr = [{ v: 1, k: '+' }, { v: 2, k: '+' }, { v: 3, k: '+' }];
  let isEmpty = false;
  let val = arr.reduce((pre, cur) => {
    if(cur.v === ''){
      isEmpty = true;
    }
    switch (cur.k) {
      case '+': return add(pre || 0, cur.v || 0); break;
      case '-': return sub(pre || 0, cur.v || 0); break;
      case '*': return mul(pre || 0, cur.v || 0); break;
      case '/': return div(pre || 0, cur.v || 0); break;
      default : return add(pre || 0, cur.v || 0); break;
    }
  }, 0);
  
  if(isEmpty){
    return '';
  }else if(isXY === 'N'){
    return roundToDecimalPlace(val, decimalPlaces)
  }else{
    return cpEvenRound(val, decimalPlaces)
  }
}

//公式计算
function customRound(value, decimalPlaces) {
  const factor = Math.pow(10, decimalPlaces); // 放大倍数
  let scaledValue = value * factor;
  let intValue = Math.trunc(scaledValue); // 转换为整数值以便处理
  let remainder = scaledValue - intValue; // 获取小数部分

  const lastDigit = Math.abs(intValue % 10); // 小数保留位后的第一位
  const beforeLastDigit = Math.abs(Math.trunc(intValue / 10) % 10); // 小数保留位的值

  // 处理保留位为5的情况
  if (lastDigit === 5) {
    // 检查5后面是否有非0值（即 remainder > 0 且大于最小容差）
    if (remainder > 0.0000001) {
      intValue = Math.ceil(scaledValue); // 有值，直接进一
    } else {
      // 没有非0值时，判断5前一位数字的奇偶性
      if (beforeLastDigit % 2 !== 0) {
        intValue += 1; // 奇数进一
      }
    }
  } else {
    // 正常四舍六入
    intValue = Math.round(scaledValue);
  }

  // 根据需要返回整数或保留小数的结果
  if (decimalPlaces === 0) {
    return Math.trunc(intValue / factor); // 返回整数
  } else {
    return intValue / factor; // 返回小数
  }
}

/**
 * 修约精度
 * @param num{数值}
 * @param len{精度（1、0.5、10、0.1、0.01）}
 * @param even{修约（四舍五入、四舍六入）}
 * @returns {number}
 */
var cpEvenRound = function(num, len,even) {
    if(len == 0){
      len = 1
    }else if(len == 1){
      len = 0.1
    }else if(len == 2){
      len = 0.01
    }else if(len == 3){
      len = 0.001
    }else if(len == 4){
      len = 0.0001
    }
  
    try {
        if(isNaN(Number(num))){
            return num;
        }
        num = numDecimal(num,10);
        num = Number(num);
        var d = ((len || 0).toString().trim() || 0);
        var rule = 6;//(even!=undefined&&(even.toString().indexOf("5")!=-1||even.toString().indexOf("五")!=-1))?5:6;
        if (len=="0.5"){
            //TODO 精度为 0.5 时
            //(0.75 * 2).修约 / 2
            if (rule==5)
                num = fiveRound(num*2,0)/2;//四舍五入
            else
                num = sixRound(num*2,0)/2;//四舍六入
            //小数位自动补 "0"
            num = numDecimal(num,1);
        }else if (len=="5"){
            //TODO 精度为 ”5“ 时 ，个位只会是（0 | 5）
            //(0.75 * 0.2).修约 / 0.2
            if (rule==5)
                num = fiveRound(num*0.2,0)/0.2;//四舍五入
            else
                num = sixRound(num*0.2,0)/0.2;//四舍六入
            //小数位自动补 "0"
            num = numDecimal(num,0);
        }else if (len=="10"){
            //TODO 精度为 ”10“ 时
            //(0.75 / 10).修约 * 10
            if (rule==5)
                num = fiveRound(num/10,0)*10;//四舍五入
            else
                num = sixRound(num/10,0)*10;//四舍六入
        }else {
            //小数点后有几位，就保留几位小数,例：0.1、0.01
            if (len!=undefined&&len.toString().split(".").length>1)
                d = len.toString().split(".")[1].length;
            else d = 0;
            if (rule==5)
                num = fiveRound(num,d);
            else
                num = sixRound(num,d);
            //小数位自动补 "0"
            num = numDecimal(num,d);
        }
    }catch (e) {
        console.log("修约错误："+e.message);
    }
    console.log("修约结果："+num);
    return num;
}


/**
 * 四舍五入
 * @param num
 * @param decimalPlaces{小数位}
 * @returns {number}
 */
var fiveRound = function(num, decimalPlaces) {
    var d = decimalPlaces || 0;
    return Math.round(num * Math.pow(10, d)) / Math.pow(10, d);
}

/**
 * 四舍六入
 * @param num
 * @param decimalPlaces{小数位}
 * @returns {number}
 */
var sixRound_old = function(num, decimalPlaces) {
    var d = decimalPlaces || 0;
    var m = Math.pow(10, d);
    var n = +(d ? num * m : num).toFixed(8);
    var i = Math.floor(n), f = n - i;
    var e = 1e-8;
    var r = (f > 0.5 - e && f < 0.5 + e) ?
        ((i % 2 == 0) ? i : i + 1) : Math.round(n);
    return d ? r / m : r;
}

/**
 * 四舍六入
 * @param num
 * @param decimalPlaces{小数位}
 * @returns {number}
 */
var sixRound = function(num, decimalPlaces) {
    if (isNaN(num) || decimalPlaces < 0) return '';
    
    // 处理符号并转换绝对值
    const sign = Math.sign(num);
    const absNum = Math.abs(num);
    
    // 将数字转换为字符串以避免浮点精度问题
    const [intPart, decimalPart = ''] = absNum.toString().split('.');
    // 补足小数位（确保有足够的位数进行判断）
    const paddedDecimal = decimalPart.padEnd(decimalPlaces + 1, '0');
    
    // 不需要舍入的情况
    if (paddedDecimal.length <= decimalPlaces) {
        return sign * parseFloat(`${intPart}.${paddedDecimal}`);
    }
    
    // 提取关键位
    let targetDigit, checkDigit, remainingDigits, baseValue;
    
    if (decimalPlaces === 0) {
        // 修约为整数的情况
        targetDigit = parseInt(intPart.slice(-1)); // 个位数字
        checkDigit = parseInt(paddedDecimal[0]);   // 十分位数字
        remainingDigits = paddedDecimal.substring(1);
        baseValue = parseInt(intPart);
    } else {
        // 修约为小数的情况
        targetDigit = parseInt(paddedDecimal[decimalPlaces - 1]);  // 保留位的最后一位
        checkDigit = parseInt(paddedDecimal[decimalPlaces]);       // 需要判断的位
        remainingDigits = paddedDecimal.substring(decimalPlaces + 1);
        baseValue = parseFloat(`${intPart}.${paddedDecimal.substring(0, decimalPlaces)}`);
    }
    
    // 舍入判断逻辑
    let shouldRoundUp = false;
    
    // 规则实现
    if (checkDigit > 5) {
        // 大于5直接进位
        shouldRoundUp = true;
    } else if (checkDigit < 5) {
        // 小于5直接舍去
        shouldRoundUp = false;
    } else { // checkDigit === 5
        if (remainingDigits.length > 0 && !/^0+$/.test(remainingDigits)) {
            // 5后还有非零数字，必须进位
            shouldRoundUp = true;
        } else {
            // 5后无数字或全零，根据前一位奇偶决定
            shouldRoundUp = targetDigit % 2 !== 0; // 奇数进位，偶数舍去
        }
    }
    
    // 计算结果（处理进位）
    let result;
    if (decimalPlaces === 0) {
        // 整数情况
        result = sign * (baseValue + (shouldRoundUp ? 1 : 0));
    } else {
        // 小数情况
        const increment = shouldRoundUp ? 1 / Math.pow(10, decimalPlaces) : 0;
        result = sign * (baseValue + increment);
        result = parseFloat(result.toFixed(decimalPlaces));
    }
    
    return result;
}


/**
 * 小数位不够自动补 “0”
 * @param num {数值}
 * @param len {保留几位小数}
 */
var numDecimal = function(num,len){
    num = ((num || 0).toString().trim() || 0);
    len = ((len || 0).toString().trim() || 0);
    if (len == "0") return num.split(".")[0];
    if (num.toString().indexOf(".") == -1 && len != 0){
        num = (num + ".0");
    }
    var split = num.toString().split(".");
    var decimal = split[1].toString();
    num = split[0].toString();
    if (decimal.length>len){
        decimal = decimal.substring(0,len);
    }else {
        while (decimal.length != len){
            decimal += "0";
        }
    }
    return num+"."+decimal;
}

function isEmpty(val) {
  if (typeof val === "boolean") {
    return false;
  }
  if (typeof val === "number") {
    return false;
  }
  if (val instanceof Array) {
    if (val.length === 0) return true;
  } else if (val instanceof Object) {
    if (JSON.stringify(val) === "{}") return true;
  } else {
    if (
      val === "null" ||
      val == null ||
      val === "undefined" ||
      val === undefined ||
      val === ""
    )
      return true;
    return false;
  }
  return false;
}

export {
  add,
  sub,
  mul,
  div,
  roundToDecimalPlace,
  calcEquation,
  cpEvenRound
}