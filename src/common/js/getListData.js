export default {
  getRoleListAll: async (_this) => {
    let res = await _this.$api.getRoleList({},_this);
    if(res.data.list){
      return res.data.list.map(item => {
        return {
          label: item.roleName,
          value: item.id,
        }
      })
    }else{
      return []
    }
  },
  
  getUserAll: async (_this, params = {}) => {
    let res = await _this.$api.getUserListAll(params,_this);
    if(res.data.list){
      return res.data.list.map(item => {
        return {
          label: item.userName,
          value: item.id,
          job: item.job,
          roleId: item.roleId,
          societyUserName: item.societyUserName,
        }
      })
    }else{
      return []
    }
  },
  
  getEquipmentAll: async (_this) => {
    let res = await _this.$api.getEquipmentAll({},_this);
    if(res.data.list){
      return res.data.list.map(item => {
        return {
          label: item.equipmentName,
          value: item.equipmentNo,
          equipmentNo: item.equipmentNo,
        }
      })
    }else{
      return []
    }
  },
  
  
  getEngineeringListAll: async (_this, contractId) => {
    let res = await _this.$api.getEngineeringListAll({
      contractId: contractId
    },_this);
    if(res.data.list){
      return res.data.list.map(item => {
        return {
          label: item.projectName,
          value: item.id,
        }
      })
    }else{
      return []
    }
  },
  
  getContractAll: async (_this) => {
    let res = await _this.$api.getContractList({},_this);
    if(res.data.list){
      return res.data.list.map(item => {
        return {
          contractId: item.cid,
          id: item.id,
          label: item.contractNo + '--' + item.contractName + '--' +  item.customerName,
          value: item.contractNo,
        }
      })
    }else{
      return []
    }
  },

  getTestProjectInfo: async (parma, _this) => {
    let res = await _this.$api.getTestProject2(parma,_this);
    if(res.data.list){
      return res.data.list.map(item => {
        return {
          label: item.testName,
          value: item.testName,
        }
      })
    }else{
      return []
    }
  },

  // 获取所有任务单
  getTglTrwdAllList: async (parma, _this) => {
    let res = await _this.$api.getTglTrwdAllList(parma,_this);
    if(res.data.list){
      return {
        data: res.data.list,
        opts: res.data.list.map(item => {
          return {
            label: `${item.ftpz},${item.fjzbw},${item.fphbno}`,
            value: `${item.frwdh}`,
          }
        })
      }
    }else{
      return {
        data: [],
        opts: []
      }
    }
  },
  
  getNameFromId(oVal, list){
    for(let i =0;i<list.length; i++){
      if(oVal == list[i].value){
        return list[i].label;
      }
    }
  },
  
};