var SIGN_REGEXP = /([yMdHhsm])(\1*)/g;
var DEFAULT_PATTERN = 'yyyy-MM-dd';
function padding(s, len) {
    var len = len - (s + '').length;
    for (var i = 0; i < len; i++) { s = '0' + s; }
    return s;
};
// 字符转日期（yyyyMMdd -> yyyy-MM-dd）
var yyyyMMdd2DateRegex = /^(\d{4})(\d{2})(\d{2})$/;

import emEnum from './emEnum';
import API from '../../api/index'

export default {
    // 公用正则表达式（不能内部使用，需考虑怎么内部使用）
    Regex: {
        // 日期字符串转时间
        yyyyMMdd2DateRegex: yyyyMMdd2DateRegex,
        longDateRegex: /^(\d{1,4})(-|\/)(\d{1,2})\2(\d{1,2}) (\d{1,2}):(\d{1,2}):(\d{1,2})$/,
    },
    // 计算 startDate days 后的时间
    calculateFutureDate(startDate, days) {
            // 创建起始日期的副本
            const futureDate = new Date(startDate);
            // 增加指定天数
            futureDate.setDate(futureDate.getDate() + days);
            
            // 格式化为YYYY-MM-DD
            const year = futureDate.getFullYear();
            const month = String(futureDate.getMonth() + 1).padStart(2, '0');
            const day = String(futureDate.getDate()).padStart(2, '0');
            
            return `${year}-${month}-${day}`;
        },
    // 获取查询字符串
    getQueryStringByName: function (name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
        var r = window.location.search.substring(1).match(reg);
        var context = "";
        if (r != null)
            context = r[2];
        reg = null;
        r = null;
        return context == null || context == "" || context == "undefined" ? "" : context;
    },
    // 获取GUID
    GUID: function () {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
            var r = Math.random()*16|0, v = c == 'x' ? r : (r&0x3|0x8);
            return v.toString(16);
        });
    },
    // 获取浮点数值
    getFloatVal: function (val, dnum = -1) {
        val = val.replace(/[^\d.]/g, "");  // 保留数字
        val = val.replace(/^00/, "0.");    // 开头不能有两个0
        val = val.replace(/^\./g, "0.");   // 开头为小数点转换为0.
        val = val.replace(/\.{2,}/g, "."); // 两个以上的小数点转换成一个
        val = val.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");  // 只保留一个小数点
        /^0\d+/.test(val) ? val = val.slice(1) : '';  // 两位以上数字开头不能为0

        // 小数位数
        if(dnum == 0) {
            /\.\d{0}/.test(val) ? val = val.slice(0, -1) : ''; //限制最多0位小数
        } else if(dnum == 1) {
            /\.\d{2}/.test(val) ? val = val.slice(0, -1) : ''; //限制最多1位小数
        } else if(dnum == 2) {
            /\.\d{3}/.test(val) ? val = val.slice(0, -1) : ''; //限制最多2位小数
        } else if(dnum == 3) {
            /\.\d{4}/.test(val) ? val = val.slice(0, -1) : ''; //限制最多3位小数
        } else if(dnum == 4) {
            /\.\d{5}/.test(val) ? val = val.slice(0, -1) : ''; //限制最多4位小数
        } else if(dnum == 5) {
            /\.\d{6}/.test(val) ? val = val.slice(0, -1) : ''; //限制最多5位小数
        } else if(dnum == 6) {
            /\.\d{7}/.test(val) ? val = val.slice(0, -1) : ''; //限制最多6位小数
        } else if(dnum == 7) {
            /\.\d{8}/.test(val) ? val = val.slice(0, -1) : ''; //限制最多7位小数
        } else if(dnum == 8) {
            /\.\d{9}/.test(val) ? val = val.slice(0, -1) : ''; //限制最多8位小数
        } else if(dnum == 9) {
            /\.\d{10}/.test(val) ? val = val.slice(0, -1) : ''; //限制最多9位小数
        } else if(dnum == 10) {
            /\.\d{11}/.test(val) ? val = val.slice(0, -1) : ''; //限制最多10位小数
        }
        return val
    },
    // 格式化指标显示
    formatIndicatorShow: function (val, projectName = "") {
        let result = val;
        if(!!val) {
            result = val.replaceAll(">", "大于")
                        .replaceAll("<", "小于")
                        .replaceAll("==", "等于")
                        .replaceAll(">=", "大于等于")
                        .replaceAll("<=", "小于等于")
                        .replaceAll("!=", "不等于")
                        .replaceAll("&&", "且")
                        .replaceAll("||", "或")
                        .replaceAll("val", projectName)
                        .replaceAll(" 且 " + projectName, ",");
        }
        return result;
    },
    // 日期格式化
    formatDate: {
        // 日期转字符串
        format: function (date, pattern) {
            pattern = pattern || DEFAULT_PATTERN;
            return pattern.replace(SIGN_REGEXP, function ($0) {
                switch ($0.charAt(0)) {
                    case 'y': return padding(date.getFullYear(), $0.length);
                    case 'M': return padding(date.getMonth() + 1, $0.length);
                    case 'd': return padding(date.getDate(), $0.length);
                    case 'w': return date.getDay() + 1;
                    case 'H': return padding(date.getHours(), $0.length);
                    case 'h': return padding(date.getHours(), $0.length);
                    case 'm': return padding(date.getMinutes(), $0.length);
                    case 's': return padding(date.getSeconds(), $0.length);
                }
            });
        },
        // 格式化yyyyMMdd格式的日期字符串
        yyyyMMdd2Caption: function (prefix, dateString) {
            // 把yyyyMMdd格式的日期字符串转为可用的时间字符串
            let dateStr = this.yyyyMMdd2Date(dateString);
            let week = this.date2Week(prefix, dateStr);
            let formatStr = this.format(new Date(dateStr), "M月d日");
            return week + ' ' + formatStr;
        },
        // 格式化日期字符串
        date2Caption: function (prefix, dateString) {
            // 获取周几
            let week = this.date2Week(prefix, dateString);
            let formatStr = this.format(new Date(dateString), "M月d日");
            return week + ' ' + formatStr;
        },
        // 判断开始时间是否在时间段内
        dateIsBlongRange: function (dateString, dateRange) {
            // 要判断的时间
            let date = this.format(new Date(dateString.replace(/-/g, '/')), "yyyy-MM-dd HH:mm:ss");
            let dateDate = new Date(date.replace(/-/g, '/'));
            // 获取时间段的开始和结束时间
            let begin = dateRange.substr(0, dateRange.indexOf("~"));
            let end = dateRange.substr(dateRange.indexOf("~") + 1);
            let dateStr = this.format(new Date(dateString.replace(/-/g, '/')), "yyyy-MM-dd");
            let beginDate = new Date(dateStr.replace(/-/g, '/') + " " + begin + ":00");
            let endDate = new Date(dateStr.replace(/-/g, '/') + " " + end + ":00");
            // 返回是否在范围内
            return !!dateDate && !! beginDate && !!endDate && beginDate <= dateDate && endDate > dateDate;
        },
        // 字符转日期（yyyyMMdd -> yyyy-MM-dd）
        yyyyMMdd2Date: function (dateString) {
            if (!!dateString) {
                return dateString.replace(yyyyMMdd2DateRegex, "$1-$2-$3");
            }
            return null;
        },
        // 根据日期获取周几
        date2Week: function (prefix, dateString) {
            let dayStr = "日一二三四五六";
            if(prefix === "星期") {
                dayStr = "天一二三四五六";
            }
            if (!!dateString) {
                let date = new Date(dateString.replace(/-/g, '/'));
                return prefix + dayStr.charAt(date.getDay());
            }
            return prefix;
        },
        // 字符串转日期
        parse: function (dateString, pattern) {
            var matchs1 = pattern.match(SIGN_REGEXP);
            var matchs2 = dateString.match(/(\d)+/g);
            if (matchs1.length == matchs2.length) {
                var _date = new Date(1970, 0, 1);
                for (var i = 0; i < matchs1.length; i++) {
                    var _int = parseInt(matchs2[i]);
                    var sign = matchs1[i];
                    switch (sign.charAt(0)) {
                        case 'y': _date.setFullYear(_int); break;
                        case 'M': _date.setMonth(_int - 1); break;
                        case 'd': _date.setDate(_int); break;
                        case 'H': _date.setHours(_int); break;
                        case 'h': _date.setHours(_int); break;
                        case 'm': _date.setMinutes(_int); break;
                        case 's': _date.setSeconds(_int); break;
                    }
                }
                return _date;
            }
            return null;
        }
    },

    // 根据导出列格式化Json数据
    formatJson: function (jsonData, filterVal) {
        return jsonData.map(v => filterVal.map(j => v[j]));
    },

    // 字符串转为Dom
    string2Dom: function (arg) {
        var objE = document.createElement("div");
        objE.innerHTML = arg;
        return objE.childNodes;
    },

    // 数组处理
    array: {
        // 向数组中增加元素
        addElement: function(array, ele) {
            array.push(ele);
            return array;
        },
        // 从数组中移除元素
        removeElement: function(array, ele) {
            let index = -1;
            for (var i = 0; i < array.length; i++) {
                if (JSON.stringify(array[i]) == JSON.stringify(ele)) {
                    index = i;
                }
            }
            if (index > -1) {
                array.splice(index, 1);
            }
            return array;
        }
    },

    // 比较(时间)
    compareDateTime: function (propertyName) {
        return function (object1, object2) {
            var value1 = new Date(object1[propertyName]);
            var value2 = new Date(object2[propertyName]);
            if (value2 > value1) {
                return -1;
            } else if (value2 < value1) {
                return 1;
            } else {
                return 0;
            }
        }
    },

    // 比较(id)
    compareID: function (propertyName) {
        return function (object1, object2) {
            var value1 = parseInt(object1[propertyName]);
            var value2 = parseInt(object2[propertyName]);
            if (value2 > value1) {
                return -1;
            } else if (value2 < value1) {
                return 1;
            } else {
                return 0;
            }
        }
    },
    // 用户数据相关处理
    userData: {
        // 失效用户重新登录
        reLogin: function (_this, tip = true, msg = '用户失效，请重新登录！') {
            if (tip) {
                _this.$message({
                    message: msg,
                    type: 'error'
                });
            }
            this.clearLocalStorage();
            // 转到登录页面
            _this.$router.push({ path: '/login' });
        },
        // 清除 localStorage 中保存的数据
        clearLocalStorage: function () {
            localStorage.removeItem('bbToken');
            localStorage.removeItem('vuex');
            sessionStorage.removeItem('vuex');
            API.token = undefined;
            API.uid = undefined;
        }
    },

    experimentTypeToName(type) {
        switch (type + "") {
            case "1":
                return "水泥";
            case "2":
                return "粉煤灰";
            case "3":
                return "矿渣粉";
            case "4":
                return "粗骨料";
            case "5":
                return "细骨料";
            case "6":
                return "外加剂";
            case "7":
                return "混凝土";
            default: 
                return "";
        }
    }
};
