/*
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-07-16 22:52:01
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2024-11-30 23:13:14
 * @FilePath: /quality_center_web/src/pages/supplierMgt/config.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { h } from 'vue'
import moment from 'moment'
export const supplierColumn = [{
  label: '供应商编号',
  prop: 'erpCompanyId',
},{
  label: '供应商名称',
  prop: 'erpCompanyName',
},{
  label: '供应商简称',
  prop: 'supplierAbbreviation',
},{
  label: '联系人/联系电话',
  prop: 'userName',
  formatter: (row) => {    
    return `${row.userName || '--'}/${row.userPhone || '--'}`
  },
},
{
  label: '备案证编号',
  prop: 'certificateNo',
},{
  label: '备案证有效期',
  prop: '',
  width: "220",
  formatter: (row) => {    
    if (row.certificateDateStart && row.certificateDateEnd) {
      return `${moment(row.certificateDateStart).format("YYYY-MM-DD")} 至 ${moment(row.certificateDateEnd).format("YYYY-MM-DD")}`
    }
    return "--";
  },
}]

export const supplierMaterialsColumn = [
  {
    label: '物料编号',
    prop: 'erpWlId',
  },
  {
    label: '物料名称',
    prop: 'materialsName',
    formatter: (row) => {
        return `${row.materialsName} / ${row.materialsSpec}`
    },
  },
  {
    label: '材料类型',
    prop: 'materialsType',
    formatter: (row,materialsTypeOpts) => {
        for(let i =0;i<materialsTypeOpts.length; i++){
            if(row.materialsType == materialsTypeOpts[i].value){
                return materialsTypeOpts[i].label;
            }
        }
    },
  },
  {
    label: '材料名称',
    prop: 'materialsConfigName',
  },
  {
    label: '材料规格',
    prop: 'materialsConfigSpec',
    formatter: (row) => {
      return row.materialsConfigSpec ? row.materialsConfigSpec : row.materialsConfigName;
    },
  },
  {
    label: '厂家',
    prop: 'manufacturers',
  },
  {
    label: '厂家简称',
    prop: 'manufacturersCalled',
  },
  {
    label: '单价',
    prop: 'unitPrice',
  },
  {
    label: '供应商名称',
    prop: 'supplierCompanyName',
  },
  {
    label: '型式检验编号',
    prop: 'inspectionNo',
  },
  {
    label: '型式检验结果',
    prop: 'inspectionResult',
  },
  {
    label: '型式检验时间',
    prop: 'inspectionDateStart',
    formatter: (row) => {    
      if (row.inspectionDateStart && row.inspectionDateEnd) {
        return `${moment(row.inspectionDateStart).format("YYYY-MM-DD")} 至 ${moment(row.inspectionDateEnd).format("YYYY-MM-DD")}`
      }
      return "--";
    },
  },
  {
    label: '是否甲供',
    prop: 'selfSupply',
    formatter: (row) => {
      if (row.selfSupply == 1) {
        return '是'
      }
      return '否'
    }
  },
]