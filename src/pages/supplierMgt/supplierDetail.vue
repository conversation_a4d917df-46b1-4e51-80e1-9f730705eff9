<!--
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-07-16 22:48:09
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2024-12-19 22:59:09
 * @FilePath: /quality_center_web/src/pages/supplierMgt/supplierList.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div class="content-box">
        <div class="flex-box flex-column content">
            <el-row class="row-title">
                <span class="row-title-label">
                    基本信息（ 编号：{{ detailInfo.erpCompanyId }}）
                    <span class="supplier-name-status">正常</span>
                </span>
            </el-row>

            <el-row>
                <!-- <div class="supplier-no">
                    {{ detailInfo.erpCompanyId }}
                    <span :class="[detailInfo.status=='1'?'supplier-name-status':'supplier-name-status2']">{{detailInfo.status=='1'?'正常':'禁用'}}</span>
                </div> -->
                <div class="">
                    <section class="jbz-intro">
                        <div class="display-inline">
                            <span class="font_2">供应商名称：</span>
                            <span class="font_3">{{detailInfo.erpCompanyName || '--'}}</span>
                        </div>
                        <div class="display-inline">
                            <span class="font_2">供应商简称：</span>
                            <span class="font_3">{{detailInfo.supplierAbbreviation || '--'}}</span>
                        </div>
                        <div class="vertical-space_3 display-inline">
                            <span class="font_2">联系人：</span>
                            <span class="font_3">{{detailInfo.userName || '--'}}/{{detailInfo.userPhone || '--'}}</span>
                        </div>
                        <div class="flex-row" style="justify-content: flex-start; align-items: flex-start; margin-top: 10px;">
                            <span class="font_2">备案照片：</span>
                            <div v-for="(item, index) in certificateImgList" :key="item.filePath" style="position: relative;">
                                <img style="width: 148px; height: 148px; margin-right: 10px; object-fit: cover;" :src="item.filePath" />
                            </div>
                        </div>
                        <div class="flex-row" style="justify-content: flex-start; align-items: flex-start; margin-top: 10px;">
                            <span class="font_2">营业执照：</span>
                            <div v-for="(item, index) in businessLicenseList" :key="item.filePath" style="position: relative;">
                                <img style="width: 148px; height: 148px; margin-right: 10px; object-fit: cover;" :src="item.filePath" />
                            </div>
                        </div>
                    </section>
                    <section class="supplier-ul">
                        <div class="supplier-flex">
                            <div class="supplier-flex-li">
                                <div class="supplier-flex-num">{{statisticsInfo.materialsCount != null ? statisticsInfo.materialsCount : '0'}}</div>
                                <div class="supplier-flex-count">物料总数</div>
                            </div>
                            <div class="supplier-flex-li">
                                <div class="supplier-flex-num">{{statisticsInfo.formalWarnCount != null ? statisticsInfo.formalWarnCount : "0"}}</div>
                                <div class="supplier-flex-count">型检预警</div>
                            </div>
                            <div class="supplier-flex-li">
                                <div class="supplier-flex-num">{{statisticsInfo.formalOverdueCount != null ? statisticsInfo.formalOverdueCount : "0"}}</div>
                                <div class="supplier-flex-count">型检过期</div>
                            </div>
                            <div class="supplier-flex-li">
                                <div class="supplier-flex-num">{{statisticsInfo.doubleWarnCount != null ? statisticsInfo.doubleWarnCount : "0"}}</div>
                                <div class="supplier-flex-count">复检预警</div>
                            </div>
                            <div class="supplier-flex-li">
                                <div class="supplier-flex-num">{{statisticsInfo.doubleOverdueCount != null ? statisticsInfo.doubleOverdueCount : "0"}}</div>
                                <div class="supplier-flex-count">复检过期</div>
                            </div>
                        </div>

                    </section>
                </div>
            </el-row>

            <el-row class="sub-table">
                <el-row class="row-title" style="height: 30px;">
                    <span class="row-title-label">编辑物料</span>
                    <!-- <el-button style="position: absolute; right: 30px;" type="primary" size="mini" :disabled="loading" @click="addDrawerForm">新增物料</el-button> -->
                    <el-button style="position: absolute; right: 0px;" type="primary" size="mini" @click="gotoMaterialsConfig">配置物料规则</el-button>
                </el-row>

                <el-row class="table-view" style="margin-top: 10px;">
                    <el-table
                        :data="tableData"
                        height="100%"
                        border
                        :row-style="cellStyle"
                        >
                        <template v-for="item in tableColumn" >
                            <el-table-column
                                :prop="item.prop" 
                                :label="item.label" 
                                :fixed="item.fixed" 
                                :width="item.width || ''"
                                :formatter="item.prop === 'materialsType' ? (row) => item.formatter(row,materialsTypeOpts) : item.formatter"
                                align="center" 
                            />
                        </template>
                        
                        
                        <el-table-column width="150" label="操作" align="center" key="handle" fixed="right" :resizable="false">
                            <template slot-scope="scope">
                                <el-button type="text" size="small" @click="addDrawerForm(scope.row)">编辑</el-button>
                                <!-- <el-button type="text" size="small" style="color: #ff0000;margin-left: 30px;" @click="deleteSupplierCompanyMaterials(scope.row)">删除</el-button> -->
                            </template>
                        </el-table-column>
                    </el-table>
                </el-row>

                <el-row class="page-view">
                    <Pagination
                        :total="total" 
                        :pageNum="pageObj.pageNum" 
                        :pageSize="pageObj.pageSize" 
                        @getData="querySupplierCompanyMaterialsListPageResp" 
                    />
                </el-row>
            </el-row>
        </div>

        <el-drawer
            title="物料供应配置"
            :visible.sync="drawer"
            direction="rtl"
            :before-close="handleClose"
        >
            <div class="flex-box flex-column h100p drawer-box">
                <div class="flex-item ofy-auto">
                    <el-form label-width="160px" 
                        :model="editForm"
                        :rules="editFormRules"
                        ref="editForm"
                        :show-message="false"
                    >
                    <el-form-item
                        v-for="(el,index) in formContent"
                        :key="index"
                        :label="`${el.label}：`"
                        :required="el.required || false"
                        :prop="el.prop"
                    >
                        <el-select
                            v-if="el.type === 'select'"
                            v-model="editForm[el.prop]"
                            @change="el['change']"
                            filterable clearable 
                            :placeholder="'请选择' + el.placeholder"
                            style="width: 350px"
                            :value-key="el.valueKey"
                        >
                            <el-option
                                v-for="(item, index) in el.options"
                                :key="item.label+'_'+index"
                                :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                        <el-input 
                            v-else-if="el.type === 'input'"
                            v-model="editForm[el.prop]" 
                            :disabled="el.disabled"
                            clearable
                            :placeholder="el.placeholder || `请输入${el.label}`"
                            style="width: 350px"
                        />
                        <el-input 
                            v-else-if="el.type === 'number'"
                            v-model="editForm[el.prop]" 
                            :disabled="el.disabled"
                            clearable
                            :placeholder="el.placeholder || `请输入${el.label}`"
                            style="width: 350px"
                            :type="el.type"
                            v-manual-update
                        />
                        <el-date-picker
                            v-else-if="el.type === 'date' || el.type === 'datetime'"
                            :type="el.type"
                            v-model="editForm[el.prop]"
                            placeholder="选择日期/时间"
                            :format="el.format"
                            :value-format="el.valueFormat"
                            :style="{'width': el.width ? el.width : 350 + 'px'}"
                            :picker-options="el.prop === 'inspectionDateEnd' ? endTimePickerOptions : {}"
                        >
                        </el-date-picker>
                    </el-form-item>
                    </el-form>
                </div>
                <div class="drawer-footer">
                    <el-button type="primary" @click="drawer = false" plain>取消</el-button>
                    <el-button type="primary" @click="saveTarget">保存</el-button>
                </div>
            </div>
        </el-drawer>
    </div>
  </template>
  
<script>
import { supplierMaterialsColumn as tableColumn } from "./config.js"
import Pagination from "@/components/Pagination/index.vue";
import DrawerForm from "@/components/drawerForm.vue";
import moment from "moment";
export default {
    components: {
        Pagination,
        DrawerForm
    },
    data() {
        return {
            baseUrl: process.env.NODE_ENV === 'development' ? "/api" : "/mes-platform",
            filePrefix: this.$store.state.loginStore.userInfo.filePrefix,

            loading: false,
            
            tableColumn: tableColumn,
            pageObj: {
                pageNum: 1, // 页数
                pageSize: 10, // 条数
            },
            total: 1,
            tableData: [],
            detailInfo: {},
            formContent: [],
            materialsTypeOpts: [],
            specConfigOpts: [],
            specConfig_2Opts: [],
            drawer: false,
            editForm: {},
            certificateImgList: [],
            businessLicenseList: [],
            statisticsInfo: {},

            editFormRules: {
                materialsConfigType: [{ required: true, message: '请选择材料类型', trigger: 'change' }],
                materialsConfigName: [{ required: true, message: '请选择材料名称', trigger: 'change' }],
                materialsConfigSpec: [{ required: true, message: '请选择材料规格', trigger: 'change' }],
                manufacturers: [{ required: true, message: '请输入厂家名称', trigger: 'blur' }],
                unitPrice: [{ required: true, message: '请输入单价', trigger: 'blur' }],
                inspectionNo: [{ required: true, message: '请输入型式检验编号', trigger: 'blur' }],
                inspectionResult: [{ required: true, message: '请输入型式检验结果', trigger: 'blur' }],
                inspectionDateStart: [{ required: true, message: '请选择型式检验开始时间', trigger: 'change' }],
                inspectionDateEnd: [{ required: true, message: '请选择型式检验结束时间', trigger: 'change' }],
            },
        };
    },

    watch: {
        "editForm.inspectionDateStart": {
            handler(newVal) {
                if (newVal && this.editForm.inspectionDateEnd) {
                    if (moment(newVal).isAfter(this.editForm.inspectionDateEnd)) {
                        this.editForm.inspectionDateEnd = "";
                    }
                }
            },
        },
    },

    computed: {
        endTimePickerOptions() {
            return {
                disabledDate: (time) => {
                    if (this.editForm.inspectionDateStart) {
                        return moment(time).isBefore(this.editForm.inspectionDateStart);
                    }
                    return false;
                },
            };
        },
    },

    created() {
        this.$api.getDictValue({
            dictCode: 'MASTERIAL_TYPE'
        }, this).then(res => {
            this.materialsTypeOpts = res.data.list.map(item => {
                return {
                    label: item.dictValueName,
                    value: item.dictValueCode + '',
                }
            })

            this.formContent = [
                {
                    type: 'input',
                    label: '物料名称',
                    prop: 'materialsName',
                    disabled: true,
                    placeholder: "物料名称"
                },
                {
                    type: 'input',
                    label: '物料规格',
                    prop: 'materialsSpec',
                    disabled: true,
                    placeholder: "物料规格"
                },
                {
                    type: 'select',
                    label: '材料类型',
                    prop: 'materialsConfigType',
                    placeholder: "材料类型",
                    options: this.materialsTypeOpts,
                    change: (item) => this.setMaterials(item)
                },
                {
                    type: 'select',
                    label: '材料名称',
                    prop: 'materialsConfigName',
                    options: this.specConfigOpts,
                    valueKey: 'materialsName',
                    placeholder: "材料名称",
                    change: (item) => this.changeSelect(item)
                },
                {
                    type: 'select',
                    label: '材料规格',
                    prop: 'materialsConfigSpec',
                    options: this.specConfig_2Opts,
                    valueKey: 'materialsSpec',
                    placeholder: "材料规格",
                    change: (item) => this.changeSelectSpec(item)
                },
                {
                    type: 'input',
                    label: '厂家名称',
                    prop: 'manufacturers',
                    placeholder: "厂家名称",
                    required: true,
                },
                {
                    type: 'input',
                    label: '厂家简称',
                    prop: 'manufacturersCalled',
                    placeholder: "厂家简称",
                    required: true,
                },
                {
                    type: 'number',
                    label: '单价',
                    prop: 'unitPrice',
                    placeholder: "单价",
                    required: true,
                },
                {
                    type: 'input',
                    label: '型式检验编号',
                    prop: 'inspectionNo',
                    placeholder: "型式检验编号",
                    required: true,
                },
                {
                    type: 'input',
                    label: '型式检验结果',
                    prop: 'inspectionResult',
                    placeholder: "型式检验结果",
                    required: true,
                },
                {
                    type: 'date',
                    label: '型式检验开始时间',
                    prop: 'inspectionDateStart',
                    format: 'yyyy-MM-dd',
                    valueFormat: "yyyy-MM-dd HH:mm:ss",
                    required: true,
                },
                {
                    type: 'date',
                    label: '型式检验结束时间',
                    prop: 'inspectionDateEnd',
                    format: 'yyyy-MM-dd',
                    valueFormat: "yyyy-MM-dd HH:mm:ss",
                    required: true,
                },
                {
                    type: 'select',
                    label: '是否甲供',
                    prop: 'selfSupply',
                    options: [{
                        label: '是',
                        value: '1'
                    }, {
                        label: '否',
                        value: '0'
                    }],
                    placeholder: "是否甲供",
                    change: (item) => this.changeSelfSupply(item)
                },
            ];

            this.queryDetailResp();
            this.initData();
        });

        this.getSupplierCompanyMaterialsStatisticsResp();
    },
    methods: {
        cellStyle({row, column, rowIndex, columnIndex}) {
            if (row.warningStatus == '1') {
                return {color: "#FF7F50"};
            } else if (row.warningStatus == '2') {
                return {color: "#FF4500"};
            } else {
                return {color: "#333"};
            }
        },
        queryDetailResp() {
            this.$api.querySupplierCompnayDetail(`id=${this.$route.query.id}`, this)
            .then(res => {
                if (res.code == 1) {
                    this.detailInfo = res.data;
                    if (res.data.certificateImg) {
                        this.certificateImgList = res.data.certificateImg.split(",").map(item => {
                            return {
                                fileName: item,
                                filePath: this.filePrefix + item
                            }
                        });
                    }
                    if (res.data.businessLicense) {
                        this.businessLicenseList = res.data.businessLicense.split(",").map(item => {
                            return {
                                fileName: item,
                                filePath: this.filePrefix + item
                            }
                        });
                    }
                    
                }else{
                    this.$message.error(res.msg || '查询失败')
                }
            });
        },

        initData() {
            this.pageObj = {
                pageNum: 1, // 页数
                pageSize: 10, // 条数
            }

            this.querySupplierCompanyMaterialsListPageResp();
        },

        querySupplierCompanyMaterialsListPageResp(pageNum, pageSize) {
            this.loading = true;
            if (pageNum) this.pageObj.pageNum = pageNum;
            if (pageSize) this.pageObj.pageSize = pageSize;
            
            this.$api.querySupplierCompanyMaterialsListPage({
                ...this.pageObj,
                params: {
                    supplierCompanyId: this.$route.query.id
                }
            }, this)
            .then(res => {
                this.loading = false;
                if (res.code == 1) {
                    this.tableData = res.data.list;
                    this.total = res.data.total;
                }else{
                    this.$message.error(res.msg || '查询失败')
                }
            });
        },

        setMaterials(row) {
            this.$api.querySelectByMaterialsType(`materialsType=${row}`, this).then(res => {
                if (res.code == 1) {
                    this.specConfigOpts = res.data.list.map(item => {
                        return {
                            label: item.materialsName + (item.sampleJudge ? `【${item.sampleJudge}】` : ''),
                            value: item,
                        }
                    })
                    this.$set(this.formContent[3], "options", this.specConfigOpts);
                }
            })
            
            // 清空材料名称 和材料规格
            this.$set(this.editForm, "materialsConfigType", row);
            this.$set(this.editForm, "materialsConfigName", "");
            this.$set(this.editForm, "materialsConfigSpec", "");
        },

        queryMaterialsSpecOpts(materialType, materialsName) {
            this.$api.queryMaterialsSpecConfigAll({
                materialsType: materialType,
                materialsName: materialsName
            }, this).then(res => {
                if (res.code == 1) {
                    this.specConfig_2Opts = res.data.list.map(item => {
                        return {
                            label: item.materialsSpec || item.materialsName,
                            value: item,
                        }
                    })
                    this.$set(this.formContent[4], "options", this.specConfig_2Opts);
                }
            })
        },

        getSupplierCompanyMaterialsStatisticsResp() {
            this.$api.getSupplierCompanyMaterialsStatistics({
                supplierCompanyId: this.$route.query.id
            }, this).then(res => {
                if (res.code == 1) {
                    this.statisticsInfo = res.data;
                }
            })
        },

        changeSelect(item) {
            let materialsName = item.materialsName + (item.sampleJudge ? `【${item.sampleJudge}】` : '');
            this.$set(this.editForm, "materialsConfigName", materialsName || '');
            this.$set(this.editForm, "materialsConfigNameOrg", item.materialsName || '');
            this.queryMaterialsSpecOpts(item.materialType, item.materialsName || '');
            this.$set(this.editForm, "materialsConfigSpec", "");
        },
        changeSelectSpec(item) {
            this.$set(this.editForm, "materialsConfigSpec", item.materialsSpec || '');
            this.$set(this.editForm, "specId", item.id);
        },
        changeSelfSupply(item) {
            this.$set(this.editForm, "selfSupply", item);
        },
        handleClose(done) {
            this.$confirm('确认关闭？')
            .then(_ => {
                if(done){
                    done();
                }
                this.drawer = false;
            })
            .catch(_ => {});
        },

        addDrawerForm(row) {
            console.log('物料信息：', row);
            let materialsConfigName = row.materialsConfigName + (row.sampleJudge ? `【${row.sampleJudge}】` : '')
             if (materialsConfigName === null || materialsConfigName === undefined || materialsConfigName === '' || materialsConfigName === 'null') {
                // 满足条件
                materialsConfigName = '';
            }
            if (row) {
                this.editForm = {
                    supplierCompanyId: this.$route.query.id || '',
                    id: row.erpWlId || '',
                    materialsNo: row.materialsNo || '',
                    specId: row.specId || '',
                    erpWlId:row.erpWlId || '',
                    // materialsType: `${row.materialsType || ''}`,
                    materialsName: row.materialsName || '',
                    materialsSpec: row.materialsSpec || '',
                    manufacturers: row.manufacturers || '',
                    manufacturersCalled: row.manufacturersCalled || '',
                    inspectionNo: row.inspectionNo || '',
                    inspectionResult: row.inspectionResult || '',
                    inspectionDateStart: row.inspectionDateStart || '',
                    inspectionDateEnd: row.inspectionDateEnd || '',
                    selfSupply: row.selfSupply != null ? row.selfSupply + "" : "0",
                    materialsConfigType:`${row.materialsConfigType || ''}`,
                    materialsConfigName:materialsConfigName,
                    materialsConfigNameOrg:row.materialsConfigName || '',
                    materialsConfigSpec:row.materialsConfigSpec || '',
                    unitPrice:row.unitPrice || '',
                    erpCompanyId:this.$route.query.id
                };
                this.$api.querySelectByMaterialsType(`materialsType=${row.materialsType}`, this).then(res => {
                    if (res.code == 1) {
                        this.specConfigOpts = res.data.list.map(item => {
                            return {
                                label: item.materialsName + (item.sampleJudge ? `【${item.sampleJudge}】` : ''),
                                value: item,
                            }
                        })
                        this.$set(this.formContent[3], "options", this.specConfigOpts);
                    }
                })
            }else{
                this.editForm = {
                    supplierCompanyId: this.$route.query.id
                };
            }

            this.drawer = true;
        },

        saveTarget() {
            this.$refs.editForm.validate((valid) => {
                if (valid) {
                    let api =  "updateSupplierCompanyMaterials"; 
                    let parma = {
                        supplierCompanyId: this.$route.query.id,
                        specId: this.editForm.specId,
                        // materialsType: parseInt(this.editForm.materialsType),
                        manufacturers: this.editForm.manufacturers,
                        manufacturersCalled: this.editForm.manufacturersCalled,
                        inspectionNo: this.editForm.inspectionNo,
                        inspectionResult: this.editForm.inspectionResult,
                        inspectionDateStart: this.editForm.inspectionDateStart,
                        inspectionDateEnd: this.editForm.inspectionDateEnd,
                        selfSupply: this.editForm.selfSupply != null ? this.editForm.selfSupply + "" : "0",
                        materialsConfigType:this.editForm.materialsConfigType,
                        materialsName:this.editForm.materialsName,
                        materialsSpec:this.editForm.materialsSpec,
                        materialsConfigName:this.editForm.materialsConfigNameOrg,
                        materialsConfigSpec:this.editForm.materialsConfigSpec,
                        erpWlId:this.editForm.erpWlId,
                        id:this.editForm.erpWlId,
                        erpCompanyId:this.editForm.erpCompanyId,
                        unitPrice: parseFloat(this.editForm.unitPrice).toFixed(4)
                    }
                    this.$api[api](parma, this).then(res => {
                        if (res.succ) {
                            this.$message({
                                showClose: true,
                                message: "操作成功",
                                type: "success",
                            });
                            this.drawer = false;
                            this.queryDetailResp();
                            this.initData();
                        }
                    })
                }
            });
        },

        deleteSupplierCompanyMaterials(row) {
            this.$confirm("删除后不能恢复，确定要删除吗？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                //删除
                this.$api.deleteSupplierCompanyMaterials({id: row.id}, this).then(res => {
                    if (res.succ) {
                        this.$message({
                            showClose: true,
                            message: "操作成功",
                            type: "success",
                        });
                        this.initData();
                    }
                })
            });
        },

        gotoMaterialsConfig() {
            this.$router.push({
                name: "materialsConfig",
            })
        }
    },
};
</script>

<style scoped lang="scss">
.content-box{
    padding: 16px;
}

.content{
    width: 100%;
    height: 100%;
    padding: 16px;
    background: #FFFFFF;
    border-radius: 16px;
    overflow: auto;
}
.row-title {
  border-bottom: #DDDFE6 1px solid;
  padding-bottom: 8px;
  position: relative;
  .row-title-label {
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 16px;
    color: #1F2329;
  }
}
.dg-card-view {
  margin-top: 24px;
  width: 100%;
  .dj-card {
    width: 177px;
    height: 72px;
    border-radius: 4px;
    position: relative;
    justify-content: center;
    .dj-label {
      font-family: PingFangSC, PingFang SC;
      font-size: 14px;
      color: #6A727D;
      margin-top: 4px
    }
    .dj-value {
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      font-size: 20px;
      color: #1F2329;
    }
    .dj-img {
      width: 46px;
      height: 46px;
      position: absolute;
      right: 0;
      bottom: 0
    }
  }
}
.cs-view {
  width: '100%';
  height: 52px;
  background: #F2F6FE;
  border-radius: 4px;
  margin-top: 16px;
  padding-left: 16px;
  padding-right: 16px;
  font-size: 14px;
  color: #6A727D;
}
.chart-view {
  margin-right: 12px; 
  border: #DDDFE6 1px solid; 
  border-radius: 4px;
  height: 331px;
  margin-top: 24px;
  padding: 16px;
  .qdsjb-chart {
    width: 100%;
    height: 260px;
    margin-top: 16px;
  }
}
.sjb-view {
  width: calc((100% - 20px) / 3);
  height: 331px;
  border-radius: 4px;
  border: 1px solid #DDDFE6;
  justify-content: flex-start;

  .no-view {
    background: rgba(80,127,153,0.1);
    padding: 24px;
    width: 100%;
    text-align: center;
  }
  .value-view {
    margin-top: 38px;
    .value-label {
      font-weight: 600;
      font-size: 20px;
      color: #1F2329;
    }
    .desc-label {
      font-size: 14px;
      color: #6A727D;
    }
  }
}

.yz-form-item {
  font-weight: 400;
  font-size: 14px;
  color: #1F2021;
  justify-content: flex-start;
  padding-left: 24px;
  padding-bottom: 0px;
}
.yzset-drawer__footer {
  position: absolute;
  bottom: 30px;
  right: 30px;
}

.sub-table {
  position: relative;
  height: calc(100% - 360px);
  margin-top: 40px;
  .table-view {
    height: 100%;
  }
  .page-view {
    margin-top: 10px
  }
}
::v-deep .el-table__empty-block { // Element自带类名
  height: 360px !important;
}
::v-deep .el-row::before, .el-row::after {
  content: revert;
}
::-webkit-scrollbar {
	display:none
}

.supplier-no{
    margin-top:11px;
    font-weight: bold;
}
.supplier-name-status{
    font-size:14px;
    color:#fff;
    width: 40px;
    height: 22px;
    line-height: 22px;
    border-radius: 4px;
    background: rgba(0, 186, 108, 1);
    display: inline-block;
    text-align: center;
    margin-left:16px;
}
.supplier-name-status2{
    font-size:14px;
    color:#fff;
    width: 40px;
    height: 22px;
    line-height: 22px;
    border-radius: 4px;
    background:#9CA5B9;
    display: inline-block;
    text-align: center;
    margin-left:16px;
}
.jbz-intro {
    position:relative;
    .display-inline{
        display: inline-block;
        margin-right:64px;
        font-size:14px;
    }
    .font_1 {
        font-size: 16px;
        font-weight: bold;
        color: rgba(31, 35, 41, 1);
    }
    .font_2 {
        font-size: 14px;
        font-weight: 400;
        color: rgba(106, 114, 125, 1);
    }
    .font_3 {
        font-size: 14px;
        font-weight: 400;
        color: rgba(31, 35, 41, 1);
    }
    .vertical-space_1 {
        margin-top: 11px;
    }
    .vertical-space_2 {
        margin-top: 22px;
    }
    .vertical-space_3 {
        margin-top: 15px;
    }
}

.supplier-ul{
    .supplier-flex{
        display: inline-block;
        justify-content: space-around;
        // width:60%;
        margin-top:40px;
        margin-bottom:14px;
    }
    .supplier-flex-li{
        height:40px;
        line-height:40px;
        border-right:1px solid #ddd;
        margin:10px;
        margin-right:24px;
        padding-right:34px;
        display: inline-block;
        &:last-child{
            border-right:0px;
        }
        .supplier-flex-num{
            font-size:26px;
            text-align: center;
            line-height:35px;
            margin-top:-15px;
            font-weight: bold;
        }
        .supplier-flex-count{
            font-size:14px;
            color:rgba(106,114,125,1);
            text-align: center;
        }
    }
}
</style>