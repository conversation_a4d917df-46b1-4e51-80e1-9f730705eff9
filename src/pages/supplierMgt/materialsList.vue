<!--
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-07-16 22:48:09
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2024-12-19 20:12:58
 * @FilePath: /quality_center_web/src/pages/supplierMgt/supplierList.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div class="content-box">
      <div class="flex-box flex-column content">
        <div class="search-box flex-box">
          <el-form class="flex-item" ref="searchForm" :inline="true" :model="searchForm" :disabled="loading">
            <!-- <el-form-item label="材料类型：">
                <el-checkbox-group v-model="searchForm.materialsTypeList"
                @change="initData()"
                >
                    <el-checkbox v-for="item in materialsTypeOpts" :key="item.value" :label="item.value">{{item.label}}</el-checkbox>
                </el-checkbox-group>
            </el-form-item> -->

            <el-form-item label="材料类型：">
                <el-select v-model="searchForm.materialsType" placeholder="请选择" @change="initData()" clearable>
                    <el-option v-for="item in materialsTypeOpts" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="供应商：">
              <el-input v-model="searchForm.supplierCompanyName" clearable
                placeholder="请输入供应商" 
                style="width: 180px" 
              />
            </el-form-item>
            
            
            <el-form-item label="材料名称：">
              <el-input v-model="searchForm.materialsConfigName" clearable
                placeholder="请输入材料名称" 
                style="width: 180px" 
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" :disabled="loading" @click="handleFilter">搜索</el-button>
              <el-button type="text" icon="el-icon-refresh-right" :disabled="loading" @click="resetForm()">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
        
        <div class="flex-item overHide">
          <div class="scroll-div">
            <el-table
              :data="tableData"
              v-loading="loading"
              :row-style="cellStyle"
              style="width: 100%">
              <af-table-column
                v-for="item in tableColumn" 
                :key="item.prop" :prop="item.prop" 
                :label="item.label" 
                :formatter="item.prop === 'materialsType' ? (row) => item.formatter(row,materialsTypeOpts) : item.formatter"
                :fixed="item.fixed" 
                :width="item.width || ''"
                align="center" 
              />
              <af-table-column width="180" label="操作" align="center" key="handle" :resizable="false">
                <template slot-scope="scope">
                    <el-button type="text" size="small" @click="addDrawerForm(scope.row)">编辑</el-button>
                </template>
              </af-table-column>
            </el-table>
          </div>
        </div>
        <div class="mt16 mb4">
          <Pagination
            :total="total" 
            :pageNum="pageObj.pageNum" 
            :pageSize="pageObj.pageSize" 
            @getData="initData" 
          />
        </div>
      </div>

    <el-drawer
        title="物料供应配置"
        :visible.sync="drawer"
        direction="rtl"
        :before-close="handleClose"
    >
        <div class="flex-box flex-column h100p drawer-box">
            <div class="flex-item ofy-auto">
                <el-form label-width="160px" 
                    :model="editForm"
                    :rules="editFormRules"
                    ref="editForm"
                    :show-message="false"
                >
                <el-form-item
                    v-for="(el,index) in formContent"
                    :key="index"
                    :label="`${el.label}：`"
                    :required="el.required || false"
                    :prop="el.prop"
                >
                    <el-select
                        v-if="el.type === 'select'"
                        v-model="editForm[el.prop]"
                        @change="el['change']"
                        filterable clearable 
                        :placeholder="'请选择' + el.placeholder"
                        :value-key="el.valueKey"
                        style="width: 350px"
                    >
                        <el-option
                            v-for="item in el.options"
                            :key="item.label"
                            :label="item.label"
                            :value="item.value">
                        </el-option>
                    </el-select>
                    <el-input 
                        v-else-if="el.type === 'input'"
                        v-model="editForm[el.prop]" 
                        :disabled="el.disabled"
                        clearable
                        :placeholder="el.placeholder || `请输入${el.label}`"
                        style="width: 350px"
                    />
                    <el-input 
                        v-else-if="el.type === 'number'"
                        v-model="editForm[el.prop]" 
                        :disabled="el.disabled"
                        clearable
                        :placeholder="el.placeholder || `请输入${el.label}`"
                        style="width: 350px"
                        :type="el.type"
                        v-manual-update
                    />
                    <el-date-picker
                        v-else-if="el.type === 'date' || el.type === 'datetime'"
                        :type="el.type"
                        v-model="editForm[el.prop]"
                        placeholder="选择日期/时间"
                        :format="el.format"
                        :value-format="el.valueFormat"
                        :style="{'width': el.width ? el.width : 350 + 'px'}"
                    >
                    </el-date-picker>
                </el-form-item>
                </el-form>
            </div>
            <div class="drawer-footer">
                <el-button type="primary" @click="drawer = false" plain>取消</el-button>
                <el-button type="primary" @click="saveTarget">保存</el-button>
            </div>
        </div>
    </el-drawer>
    </div>
  </template>
  
<script>
import { supplierMaterialsColumn as tableColumn } from "./config.js"
import Pagination from "@/components/Pagination/index.vue";
import DrawerForm from "@/components/drawerForm.vue";
export default {
    components: {
        Pagination,
        DrawerForm
    },
    data() {
        return {
            loading: false,
            searchForm: {},
            tableColumn: tableColumn,
            pageObj: {
                pageNum: 1, // 页数
                pageSize: 10, // 条数
            },
            total: 1,
            tableData: [],

            drawer: false,
            formContent: [],
            editForm: {},
            specConfigOpts: [],
            specConfig_2Opts: [],
            materialsTypeOpts: [],
            editFormRules: {
                materialsConfigType: [{ required: true, message: '请选择材料类型', trigger: 'change' }],
                materialsConfigName: [{ required: true, message: '请选择材料名称', trigger: 'change' }],
                materialsConfigSpec: [{ required: true, message: '请选择材料规格', trigger: 'change' }],
                manufacturers: [{ required: true, message: '请输入厂家名称', trigger: 'blur' }],
                unitPrice: [{ required: true, message: '请输入单价', trigger: 'blur' }],
                inspectionNo: [{ required: true, message: '请输入型式检验编号', trigger: 'blur' }],
                inspectionResult: [{ required: true, message: '请输入型式检验结果', trigger: 'blur' }],
                inspectionDateStart: [{ required: true, message: '请选择型式检验开始时间', trigger: 'change' }],
                inspectionDateEnd: [{ required: true, message: '请选择型式检验结束时间', trigger: 'change' }],
                selfSupply: [{ required: true, message: '请选择甲供', trigger: 'change' }],
            },
            activeRow: {}
        };
    },

    created() {
        this.$api.getDictValue({
            dictCode: 'MASTERIAL_TYPE'
        }, this).then(res => {
            this.materialsTypeOpts = res.data.list.map(item => {
                return {
                    label: item.dictValueName,
                    value: item.dictValueCode + '',
                }
            })

            this.formContent = [
                {
                    type: 'input',
                    label: '物料名称',
                    prop: 'materialsName',
                    disabled: true,
                    placeholder: "物料名称"
                },
                {
                    type: 'input',
                    label: '物料规格',
                    prop: 'materialsSpec',
                    disabled: true,
                    placeholder: "物料规格"
                },
                {
                    type: 'select',
                    label: '材料类型',
                    prop: 'materialsConfigType',
                    placeholder: "材料类型",
                    options: this.materialsTypeOpts,
                    change: (item) => this.setMaterials(item)
                },
                {
                    type: 'select',
                    label: '材料名称',
                    prop: 'materialsConfigName',
                    options: this.specConfigOpts,
                    valueKey: 'materialsName',
                    placeholder: "材料名称",
                    change: (item) => this.changeSelect(item)
                },
                {
                    type: 'select',
                    label: '材料规格',
                    prop: 'materialsConfigSpec',
                    options: this.specConfig_2Opts,
                    valueKey: 'materialsSpec',
                    placeholder: "材料规格",
                    change: (item) => this.changeSelectSpec(item)
                },
                {
                    type: 'input',
                    label: '厂家名称',
                    prop: 'manufacturers',
                    required: true,
                    placeholder: "厂家名称"
                },
                {
                    type: 'input',
                    label: '厂家简称',
                    prop: 'manufacturersCalled',
                    required: false,
                    placeholder: "厂家简称"
                },
                {
                    type: 'number',
                    label: '单价',
                    prop: 'unitPrice',
                    placeholder: "单价",
                    required: true,
                },
                {
                    type: 'input',
                    label: '型式检验编号',
                    prop: 'inspectionNo',
                    required: true,
                    placeholder: "型式检验编号"
                },
                {
                    type: 'input',
                    label: '型式检验结果',
                    prop: 'inspectionResult',
                    required: true,
                    placeholder: "型式检验结果"
                },
                {
                    type: 'date',
                    label: '型式检验开始时间',
                    prop: 'inspectionDateStart',
                    required: true,
                    format: 'yyyy-MM-dd',
                    valueFormat: "yyyy-MM-dd HH:mm:ss"
                },
                {
                    type: 'date',
                    label: '型式检验结束时间',
                    prop: 'inspectionDateEnd',
                    required: true,
                    format: 'yyyy-MM-dd',
                    valueFormat: "yyyy-MM-dd HH:mm:ss"
                },
                {
                    type: 'select',
                    label: '是否甲供',
                    prop: 'selfSupply',
                    options: [{
                        label: '是',
                        value: '1'
                    }, {
                        label: '否',
                        value: '0'
                    }],
                    placeholder: "是否甲供",
                    change: (item) => this.changeSelfSupply(item)
                },
            ];
        })
        this.initData();
    },
    methods: {
        cellStyle({row, column, rowIndex, columnIndex}) {
            if (row.warningStatus == '1') {
                return {color: "#FF7F50"};
            } else if (row.warningStatus == '2') {
                return {color: "#FF4500"};
            } else {
                return {color: "#333"};
            }
        },
        handleFilter() {
            this.initData(1);
        },
        resetForm(){
            this.searchForm = {};
            this.initData(1);
        },
        initData(opageNum, opageSize){
            this.loading = true;
            if (opageNum) this.pageObj.pageNum = opageNum;
            if (opageSize) this.pageObj.pageSize = opageSize;
                
            const params ={
                ...this.pageObj,
                params: this.searchForm
            }
            this.$api.querySupplierCompanyMaterialsListPage(params, this)
            .then(res => {
                this.loading = false;
                if (res.code == 1) {
                    this.tableData = res.data.list;
                    this.total = res.data.total;
                }else{
                    this.$message.error(res.msg || '查询失败')
                }
            });
        },

        handleClose(done) {
            this.$confirm('确认关闭？')
            .then(_ => {
                if(done){
                    done();
                }
                this.drawer = false;
            })
            .catch(_ => {});
        },

        addDrawerForm(row) {
            if (row) {
                this.editForm = {
                    supplierCompanyId: this.$route.query.id,
                    id: row.erpWlId,
                    materialsNo: row.materialsNo,
                    specId: row.specId,
                    erpWlId:row.erpWlId,
                    // materialsType: `${row.materialsType || ''}`,
                    materialsName: row.materialsName,
                    materialsSpec: row.materialsSpec,
                    manufacturers: row.manufacturers,
                    manufacturersCalled: row.manufacturersCalled,
                    inspectionNo: row.inspectionNo,
                    inspectionResult: row.inspectionResult,
                    inspectionDateStart: row.inspectionDateStart,
                    inspectionDateEnd: row.inspectionDateEnd,
                    selfSupply: row.selfSupply != null ? row.selfSupply + "" : "0",
                    materialsConfigType:`${row.materialsConfigType || ''}`,
                    materialsConfigName:(row.materialsConfigName || "") + (row.sampleJudge ? `【${row.sampleJudge}】` : ''),
                    materialsConfigNameOrg: row.materialsConfigName,
                    materialsConfigSpec:row.materialsConfigSpec,
                    unitPrice:row.unitPrice,
                    erpCompanyId:row.erpCompanyId
                };

                this.$api.querySelectByMaterialsType(`materialsType=${row.materialsType}`, this).then(res => {
                    if (res.code == 1) {
                        this.specConfigOpts = res.data.list.map(item => {
                            return {
                                label: item.materialsName + (item.sampleJudge ? `【${item.sampleJudge}】` : ''),
                                value: item,
                            }
                        })
                        this.$set(this.formContent[3], "options", this.specConfigOpts);
                    }
                })
            }
            this.drawer = true;
        },

        saveTarget() {
            // editForm 验证
            this.$refs.editForm.validate((valid) => {
                if (valid) {
                    let api =  "updateSupplierCompanyMaterials"; 
                    let parma = {
                        supplierCompanyId: this.$route.query.id,
                        specId: this.editForm.specId,
                        // materialsType: parseInt(this.editForm.materialsType),
                        manufacturers: this.editForm.manufacturers,
                        manufacturersCalled: this.editForm.manufacturersCalled,
                        inspectionNo: this.editForm.inspectionNo,
                        inspectionResult: this.editForm.inspectionResult,
                        inspectionDateStart: this.editForm.inspectionDateStart,
                        inspectionDateEnd: this.editForm.inspectionDateEnd,
                        selfSupply: this.editForm.selfSupply != null ? this.editForm.selfSupply + "" : "0",
                        materialsConfigType:this.editForm.materialsConfigType,
                        materialsConfigName: this.editForm.materialsConfigNameOrg,
                        materialsConfigSpec:this.editForm.materialsConfigSpec,
                        materialsName:this.editForm.materialsName,
                        materialsSpec:this.editForm.materialsSpec,
                        erpWlId:this.editForm.erpWlId,
                        id:this.editForm.erpWlId,
                        erpCompanyId:this.editForm.erpCompanyId,
                        unitPrice: parseFloat(this.editForm.unitPrice).toFixed(4)
                    }
                    this.$api[api](parma, this).then(res => {
                        if (res.succ) {
                            this.$message({
                                showClose: true,
                                message: "操作成功",
                                type: "success",
                            });
                            this.drawer = false;
                            this.initData();
                        }
                    })
                }
            });
        },

        setMaterials(row, materialsName, materialsSpec) {
            this.$api.querySelectByMaterialsType(`materialsType=${row}`, this).then(res => {
                if (res.code == 1) {
                    this.specConfigOpts = res.data.list.map(item => {
                        return {
                            label: item.materialsName + (item.sampleJudge ? `【${item.sampleJudge}】` : ''),
                            value: item,
                        }
                    })
                    this.$set(this.formContent[3], "options", this.specConfigOpts);
                }
            })
            // 清空材料名称 和材料规格
            // 清空材料名称 和材料规格
            this.$set(this.editForm, "materialsConfigType", row);
            this.$set(this.editForm, "materialsConfigName", "");
            this.$set(this.editForm, "materialsConfigSpec", "");
        },
        changeSelect(item) {
            
            let materialsName = item.materialsName + (item.sampleJudge ? `【${item.sampleJudge}】` : '');
            this.$set(this.editForm, "materialsConfigName", materialsName);
            this.$set(this.editForm, "materialsConfigNameOrg", item.materialsName); // 不拼接 sampleJudge 的字段，只前端自己用
            this.queryMaterialsSpecOpts(item.materialsType, item.materialsName, item.sampleJudge);
            this.$set(this.editForm, "materialsConfigSpec", "");
        },
        changeSelectSpec(item) {
            this.$set(this.editForm, "materialsConfigSpec", item.materialsSpec);
            this.$set(this.editForm, "specId", item.id);
        },
        changeSelfSupply(item) {
            this.$set(this.editForm, "selfSupply", item);
        },
        queryMaterialsSpecOpts(materialType, materialsName, sampleJudge) {
            this.$api.queryMaterialsSpecConfigAll({
                materialsType: materialType,
                materialsName: materialsName,
                sampleJudge: sampleJudge
            }, this).then(res => {
                if (res.code == 1) {
                    this.specConfig_2Opts = res.data.list.map(item => {
                        return {
                            label: item.materialsSpec || item.materialsName,
                            value: item,
                        }
                    })
                    this.$set(this.formContent[4], "options", this.specConfig_2Opts);
                }
            })
        },
    },
};
</script>

<style scoped lang="scss">
.multi-form-item-box{
    padding: 0 0 4px 0;
    // display: flex;
    // justify-content: space-between;
    .el-select,.el-input{
    margin-right: 20px;
    }
}
::v-deep .el-button{
    padding-left: 13px;
    padding-right: 13px;
}
.el-form-item{
    margin-bottom: 8px;
}
::v-deep .el-form--inline{
    .el-form-item{
    margin-right: 24px;
    margin-bottom: 0;
    &:last-child{
        margin: 0;
    }
    }
}
::v-deep .el-table{
    .expanded,.expanded:hover{
    background-color: #FFFBD9;
    
    }
    .expanded + tr{
    background-color: #FFFBD9;
    td{
        background-color: #FFFBD9;
    }
    }
    
    .table-child-box{
    margin: 16px;
    padding: 16px;
    background: #FFFFFF;
    }
}

.content-box{
    padding: 16px;
}
.content{
    width: 100%;
    height: 100%;
    padding: 16px;
    background: #FFFFFF;
    border-radius: 16px;
}

.search-box{
    padding-bottom: 16px;
    line-height: 40px;
}
</style>