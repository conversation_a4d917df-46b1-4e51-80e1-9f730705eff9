<!--
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-07-16 22:48:09
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2024-09-12 22:25:46
 * @FilePath: /quality_center_web/src/pages/supplierMgt/supplierList.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div class="content-box">
      <div class="flex-box flex-column content">
        <div class="search-box flex-box">
          <el-form class="flex-item" ref="searchForm" :inline="true" :model="searchForm" :disabled="loading">
            <el-form-item label="供应商名称：">
              <el-input v-model="searchForm.supplierName" clearable
                placeholder="请输入供应商名称" 
                style="width: 180px" 
              />
            </el-form-item>

            <el-form-item label="供应商简称：">
              <el-input v-model="searchForm.supplierAbbreviation" clearable
                placeholder="请输入供应商简称" 
                style="width: 180px" 
              />
            </el-form-item>

            <el-form-item label="联系人：">
              <el-input v-model="searchForm.userName" clearable
                placeholder="请输入联系人" 
                style="width: 180px" 
              />
            </el-form-item>
            
            
            <el-form-item label="联系电话：">
              <el-input v-model="searchForm.userPhone" clearable
                placeholder="请输入联系电话" 
                style="width: 180px" 
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" :disabled="loading" @click="handleFilter">搜索</el-button>
              <el-button type="text" icon="el-icon-refresh-right" :disabled="loading" @click="resetForm()">重置</el-button>
            </el-form-item>
          </el-form>
          <!-- <el-button type="primary" @click="handleSetTarget()">新增供应商</el-button> -->
        </div>
        
        <div class="flex-item overHide">
          <div class="scroll-div">
            <el-table
              :data="tableData"
              v-loading="loading"
              style="width: 100%"
              :row-style="cellStyle"
            >
              <af-table-column
                v-for="item in tableColumn" 
                :key="item.prop" :prop="item.prop" 
                :label="item.label" 
                :formatter="item.formatter"
                :fixed="item.fixed" 
                :width="item.width || ''"
                align="center" 
              />
              <af-table-column width="200" label="操作" align="center" key="handle" :resizable="false">
                <template slot-scope="scope">
                    <el-button  type="text" size="small" @click="gotoDetail(scope.row)">详情</el-button>
                    <el-button  type="text" size="small" @click="handleSetTarget(scope.row)">修改</el-button>
                    <!-- <el-button  type="text" size="small" style="color: #ff0000;" @click="handleDel(scope.row)">删除</el-button> -->
                </template>
              </af-table-column>
            </el-table>
          </div>
        </div>
        <div class="mt16 mb4">
          <Pagination
            :total="total" 
            :pageNum="pageObj.pageNum" 
            :pageSize="pageObj.pageSize" 
            @getData="initData" 
          />
        </div>
      </div>
      
      
      <DrawerForm
        :formContent="formContent"
        ref="drawerForm"
        @saveTarget="saveTarget"
      >
        <template #customLastForm>
          <el-form-item
              :label="`备案证图片：`"
            >
            <div class="flex-row img-box" style="justify-content: flex-start; flex-wrap: wrap;">
                <div v-for="(item, index) in certificateImgList" :key="item.filePath" style="position: relative;">
                    <img style="width: 148px; height: 148px; margin-right: 10px; object-fit: cover;" :src="item.filePath" />
                    <i class="el-icon-delete" style="position: absolute; right: 0; top: 0; padding: 10px;" @click="handleCertificateImgRemove(item, index)"></i>
                </div>
                
                <el-upload
                    :action="baseUrl + '/upload/file'"
                    list-type="picture-card"
                    :show-file-list="false"
                    :file-list="certificateImgList"
                    :on-success="handleCertificateImgSuccess">
                    <i class="el-icon-plus avatar-uploader-icon"></i>
                </el-upload>
            </div>
          </el-form-item>
          <el-form-item
              :label="`营业执照图片：`"
            >
            <div class="flex-row img-box" style="justify-content: flex-start; flex-wrap: wrap;">
                <div v-for="(item, index) in businessLicenseList" :key="item.filePath" style="position: relative;">
                    <img style="width: 148px; height: 148px; margin-right: 10px; object-fit: cover;" :src="item.filePath" />
                    <i class="el-icon-delete" style="position: absolute; right: 0; top: 0; padding: 10px;" @click="handleBusinessLicenseRemove(item, index)"></i>
                </div>
                
                <el-upload
                    :action="baseUrl + '/upload/file'"
                    list-type="picture-card"
                    :show-file-list="false"
                    :file-list="businessLicenseList"
                    :on-success="handleBusinessLicenseSuccess">
                    <i class="el-icon-plus avatar-uploader-icon"></i>
                </el-upload>
            </div>
          </el-form-item>
        </template>
      </DrawerForm>
    </div>
  </template>
  
<script>
import { supplierColumn as tableColumn } from "./config.js"
import Pagination from "@/components/Pagination/index.vue";
import DrawerForm from "@/components/drawerForm.vue";
export default {
    components: {
        Pagination,
        DrawerForm
    },
    data() {
        return {
            addApi: 'addSupplierCompnay',
            delApi: 'delSupplierCompnay',
            updateApi: 'setSupplierCompnay',
            getListApi: 'getSupplierCompnayList',
            baseUrl: process.env.NODE_ENV === 'development' ? "/api" : "/mes-platform",
            filePrefix: this.$store.state.loginStore.userInfo.filePrefix,
            roleId: this.$store.state.loginStore.userInfo.roleId,

            loading: false,
            
            searchForm: {},
            
            
            tableColumn: tableColumn,
            pageObj: {
                pageNum: 1, // 页数
                pageSize: 10, // 条数
            },
            total: 1,
            tableData: [],
            
            activeRow: {},//编辑对象
            // certificateImg: [{
            //     fileName: "",
            //     filePath: "",
            // }],
            certificateImgList: [],
            businessLicenseList: [],
            certificateImg: [],
            businessLicense: [],
            formContent: [
                {
                    type: 'input',
                    label: '供应商名称',
                    prop: 'erpCompanyName',
                    disabled: true
                },
                {
                    type: 'input',
                    label: '供应商简称',
                    prop: 'supplierAbbreviation',
                },
                {
                    type: 'input',
                    label: '联系人',
                    prop: 'userName',
                    required: true,
                },
                {
                    type: 'input',
                    label: '联系电话',
                    prop: 'userPhone',
                    required: true,
                },
                {
                    type: 'input',
                    label: '备案证编号',
                    prop: 'certificateNo',
                    required: true,
                },
                {
                    type: 'date',
                    label: '备案证有效期始',
                    prop: 'certificateDateStart',
                    format: 'yyyy-MM-dd',
                    valueFormat: "yyyy-MM-dd HH:mm:ss",
                    required: true,
                },
                {
                    type: 'date',
                    label: '备案证有效期至',
                    prop: 'certificateDateEnd',
                    format: 'yyyy-MM-dd',
                    valueFormat: "yyyy-MM-dd HH:mm:ss",
                    required: true,
                },
                {
                    type: 'input',
                    label: '备案证名称',
                    prop: 'certificateName',
                },
            ],
        };
    },

    created() {
        this.initData();
    },
    methods: {
        cellStyle({row, column, rowIndex, columnIndex}) {
            if (row.warningStatus == '1') {
                return {color: "#FF7F50"};
            } else if (row.warningStatus == '2') {
                return {color: "#FF4500"};
            } else {
                return {color: "#333"};
            }
        },
        handleFilter() {
            console.log(this.searchForm)
            this.initData(1);
        },
        resetForm(){
            this.searchForm = {};
            this.initData(1);
        },
        initData(opageNum, opageSize){
            this.loading = true;
            if (opageNum) this.pageObj.pageNum = opageNum;
            if (opageSize) this.pageObj.pageSize = opageSize;
                
            const params ={
                ...this.pageObj,
                params: this.searchForm
            }
            //获取列表
            this.$api[this.getListApi](params, this).then(res => {
                this.loading = false;
                if(res.succ){
                    this.tableData = res.data.list;
                    this.total = res.data.total;
                }else{
                    this.$message.error(res.msg || '查询失败')
                }
            }).catch(err => {
                this.loading = false;
            })
        },

        handleDel(row){
            this.$confirm("删除后不能恢复，确定要删除吗？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                //删除
                this.$api[this.delApi]({
                id: row.id
                }, this).then((res) => {
                if (res.succ) {
                    this.$message({
                        showClose: true,
                        message: "删除成功",
                        type: "success",
                    });
                    if(this.tableData.length == 1 && this.pageObj.pageNum > 1){
                        this.pageObj.pageNum = this.pageObj.pageNum -1
                    }
                    this.initData();
                }
                });
            }).catch(() => {
                this.$message({
                    type: "info",
                    message: "已取消删除",
                });
            });
        },

        gotoDetail(row) {
            this.$router.push('/systemMgt/supplierDetail?id='+row.id);
        },
        
        handleSetTarget(row){
            if (row) {
                let activeRow = {
                    ...row
                };
                let certificateImgList = [];
                if (row.certificateImg) {
                    row.certificateImg.split(",").map((item, index) => {
                        certificateImgList.push({
                            fileName: item,
                            filePath: this.filePrefix + item
                        })
                    });
                }
                this.certificateImgList = certificateImgList;
                this.businessLicenseList = [];
                let businessLicenseList = [];
                if (row.businessLicense) {
                    row.businessLicense.split(",").map((item, index) => {
                        businessLicenseList.push({
                            fileName: item,
                            filePath: this.filePrefix + item
                        })
                    });
                    this.businessLicenseList = businessLicenseList;
                }
                // 这里把erpId赋值给id  为了上方的title显示为编辑
                activeRow.id = activeRow.erpCompanyId
                this.$refs.drawerForm.initData(activeRow);
            }else{
                this.businessLicenseList = [];
                this.certificateImgList = [];
                this.$refs.drawerForm.initData();
            }
        },
        //图片
        handleCertificateImgSuccess(response) {
            console.log(">>response>>>", response);
            if (response.code == 1) {
                this.certificateImgList.push(response.data);
                this.certificateImg.push(response.data.fileName);
            }
        },
        handleCertificateImgRemove(file, index) {
            this.certificateImgList.splice(index, 1);
            this.certificateImg.splice(index, 1);
        },
        handleBusinessLicenseSuccess(response) {
            console.log(">>response>>>", response);
            if (response.code == 1) {
                this.businessLicenseList.push(response.data);
                this.businessLicense.push(response.data.fileName);
            }
        },
        handleBusinessLicenseRemove(file, index) {
            this.businessLicenseList.splice(index, 1);
            this.businessLicense.splice(index, 1);
        },

        saveTarget(formData){
            this.$refs.drawerForm.$refs.editForm.validate((valid) => {
                if (valid) {
                    let parma = {
                        ...formData,
                        certificateImg: this.certificateImg.join(","),
                        businessLicense: this.businessLicense.join(",")
                    }
                    if(formData.id){
                        this.$api[this.updateApi](parma, this).then((res) => {
                            if (res.succ) {
                                this.$message({
                                    showClose: true,
                                    message: "修改成功",
                                    type: "success",
                                });
                                this.$refs.drawerForm.handleClose();
                                this.initData();
                            }
                        });
                    }else{
                        this.$api[this.addApi](parma, this).then((res) => {
                            if (res.succ) {
                                this.$message({
                                    showClose: true,
                                    message: "添加成功",
                                    type: "success",
                                });
                                this.initData();
                                this.$refs.drawerForm.handleClose();
                            }
                        });
                    }
                }
            });
        },
    },
};
</script>

<style scoped lang="scss">
.multi-form-item-box{
    padding: 0 0 4px 0;
    // display: flex;
    // justify-content: space-between;
    .el-select,.el-input{
    margin-right: 20px;
    }
}
::v-deep .el-button{
    padding-left: 13px;
    padding-right: 13px;
}
.el-form-item{
    margin-bottom: 8px;
}
::v-deep .el-form--inline{
    .el-form-item{
    margin-right: 24px;
    margin-bottom: 0;
    &:last-child{
        margin: 0;
    }
    }
}
::v-deep .el-table{
    .expanded,.expanded:hover{
    background-color: #FFFBD9;
    
    }
    .expanded + tr{
    background-color: #FFFBD9;
    td{
        background-color: #FFFBD9;
    }
    }
    
    .table-child-box{
    margin: 16px;
    padding: 16px;
    background: #FFFFFF;
    }
}

.content-box{
    padding: 16px;
}
.content{
    width: 100%;
    height: 100%;
    padding: 16px;
    background: #FFFFFF;
    border-radius: 16px;
}

.search-box{
    padding-bottom: 16px;
    line-height: 40px;
}
</style>