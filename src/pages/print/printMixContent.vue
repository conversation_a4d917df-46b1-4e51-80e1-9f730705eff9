<!--
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-05-21 23:47:00
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2024-11-11 23:10:24
 * @FilePath: /quality_center_web/src/pages/print/printContent.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div class="page-content">
        <el-button class="btn-position" size="mini" type="primary" v-print="'#printMore'" @click="printClick">打印</el-button>
        <div class="printContent" id="printMore">
            <!-- 配合比设计报告 -->
            <template>
                <hntphb v-for="(item, index) in taskList" :key="index + 'hntphb'" 
                        :mixProportion="item?.mixProportion"
                        :printVerifyRecordOtherInfo="item?.printVerifyRecordOtherInfo"
                        :supplyTaskList="item?.supplyTaskList"
                        :companyInfo="item?.companyInfo"
                        :shxhSynchronizedataList="item?.shxhSynchronizedataList"
                        :rwdextraList="item?.rwdextraList || []"
                        :mixMaterialsExperimentInfo = "item?.mixMaterialsExperimentInfo"
                        :mixExperimentInfo = "item?.mixExperimentInfo"
                        :companyName="item?.companyName"
                />
            </template>
        </div>
    </div>
</template>

<script>

import hntphb from "./components/hnt/hntphb.vue";

export default {
    components: {
        hntphb,
    },

    data() {
        return {
            mixIdList: this.$route.query.mixIdList || '',
            taskList: []
        }
    },

    mounted() {
        this.$api.queryPrintInfoByMixIds({
            mixIdList: this.mixIdList.split(",")
        }, this).then(res => {
            if (res.code == 1) {
                this.taskList = res.data.list;
            }
        })
    },

    methods: {
        printClick() {
            
        }
    }
}
</script>

<style lang="scss" scoped>
.page-content {
    background: white !important;
    width: 100%;
    height: 100%;
    overflow: auto;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
}
.printContent {
    font-family: MES-Song;
    font-weight: 500;
    font-size: 17px;
}
</style>