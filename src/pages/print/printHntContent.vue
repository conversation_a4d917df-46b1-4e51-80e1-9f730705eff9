<!--
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-05-21 23:47:00
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2024-10-26 15:47:16
 * @FilePath: /quality_center_web/src/pages/print/printContent.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div class="page-content">
        <el-button class="btn-position" size="mini" type="primary" v-print="'#printMore'" @click="printClick">打印</el-button>
        <div class="printContent" id="printMore">
            <template v-for="(task, index) in taskList">
                <template v-for="(experimentDetail, subIndex) in task.experimentDetailList">
                    <!-- 混凝土抗压强度检测报告 -->
                    <template  v-if="experimentDetail.testProjectCode === 'CONCRETE_PARAM_KYQD'">
                        <!-- <hntky :key="index + 'hntky'" 
                            :experimentInfo="task?.experimentInfo" 
                            :experimentDetailList="task?.experimentDetailList" 
                            :shxhSynchronizedataList="task?.shxhSynchronizedataList"
                            :rwdextraList="item?.rwdextraList || []"
                            :supplyTaskList="item?.supplyTaskList"
                            :companyName="item?.companyName"
                        /> -->
                        <hntky :key="index + 'hntky'" 
                        :subItem="task"
                            :rwdextraList="task?.rwdextraList || []"
                            :supplyTaskList="task?.supplyTaskList"
                            :companyName="task?.companyName"
                        />
                    </template>


                    

                    <!-- 混凝土抗渗性能检测报告 -->
                    <template  v-if="experimentDetail.testProjectCode === 'CONCRETE_PARAM_KSDJ'">
                        <hntks :key="index + 'hntks'" 
                        :experimentInfo="task?.ksInfo?.experimentInfo" 
                        :experimentDetailList="task?.ksInfo?.experimentDetailList" 
                        :shxhSynchronizedataList="task?.ksInfo?.shxhSynchronizedataList"
                        :rwdextraList="task?.rwdextraList || []"
                        :supplyTaskList="task?.supplyTaskList"
                        :companyName="task?.companyName"
                        />
                    </template>

                    <!-- 混凝土抗折强度检测报告(延后) -->
                    <template  v-if="experimentDetail.testProjectCode === 'CONCRETE_PARAM_KZQD'">
                        <hntkz :key="index + 'hntkz'" 
                            :experimentInfo="task.experimentInfo" 
                            :experimentDetailList="task.experimentDetailList" 
                            :shxhSynchronizedataList="task.shxhSynchronizedataList"
                            :rwdextraList="task.rwdextraList"
                            :supplyTaskList="task.supplyTaskList"
                            :companyName="task.companyName"
                        />
                    </template>
                </template>

                <!-- 混凝土氯离子检测报告和性能检测报告一样 -->
                <template  v-if="showHntjcFlag">
                    <hntjc :key="index + 'hntjc'" 
                        :experimentInfo="task?.xnbgInfo?.experimentInfo" 
                        :experimentDetailList="task?.xnbgInfo?.experimentDetailList" 
                        :shxhSynchronizedataList="task?.xnbgInfo?.shxhSynchronizedataList"
                        :rwdextraList="task?.rwdextraList || []"
                        :supplyTaskList="task?.supplyTaskList"
                        :companyName="task?.companyName"
                    />
                </template>
            </template>
        </div>
    </div>
</template>

<script>
import hntks from "./components/hnt/hntks.vue";
import hntky from "./components/hnt/hntky.vue";
import cchgz from "./components/hnt/cchgz.vue";
import kpjd from "./components/hnt/kpjd.vue";
import hntphb from "./components/hnt/hntphb.vue";
import hntkz from "./components/hnt/hntkz.vue"
import hntjc from "./components/hnt/hntjc.vue";
import hntbs from "./components/hnt/hntbs.vue"
import hnthssf from "./components/hnt/hnthssf.vue";

export default {
    components: {
        hntks,
        hntky,
        cchgz,
        kpjd,
        hntphb,
        hntkz,
        hntjc,
        hntbs,
        hnthssf
    },

    data() {
        return {
            experimentIds: this.$route.query.experimentIds || '',
            printTypes: this.$route.query.printType ? this.$route.query.printType.split(",") : '',
            taskList: [],
            showHntjcFlag: false,
            printInfo: ""
        }
    },

    mounted() {
        this.$api.queryExperimentPrintInfo({
            experimentIdList: this.experimentIds.split(",")
        }, this).then(res => {
            if (res.code == 1) {
                this.taskList = res.data.list;
                let arr = [
                    'CONCRETE_PARAM_HYX', 'CONCRETE_PARAM_BSX', 
                    'CONCRETE_PARAM_ZJX', 'CONCRETE_PARAM_XNBG_TLD',
                    'CONCRETE_PARAM_XNBG_HQL', 'CONCRETE_PARAM_XNBG_CNSJ',
                    'CONCRETE_PARAM_XNBG_ZNSJ', 'CONCRETE_PARAM_XNBG_LLZHL',
                    'CONCRETE_PARAM_XNBG_MSL', 'CONCRETE_PARAM_XNBG_KZD', 
                ];
                for (const task of this.taskList) {
                    for (const item of task.experimentDetailList) {
                        if (arr.indexOf(item.testProjectCode) > -1) {
                            this.showHntjcFlag = true;
                            break;
                        }
                    }
                }

                this.printInfo = JSON.stringify(res);
            }
        })
    },

    methods: {
        addExperimentPrintRecordResp(printJson) {
            this.$api.addExperimentPrintRecord({printJson});
        },
        printClick() {
            // this.addExperimentPrintRecordResp(this.printInfo);
        },
        // addExperimentPrintRecordResp(printJson) {
        //     this.$api.addExperimentPrintRecord({printJson, experimentIdList: this.experimentIds.split(",")});
        // }
    }
}
</script>

<style lang="scss" scoped>
.page-content {
    background: white !important;
    width: 100%;
    height: 100%;
    overflow: auto;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
}
.printContent {
    font-family: MES-Song;
    font-weight: 500;
    font-size: 17px;
}
</style>