<!--
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-05-21 23:47:00
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2024-10-14 22:54:34
 * @FilePath: /quality_center_web/src/pages/print/printContent.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div class="page-content">
        <el-button class="btn-position" size="mini" type="primary" v-print="'printMore'" @click="printClick">打印</el-button>
        <div class="printContent" id="printMore">
            <div class="landscape">
                <table style="width: 100%;">
                    <!-- 公共拥有的头部 start -->
                    <thead style="width: 100%">
                        <tr>
                            <td style="font-weight:bold; font-size: 30px; text-align: center">
                                鉴 定 周 期 一 览 表
                            </td>
                        </tr>
                    </thead>
                    <!-- 公共拥有的头部 end -->
                    <!-- 表单内容 start -->
                    <tbody style="width: 100%;">
                        <div class="table-content">
                            <PrintTable :data="tableData" style="width: 100%;">
                                <el-table-column
                                    v-for="item in tableColumn1"
                                    :key="item.prop"
                                    :prop="item.prop"
                                    :label="item.label"
                                    :formatter="item.formatter"
                                    :fixed="item.fixed"
                                    :width="item.width || ''"
                                    align="center"
                                />
                                <el-table-column align="center" label="2">
                                    <el-table-column  align="center" label="测量（准确度）">
                                        <el-table-column prop="technicalParameter" align="center" label="范围">

                                        </el-table-column>
                                    </el-table-column>
                                    <el-table-column prop="division" align="center" label="刻度值">
                                        
                                    </el-table-column>
                                    <el-table-column prop="accuracyClass" align="center" label="等级">
                                    
                                    </el-table-column>
                                </el-table-column>
                                <el-table-column align="center" label="使用部门">
                                    <span>试验室</span>
                                </el-table-column>
                                <el-table-column align="center" label="检定单位" prop="lastCalibrationRecord.calibrationUnit">
                                    
                                </el-table-column>
                                <el-table-column align="center" label="检定周期(天)" prop="verificationCycle">
                                    
                                </el-table-column>
                                <el-table-column align="center" label="周期检定日程（计划）">
                                    <el-table-column align="center" label="计划日期（计划数）/完成日期（完成数）">
                                        <el-table-column prop="nextMonth" width="40" label="1">
                                            <span slot-scope="scope">{{ scope.row.nextMonth == '1' ? '/' : '' }}</span>
                                        </el-table-column>
                                        <el-table-column prop="nextMonth" width="40" label="2">
                                            <span slot-scope="scope">{{ scope.row.nextMonth == '2' ? '/' : '' }}</span>
                                        </el-table-column>
                                        <el-table-column prop="nextMonth" width="40" label="3">
                                            <span slot-scope="scope">{{ scope.row.nextMonth == '3' ? '/' : '' }}</span>
                                        </el-table-column>
                                        <el-table-column prop="nextMonth" width="40" label="4">
                                            <span slot-scope="scope">{{ scope.row.nextMonth == '4' ? '/' : '' }}</span>
                                        </el-table-column>
                                        <el-table-column prop="nextMonth" width="40" label="5">
                                            <span slot-scope="scope">{{ scope.row.nextMonth == '5' ? '/' : '' }}</span>
                                        </el-table-column>
                                        <el-table-column prop="nextMonth" width="40" label="6">
                                            <span slot-scope="scope">{{ scope.row.nextMonth == '6' ? '/' : '' }}</span>
                                        </el-table-column>
                                        <el-table-column prop="nextMonth" width="40" label="7">
                                            <span slot-scope="scope">{{ scope.row.nextMonth == '7' ? '/' : '' }}</span>
                                        </el-table-column>
                                        <el-table-column prop="nextMonth" width="40" label="8">
                                            <span slot-scope="scope">{{ scope.row.nextMonth == '8' ? '/' : '' }}</span>
                                        </el-table-column>
                                        <el-table-column prop="nextMonth" width="40" label="9">
                                            <span slot-scope="scope">{{ scope.row.nextMonth == '9' ? '/' : '' }}</span>
                                        </el-table-column>
                                        <el-table-column prop="nextMonth" width="40" label="10">
                                            <span slot-scope="scope">{{ scope.row.nextMonth == '10' ? '/' : '' }}</span>
                                        </el-table-column>
                                        <el-table-column prop="nextMonth" width="40" label="11">
                                            <span slot-scope="scope">{{ scope.row.nextMonth == '11' ? '/' : '' }}</span>
                                        </el-table-column>
                                        <el-table-column prop="nextMonth" width="40" label="12">
                                            <span slot-scope="scope">{{ scope.row.nextMonth == '12' ? '/' : '' }}</span>
                                        </el-table-column>
                                    </el-table-column>
                                </el-table-column>
                                <el-table-column align="center" label="上一年度报告日期（最近检定时间）" prop="lastCalibrationRecord.calibrationTime">
                                    <span slot-scope="scope">
                                        {{ scope.row.lastCalibrationRecord && scope.row.lastCalibrationRecord.calibrationTime 
                                        ? formatDate(scope.row.lastCalibrationRecord.calibrationTime) : '' }}
                                    </span>
                                </el-table-column>
                            </PrintTable>
                        </div>
                    </tbody>
                    <!-- 表单内容 end -->
                    <!-- 公共拥有的尾部 start-->
                    <tfoot>
                        <tr>
                            <td></td>
                        </tr>
                    </tfoot>
                    <!-- 公共拥有的尾部 end -->
                </table>
            </div>
        </div>
    </div>
</template>

<script>
import moment from 'moment';
import PrintTable from './components/print-table.vue';
export default {
    components: {
        PrintTable
    },

    data() {
        return {
            tableData: [],
            tableColumn1: [
                {
                    label: '类别',
                    prop: 'equipmentCategory',
                },
                {
                    label: '序号',
                    prop: 'indexNumber',
                },
                {
                    label: '计量器具名称',
                    prop: 'equipmentName',
                },
                {
                    label: '本厂编号（或数量）',
                    prop: 'equipmentNo',
                },{
                    label: '数量',
                    prop: 'number',
                },
            ],

            printInfo: ""
        }
    },

    mounted() {
        this.$api.getEquipmentPrintInfo({}, this).then(res => {
            if(res.succ){
                this.tableData = res.data.list.map((item, index) => {
                    let nextMonth = '';
                    if (item.lastCalibrationRecord && item.lastCalibrationRecord.calibrationTime && item.verificationCycle) {
                        nextMonth = moment(item.lastCalibrationRecord.calibrationTime).add(parseInt(item.verificationCycle), 'day').format("M")
                    }
                    return {
                        ...item,
                        indexNumber: index + 1,
                        nextMonth: nextMonth
                    }
                });
                this.printInfo = JSON.stringify(res);
            }else{
                this.$message.error(res.msg || '查询失败')
            }
        })
    },

    methods: {
        formatDate(date) {
            if (date) {
                return moment(date).format('YYYY-MM-DD');
            }
            return '';
        },

        printClick() {
            // this.addExperimentPrintRecordResp(this.printInfo);
        },
        // addExperimentPrintRecordResp(printJson) {
        //     this.$api.addExperimentPrintRecord({printJson});
        // }
    }
}
</script>

<style lang="scss" scoped>
.page-content {
    background: white !important;
    width: 100%;
    height: 100%;
    overflow: auto;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
}
.printContent {
    width: calc(80% - 280px);
    margin-top: 20px;
    font-family: MES-Song;
    font-weight: 500;
    font-size: 17px;
}
::v-deep .el-table__body {
    table-layout: auto;
    border: 0.5px solid black !important;
    border-color: black !important;
    border-collapse: collapse;
    font-size: 8px !important;
    color: black !important;
    td {
        border: 0.5px solid  black !important;
        border-bottom: none !important;
        padding: 10px 3px;
        font-size: 16px;
    }
    th {
        border: 0.5px solid black !important;
        border-bottom: none !important;
        padding: 10px 3px;
        font-size: 16px;
    }
}
.table-content {
    margin-top: 20px;
    width: 100%;
}
.btn-position {
    position: absolute;
    right: 130px;
    top: 20px;
}
</style>