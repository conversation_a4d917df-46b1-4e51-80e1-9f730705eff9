<!--
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-05-21 23:47:00
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2024-10-14 22:54:46
 * @FilePath: /quality_center_web/src/pages/print/printContent.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div class="page-content">
        <el-button class="btn-position" size="mini" type="primary" v-print="'#printMore'" @click="printClick">打印</el-button>
        <div class="printContent" id="printMore">
            <div class="portrait">
                <!-- <div class="company-label">{{companyName}}</div> -->
                <div class="title-label">仪器设备校准/检测使用确认表</div>
                <div align="center" class="lab-report">
                    <div class="lab-sub-title flex-row">
                        <div class="lab-sample-id"><span>{{ '' }} </span></div>
                        <div class="lab-report-id">序号:<span>{{ '' }} </span></div>
                    </div>
                    <table border="1" class="border">
                        <tr height="45">
                            <td colspan="2">设备名称</td>
                            <td colspan="4" width="100" left>{{ dataInfo.equipmentName || '/' }}</td>
                            <td colspan="2">设备编号</td>
                            <td colspan="4" width="100" left>{{ dataInfo.equipmentNo || '/' }}</td>
                        </tr>
                        <tr height="45">
                            <td colspan="2">校准单位</td>
                            <td colspan="10" left>{{ dataInfo.calibrationUnit || '/' }}</td>
                        </tr>
                        <tr height="45">
                            <td colspan="2">校准日期</td>
                            <td colspan="4" left>{{ formatDate(dataInfo.calibrationTime) }}</td>
                            <td colspan="2">校准报告编号</td>
                            <td colspan="4" left>{{ dataInfo.reportNo || '/' }}</td>
                        </tr>
                    </table>
                    <table border="1" class="border">
                        <tr height="440">
                            <td colspan="12" style="padding: 10px; text-align: left; vertical-align: top;">
                                <div>&nbsp;&nbsp;校准结果判断：{{ dataInfo.calibrationText || '/' }}</div>

                                <div style="text-align: right; margin-top: 200px; margin-right: 200px;">设备管理员：</div>
                                <div style="text-align: right; margin-right: 200px; margin-top: 30px;">
                                    <span>&nbsp;&nbsp;&nbsp;&nbsp;年</span>
                                    <span>&nbsp;&nbsp;&nbsp;&nbsp;月</span>
                                    <span>&nbsp;&nbsp;&nbsp;&nbsp;日</span>
                                </div>
                            </td>
                        </tr>
                    </table>
                    <table border="1" class="border">
                        <tr height="440">
                            <td colspan="12" style="padding: 10px; text-align: left; vertical-align: top;">
                                <div>&nbsp;&nbsp;技术负责人意见：</div>

                                <div style="text-align: right; margin-top: 200px; margin-top: 200px; margin-right: 200px;">批准：</div>
                                <div style="text-align: right; margin-top: 30px; margin-right: 200px;">
                                    <span>&nbsp;&nbsp;&nbsp;&nbsp;年</span>
                                    <span>&nbsp;&nbsp;&nbsp;&nbsp;月</span>
                                    <span>&nbsp;&nbsp;&nbsp;&nbsp;日</span>
                                </div>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import moment from 'moment';
export default {
    components: {
        
    },

    data() {
        return {
            cid: this.$route.query.cid || '',
            dataInfo: {},
            printInfo: "",
        }
    },

    mounted() {
        this.$api.getCalibrationRecordById('id='+this.cid, this).then(res => {
            if(res.succ){
                this.dataInfo = res.data;

                this.printInfo = JSON.stringify(res);
            }else{
                this.$message.error(res.msg || '查询失败')
            }
        })
    },

    methods: {
        formatDate(date) {
            if (date) {
                return moment(date).format('YYYY-MM-DD');
            }
            return '';
        },
        printClick() {
            // this.addExperimentPrintRecordResp(this.printInfo);
        },
        // addExperimentPrintRecordResp(printJson) {
        //     this.$api.addExperimentPrintRecord({printJson});
        // }
    }
}
</script>

<style lang="scss" scoped>
@import './print.css';
.page-content {
    background: white !important;
    width: 100%;
    height: 100%;
    overflow: auto;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
}
.lab-sub-title {
    margin-top: 20px;
}
.lab-report table {
    margin-top: 0px;
}
.printContent {
    font-family: MES-Song;
    font-weight: 500;
    font-size: 17px;
}
</style>