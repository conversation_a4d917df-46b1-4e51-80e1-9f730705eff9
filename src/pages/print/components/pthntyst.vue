<!--
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-05-22 00:03:51
 * @LastEditors: huaze.fan <EMAIL>
 * @LastEditTime: 2025-03-21 15:37:21
 * @FilePath: /quality_center_web/src/pages/print/components/hntks.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="portrait">
    <div class="tip-view" style="margin-top: 48px;">
      <div class="tip-label">C-11a-0806</div>
    </div>
    <div class="company-label" style="line-height: 36px;font-weight: 900; font-size: 28px;">{{companyName}}</div>
    <div class="title-label" style="line-height: 36px;font-weight: 900; font-size: 36px;">普通混凝土用石检测报告</div>
    <div align="center" class="lab-report">
      <div class="lab-sub-title" style="margin-top: 30px;">
        <div class="lab-sub-title-container" style="margin-top: 40px; margin-left: 180px;">第1页{{'&nbsp&nbsp&nbsp&nbsp'}}共1页</div>
        <div class="lab-sub-title-container">
          <div class="lab-sample-id">委托编号:<span>{{ shxhSynchronizedata?.consignId | isNull }} </span></div>
          <div class="lab-report-id" style="margin-top: 15px;">报告编号:<span>{{ experimentInfo?.reportNo | isNull }} </span></div>
        </div>
      </div>
      <table border="1" class="border" style="margin-bottom: 20px;margin-top: 15px; table-layout: fixed;">
        <tbody>
          <tr height="40">
            <td colspan="4">委托部门</td>
            <!-- <td colspan="20" left>{{ experimentInfo?.experimentDept | isNull }}</td> -->
            <td colspan="20" left>{{ '---' }}</td>
          </tr>
          <tr height="40">
            <td colspan="4">委托日期</td>
            <td left colspan="11">{{ experimentInfo?.entrustTime | momentDate }}</td>
            <td colspan="4">报告日期</td>
            <td left colspan="5">{{ experimentInfo?.reportDate | momentDate }}</td>
          </tr>
        </tbody>
      </table>
      <table border="1" class="border" style="margin-top: 30px; table-layout: fixed;">
        <tbody>
          <tr height="40">
            <td colspan="5">样品编号</td>
            <td colspan="5" left>{{ shxhSynchronizedata?.sampleId | shxhNull }}</td>
            <td colspan="4">样品名称</td>
            <td colspan="7" left>{{ experimentInfo?.materialsName | isNull }}</td>
            <td colspan="3">种类规格</td>
            <td colspan="7" left>{{ experimentInfo?.materialsSpecs | isNull }}</td>
          </tr>
          <tr height="40">
            <td colspan="5">生产单位</td>
            <td colspan="16" left>{{ getShowCJ(experimentInfo, companyInfo?.cglConfig) }}</td>
            <td colspan="3">备案证号</td>
            <td colspan="7" style="font-size: 16px;" nowrap="nowrap" left>{{ experimentInfo?.certificateNo | shxhNull }}</td>
          </tr>
          <tr height="40">
            <td colspan="5">工程部位</td>
            <!-- <td colspan="16">{{ rwdextraObj?.fjzbw | shxhNull }}</td> -->
            <td colspan="16" left>{{ '--' }}</td>
            <td colspan="3">代表数量</td>
            <td colspan="7" left>{{ experimentInfo?.behalfNumber | isNull }}t</td>
          </tr>
          <tr height="40">
            <td colspan="5">检测日期</td>
            <td colspan="8" style="font-size: 16px;"  left>{{ experimentInfo?.jcrq | isNull }}</td>
            <td colspan="3">检测方法</td>
            <td colspan="5" left>{{ experimentInfo?.experimentGist | isNull }}</td>
            <td colspan="3">评定依据</td>
            <td colspan="7" left>{{ experimentInfo?.judgeGist | isNull }}</td>
          </tr>
          <tr height="40">
            <td rowspan="14" colspan="1">1<br />&nbsp;<br />级<br />&nbsp;<br />配<br />&nbsp;<br />检<br />&nbsp;<br />测</td>
            <td rowspan="2" colspan="4">公称粒径(mm)</td>
            <td colspan="15">标准颗粒级配区</td>
            <td rowspan="2" colspan="2">累计筛余(%)</td>
            <td colspan="6">检测项目</td>
            <td colspan="3">检测结果</td>
          </tr>
          <tr height="40">
            <td colspan="3" >5~16<br>
              /mm</td>
            <td colspan="3">5~25<br>
              /mm</td>
            <td colspan="3">5~31.5<br>
              /mm</td>
            <td colspan="3">5~40<br>
              /mm</td>
            <td colspan="3">{{experimentInfo?.materialsSpecs == '(5～20)mm' ? '5~20' : '--'}}<br>
              /mm</td>
            <td colspan="1">2</td>
            <td colspan="5">表观密度(kg/m³)</td>
            <td colspan="3">{{stringToFixed(experimentDetailObj?.COARSE_AGGREGATE_PARAM_CGL_BGMD?.pjz, 0)}}</td>

          </tr>
          <tr height="40">
            <td colspan="4" >2.50</td>
            <td colspan="3">95~100</td>
            <td colspan="3">95~100</td>
            <td colspan="3">95~100</td>
            <td colspan="3">--</td>
            <td colspan="3">{{experimentInfo?.materialsSpecs == '(5～20)mm' ? '95~100' : '--'}}</td>
            <td colspan="2">{{ sfxLjsypjz('2.50mm') }}</td>
            <td colspan="1">3</td>
            <td colspan="5">堆积密度(kg/m³)</td>
            <td colspan="3">{{stringToFixed(experimentDetailObj?.COARSE_AGGREGATE_PARAM_CGL_DJMD?.pjz, 0)}}</td>
            
          </tr>
          <tr height="40">
            <td colspan="4" >5.00</td>
            <td colspan="3">85~100</td>
            <td colspan="3">90~100</td>
            <td colspan="3">90~100</td>
            <td colspan="3">95~100</td>
            <td colspan="3">{{experimentInfo?.materialsSpecs == '(5～20)mm' ? '90~100' : '--'}}</td>
            <td colspan="2">{{ sfxLjsypjz('5.00mm') }}</td>
            <td colspan="1">4</td>
            <td colspan="5">紧密密度(kg/m³)</td>
            <td colspan="3">{{ stringToFixed(experimentDetailObj?.COARSE_AGGREGATE_PARAM_CGL_JMMD?.pjz, 0) }}</td>
           
          </tr>
          <tr height="40">
            <td colspan="4" >10.0</td>
            <td colspan="3">30~60</td>
            <td colspan="3">--</td>
            <td colspan="3">70~90</td>
            <td colspan="3">70~90</td>
            <td colspan="3">{{experimentInfo?.materialsSpecs == '(5～20)mm' ? '40~80' : '--'}}</td>
            <td colspan="2">{{ sfxLjsypjz('10.0mm') }}</td>
            <td colspan="1">5</td>
            <td colspan="5">含泥量(%)</td>
            <td colspan="3">{{ stringToFixed(experimentDetailObj?.COARSE_AGGREGATE_PARAM_CGL_HNL?.hnlpjz, 1) }}</td>
            
          </tr>
          <tr height="40">
            <td colspan="4" >16.0</td>
            <td colspan="3">0~10</td>
            <td colspan="3">30~70</td>
            <td colspan="3">--</td>
            <td colspan="3">--</td>
            <td colspan="3">--</td>
            <td colspan="2">{{ sfxLjsypjz('16.0mm') }}</td>
            <td colspan="1">6</td>
            <td colspan="5">泥块含量(%)</td>
            <td colspan="3">{{ stringToFixed(experimentDetailObj?.COARSE_AGGREGATE_PARAM_CGL_HNKL?.hnlpjz, 1) }}</td>
            
          </tr>
          <tr height="40">
            <td colspan="4" >20.0</td>
            <td colspan="3">0</td>
            <td colspan="3">--</td>
            <td colspan="3">15~45</td>
            <td colspan="3">30~65</td>
            <td colspan="3">{{experimentInfo?.materialsSpecs == '(5～20)mm' ? '0~10' : '--'}}</td>
            <td colspan="2">{{ sfxLjsypjz('20.0mm') }}</td>
            <td colspan="1">7</td>
            <td colspan="5">针片状颗粒含量(%)</td>
            <td colspan="3">{{ stringToFixed(experimentDetailObj?.COARSE_AGGREGATE_PARAM_CGL_ZPZKLHL?.zpzkklhl, 0) }}</td>
            
          </tr>
          <tr height="40">
            <td colspan="4" >25.0</td>
            <td colspan="3">--</td>
            <td colspan="3">0~5</td>
            <td colspan="3">--</td>
            <td colspan="3">--</td>
            <td colspan="3">{{experimentInfo?.materialsSpecs == '(5～20)mm' ? '0' : '--'}}</td>
            <td colspan="2">{{ sfxLjsypjz('25.0mm') }}</td>
            <td colspan="1">8</td>
            <td colspan="5">压碎值指标(%)</td>
            <td colspan="3">{{ stringToFixed(experimentDetailObj?.COARSE_AGGREGATE_PARAM_CGL_YSZZB?.pjz, 0) }}</td>
          </tr>
          <tr height="40">
            <td colspan="4" >31.5</td>
            <td colspan="3">--</td>
            <td colspan="3">0</td>
            <td colspan="3">0~5</td>
            <td colspan="3">--</td>
            <td colspan="3">--</td>
            <td colspan="2">{{ sfxLjsypjz('31.5mm') }}</td>
            <td colspan="1">9</td>
            <td colspan="5">含水率(%)</td>
            <td colspan="3">{{ stringToFixed(experimentDetailObj?.COARSE_AGGREGATE_PARAM_CGL_MCHSL?.muhsl, 1) }}</td>
            
          </tr>
          <tr height="40">
            <td colspan="4" >40.0</td>
            <td colspan="3">--</td>
            <td colspan="3">--</td>
            <td colspan="3">0</td>
            <td colspan="3">0~5</td>
            <td colspan="3">--</td>
            <td colspan="2">{{ sfxLjsypjz('40.0mm') }}</td>
            <td colspan="1">10</td>
            <td colspan="5">--</td>
            <td colspan="3">--</td>
            
          </tr>
          <tr height="40">
            <td colspan="4" >50.0</td>
            <td colspan="3">--</td>
            <td colspan="3">--</td>
            <td colspan="3">--</td>
            <td colspan="3">0</td>
            <td colspan="3">--</td>
            <td colspan="2">--</td>
            <td colspan="1">11</td>
            <td colspan="5">--</td>
            <td colspan="3">--</td>
            
          </tr>
          <tr height="40">
            <td colspan="4" >63.0</td>
            <td colspan="3">--</td>
            <td colspan="3">--</td>
            <td colspan="3">--</td>
            <td colspan="3">--</td>
            <td colspan="3">--</td>
            <td colspan="2">--</td>
            <td colspan="1">12</td>
            <td colspan="5">--</td>
            <td colspan="3">--</td>
            
          </tr>
          <tr height="40">
            <td colspan="4" >80.0</td>
            <td colspan="3">--</td>
            <td colspan="3">--</td>
            <td colspan="3">--</td>
            <td colspan="3">--</td>
            <td colspan="3">--</td>
            <td colspan="2">--</td>
            <td colspan="1">13</td>
            <td colspan="5">--</td>
            <td colspan="3">--</td>
            
          </tr>
          <tr height="40">
            <td colspan="4" >100.0</td>
            <td colspan="3">--</td>
            <td colspan="3">--</td>
            <td colspan="3">--</td>
            <td colspan="3">--</td>
            <td colspan="3">--</td>
            <td colspan="2">--</td>
            <td colspan="1">14</td>
            <td colspan="5">--</td>
            <td colspan="3">--</td>
            
          </tr>
          <tr height="50">
            <td rowspan="2" colspan="5">实测级配范围(mm)</td>
            <td colspan="17" rowspan="2" left>{{ experimentDetailObj?.COARSE_AGGREGATE_PARAM_CGL_SFX?.dxjl | isNull }}</td>
            <td colspan="1">15</td>
            <td colspan="5">--</td>
            <td colspan="3">--</td>
          </tr>
          <tr height="50">
            <td colspan="1">16</td>
            <td colspan="5">--</td>
            <td colspan="3">--</td>
          </tr>
          <tr height="60">
            <td colspan="5">检测结论</td>
            <td colspan="26" left>{{ getShowResultStr(experimentInfo?.conclusion) }}</td>
          </tr>
        </tbody>
      </table>
      <table border="1" class="border" style="margin-top: 20px; table-layout: fixed;">
        <tbody>
          <tr height="120">
            <td colspan="5">备注</td>
            <td colspan="26" left>{{ experimentInfo?.remark | isNull }}</td>
          </tr>
        </tbody>
      </table>
      <table class="lab-sign" style="border: none;">
        <tr height="40" style="border: none;">
          <td style="border: none;">
            <div class="sign-user">检测报告专用章：</div>
          </td>
          <td style="border: none;">
            <div class="sign-user">
              批准：
              <!-- <img v-if="true" src="" width="150" height="50" /> -->
            </div>
          </td>
          <td style="border: none;">
            <div class="sign-user">
              审核：
              <!-- <img v-if="true" src="" width="150" height="50" /> -->
            </div>
          </td>
          <td style="border: none;">
            <div class="sign-user">
              检测：
              <!-- <img v-if="true" src="" width="150" height="50" /> -->
            </div>
          </td>
        </tr>
      </table>
    </div>
  </div>
</template>

<script>
import {calcEquation} from "@/utils/calculate.js"
import {cpEvenRound} from "@/utils/calculate.js"
import moment from "moment";
  export default {
    props: {
      taskInfo: {
        type: Object,
        default: () => {
          return {}
        }
      },
      companyName: {
        type: String,
        default: ""
      },

      mixExperimentInfo:{
            type: Object,
            default: () => { return {} }
      },
        mixProportion: {
            type: Object,
            default: () => { return {} }
        },
        companyInfo: {
            type: Object,
            default: () => { return {} }
        },
    },
    watch: {
      taskInfo: {
        handler(newValue, oldValue) {
          const {
            mixExperimentInfo,
          } = newValue;
          let objInfo = mixExperimentInfo?.cglInfo || {};
          if(objInfo && objInfo.shxhSynchronizedataList && objInfo.shxhSynchronizedataList.length > 0){
              this.shxhSynchronizedata = objInfo.shxhSynchronizedataList[0];
          }
          this.experimentInfo = objInfo?.experimentInfo || {}
          this.experimentDetailObj = {}
          objInfo.experimentDetailList.forEach(item =>{
            this.experimentDetailObj[item.testProjectCode] = item.objJson
            this.$set(this.experimentDetailObj, item.testProjectCode, item.objJson);
            
            if (item.testProjectCode == 'COARSE_AGGREGATE_PARAM_CGL_SFX') {
              let sfxInfo = new Array(12);
              if (item.objJson.sfxInfo) {
                // 倒序遍历原数组 ['', '', '']
                  for (let i = 0; i < item.objJson.sfxInfo.length; i++) {
                      const reversedIndex = item.objJson.sfxInfo.length - 1 - i;
                      if (i < sfxInfo.length) {
                        sfxInfo[i] = item.objJson.sfxInfo[reversedIndex];
                      }
                  }

                // item.objJson.sfxInfo.map((item, index) => {
                //   sfxInfo[index] = item;
                // });
              }
              this.$set(this, 'sfxInfo', sfxInfo);
            }
          })
        },
        deep: true,
        immediate: true
      }
    },
    data() {
      return {
        experimentInfo: {},
        experimentDetailObj: {},
        shxhSynchronizedata: {},
        supplyTask: this.supplyTaskList && this.supplyTaskList.length > 0 ? this.supplyTaskList[0] : {},
        sfxInfo: [], // 筛分析
        yst:{},
      }
    },
    filters: {
      momentDate: function(value) {
        if (value) return moment(value).format("YYYY-MM-DD");
        return "---";
      },
      isNull: function(value) {
        if (value == undefined || value == null || value === '') return '---'

        return value;
      },
      shxhNull: function(value) {
        if (value == undefined || value == null || value === '') return '---'

        return value;
      },
      sfxInfoSkgjzj: function(value) {
        if (value) return value.skgjzj || '--';
        return '--';
      },
      // 计算累计筛余
      sfxLjsypjz: function(value) {
        if (value) {
          return value.ljsy || '--';
        }
        return "--"
      }
    },
    mounted(){
      if (this.mixProportion && this.mixProportion.proportionMaterial) {
            var proportionMaterial = this.mixProportion.proportionMaterial || {};
            
            this.yst = proportionMaterial.cgl || {};
            
        }
    },

    methods: {
      getShowCJ(obj, config){
            let cj = obj?.factory;
            let gysmc = obj?.supplyCompanyName;
            var showName = cj;
            var result = '/';

            if(config == 1){
                if(!this.isEmpty(cj)){
                    showName = cj
                } 
            } else {
                if(!this.isEmpty(gysmc)){
                    showName = gysmc
                }
            }
            result = this.isEmpty(showName) ? '/' : showName;
            return result;
        },

      isEmpty(val) {
            if (typeof val === "boolean") {
                return false;
            }
            if (typeof val === "number") {
                return false;
            }
            if (val instanceof Array) {
                if (val.length === 0) return true;
            } else if (val instanceof Object) {
                if (JSON.stringify(val) === "{}" || val == null ) return true;
            } else {
                if (
                val === "null" ||
                val == null ||
                val === "undefined" ||
                val === undefined ||
                val === ""
                )
                return true;
                return false;
            }
            return false;
        },

      getCpEvenRound(str, index){
            if(this.isEmpty(str)) return '/'
            return cpEvenRound(str, index);
        },

      getShowResultStr(str){
        var res = str;
        if(this.isEmpty(str)) return '--';
        if(str.includes(':')){
          res = str.split(':').slice(1).join(':');
        }
        if(str.includes('：')){
          res = str.split('：').slice(1).join('：');
        }
        if(res.includes('。')){
          res = res.split('。')[0] + '。';
        }
        return res.replace(';', '').replace('；', '');

      },
      sfxInfoSkgjzj: function(value, index) {
        try {
          if (value && value[index]) {
            return value[index].skgjzj || '/';
          }
        } catch (error) {
          return '/';
        }
      },
      stringToFixed(string, fixed){
            if(this.isEmpty(string)) return '/'
            return cpEvenRound(string, fixed);
      },
      // 计算累计筛余
      sfxLjsypjz: function(value) {
        if (value) {
          const targetObject = this.sfxInfo.find(item => item?.skgjzj === value);
          const foundLjsy = targetObject ? targetObject.ljsy : '--';
          return this.stringToFixed(foundLjsy, 0) || '--';
        }
        return "--"
      }
    }
  }
</script>

<style lang="scss" scoped>
  @import '../print.css';
  .border {
    border: 3px solid black; /* 设置边框粗细 */
}
.border th, .border td{
    border: 2px solid black; /* 设置边框粗细 */
}
</style>