<!--
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-05-22 00:03:51
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-04-08 01:00:37
 * @FilePath: /quality_center_web/src/pages/print/components/hntks.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div class="portrait" style="margin-top: 60px; font-size: 14px;">
        <template v-for="(item, index) in tableDataList">
            <div align="center" class="lab-report">
        <div class="company-label" :style="{lineHeight: '36px', fontWeight: '900', fontSize: '28px', marginTop: indexNum > 0 ? '80px' : 0}">{{companyName}}</div>
        <div class="title-label" style="font-weight: 900; font-size: 36px; line-height: 36px;">预拌(商品)混凝土出厂质量证明书</div>

        <div class="lab-sub-title">
            <div class="lab-sub-title-container" style="margin-top: 10px;">{{companyInfo?.qualityCertificateIso}}</div>
            <div class="lab-sub-title-container">
                <div class="lab-sample-id">NO:<span>{{ showYearNoStr(erpRwdextra?.rwdextraExtend?.qualityCertificateNo, erpRwdextra?.rwdextraExtend?.createTime) }} </span></div>
            </div>
        </div>

        <table  border="1" class="border" style="margin-bottom: 10px;margin-top: auto;table-layout:fixed;">
            <tr height="46">
                <td colspan="4" >生产企业</td>
                <!-- erpRwdextra?.buildNameNew | isNull -->
                 <!-- :colspan="!!wjj2?.wlgg ? 3 : 2" -->
                <td :colspan="!!wjj2?.wlgg ? 10 : 9">{{companyName  }}</td>
                <td colspan="3">备案编号</td>
                <td :colspan="!!wjj2?.wlgg ? 7 : 6">{{ companyInfo?.companyRecord | isNull }}</td>
            </tr>
            <tr height="46">
                <td colspan="4">购货单位</td>
                <td :colspan="!!wjj2?.wlgg ? 10 : 9">{{ rwdextraObj?.fhtdw | isNull }}</td>
                <td colspan="3">合同编号</td>
                <td :colspan="!!wjj2?.wlgg ? 7 : 6">{{ rwdextraObj?.fhtbh | isNull }}</td>
            </tr>
            <tr height="80">
                <td colspan="4">工程名称及部位</td>
                <td :colspan="!!wjj2?.wlgg ? 20 : 18" left>
                    {{ rwdextraObj?.fgcmc | isNull }}
                    <br/>
                    {{ rwdextraObj?.fjzbw | isNull }}
                </td>
            </tr>
            <tr height="46">
                <td colspan="4">供应数量(m³)</td>
                <td :colspan="!!wjj2?.wlgg ? 7 : 6">{{ getYongLiangInt(rwdextraObj?.fwcsl, 2) }}</td>
                <td colspan="3">供应日期</td>
                <td :colspan="!!wjj2?.wlgg ? 10 : 9">{{ getGongYingDate(erpRwdextra?.rwdextraExtend?.supplyStartTime, erpRwdextra?.rwdextraExtend?.supplyEndTime) }}</td>
                <!-- <td >&nbsp;</td> -->
            </tr>
            <tr height="46">
                <td colspan="4">强度等级或品种</td>
                <!-- {{ mixProportion?.proportionQddj || '' }}{{getKsdj()}} -->
                <td colspan="4">{{getTPZString(rwdextraObj?.ftpz)}}</td>
                <td :colspan="!!wjj2?.wlgg ? 3 : 2">坍落度</td>
                <td colspan="4">({{ rwdextraObj?.ftld | isNull }})mm</td>
                <td colspan="4">配合比号</td>
                <td :colspan="!!wjj2?.wlgg ? 5 : 4">{{ rwdextraObj?.fphbNo | isNull }}</td>
            </tr>
            <tr height="46">
                <td rowspan="6" width="80">配<br />合<br />比</td>
                <td colspan="3">名称</td>
                <td colspan="2">{{getShowString(getYongLiangInt(sn?.dyyl, 0), '水泥')}}</td>
                <td colspan="2">{{getShowString(getYongLiangInt(xgl?.dyyl, 0), '砂')}}</td>
                <td colspan="2">{{getShowString(getYongLiangInt(cgl?.dyyl, 0), '石')}}</td>
                <td colspan="2">{{getShowString(getYongLiangInt(water?.dyyl, 0), '水')}}</td>
                <td colspan="2">{{getShowString(getYongLiangInt(wjj1?.dyyl, 0), !!wjj2?.wlgg ? '外加剂1' : '外加剂')}}</td>
                <td colspan="2" v-if="!!wjj2?.wlgg">{{getShowString(getYongLiangInt(wjj2?.dyyl, 0), '外加剂2')}}</td>
                <td colspan="2">{{getShowString(getYongLiangInt(fmh?.dyyl, 0), '粉煤灰')}}</td>
                <td colspan="2">{{getShowString(getYongLiangInt(kzf?.dyyl, 0), '矿粉')}}</td>
                <td colspan="2">{{getShowString(getYongLiangInt(wcl1?.dyyl, 0), '外掺料1')}}</td>
                <td colspan="2">{{getShowString(getYongLiangInt(wcl2?.dyyl, 1), '外掺料2')}}</td>
            </tr>
            <tr height="60">
                <td colspan="3">品种、规格</td>
                <td colspan="2">{{ getShowString(getYongLiangInt(sn?.dyyl, 0), sn?.wlgg) }}</td>
                <td colspan="2">{{getShowString(getYongLiangInt(xgl?.dyyl, 0), xgl?.clmc + xgl?.clgg)}}</td>
                <td colspan="2">{{getShowString(getYongLiangInt(cgl?.dyyl, 0), cgl?.clmc + cgl?.clgg)}}</td>
                <td colspan="2">{{getShowString(getYongLiangInt(water?.dyyl, 0), water?.clgg)}}</td>
                <td colspan="2">{{ getShowString(wjj1?.dyyl, wjj1?.wlgg)}}</td>
                <td colspan="2" v-if="!!wjj2?.wlgg">{{getShowString(wjj2?.dyyl, wjj2?.wlgg)}}</td>
                <td colspan="2">{{getShowString(getYongLiangInt(fmh?.dyyl, 0), fmh?.clgg)}}</td>
                <td colspan="2">{{getShowString(getYongLiangInt(kzf?.dyyl, 0), kzf?.clgg)}}</td>
                <td colspan="2">{{getShowString(getYongLiangInt(wcl1?.dyyl, 0), wcl1?.wlmc + wcl1?.wlgg)}}</td>
                <td colspan="2">{{getShowString(getYongLiangInt(wcl2?.dyyl, 1), wcl2?.clgg)}}</td>
            </tr>
            <tr height="46">
                <td colspan="3">厂名或产地</td>
                <td colspan="2">{{ getShowString(getYongLiangInt(sn?.dyyl, 0), getShowCJ(sn, companyInfo?.snConfig))  }}</td>
                <td colspan="2">{{ getShowString(getYongLiangInt(xgl?.dyyl, 0), getShowCJ(xgl, companyInfo?.xglConfig))  }}</td>
                <td colspan="2">{{ getShowString(getYongLiangInt(cgl?.dyyl, 0), getShowCJ(cgl, companyInfo?.cglConfig))  }}</td>
                <td colspan="2">{{ getShowString(getYongLiangInt(water?.dyyl, 0), getShowCJ(water, 1))  }}</td>
                <td colspan="2">{{ getShowString(wjj1?.dyyl, getShowCJ(wjj1, companyInfo?.wjjConfig))  }}</td>
                <td colspan="2" v-if="!!wjj2?.wlgg">{{ getShowString(wjj2?.dyyl, getShowCJ(wjj2, companyInfo?.wjjConfig))  }}</td>
                <td colspan="2">{{ getShowString(getYongLiangInt(fmh?.dyyl, 0), getShowCJ(fmh, companyInfo?.fmhConfig))  }}</td>
                <td colspan="2">{{ getShowString(getYongLiangInt(kzf?.dyyl, 0), getShowCJ(kzf, companyInfo?.kzfConfig))  }}</td>
                <td colspan="2">{{ getShowString(getYongLiangInt(wcl1?.dyyl, 0), getShowCJ(wcl1, companyInfo?.wcl1Config))  }}</td>
                <td colspan="2">{{ getShowString(getYongLiangInt(wcl2?.dyyl, 1), getShowCJ(wcl2, companyInfo?.wcl2Config))  }}</td>
            </tr>
            <tr height="46">
                <td colspan="3">质保书编号</td>
                <td colspan="2" style="font-size: 13px">{{ getShowString(getYongLiangInt(sn?.dyyl, 0), getZhiBaoNumStr(sn?.batch))  }}</td>
                <td colspan="2" style="font-size: 13px">{{ getShowString(getYongLiangInt(xgl?.dyyl, 0), getZhiBaoNumStr(xgl?.batch))  }}</td>
                <td colspan="2" style="font-size: 13px">{{ getShowString(getYongLiangInt(cgl?.dyyl, 0), getZhiBaoNumStr(cgl?.batch))  }}</td>
                <td colspan="2" style="font-size: 13px">{{ getShowString(getYongLiangInt(water?.dyyl, 0), getZhiBaoNumStr(water?.batch))  }}</td>
                <td colspan="2" style="font-size: 13px">{{ getShowString(wjj1?.dyyl, getZhiBaoNumStr(wjj1?.batch))  }}</td>
                <td colspan="2" v-if="!!wjj2?.wlgg">{{ getShowString(wjj2?.dyyl, getZhiBaoNumStr(wjj2?.batch))  }}</td>
                <td colspan="2" style="font-size: 13px">{{ getShowString(getYongLiangInt(fmh?.dyyl, 0), getZhiBaoNumStr(fmh?.batch))  }}</td>
                <td colspan="2" style="font-size: 13px">{{ getShowString(getYongLiangInt(kzf?.dyyl, 0), getZhiBaoNumStr(kzf?.batch))  }}</td>
                <td colspan="2" style="font-size: 13px">{{ getShowString(getYongLiangInt(wcl1?.dyyl, 0), getZhiBaoNumStr(wcl1?.batch))  }}</td>
                <td colspan="2" style="font-size: 13px">{{ getShowString(getYongLiangInt(wcl2?.dyyl, 1), getZhiBaoNumStr(wcl2?.batch))  }}</td>
            </tr>
            <tr height="46">
                <td colspan="3">复试编号</td>
                <td colspan="2" style="font-size: 13px">{{ getShowString(getYongLiangInt(sn?.dyyl, 0), sn?.xhbgbh) }}</td>
                <td colspan="2" style="font-size: 13px">{{ getShowString(getYongLiangInt(xgl?.dyyl, 0), xgl?.xhbgbh) }}</td>
                <td colspan="2" style="font-size: 13px">{{ getShowString(getYongLiangInt(cgl?.dyyl, 0), cgl?.xhbgbh) }}</td>
                <td colspan="2" style="font-size: 13px">{{ getShowString(getYongLiangInt(water?.dyyl, 0), water?.xhbgbh)  }}</td>
                <td colspan="2" style="font-size: 13px">{{ getShowString(wjj1?.dyyl, wjj1?.xhbgbh)  }}</td>
                <td colspan="2" v-if="!!wjj2?.wlgg">{{ getShowString(wjj2?.dyyl, wjj2?.xhbgbh)  }}</td>
                <td colspan="2" style="font-size: 13px">{{ getShowString(getYongLiangInt(fmh?.dyyl, 0), fmh?.xhbgbh) }}</td>
                <td colspan="2" style="font-size: 13px">{{ getShowString(getYongLiangInt(kzf?.dyyl, 0), kzf?.xhbgbh) }}</td>
                <td colspan="2" style="font-size: 13px">{{ getShowString(getYongLiangInt(wcl1?.dyyl, 0), wcl1?.xhbgbh) }}</td>
                <td colspan="2" style="font-size: 13px">{{ getShowString(getYongLiangInt(wcl2?.dyyl, 1), wcl2?.xhbgbh) }}</td>
            </tr>
            <tr height="46">
                <td colspan="3">立方米用量(Kg/m3)</td>
                <td colspan="2">{{ getYongLiangInt(sn?.dyyl, 0) }}</td>
                <td colspan="2">{{ getYongLiangInt(xgl?.dyyl, 0) }}</td>
                <td colspan="2">{{ getYongLiangInt(cgl?.dyyl, 0) }}</td>
                <td colspan="2">{{ getYongLiangInt(water?.dyyl, 0) }}</td>
                <td colspan="2">{{ wjj1?.dyyl | isNull }}</td>
                <td colspan="2" v-if="!!wjj2?.wlgg">{{ wjj2?.dyyl | isNull }}</td>
                <td colspan="2">{{ getYongLiangInt(fmh?.dyyl, 0) }}</td>
                <td colspan="2">{{ getYongLiangInt(kzf?.dyyl, 0) }}</td>
                <td colspan="2">{{ getYongLiangInt(wcl1?.dyyl, 0) }}</td>
                <td colspan="2">{{ getYongLiangInt(wcl2?.dyyl, 1) }}</td>
            </tr>
            <tr height="46">
            <td rowspan="9">混<br />凝<br />土</td>
            <td colspan="3" rowspan="2" width="300">
              <div class="cell-container">
                <span style="float:right;margin-top:0px; margin-right: 5px; width: 90%; text-align: right">项目</span>
                <span style="float:left;margin-top:60px; margin-left: 5px; width: 90%; text-align: left">试件编号</span>
              </div>    
            </td>
            <td colspan="12">强度</td>
            <td :colspan="!!wjj2?.wlgg ? 8 : 6">抗渗</td>
            </tr>
            <tr height="46">
                <td colspan="2">强度值(Mpa)</td>
                <td colspan="2">试件编号</td>
                <td colspan="2">强度值(Mpa)</td>
                <td colspan="2">试件编号</td>
                <td colspan="2">强度值(Mpa)</td>
                <td colspan="2" rowspan="8"></td>
                <td :colspan="!!wjj2?.wlgg ? 4 : 3">报告编号</td>
                <td :colspan="!!wjj2?.wlgg ? 4 : 3">结论</td>
            </tr>

            <!-- 这里开始加载数据 -->
            <tr height="46">
                <td colspan="3" style="font-size: 13px;">{{ isArrayNull(tableSampleList[index], 0) | isNull }}</td>
                <td colspan="2">{{ isArrayNull(tableQdJsonList[index], 0) | isNull }}</td>
                <td colspan="2" style="font-size: 13px;">{{ isArrayNull(tableSampleList[index], 1) | isNull }}</td>
                <td colspan="2">{{ isArrayNull(tableQdJsonList[index], 1) | isNull }}</td>
                <td colspan="2" style="font-size: 13px;">{{ isArrayNull(tableSampleList[index], 2) | isNull }}</td>
                <td colspan="2">{{ isArrayNull(tableQdJsonList[index], 2) | isNull }}</td>
                <!-- <td colspan="2"></td> -->
                <!-- 抗渗 -->
                <td :colspan="!!wjj2?.wlgg ? 4 : 3" style="font-size: 13px;">{{ ksInfo?.experimentInfo?.reportNo | isNull}}</td>
                <!-- <td colspan="3">{{ getKSDJResult(ksInfoObj?.ssksdj, rwdextraObj?.ftbj) }}</td> -->
                <td :colspan="!!wjj2?.wlgg ? 4 : 3">{{ getKangShenJieLun(rwdextraPrintInfo?.kstzjl) }}</td>
            </tr>
            <tr height="46">
                <td colspan="3" style="font-size: 13px;">{{ isArrayNull(tableSampleList[index], 3) | isNull }}</td>
                <td colspan="2">{{ isArrayNull(tableQdJsonList[index], 3) | isNull }}</td>
                <td colspan="2" style="font-size: 13px;">{{ isArrayNull(tableSampleList[index], 4) | isNull }}</td>
                <td colspan="2">{{ isArrayNull(tableQdJsonList[index], 4) | isNull }}</td>
                <td colspan="2" style="font-size: 13px;">{{ isArrayNull(tableSampleList[index], 5) | isNull }}</td>
                <td colspan="2">{{ isArrayNull(tableQdJsonList[index], 5) | isNull }}</td>
                <!-- <td colspan="2"></td> -->
                <!-- 抗折 -->
                <td :colspan="!!wjj2?.wlgg ? 8 : 6">抗折</td>
            </tr>
            <tr height="46">
                <td colspan="3" style="font-size: 13px;">{{ isArrayNull(tableSampleList[index], 6) | isNull }}</td>
                <td colspan="2">{{ isArrayNull(tableQdJsonList[index], 6) | isNull }}</td>
                <td colspan="2" style="font-size: 13px;">{{ isArrayNull(tableSampleList[index], 7) | isNull }}</td>
                <td colspan="2">{{ isArrayNull(tableQdJsonList[index], 7) | isNull }}</td>
                <td colspan="2" style="font-size: 13px;">{{ isArrayNull(tableSampleList[index], 8) | isNull }}</td>
                <td colspan="2">{{ isArrayNull(tableQdJsonList[index], 8) | isNull }}</td>
                <!-- <td colspan="2"></td> -->
                <!-- 抗折 -->
                <td :colspan="!!wjj2?.wlgg ? 4 : 3">报告编号</td>
                <td :colspan="!!wjj2?.wlgg ? 4 : 3">强度值(MPa)</td>
            </tr>
            <tr height="46">
                <td colspan="3" style="font-size: 13px;">{{ isArrayNull(tableSampleList[index], 9) | isNull }}</td>
                <td colspan="2">{{ isArrayNull(tableQdJsonList[index], 9) | isNull }}</td>
                <td colspan="2" style="font-size: 13px;">{{ isArrayNull(tableSampleList[index], 10) | isNull }}</td>
                <td colspan="2">{{ isArrayNull(tableQdJsonList[index], 10) | isNull }}</td>
                <td colspan="2" style="font-size: 13px;">{{ isArrayNull(tableSampleList[index], 11) | isNull }}</td>
                <td colspan="2">{{ isArrayNull(tableQdJsonList[index], 11) | isNull }}</td>
                <!-- <td colspan="2"></td> -->
                <!-- 抗折 -->
                <td :colspan="!!wjj2?.wlgg ? 4 : 3" style="font-size: 13px;">{{ kzInfo?.experimentInfo?.reportNo | isNull }}</td>
                <td :colspan="!!wjj2?.wlgg ? 4 : 3">{{ kzInfoObj?.zsbzqd | isNull }}</td>
            </tr>
            <tr height="46">
                <td colspan="3" style="font-size: 13px;">{{ isArrayNull(tableSampleList[index], 12) | isNull }}</td>
                <td colspan="2">{{ isArrayNull(tableQdJsonList[index], 12) | isNull }}</td>
                <td colspan="2" style="font-size: 13px;">{{ isArrayNull(tableSampleList[index], 13) | isNull }}</td>
                <td colspan="2">{{ isArrayNull(tableQdJsonList[index], 13) | isNull }}</td>
                <td colspan="2" style="font-size: 13px;">{{ isArrayNull(tableSampleList[index], 14) | isNull }}</td>
                <td colspan="2">{{ isArrayNull(tableQdJsonList[index], 14) | isNull }}</td>
                <!-- 抗折 -->
                <td :colspan="!!wjj2?.wlgg ? 4 : 3" style="font-size: 13px;">---</td>
                <td :colspan="!!wjj2?.wlgg ? 4 : 3">---</td>
            </tr>
            <tr height="46">
                <td colspan="3" style="font-size: 13px;">{{ isArrayNull(tableSampleList[index], 15) | isNull }}</td>
                <td colspan="2">{{ isArrayNull(tableQdJsonList[index], 15) | isNull }}</td>
                <td colspan="2" style="font-size: 13px;">{{ isArrayNull(tableSampleList[index], 16) | isNull }}</td>
                <td colspan="2">{{ isArrayNull(tableQdJsonList[index], 16) | isNull }}</td>
                <td colspan="2" style="font-size: 13px;">{{ isArrayNull(tableSampleList[index], 17) | isNull }}</td>
                <td colspan="2">{{ isArrayNull(tableQdJsonList[index], 17) | isNull }}</td>
                <!-- <td colspan="2"></td> -->
                <td :colspan="!!wjj2?.wlgg ? 4 : 3" rowspan="2">{{ '拌合物中水溶性氯离子含量' }}</td>
                <td :colspan="!!wjj2?.wlgg ? 4 : 3" rowspan="2">氯离子含量 ≤ {{ rwdextraPrintInfo?.llzhl | isNull }} %</td>
            </tr>
            <tr height="46">
                <td colspan="3" style="font-size: 13px;">{{ isArrayNull(tableSampleList[index], 18) | isNull }}</td>
                <td colspan="2">{{ isArrayNull(tableQdJsonList[index], 18) | isNull }}</td>
                <td colspan="2" style="font-size: 13px;">{{ isArrayNull(tableSampleList[index], 19) | isNull }}</td>
                <td colspan="2">{{ isArrayNull(tableQdJsonList[index], 19) | isNull }}</td>
                <td colspan="2" style="font-size: 13px;">{{ isArrayNull(tableSampleList[index], 20) | isNull }}</td>
                <td colspan="2">{{ isArrayNull(tableQdJsonList[index], 20) | isNull }}</td>
            </tr>
            
            <tr height="180">
                <td colspan="1">备<br />注</td>
                <td :colspan="!!wjj2?.wlgg ? 23 : 21" style="line-height: 45px;" left>
                    1、出厂质量证明书应在有关检测项目完成后一周内送到购货单位。<br />
                    2、出厂质量证明书一式二份，购货单位和预混凝土生产企业各留一份。<br />
                    3、生产任务单编号：{{ rwdextraObj?.frwno | isNull }}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{ xgl?.clgg }}氯离子含量={{getLvLiZi()}}%
                    <br/>
                    <template v-for="(item, index) in remarkStrList">
                      {{index + 4}}、{{item}}<br />
                    </template>
                </td>
            </tr>
        </table>
        <table  class="lab-sign" style="border: none;table-layout:fixed;">
            <tr height="40" style="border: none;">
            <td colspan="8" style="border: none;">
                <div class="sign-user">
                填表人：
                <!-- <img
                v-if="true"
                src=""
                width="150" height="46" /> -->
                </div>
            </td>
            <td colspan="8" style="border: none;">
                <div class="sign-user">
                技术负责人：
                <!-- <img
                v-if="true"
                src=""
                width="150" height="46" /> -->
                </div></td>
            <td colspan="8" style="border: none;">
                <div class="sign-user" style="display: block; justify-items: flex-start;">
                    <div>签发日期：{{getSignTime(rwdextraPrintInfo?.cczmqfrq)}}</div>
                    <div style="height: 5px;width: 10px;"></div>
                    <div>企业盖章：</div>
                </div>
            </td>
            </tr>
        </table>
        <div style="margin-top: 0px; font-size: 15px; font-weight: 400; text-align: right">共{{tableDataList.length}}页 当前第{{index+1}}页</div>
        </div>
        </template>
        
        <!-- 分页数据 -->
        <!-- <template v-if="tableDataList.length > 0">
            <template v-for="(item, index) in tableDataList">
                <div :key="index">
                    <table >
                    <tr height="130"></tr>
                </table>
                <table border="1" class="border" style="margin-bottom: 10px;margin-top: auto;table-layout:fixed;">
                <tr height="46">
                    <td colspan="1" rowspan="30">混<br />凝<br />土</td>
                    <td colspan="3">试件编号</td>
                    <td colspan="3">强度值</td>
                    <td colspan="3">试件编号</td>
                    <td colspan="3">强度值</td>
                    <td colspan="3">试件编号</td>
                    <td colspan="3">强度值</td>
                    <td colspan="3">试件编号</td>
                    <td colspan="3">强度值</td>
                </tr>
                <template v-for="(obj, indexNum) in item[0]">
                        <tr height="46" :key="indexNum">
                            <template v-for="(objInfo, idx) in obj">
                                <td colspan="3" style="font-size: 13px" :key="idx">{{objInfo?.ypbh | isNull}}</td>
                                <td colspan="3" :key="objInfo">{{objInfo?.qdpjz | isNull}}</td>
                            </template>
                        </tr>
                </template>
                </table>
                 <div style="margin-top: 0px; font-size: 15px; font-weight: 400; text-align: right">共{{pageNum}}页 当前第{{index + 2}}页</div>
                </div>
            </template>
        </template> -->

    </div>
</template>

<script>
import { hntTaskMixed } from './hntTaskMixed';
import {cpEvenRound} from "@/utils/calculate.js"

export default {
    mixins: [hntTaskMixed],
    props: {
        companyName: {
            type: String,
            default: ""
        },
        indexNum:{
            type: Number,
            default:0
        },
        companyInfo:{
            type: Object,
            default:() => { return {} }
        },
        ksInfo:{
            type: Object,
            default:() => { return {} }
        },
        kzInfo:{
            type: Object,
            default:() => { return {} }
        },
        mixMaterialsExperimentInfo:{
            type: Object,
            default:() => { return {} }
        }
    },

    data() {
        return {
            experimentDetail: [],
            ksInfoObj:{},
            kzInfoObj:{},
            llzObj: {},
            rwdextraPrintInfo:{},
            // experimentInfo: [],
            qdJsonInfo: [],
            reportNoList: [], // 报告编号数组
            sampleNoList: [], // 样品编号数组
            conclusionList: [], // 结论数组

            tableDataList:[],
            tableKyInfoList:[],
            tableQdJsonList:[],
            tableSampleList:[],
            pageNum:1,

            remarkStrList:[],
        }
    },
    methods:{

    getShowString(check, str){
        console.log('check:',check);
        console.log('getShowString:',str);
        if(check == '' || check == '0' || !check || check == '---' || str == '' || !str) {
            return '---';
        }
        return str;

    },

        showYearNoStr(no, creatTime){
            console.log('creatTime：', creatTime);
            let timeList = creatTime?.split('-');
            let year = '';
            if(timeList && timeList.length > 0){
                year = timeList[0].slice(-2);
                console.log('year：', year);
            }
            if(this.isEmpty(no)) return '---';
            return year + no;

        },
         // ksdj 字段
      getKsdj() {
        let str = this.rwdextraObj?.ftbj || "";
        let list = str.split('|');
        if (list.length > 3) {
            return list[3];
        }
        return "---";
      },

        getKSDJResult(val1, val2){
            if(this.isEmpty(val2) || this.isEmpty(val1)) {
                return '---';
            }
            let list = val2.split("|");
            var checkVal = ''
            if(list.length > 4){
                checkVal = list[3];
            }
            if((val1.includes('P') || val1.includes('p')) && checkVal.includes('P') || checkVal.includes('p')){
                let ksdj1 = val1.slice(1);
                let ksdj2 = checkVal.slice(1);
                return parseInt(ksdj1) >= parseInt(ksdj2) ? '合格' : '不合格'
                
            }
            return '---';

        },
        isNullOrEmptyObject(obj) {
            return obj === null || Object.keys(obj || {}).length === 0;
        },

        getTPZString(tpz){
        console.log('返回的砼品种：', tpz);
        // let res = '';
            // if(tpz.includes('自密实')){
            //     res = '自密实';
            //     res = '';
            // }

            // if(tpz.includes('耐久性')){
            //     res = res + '耐久性';
            //     res = '';
            // }
            return tpz;
        },

        getSignTime(time){
            return time;
            if(!!time){
                return time;
            }
            var kyInfo = {};
            if(this.kyInfoList.length > 0){
                // kyInfo = this.kyInfoList[0];
                kyInfo = this.kyInfoList.reduce((maxObj, currentObj) => {
                    const currentDate = new Date(currentObj.experimentInfo.entrustTime);
                    const maxDate = maxObj? new Date(maxObj.experimentInfo.entrustTime) : null;
                    if (!maxDate || currentDate > maxDate) {
                        return currentObj;
                    }
                    return maxObj;
                }, null);
            }
            // 报告日期
            var reportTime = '';
            // 委托日期
            var entrustTime  = kyInfo?.experimentInfo?.entrustTime || '';
            console.log('委托日期:',kyInfo?.experimentInfo?.entrustTime);
            // 这个签发日期，有做过逻辑处理，但是，日期是12日-13日，要拿13日来处理逻辑
            var gongyingTime1 = this.erpRwdextra?.rwdextraExtend?.supplyStartTime;
            var gongyingTime2 = this.erpRwdextra?.rwdextraExtend?.supplyEndTime;
            if(gongyingTime1?.includes('-12') && gongyingTime2?.includes('-13')){
                entrustTime = gongyingTime2;
            }
            if(entrustTime != '') {
                reportTime = this.getFutureDate(entrustTime, 28);
            }
            // 没有抗渗 取值抗压的报告日期，即委托日期+28天
            if(this.isNullOrEmptyObject(this.ksInfo)){
                return reportTime;
            } else {
            // 有抗渗
            // 如果是P6:抗压报告日期+3天
            // 如果是P8:抗压报告日期+4天
            // 如果是P10:抗压报告日期+5天
            // 如果是P12:抗压报告日期+6天
            const ksdj = this.ksInfo?.experimentInfo?.ksdj || '';
            switch (ksdj) {
                case 'P6':
                    return this.getFutureDate(reportTime, 3);
                case 'P8':
                    return this.getFutureDate(reportTime, 4);
                case 'P10':
                    return this.getFutureDate(reportTime, 5);
                case 'P12':
                    return this.getFutureDate(reportTime, 6);
                // 可以有任意数量的 case 语句
                default:
                    return '';
                    // 当 expression 与所有 case 的值都不匹配时执行的代码
                }


            }

        },
        getFutureDate(dateStr, days) {
            if(!dateStr) return '---';
            if(dateStr.length < 10 ) return dateStr;
            // 将日期字符串解析为 Date 对象
            const parts = dateStr?.split('-');
            const year = parseInt(parts[0], 10);
            const month = parseInt(parts[1], 10) - 1; // 月份从 0 开始计数
            const day = parseInt(parts[2], 10);
            const date = new Date(year, month, day);

            // 增加指定天数
            date.setDate(date.getDate() + days);

            // 格式化日期为 yyyy-mm-dd 格式
            const formattedYear = date.getFullYear();
            const formattedMonth = String(date.getMonth() + 1).padStart(2, '0');
            const formattedDay = String(date.getDate()).padStart(2, '0');
            return `${formattedYear}-${formattedMonth}-${formattedDay}`;
        },

        getZhiBaoNumStr(str){
            if(!str) return '---';
            const index = str.search(/[^a-zA-Z0-9]/);
            return index === -1 ? str : str.slice(0, index);
        },

        getShowCJ(obj, config){
            let cjjc = obj?.cjjc;
            let cj = obj?.cj;
            let gysmcjc = obj?.gysjc;
            let gysmc = obj?.gys;
            var showName = cjjc;
            var result = '---';

            if(config == 1){
                if(this.isEmpty(cjjc) && !this.isEmpty(cj)){
                    showName = cj
                } 
            } else {
                if(!this.isEmpty(gysmcjc)){
                    showName = gysmcjc
                } else {
                    showName = gysmc
                }
            }
            result = this.isEmpty(showName) ? '---' : showName;
            return result;
        },
        getLvLiZi(){
            var xglInfo = {};
            var result = '-';
            var mapInfo =  this.mixMaterialsExperimentInfo?.xglInfo;
            if(this.isEmpty(mapInfo)){
                result = '-'
            }
            var list = mapInfo?.experimentDetailList;
            if (list && list.length > 0) {
                list.map(item => {
                if (item.testProjectCode === 'FINE_AGGREGATE_PARAM_XGL_LLZHL') {
                        // 细骨料
                        xglInfo = item.objJson || {};
                    }
                });
            }
            result = this.isEmpty(xglInfo?.pjz) ? '-' : xglInfo?.pjz;
            return result;
        },

        // 将数组 originalArray 装换成每个元素是长度是 subLength 的二位数组
      convertArray(originalArray, subLength) {
        const result = [];
        var flage = false;
          for (let i = 0; i < originalArray.length; i += subLength) {
            const subArray = originalArray.slice(i, i + subLength);
            // 补充不足4个元素的情况
            while (subArray.length < subLength) {
              flage = true
              subArray.push('');
            }
            result.push(subArray);
          }
          if(!flage && originalArray.length % subLength != 0){
            var list = [];
            for(let i = 0; i<subLength; i++){
              list.push('');
            }
            result.push(list);
          }
          return result;
        },
        // 
        convertTo2DArray(arr, subArrayLength, columNum=4) {
            // 计算输出二维数组的长度
            const outputLength = Math.ceil(arr.length / subArrayLength);
            let output = new Array(outputLength);
            for (let i = 0; i < outputLength; i++) {
                output[i] = new Array(subArrayLength).fill({});
                output[i].splice(0, output[i].length, ...arr.slice(i * subArrayLength, (i + 1) * subArrayLength));
                const subListArrayLength = Math.ceil(subArrayLength / columNum);
                let subListArray = new Array(subListArrayLength);
                for (let j = 0; j < subListArrayLength; j++) {
                    subListArray[j] = new Array(subListArrayLength).fill([{},{},{},{}]);
                    subListArray[j].splice(0, subListArrayLength, ...output[i].slice(j * columNum, (j + 1) * columNum));

                if(subListArray[j].length < 4){
                    for (let k = 4-subListArray[j].length; k < 4; k++) {
                        subListArray[j][k] = {};
                    }
                }
                if(subListArray[j].length == 0){
                    subListArray[j] = [{},{},{},{}];
                }
                }
                
                output[i].splice(0, output[i].length, subListArray);
                
            }
            this.pageNum = output.length + 1
            return output;
        },
        getTimeStr(time){
            if(!this.isEmpty(time)){
                return time.length > 10 ? time?.substring(0,10) : time
            }
            return '';
        },
        getGongYingDate(start, end){

            var time1 = this.getTimeStr(start);
            var time2 = this.getTimeStr(end);
            if(!this.isEmpty(time1) && this.isEmpty(time2)){
                return time1;
            }
            if(!this.isEmpty(time1) && !this.isEmpty(time2) && time1 != time2){
                return time1 + ' 至' + time2;
            }
            if(this.isEmpty(time1) && !this.isEmpty(time2)){
                return time2;
            }
            if(time1 == time2 && !this.isEmpty(time1)){
                return time2;
            }
            return '--- 至 ---';
        },
        getYongLiangInt(str, index){
            if(this.isEmpty(str) || str == '0') return '---'
            return cpEvenRound(str, index);
        },

        getKangShenJieLun(value){
            if(this.isEmpty(value) || value == '不合格') return '---';
            return value;
        },
        isEmpty(val) {
            if (typeof val === "boolean") {
                return false;
            }
            if (typeof val === "number") {
                return false;
            }
            if (val instanceof Array) {
                if (val.length === 0) return true;
            } else if (val instanceof Object) {
                if (JSON.stringify(val) === "{}" || val == null ) return true;
            } else {
                if (
                val === "null" ||
                val == null ||
                val === "undefined" ||
                val === undefined ||
                val === ""
                )
                return true;
                return false;
            }
            return false;
        }
    },

    mounted() {

        this.rwdextraPrintInfo = this.erpRwdextra?.rwdextraExtend?.rwdextraPrintInfo || {};
        if(!this.isEmpty(this.rwdextraPrintInfo?.cczmsbz)){
            this.remarkStrList = this.rwdextraPrintInfo?.cczmsbz.split("；");
        }

        let ksInfoList = this.ksInfo?.experimentDetailList || [];
        ksInfoList.map((item)=>{
            if(item?.testProjectCode == 'CONCRETE_PARAM_KSDJ'){
                this.ksInfoObj = item?.objJson;
            }
        })
        let kzInfoList = this.kzInfo?.experimentDetailList || [];
        kzInfoList.map((item)=>{
            if(item?.testProjectCode == 'CONCRETE_PARAM_KZQD'){
                this.kzInfoObj = item?.objJson;
            }
        })
        try {
            let list = this.xnbgInfo?.experimentDetailList || [];
            if (list) {
                list.map(item => {
                    let testProjectCode = item?.testProjectCode || ''
                     // 抗渗
                     if(testProjectCode == 'CONCRETE_PARAM_XNBG_LLZHL'){
                        this.llzObj = item?.objJson || {}
                    }
                    
                });
            }
        
        } catch (error) {
            this.llzObj = {};
        }
        try {
            var dataList = [];
            if (this.rwdextraPrintInfo?.kyInfoList?.length > 0) {
                this.rwdextraPrintInfo?.kyInfoList.map(item => {
                    // 组建样品编号数组，有多少个试块试验就组多少个
                    if (item?.ypbh) {
                        // 李威说只取后四位
                        this.sampleNoList.push(item?.ypbh || '');
                        let tableObj = {}
                        tableObj.ypbh = item?.ypbh || '';
                        let resp = cpEvenRound(item?.zsbzqd || '', 1);
                        tableObj.zsbzqd = resp == '0.0' ? '---' : resp;
                        dataList.push(tableObj);
                    }else{
                        this.sampleNoList.push("");
                    }
                    if (item?.zsbzqd) {
                        // 李威说只取后四位
                        let resp = cpEvenRound(item?.zsbzqd || '', 1);
                        this.qdJsonInfo.push(resp == '0.0' ? '---' : resp);
                    }else{
                        this.qdJsonInfo.push("");
                    }
                    
                });

                this.tableDataList = this.convertArray(dataList, 7 * 3);
                if(this.tableDataList.length == 0){
                     this.tableDataList = this.convertArray([''], 7 * 2);
                }
                console.log('this.tableDataList:',this.tableDataList);

                this.tableSampleList = this.convertArray(this.sampleNoList.length == 0 ? [''] : this.sampleNoList, 7 * 3);
                this.tableQdJsonList = this.convertArray(this.qdJsonInfo.length == 0 ? [''] : this.qdJsonInfo, 7 * 3);
                this.tableKyInfoList = this.convertArray(this.kyInfoList.length == 0 ? [''] : this.kyInfoList, 7 * 3);

                // this.tableSampleList = this.convertArray(this.sampleNoList, 7 * 3);
                // this.tableQdJsonList = this.convertArray(this.qdJsonInfo, 7 * 3);
                // this.tableKyInfoList = this.convertArray(this.kyInfoList, 7 * 3);
                console.log('tableKyInfoList---->>>>', this.tableKyInfoList);
                // console.log('长度：', this.sampleNoList.length);
                // if(dataList.length > 21){
                //     let tableList = dataList.slice(21);
                //     this.tableDataList = this.convertTo2DArray(tableList, 29 * 4, 4);
                // }
            } else {
                // 后台返回空的情况
                this.tableDataList = this.convertArray([''], 7 * 2);
                console.log('this.tableDataList->:',this.tableDataList);
            }
        } catch (error) {
            this.experimentDetail = {};
            this.sampleNoList = []
            this.qdJsonInfo = [];
        }
    },
}
</script>

<style lang="scss" scoped>
@import '../../print.css';

.border {
    border: 3px solid black; /* 设置边框粗细 */
}
.border th, .border td{
    border: 2px solid black; /* 设置边框粗细 */
}

.cell-container {
  position: relative;
  width: 100%;
  height: 100%;
}

  .cell-container::before {
    content: '';
    position: absolute;
    top: -1px;
    left: 0;
    width: 130%; 
    height: 1.5px;
    background-color: black !important;
    transform-origin: top left;
    transform: rotate(39deg);
  }

/* 打印样式 */
@media print {
    .cell-container::before {
    content: '';
    position: absolute;
    top: -7px;
    left: 0;
    width: 130%; 
    height: 0px;
    border: 1px solid black !important;
    transform-origin: top left;
    transform: rotate(41deg);
  }
}


</style>