<!--
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-05-22 00:03:51
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2024-10-17 22:00:37
 * @FilePath: /quality_center_web/src/pages/print/components/hntks.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div class="portrait">
        <div class="tip-view">
            <div class="tip-label">C-39b-0907</div>
        </div>
        <div class="company-label">{{companyName}}</div>
        <div class="title-label">混凝土拌合物用水检测报告</div>
        <div align="center" class="lab-report">
            
        <div class="lab-sub-title">
            <div class="lab-sub-title-container" style="margin-top: 40px; margin-left: 140px;">第 1 页  共 1 页</div>
            <div class="lab-sub-title-container">
            <div class="lab-sample-id">委托编号:<span>{{ '/' }} </span></div>
            <div class="lab-report-id">报告编号:<span>{{ '/' }} </span></div>
            </div>
        </div>
            
        <table  border="1" class="border" >
            <tr height="50">
                <td colspan="1">委托单位</td>
                <td colspan="7" center>{{ experimentInfo.experimentDept | isNull }}</td>
            </tr>
            <tr height="50">
                <td colspan="1">委托日期</td>
                <td colspan="3" center>{{ experimentInfo.entrustTime | momentDate }}</td>
                <td colspan="1">报告日期</td>
                <td colspan="3" center>{{ experimentInfo.reportDate | momentDate }}</td>
            </tr>
            <tr height="50">
                <td colspan="1">样品名称</td>
                <td colspan="3" center>{{ '/' }}</td>
                <td colspan="1">样品编号</td>
                <td colspan="3" center>{{ shxhSynchronizedata.sampleId | shxhNull }}</td>
            </tr>
            <tr height="50">
                <td colspan="1">型号规格</td>
                <td colspan="7" center>{{ '/' }}</td>
            </tr>
            <tr height="50">
                <td colspan="1">样品数量</td>
                <td colspan="3" center>{{ '/' }}</td>
                <td colspan="1">代表数量</td>
                <td colspan="3" center>{{ '/' }}t</td>
            </tr>
            <tr height="50">
                <td colspan="1">样品状态</td>
                <td colspan="7" center>{{ '/' }}</td>
            </tr>
            <tr height="50">
                <td colspan="1">评定依据</td>
                <td colspan="7" center>{{ '/' }}</td>
            </tr>
            <tr height="50">
                <td colspan="1">检测方法</td>
                <td colspan="7" center>{{ '/' }}</td>
            </tr>
            <tr height="50">
                <td colspan="1">检测日期</td>
                <td colspan="7" center>{{ '/' }}</td>
            </tr>
            <tr height="50">
                <td colspan="1">序号</td>
                <td colspan="2">检测项目</td>
                <td colspan="1">标准值</td>
                <td colspan="2">检测结果</td>
                <td colspan="2">单向判定</td>
            </tr>
            <tr height="50">
                <td colspan="1">{{ '/' }}</td>
                <td colspan="2">{{ '/' }}</td>
                <td colspan="1">{{ '/' }}</td>
                <td colspan="2">{{ '/' }}</td>
                <td colspan="2">{{ '/' }}</td>
            </tr>
            <tr height="100">
                <td colspan="1">检查结论</td>
                <td colspan="7" center>{{ experimentInfo.conclusion | isNull }}</td>
            </tr>
            <tr height="50">
                <td colspan="1">备注</td>
                <td colspan="7" center>{{ experimentInfo.remark | isNull }}</td>
            </tr>
        </table>

        <table class="lab-sign" style="border: none;">
            <tr height="45" style="border: none;">
            <td style="border: none;"><div class="sign-user">检测报告专用章：</div></td>
            <td style="border: none;">
                <div class="sign-user">
                批准：
                <!-- <img
                v-if="true"
                src=""
                width="150" height="50" /> -->
                </div>
            </td>
            <td style="border: none;">
                <div class="sign-user">
                审核：
                <!-- <img
                v-if="true"
                src=""
                width="150" height="50" /> -->
                </div>
            </td>
            <td style="border: none;">
                <div class="sign-user">
                检测：
                <!-- <img
                v-if="true"
                src=""
                width="150" height="50" /> -->
                </div>
            </td>
            </tr>
        </table>

        </div>
    </div>
</template>

<script>
import { hntMixed } from './hntMixed';

export default {
    mixins: [hntMixed],
    props: {
        data: {
            type: Object,
            default: () => { return {} }
        },
        companyName: {
            type: String,
            default: ""
        },
    },
}
</script>

<style lang="scss" scoped>
@import '../../print.css';
</style>