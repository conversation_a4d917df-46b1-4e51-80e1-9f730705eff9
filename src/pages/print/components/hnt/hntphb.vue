<!--
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-05-22 00:03:51
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-04-07 22:48:51
 * @FilePath: /quality_center_web/src/pages/print/components/hntks.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div class="portrait" style="margin-top: 100px;">
        <!-- <div class="tip-view">
            <div class="tip-label">C-39b-0907</div>
        </div> -->
        <div class="company-label" :style="{lineHeight: '45px', fontWeight: '900', fontSize: '32px', marginTop: indexNum > 0 ? '140px' : 0}">{{companyName}}</div>
        <div class="title-label" style="line-height: 45px; margin-bottom: 20px;font-weight: 900; font-size: 50px;">混  凝  土  配  合  比  设  计  报  告</div>
        <div align="center" class="lab-report">

            <div class="lab-sub-title">
                <div class="lab-sub-title-container" style="margin-bottom: 0px; align-self: end; ">{{companyInfo?.proportioningIso}}</div>
                <div class="lab-sub-title-container">
                    <div class="lab-sample-id" style="line-height: 32px;">委托编号:<span>{{ rwdextraObj?.frwno | isNull }} </span></div>
                    <div class="lab-report-id" style="line-height: 32px;">报告编号:<span>{{ rwdextraObj?.frwno | isNull }} </span></div>
                </div>
            </div>

            <table  border="1" class="border" style="margin-bottom: 20px;margin-top: auto; table-layout:fixed;">
                <tr height="80">
                    <td colspan="3">委托单位</td>
                    <!-- erpRwdextra?.buildNameNew | isNull  -->
                    <td colspan="8">{{ rwdextraObj?.fhtdw | isNull }}</td>
                    <td :colspan="!!wjj2?.wlgg ? 6 : 5">配合比代号</td>
                    <td :colspan="!!wjj2?.wlgg ? 6 : 5">{{ rwdextraObj?.fphbNo | isNull }}</td>
                    <!-- <td colspan="3">{{ rwdextraObj?.fjhsl | isNull }}</td> -->
                </tr>
                <tr height="100">
                    <td colspan="3">工程名称</td>
                    <td colspan="8">{{ rwdextraObj?.fgcmc | isNull }}</td>
                    <td :colspan="!!wjj2?.wlgg ? 3 : 2">申请日期</td>
                    <td colspan="3">{{ getTimeStr(rwdextraObj?.fjhrq) }}</td>
                    <td :colspan="!!wjj2?.wlgg ? 3 : 2">报告日期</td>
                    <td colspan="3">{{ getTimeStr(rwdextraObj?.fjhrq) }}</td>
                </tr>
                <tr height="130">
                    <td colspan="3">设计要求</td>
                    <td colspan="8"> 砼等级：{{ rwdextraObj?.ftpz | isNull }}</td>
                    <td colspan="3">龄期：{{ getLingQi(mixProportion?.proportionLq) }}</td>
                    <td :colspan="!!wjj2?.wlgg ? 9 : 7">用途：{{ rwdextraObj?.fjzbw | isNull  }}</td>
                </tr>
                <tr height="40">
                    <td :rowspan="!!wjj2?.wlgg ? '10' : '9'" colspan="1" width="80">原<br />材<br />料</td>
                    <td colspan="20" style="border:none; text-align: left; padding-left: 15px;">
                        <div style="display: flex; width: 100%;">
                            <div style="width: 120px;">
                                <!-- 
                                
                    <td colspan="2">{{ sn?.dyyl | isNull }}</td>
                    <td colspan="2">{{ xgl?.dyyl | isNull }}</td>
                    <td colspan="2">{{ cgl?.dyyl | isNull }}</td>
                    <td colspan="2">{{ water?.dyyl | isNull }}</td>
                    <td colspan="2">{{ wjj1?.dyyl | isNull }}</td>
                    <td colspan="2" v-if="!!wjj2?.wlgg">{{ wjj2?.dyyl | isNull }}</td>
                    <td colspan="2">{{ fmh?.dyyl | isNull }}</td>
                    <td colspan="2">{{ kzf?.dyyl | isNull }}</td>
                    <td colspan="2">{{ wcl1?.dyyl | isNull }}</td>
                    <td colspan="2">{{ getYongLiangInt(wcl2?.dyyl, 1) }}</td> -->
                            1.水泥
                            </div>
                            <div style="width: 150px;">
                                {{ getShowString(sn?.dyyl, sn?.wlgg)}}
                            </div>
                            <div>
                                <!-- {{ getChangJia(sn?.gys, sn?.cj, companyInfo?.snConfig) }}&nbsp;{{ sn?.certificateNo }} -->
                                {{getShowString(sn?.dyyl, getChangJia(sn?.gys, sn?.cj, companyInfo?.snConfig, sn?.certificateNo) )}} 
                            </div>
                        </div>
                    </td>
                </tr>
                <tr height="40">
                    <td colspan="20" style="border:none; text-align: left;  padding-left: 15px;">
                        <div style="display: flex; width: 100%;">
                            <div style="width: 120px;">
                            2.砂
                            </div>
                            <div style="width: 150px;">
                                <span v-if="isHiddenRGS">{{getShowString(xgl?.dyyl, xgl?.clmc) }}</span>
                                {{getShowString(xgl?.dyyl, xgl?.clgg)}}
                            </div>
                            <div>
                                {{getShowString(xgl?.dyyl , getChangJia(xgl?.gys, xgl?.cj, companyInfo?.xglConfig, xgl?.certificateNo) )}} 
                                <!-- {{ getChangJia(xgl?.gys, xgl?.cj, companyInfo?.xglConfig) }}&nbsp;{{ xgl?.certificateNo }} -->
                            </div>
                        </div>
                    </td>
                </tr>
                <tr height="40">
                    <td colspan="20" style="border:none; text-align: left;  padding-left: 15px;">
                        <div style="display: flex; width: 100%;">
                            <div style="width: 120px">
                            3.石
                            </div>
                            <div style="width: 150px">

                                {{getShowString(cgl?.dyyl , cgl?.clmc + cgl?.clgg)}}
                                <!-- {{ cgl?.clmc }}{{ cgl?.clgg | isNull }} -->
                            </div>
                            <div>
                                {{getShowString(cgl?.dyyl , getChangJia(cgl?.gys, cgl?.cj, companyInfo?.cglConfig, cgl?.certificateNo) )}} 
                                <!-- {{ getChangJia(cgl?.gys, cgl?.cj, companyInfo?.cglConfig) }}&nbsp;{{ cgl?.certificateNo }} -->
                            </div>
                        </div>
                    </td>
                </tr>
                <tr height="40">
                    <td colspan="20" style="border:none; text-align: left;  padding-left: 15px;">
                        <div style="display: flex; width: 100%;">
                            <div style="width: 120px;">
                            4.粉煤灰
                            </div>
                            <div style="width: 150px">
                                {{getShowString(fmh?.dyyl , fmh?.clgg)}}
                                <!-- {{ fmh?.clgg | isNull }} -->
                            </div>
                            <div>
                                {{getShowString(fmh?.dyyl , getChangJia(fmh?.gys, fmh?.cj, companyInfo?.fmhConfig, fmh?.certificateNo) )}} 
                                <!-- {{ getChangJia(fmh?.gys, fmh?.cj, companyInfo?.fmhConfig) }}&nbsp;{{ fmh?.certificateNo }} -->
                            </div>
                        </div>
                    </td>
                </tr>
                <tr height="40">
                    <td colspan="20" style="border:none; text-align: left; padding-left: 15px;">
                        <div style="display: flex; width: 100%;">
                            <div style="width: 120px;">
                            5.外加剂{{!!wjj2?.wlgg ? '1' : ''}}
                            </div>
                            <div style="width: 150px;">

                                {{getShowString(wjj1?.dyyl , wjj1?.wlgg)}}
                                <!-- {{ wjj1?.wlgg | isNull }} -->
                            </div>
                            <div>
                                {{getShowString(wjj1?.dyyl , getChangJia(wjj1?.gys, wjj1?.cj, companyInfo?.wjjConfig, wjj1?.certificateNo) )}} 
                                <!-- {{ getChangJia(wjj1?.gys, wjj1?.cj, companyInfo?.wjjConfig) }}&nbsp;{{ wjj1?.certificateNo }} -->
                            </div>
                        </div>
                    </td>
                </tr>

                <tr v-if="!!wjj2?.wlgg" height="40">
                    <td colspan="20" style="border:none; text-align: left; padding-left: 15px;">
                        <div style="display: flex; width: 100%;">
                            <div style="width: 120px;">
                            6.外加剂2
                            </div>
                            <div style="width: 150px;">
                                {{getShowString(wjj2?.dyyl , wjj2?.wlgg)}}
                                <!-- {{ wjj2?.wlgg | isNull }} -->
                            </div>
                            <div>
                                {{getShowString(wjj2?.dyyl , getChangJia(wjj2?.gys, wjj2?.cj, companyInfo?.wjjConfig, wjj2?.certificateNo) )}} 
                                <!-- {{ getChangJia(wjj2?.gys, wjj2?.cj, companyInfo?.wjjConfig) }}&nbsp;{{ wjj2?.certificateNo }} -->
                            </div>
                        </div>
                    </td>
                </tr>
                <tr height="40">
                    <td colspan="20" style="border:none; text-align: left; padding-left: 15px;">
                        <div style="display: flex; width: 100%;">
                            <div style="width: 120px;">
                            {{!!wjj2?.wlgg ? '7' : '6'}}.水
                            </div>
                            <div style="width: 150px;">
                                {{getShowString(water?.dyyl , water?.clgg)}}
                                <!-- {{ water?.clgg | isNull }} -->
                            </div>
                            <div>
                                {{getShowString(water?.dyyl , getChangJia(water?.gys, water?.cj, 1, water?.certificateNo) )}} 
                                <!-- {{ getChangJia(water?.gys, water?.cj, 1) }}&nbsp;{{ water?.certificateNo }} -->
                            </div>
                        </div>
                    </td>
                </tr>
                <tr height="40">
                    <td colspan="20" style="border:none; text-align: left; padding-left: 15px;">
                        <div style="display: flex; width: 100%;">
                            <div style="width: 120px">
                            {{!!wjj2?.wlgg ? '8' : '7'}}.矿粉
                            </div>
                            <div style="width: 150px">
                                {{getShowString(kzf?.dyyl , kzf?.clgg)}}
                                <!-- {{ kzf?.clgg | isNull }} -->
                            </div>
                            <div>
                                {{getShowString(kzf?.dyyl , getChangJia(kzf?.gys, kzf?.cj, companyInfo?.kzfConfig, kzf?.certificateNo) )}} 
                                <!-- {{ getChangJia(kzf?.gys, kzf?.cj, companyInfo?.kzfConfig) }}&nbsp;{{ kzf?.certificateNo }} -->
                            </div>
                        </div>
                    </td>
                </tr>
                <tr height="40">
                    <td colspan="20" style="border:none; text-align: left; padding-left: 15px;">
                        <div style="display: flex; width: 100%;">
                            <div style="width: 120px;">
                            {{!!wjj2?.wlgg ? '9' : '8'}}.外掺料1
                            </div>
                            <div style="width: 150px;">
                                <!-- 材料名称 + 材料规格 -->
                                 <!-- 李威通知修改成取值材料规格 -->
                                <!-- 材料规格是空的情况就显示0 -->
                                {{getShowString(wcl1?.dyyl, wcl1?.wlmc + wcl1?.wlgg)}}
                                <!-- {{ getWaiCanLiao(wcl1?.wlmc) }}{{wcl1?.wlgg}} -->
                            </div>
                            <div>
                                {{getShowString(wcl1?.dyyl , getChangJia(wcl1?.gys, wcl1?.cj, companyInfo?.wcl1Config, wcl1?.certificateNo) )}} 
                                <!-- {{ getChangJia(wcl1?.gys, wcl1?.cj, companyInfo?.wcl1Config) }}&nbsp;{{ wcl1?.certificateNo }} -->
                            </div>
                        </div>
                    </td>
                    <!-- <td colspan="4" style="border:none; text-align: center; ">{{ getWaiCanLiao(wcl1?.clgg) }}</td>
                    <td colspan="7" style="border:none; text-align: center; ">{{ getChangJia(wcl1?.cj) }}</td>
                    <td colspan="6" style="border:none; text-align: center; " left>{{ wcl1?.certificateNo }}</td> -->
                </tr>
                <tr height="40">
                    <td colspan="20" style="border:none; text-align: left;  padding-left: 15px;">
                        <div style="display: flex; width: 100%; margin-bottom: 15px;">
                            <div style="width: 120px;">
                            {{!!wjj2?.wlgg ? '10' : '9'}}.外掺料2
                            </div>
                            <div style="width: 150px">
                                {{getShowString(getYongLiangInt(wcl2?.dyyl, 1), wcl2?.clgg)}}
                                <!-- {{ getWaiCanLiao(wcl2?.clgg) }} -->
                            </div>
                            <div>
                                {{getShowString(getYongLiangInt(wcl2?.dyyl, 1), getChangJia(wcl2?.gys, wcl2?.cj, companyInfo?.wcl2Config, wcl2?.certificateNo) )}} 
                                <!-- {{ getChangJia(wcl2?.gys, wcl2?.cj, companyInfo?.wcl2Config) }}&nbsp;{{ wcl2?.certificateNo }} -->
                            </div>
                        </div>
                    </td>
                    <!-- <td colspan="4" style="border:none; text-align: center; ">{{ getWaiCanLiao(wcl2?.clgg) }}</td>
                    <td colspan="7" style="border:none; text-align: center; ">{{ getChangJia(wcl2?.cj)}}</td>
                    <td colspan="6" style="border:none; text-align: center; " left>{{ wcl2?.certificateNo }}</td> -->
                </tr>

                <tr height="50">
                    <td rowspan="6" width="80">配<br />合<br />比</td>
                    <td colspan="2">原材料</td>
                    <td colspan="2">水泥</td>
                    <td colspan="2">砂</td>
                    <td colspan="2">石</td>
                    <td colspan="2">水</td>
                    <td colspan="2">外加剂{{!!wjj2?.wlgg ? '1' : ''}}</td>
                    <td colspan="2" v-if="!!wjj2?.wlgg">外加剂2</td>
                    <td colspan="2">粉煤灰</td>
                    <td colspan="2">矿粉</td>
                    <td colspan="2">外掺料1</td>
                    <td colspan="2">外掺料2</td>
                </tr>
                <tr height="50">
                    <td colspan="2">每方砼用量(Kg)</td>
                    <td colspan="2">{{ sn?.dyyl | isNull }}</td>
                    <td colspan="2">{{ xgl?.dyyl | isNull }}</td>
                    <td colspan="2">{{ cgl?.dyyl | isNull }}</td>
                    <td colspan="2">{{ water?.dyyl | isNull }}</td>
                    <td colspan="2">{{ wjj1?.dyyl | isNull }}</td>
                    <td colspan="2" v-if="!!wjj2?.wlgg">{{ wjj2?.dyyl | isNull }}</td>
                    <td colspan="2">{{ fmh?.dyyl | isNull }}</td>
                    <td colspan="2">{{ kzf?.dyyl | isNull }}</td>
                    <td colspan="2">{{ wcl1?.dyyl | isNull }}</td>
                    <td colspan="2">{{ getYongLiangInt(wcl2?.dyyl, 1) }}</td>
                </tr>
                <tr height="50">
                    <td colspan="2">重量比</td>
                    <td colspan="2">{{ getYongLiangInt(sn?.dyylb, 2) }}</td>
                    <td colspan="2">{{getYongLiangInt(xgl?.dyylb, 2)}}</td>
                    <td colspan="2">{{getYongLiangInt(cgl?.dyylb, 2)}}</td>
                    <td colspan="2">{{getYongLiangInt(water?.dyylb, 2)}}</td>
                    <td colspan="2">{{getYongLiangInt(wjj1?.dyylb, 2)}}</td>
                    <td colspan="2" v-if="!!wjj2?.wlgg">{{getYongLiangInt(wjj2?.dyylb, 2)}}</td>
                    <td colspan="2">{{getYongLiangInt(fmh?.dyylb, 2)}}</td>
                    <td colspan="2">{{getYongLiangInt(kzf?.dyylb, 2)}}</td>
                    <td colspan="2">{{getYongLiangInt(wcl1?.dyylb, 2)}}</td>
                    <td colspan="2">{{getYongLiangInt(wcl2?.dyylb, 1)}}</td>
                </tr>
                
                <tr height="50">
                    <td colspan="3">坍落度/扩展度mm</td>
                    <td colspan="7">{{ rwdextraObj?.ftld | isNull }}</td>
                    <td colspan="3">砂率(%)</td>
                    <td :colspan="!!wjj2?.wlgg ? 9 : 7">{{ mixProportion?.printSl | isNull }}</td>
                </tr>
                <tr height="120">
                    <td colspan="1">备<br />注</td>
                    <td :colspan="!!wjj2?.wlgg ? 21 : 19" style="line-height: 34px;" left><br />
                        1、本配合比所用原材料仅对来样负技术责任。
                        <br />2、原材料品种规格发生变化，本配合比无效。 {{getSubRemark(mixProportion?.proportionMaterial?.xgl?.cjjc, mixProportion?.proportionMaterial?.xgl?.cj)}}<br />
                        3、预报方量：{{ erpRwdextra?.planquantity | isNull }} m³
                        &nbsp;&nbsp;&nbsp;
                        氯离子含量≤{{getLvLiZi(erpRwdextra?.rwdextraExtend?.rwdextraPrintInfo?.llzhl)}}%
                        &nbsp;&nbsp;&nbsp;{{getWaiCanLiang(wcl1?.wlmc, wcl1?.wlgg, wcl2?.wlmc, wcl2?.wlgg)}}<br />

                        <template v-for="(item, index) in remarkStrList">
                        {{index + 4}}、{{item}}<br />
                        </template><br />
                    </td>
                </tr>
            </table>
            <table class="lab-sign" style="border: none;">
                <tr height="45" style="border: none;">
                <td style="border: none;">
                    <div class="sign-user">
                    设计单位(盖章)：
                    <!-- <img
                    v-if="true"
                    src=""
                    width="150" height="50" /> -->
                    </div>
                </td>
                <td style="border: none;">
                    <div class="sign-user">
                    技术负责人：
                    <!-- <img
                    v-if="true"
                    src=""
                    width="150" height="50" /> -->
                    </div>
                </td>
                <td style="border: none;">
                    <div class="sign-user">
                    审核：
                    <!-- <img
                    v-if="true"
                    src=""
                    width="150" height="50" /> -->
                    </div>
                </td>
                <td style="border: none;">
                    <div class="sign-user">
                    设计：
                    <!-- <img
                    v-if="true"
                    src=""
                    width="150" height="50" /> -->
                    </div>
                </td>
                </tr>
            </table>
        </div>

    </div>
</template>

<script>
import { hntTaskMixed } from './hntTaskMixed';
import {calcEquation} from "@/utils/calculate.js"
import {cpEvenRound} from "@/utils/calculate.js"

export default {
    mixins: [hntTaskMixed],
    props: {
        companyName: {
            type: String,
            default: ""
        },
        companyInfo:{
            type: Object,
            default:{}
        },
        indexNum:{
            type: Number,
            default:0
        },
        mixMaterialsExperimentInfo:{
            type: Object,
            default:{}
        }
    },
    data(){
        return {
            remarkStrList:[],

            snCertificateNo: "",
            waterCertificateNo: "",
            kzfCertificateNo: "",
            fmhCertificateNo: "",
            xglCertificateNo: "",
            cglCertificateNo: "",
            wjj1CertificateNo: "",
            wjj2CertificateNo: "",
            wcl1CertificateNo: "",
            wcl2CertificateNo: "",
        }
    },

    mounted(){
        if(!this.isEmpty(this.erpRwdextra?.rwdextraExtend?.rwdextraPrintInfo?.phbbz)){
            this.remarkStrList = this.erpRwdextra.rwdextraExtend.rwdextraPrintInfo.phbbz.split("；");
        }

        this.setCertificateNo();
    },

    computed: {
      isHiddenRGS() {
        console.log('------------------:', this.rwdextraObj?.fgcmc)
        if (this.rwdextraObj?.fgcmc && this.rwdextraObj?.fgcmc.indexOf('上海市轨道交通19号线工程土建21标') > 0) {
          return true;
        }
        return false
      }
    },
    
    methods:{
        setCertificateNo() {
            // 备案证书编号修改取值对象
            if (this.mixExperimentInfo) {
                if (this.mixExperimentInfo.snInfo) {
                    this.snCertificateNo = this.mixExperimentInfo.snInfo.experimentInfo?.certificateNo || '';
                }
                if (this.mixExperimentInfo.waterInfo) {
                    this.waterCertificateNo = this.mixExperimentInfo.waterInfo.experimentInfo?.certificateNo || '';
                }
                if (this.mixExperimentInfo.kzfInfo) {
                    this.kzfCertificateNo = this.mixExperimentInfo.kzfInfo.experimentInfo?.certificateNo || '';
                }
                if (this.mixExperimentInfo.fmhInfo) {
                    this.fmhCertificateNo = this.mixExperimentInfo.fmhInfo.experimentInfo?.certificateNo || '';
                }
                if (this.mixExperimentInfo.xglInfo) {
                    this.xglCertificateNo = this.mixExperimentInfo.xglInfo.experimentInfo?.certificateNo || '';
                }
                if (this.mixExperimentInfo.cglInfo) {
                    this.cglCertificateNo = this.mixExperimentInfo.cglInfo.experimentInfo?.certificateNo || '';
                }
                if (this.mixExperimentInfo.wjj1Info) {
                    this.wjj1CertificateNo = this.mixExperimentInfo.wjj1Info.experimentInfo?.certificateNo || '';
                }
                if (this.mixExperimentInfo.wjj2Info) {
                    this.wjj2CertificateNo = this.mixExperimentInfo.wjj2Info.experimentInfo?.certificateNo || '';
                }
                if (this.mixExperimentInfo.wcl1Info) {
                    this.wcl1CertificateNo = this.mixExperimentInfo.wcl1Info.experimentInfo?.certificateNo || '';
                }
                if (this.mixExperimentInfo.wcl2Info) {
                    this.wcl2CertificateNo = this.mixExperimentInfo.wcl2Info.experimentInfo?.certificateNo || '';
                }
            }

            this.sn.certificateNo = this.snCertificateNo;
            this.water.certificateNo = this.waterCertificateNo;
            this.kzf.certificateNo = this.kzfCertificateNo;
            this.xgl.certificateNo = this.xglCertificateNo;
            
            this.cgl.certificateNo = this.cglCertificateNo;
            this.fmh.certificateNo = this.fmhCertificateNo;

            this.wjj1.certificateNo = this.wjj1CertificateNo;
            this.wjj2.certificateNo = this.wjj2CertificateNo;
            this.wcl1.certificateNo = this.wcl1CertificateNo;
            this.wcl2.certificateNo = this.wcl2CertificateNo;
        },
         // ksdj 字段
      getKsdj() {
        let str = this.rwdextraObj?.ftbj || "";
        let list = str.split('|');
        if (list.length > 3) {
            return list[3];
        }
        return "";
      },
        getTimeStr(time){
            if(!this.isEmpty(time)){
                return time.length > 10 ? time?.substring(0,10) : time
            }
            return '/';
        },

    getShowString(check, str){
        console.log('check:',check);
        console.log('getShowString:',str);
        if(check == '' || check == '0' || !check || check == '---' || str == '' || !str) {
            return '---';
        }
        return str;

    },

        isEmpty(val) {
            if (typeof val === "boolean") {
                return false;
            }
            if (typeof val === "number") {
                return false;
            }
            if (val instanceof Array) {
                if (val.length === 0) return true;
            } else if (val instanceof Object) {
                if (JSON.stringify(val) === "{}") return true;
            } else {
                if (
                val === "null" ||
                val == null ||
                val === "undefined" ||
                val === undefined ||
                val === ""
                )
                return true;
                return false;
            }
            return false;
        },
        getLingQi(lq){
            var result = 'R28'
            if(!this.isEmpty(lq)){
                result = 'R' +lq;
            }
            return result;
        },
        getChangJia(gysmc, cj, config, certificateNo){
            // 1-厂家 2-供应商
            var result = '---'
            if(config == 1){
                if(!this.isEmpty(cj)){
                    result = cj;
                }
            } else {
                if(!this.isEmpty(gysmc)){
                    result = gysmc;
                }
            }
            if(result === '---') {
                return result;
            }
            return result + ' ' + certificateNo;
        },
        
        getWaiCanLiang(clmc1, wcj1, clmc2, wcj2){

            var total = parseFloat(this.sn?.dyyl) + parseFloat(this.fmh?.dyyl) + parseFloat(this.kzf?.dyyl);
            var result1 = calcEquation([
                    {
                        v: this.wcl1.dyyl * 100,
                    },
                    {
                        k: '/',
                        v: total,
                    },
                    ], 0);
                    result1 = result1  ? '' : result1 + '%'

            var result2 = calcEquation([
                    {
                        v: this.wcl2.dyyl * 100,
                    },
                    {
                        k: '/',
                        v: total,
                    },
                    ], 0);
                    result2 = result2  ? '' : result2 + '%'
            if(!this.isEmpty(wcj1)){
                return clmc1 + wcj1 + result1;
            }  
            return '' + '\xa0\xa0' + '';
            /*
            if(this.isEmpty(wcj1) && this.isEmpty(wcj2)){
                return '' + '\xa0\xa0' + '';
            } else if(!this.isEmpty(wcj1) && this.isEmpty(wcj2)){
                return clmc1 + wcj1 + result1;
            } 
            
            else if(this.isEmpty(wcj1) && !this.isEmpty(wcj2)){
                return clmc2 + wcj2 + result2;
            } else {
                return clmc1 + wcj1 + result1 + '\xa0\xa0' + clmc2 + wcj2 + result2;
            }
            */
        },
        getWaiCanLiao(wcl){
            var result = '--'
            if(!this.isEmpty(wcl)){
                result = wcl;
            }
            return result;
        },
        getSubRemark(cjjc, cj){
            var result = ''
            if(!this.isEmpty(cjjc)){
                result = '砂生产厂家：' + cjjc;
                return result;
            }
            if(this.isEmpty(cjjc) && !this.isEmpty(cj)){
                result = '砂生产厂家：' + cj;
            }
            return result;
        },
        getYongLiangInt(str, index){
            if(this.isEmpty(str)) return '---'
            return cpEvenRound(str, index);
        },
        getLvLiZi(str){
            var result = '--'
            if(!this.isEmpty(str)){
                result = str;
            }
            return result;
        }
    }
}
</script>

<style lang="scss" scoped>
@import '../../print.css';
.border {
    border: 3px solid black; /* 设置边框粗细 */
}

.border th, .border td{
    border: 2px solid black; /* 设置边框粗细 */
}
</style>