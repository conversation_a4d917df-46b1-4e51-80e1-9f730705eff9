<!--
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-05-22 00:03:51
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2024-10-27 20:43:26
 * @FilePath: /quality_center_web/src/pages/print/components/hntks.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div class="landscape" style="padding-right: 20px">
        <!-- <div class="tip-view">
            <div class="tip-label">C-39b-0907</div>
        </div> -->
        <div class="company-label" style="width: 100%;">{{companyName}}</div>
        <div class="title-label" style="width: 100%;"><b>混凝土开盘鉴定</b></div>
        <div align="center" style="padding-top:10px;">
        
        <div style="width: 100%; line-height: 30px; text-align: right;font-size: 15px">台账编号：{{ showYearNoStr(openAppraisal?.ledgerNo, openAppraisal?.createTime) }}</div>
      

        <table class="border" style=" border-collapse:collapse; width: 100%; font-size: 16px; margin-top: auto; table-layout:fixed;">
            <tr height="44">
            <td colspan="14">工程名称</td>
            <td colspan="50">{{ rwdextraObj?.fgcmc | isNull }}</td>
            <td colspan="10">施工单位</td>
            <td colspan="30">{{ erpRwdextra?.buildNameNew | isNull }}</td>
            </tr>
            <tr height="44">
            <td colspan="14">生产单位</td>
            <td colspan="50">{{ companyName| isNull }}</td>
            <td colspan="10">试配单位</td>
            <td colspan="30">{{ companyName | isNull }}</td>
            </tr>
            <tr height="44">
            <td colspan="14">浇筑部位</td>
            <td colspan="50">{{ rwdextraObj?.fjzbw | isNull }}</td>
            <td colspan="10">配比编号</td>
            <td colspan="30">{{ rwdextraObj?.fphbNo | isNull }}</td>
            </tr>

            <tr height="44">
                <td rowspan="2" colspan="4">基<br />本<br />要<br />求</td>
                <td colspan="10">强度等级</td>
                <td colspan="10">{{ mixProportion?.proportionQddj | isNull }}</td>
                <td colspan="10">抗渗等级</td>
                <td colspan="10">{{ rwdextraObj?.ksdj | isNull }}</td>
                <td colspan="10">坍落度（mm）</td>
                <td colspan="10">{{ rwdextraObj?.ftld | isNull }}</td>
                <td colspan="10">报告日期</td>
                <td colspan="10">{{ getReportTimeStr(ksInfo, kyInfo) }}</td>
                <td colspan="10">鉴定日期</td>
                <td colspan="10">{{ getTimeStr(erpRwdextra?.appraisalDate || '') }}</td>
            </tr>
            <tr height="44">
                <td colspan="10">水胶比（W/B）</td>
                <td colspan="10">{{ mixProportion?.printSjb | isNull }}</td>
                <td colspan="10">砂率（%）</td>
                <td colspan="10">{{ mixProportion?.printSl | isNull }}</td>
                <td colspan="10">含水率（%）</td>
                <td colspan="10">{{ cglHsl }}</td>
                <td colspan="10">{{ xglHsl }}</td>
                <td colspan="10">任务单号 </td>
                <td colspan="20">{{ openAppraisal?.rwdextraPrintInfo?.rwdextra?.frwno | isNull }}</td>
            </tr>
            <tr height="44">
                <td rowspan="7" colspan="4">配<br />合<br />比</td>
                <td colspan="10">原材料名称</td>
                <template v-for="(item, index) in cailiaoNameList">
                    <td v-if="index != cailiaoNameList.length - 1" :colspan="nameColspanWidth" :key="item">{{ getShowString(getYongLiangInt(dtoList[index]?.mpfyl, dtoList[index]?.wlmc), item) }}</td>
                    <td v-else :colspan="lastColspanWidth" :key="item">{{ getShowString(getYongLiangInt(dtoList[index]?.mpfyl, dtoList[index]?.wlmc), item) }}</td>

                </template>
            </tr>
            <!-- 下面字段需要重新取值 -->
            <tr height="44">
                <td colspan="10">原材料规格</td>
                <template v-for="(obj, idx) in dtoList">
                    <td v-if="idx != dtoList.length-1 || dtoList.length == 1 " :colspan="nameColspanWidth" :key="idx" >
                        {{getShowString(getYongLiangInt(obj?.mpfyl, obj?.wlmc), getWlGGStr(obj)) }}
                    </td>
                    <td  v-else :colspan="lastColspanWidth" :key="obj">
                        {{getShowString(getYongLiangInt(obj?.mpfyl, obj?.wlmc), getWlGGStr(obj)) }}
                    </td>
                </template>
            </tr>
            <!-- 新增原材料厂家 -->
            <tr height="44">
                <td colspan="10">原材料厂家</td>
                <template v-for="(obj, idx) in dtoList">
                    <td v-if="idx != cailiaoNameList.length-1 || cailiaoNameList.length == 1 " :colspan="nameColspanWidth" :key="idx" >
                        <!-- {{getFactoryStr(obj)}} -->
                        {{getShowString(getYongLiangInt(obj?.mpfyl, obj?.wlmc), getFactoryStr(obj)) }}
                    </td>
                    <td  v-else :colspan="lastColspanWidth" :key="obj">
                        <!-- {{getFactoryStr(obj)}} -->
                        {{getShowString(getYongLiangInt(obj?.mpfyl, obj?.wlmc), getFactoryStr(obj)) }}
                    </td>
                </template>
            </tr>
            <tr height="44">
                <td colspan="10">每m³设计用量（kg）</td>

                <template v-for="(obj, idx) in dtoList">
                    <td v-if="idx != dtoList.length-1 || dtoList.length == 1 " :colspan="nameColspanWidth" :key="idx" >
                        {{getYongLiangInt(obj?.mpfyl, obj?.wlmc)}}
                        <!-- {{getShowString(getYongLiangInt(obj?.mpfyl, obj?.wlmc), getYongLiangInt(obj?.mpfyl, obj?.wlmc)) }} -->
                    </td>
                    <td  v-else :colspan="lastColspanWidth" :key="obj">
                        {{getYongLiangInt(obj?.mpfyl, obj?.wlmc)}}
                        <!-- {{getShowString(getYongLiangInt(obj?.mpfyl, obj?.wlmc), getYongLiangInt(obj?.mpfyl, obj?.wlmc)) }} -->
                    </td>
                </template>
            </tr>
            <tr height="44">
                <td colspan="10">生产每m³用量（kg）</td>
                <template v-for="(obj, idx) in dtoList">
                    <td v-if="idx != dtoList.length-1 || dtoList.length == 1 " :colspan="nameColspanWidth" :key="idx" >
                         <!-- {{getYongLiangInt(obj?.tzhmpfyl, obj?.wlmc)}} -->
                        {{getShowString(getYongLiangInt(obj?.mpfyl, obj?.wlmc), getYongLiangInt(obj?.tzhmpfyl, obj?.wlmc)) }}
                    </td>
                    <td  v-else :colspan="lastColspanWidth" :key="obj">
                         <!-- {{getYongLiangInt(obj?.tzhmpfyl, obj?.wlmc)}} -->
                        {{getShowString(getYongLiangInt(obj?.mpfyl, obj?.wlmc), getYongLiangInt(obj?.tzhmpfyl, obj?.wlmc)) }}
                    </td>
                </template>
            </tr>
            <tr height="44">
                <td colspan="10">调整后每m³用量（kg）</td>
                <template v-for="(obj, idx) in dtoList">
                    <td v-if="idx != dtoList.length-1 || dtoList.length == 1 " :colspan="nameColspanWidth" :key="idx" >
                        <!-- {{getMeiFangShiJi(obj, !obj?.wlmc?.includes('外'))}} -->
                        {{getShowString(getYongLiangInt(obj?.mpfyl, obj?.wlmc), getMeiFangShiJi(obj, !obj?.wlmc?.includes('外'))) }}
                    </td>
                    <td  v-else :colspan="lastColspanWidth" :key="obj">
                        <!-- {{getMeiFangShiJi(obj, !obj?.wlmc?.includes('外'))}} -->
                        {{getShowString(getYongLiangInt(obj?.mpfyl, obj?.wlmc), getMeiFangShiJi(obj, !obj?.wlmc?.includes('外'))) }}
                    </td>
                </template>
            </tr>
            <tr height="44">
                <td colspan="10">原材料报告编号</td>

                <template v-for="(obj, idx) in dtoList">
                    <td v-if="idx != dtoList.length-1 || dtoList.length == 1 " :colspan="nameColspanWidth" :key="idx" >
                        <!-- {{obj?.xhbgbh | isNull}} -->
                        {{getShowString(getYongLiangInt(obj?.mpfyl, obj?.wlmc), obj?.xhbgbh) }}
                    </td>
                    <td  v-else :colspan="lastColspanWidth" :key="obj">
                        <!-- {{obj?.xhbgbh | isNull}} -->
                        {{getShowString(getYongLiangInt(obj?.mpfyl, obj?.wlmc), obj?.xhbgbh) }}
                    </td>
                </template>
                <!-- <td :colspan="nameColspanWidth" style="font-size: 13px;">{{ phbWater?.xhbgbh | isNull }}</td>
                <td :colspan="nameColspanWidth" style="font-size: 13px;">{{ phbSn?.xhbgbh | isNull }}</td>
                <td :colspan="nameColspanWidth" style="font-size: 13px;">{{ phbKzf?.xhbgbh | isNull }}</td>
                <td :colspan="nameColspanWidth" style="font-size: 13px;">{{ phbFmh?.xhbgbh | isNull }}</td>
                <td :colspan="nameColspanWidth" style="font-size: 13px;">{{ phbXgl?.xhbgbh | isNull }}</td>
                <td :colspan="nameColspanWidth" style="font-size: 13px;">{{ phbCgl?.xhbgbh | isNull }}</td>
                <td :colspan="nameColspanWidth" style="font-size: 13px;">{{ phbWjj1?.xhbgbh | isNull }}</td>
                <td :colspan="nameColspanWidth" style="font-size: 13px;">{{ phbWcl1?.xhbgbh | isNull }}</td>
                <td :colspan="lastColspanWidth" style="font-size: 13px;">{{ phbWcl2?.xhbgbh | isNull }}</td> -->
            </tr>
            <tr height="50">
                <td rowspan="7" colspan="4">鉴<br />定<br />结<br />果</td>
                <td colspan="10">原材料</td>

                <template v-for="(item, index) in cailiaoNameList">
                    <td  v-if="index != cailiaoNameList.length - 1" :colspan="nameColspanWidth" style="position: relative; align-items: flex-start; " :key="item">
                    <span class="flex-row" style="justify-content: center;">
                        <div style="display: flex; margin-top: -2px; width: 14px; border: 1px solid black;">
                            <i style="font-size: 12px;" class="el-icon-check"></i>
                        </div>
                        &nbsp;
                        <span style="margin-left: 2px;font-size: 15px">{{ '符合' }}&nbsp;&nbsp;&nbsp;</span>
                    </span>
                    <span class="flex-row" style="justify-content: center;">
                        <div style="display: flex; margin-top: 0px; width: 14px; height: 14px; border: 1px solid black;">
                            <i style="font-size: 12px;">&nbsp;</i>
                        </div>
                        &nbsp;
                        <span style="font-size: 15px">{{ '不符合' }}</span>
                    </span>
                </td>
                <td v-else :colspan="lastColspanWidth" :key="item"><span class="flex-row" style="justify-content: center;">
                        <div style="display: flex; margin-top: -2px; width: 14px; border: 1px solid black;">
                            <i style="font-size: 12px;" class="el-icon-check"></i>
                        </div>
                        &nbsp;
                        <span style="margin-left: 2px;font-size: 15px">{{ '符合' }}&nbsp;&nbsp;&nbsp;</span>
                    </span>
                    <span class="flex-row" style="justify-content: center;">
                        <div style="display: flex; margin-top: 0px; width: 14px; height: 14px; border: 1px solid black;">
                            <i style="font-size: 12px;">&nbsp;</i>
                        </div>
                        &nbsp;
                        <span style="font-size: 15px">{{ '不符合' }}</span>
                    </span>
                </td>

                </template>
                <!-- <td colspan="10" style="position: relative; align-items: flex-start;">
                    <span class="flex-row" style="justify-content: center;">
                        <div style="display: flex; margin-top: -2px; width: 14px; border: 1px solid black;">
                            <i style="font-size: 12px;" class="el-icon-check"></i>
                        </div>
                        &nbsp;
                        <span style="margin-left: 2px;font-size: 15px">{{ '符合' }}&nbsp;&nbsp;&nbsp;</span>
                    </span>
                    <span class="flex-row" style="justify-content: center;">
                        <div style="display: flex; margin-top: 0px; width: 14px; height: 14px; border: 1px solid black;">
                            <i style="font-size: 12px;">&nbsp;</i>
                        </div>
                        &nbsp;
                        <span style="font-size: 15px">{{ '不符合' }}</span>
                    </span>
                </td>
                <td colspan="10" style="position: relative;">
                    <span class="flex-row" style="justify-content: center;">
                        <div style="display: flex; margin-top: -2px; width: 14px; border: 1px solid black;">
                            <i style="font-size: 12px;" class="el-icon-check"></i>
                        </div>
                        &nbsp;
                        <span style="margin-left: 2px;font-size: 15px">{{ '符合' }}&nbsp;&nbsp;&nbsp;</span>
                    </span>
                    <span class="flex-row" style="justify-content: center;">
                        <div style="display: flex; width: 14px; margin-top: 0px; height: 14px; border: 1px solid black;">
                            <i style="font-size: 12px;">&nbsp;</i>
                        </div>
                        &nbsp;
                        <span style="font-size: 15px">{{ '不符合' }}</span>
                    </span>
                </td>
                <td colspan="10" style="position: relative;">
                    <span class="flex-row" style="justify-content: center;">
                        <div style="display: flex; margin-top: -2px; width: 14px; border: 1px solid black;">
                            <i style="font-size: 12px;" class="el-icon-check"></i>
                        </div>
                        &nbsp;
                        <span style="margin-left: 2px;font-size: 15px">{{ '符合' }}&nbsp;&nbsp;&nbsp;</span>
                    </span>
                    <span class="flex-row" style="justify-content: center;">
                        <div style="display: flex; margin-top: 0px; width: 14px; height: 14px; border: 1px solid black;">
                            <i style="font-size: 12px;">&nbsp;</i>
                        </div>
                        &nbsp;
                        <span style="font-size: 15px">{{ '不符合' }}</span>
                    </span>
                </td>
                <td colspan="10" style="position: relative;">
                    <span class="flex-row" style="justify-content: center;">
                        <div style="display: flex; margin-top: -2px; width: 14px; border: 1px solid black;">
                            <i style="font-size: 12px;" class="el-icon-check"></i>
                        </div>
                        &nbsp;
                        <span style="margin-left: 2px;font-size: 15px">{{ '符合' }}&nbsp;&nbsp;&nbsp;</span>
                    </span>
                    <span class="flex-row" style="justify-content: center;">
                        <div style="display: flex; width: 14px; margin-top: 0px; height: 14px; border: 1px solid black;">
                            <i style="font-size: 12px;">&nbsp;</i>
                        </div>
                        &nbsp;
                        <span style="font-size: 15px" >{{ '不符合' }}</span>
                    </span>
                </td>
                <td colspan="10" style="position: relative;">
                    <span class="flex-row" style="justify-content: center;">
                        <div style="display: flex; margin-top: -2px; width: 14px; border: 1px solid black;">
                            <i style="font-size: 12px;" class="el-icon-check"></i>
                        </div>
                        &nbsp;
                        <span style="margin-left: 2px;font-size: 15px">{{ '符合' }}&nbsp;&nbsp;&nbsp;</span>
                    </span>
                    <span class="flex-row" style="justify-content: center;">
                        <div style="display: flex; width: 14px; margin-top: 0px; height: 14px; border: 1px solid black;">
                            <i style="font-size: 12px;">&nbsp;</i>
                        </div>
                        &nbsp;
                        <span style="font-size: 15px" >{{ '不符合' }}</span>
                    </span>
                </td>
                <td colspan="10" style="position: relative;">
                    <span class="flex-row" style="justify-content: center;">
                        <div style="display: flex; margin-top: -2px; width: 14px; border: 1px solid black;">
                            <i style="font-size: 12px;" class="el-icon-check"></i>
                        </div>
                        &nbsp;
                        <span style="margin-left: 2px;font-size: 15px">{{ '符合' }}&nbsp;&nbsp;&nbsp;</span>
                    </span>
                    <span class="flex-row" style="justify-content: center;">
                        <div style="display: flex; width: 14px; margin-top: 0px; height: 14px; border: 1px solid black;">
                            <i style="font-size: 12px;">&nbsp;</i>
                        </div>
                        &nbsp;
                        <span style="font-size: 15px" >{{ '不符合' }}</span>
                    </span>
                </td>
                <td colspan="10" style="position: relative;">
                    <span class="flex-row" style="justify-content: center;">
                        <div style="display: flex; margin-top: -2px; width: 14px; border: 1px solid black;">
                            <i style="font-size: 12px;" class="el-icon-check"></i>
                        </div>
                        &nbsp;
                        <span style="margin-left: 2px;font-size: 15px">{{ '符合' }}&nbsp;&nbsp;&nbsp;</span>
                    </span>
                    <span class="flex-row" style="justify-content: center;">
                        <div style="display: flex; width: 14px; margin-top: 0px; height: 14px; border: 1px solid black;">
                            <i style="font-size: 12px;">&nbsp;</i>
                        </div>
                        &nbsp;
                        <span style="font-size: 15px" >{{ '不符合' }}</span>
                    </span>
                </td>
                <td colspan="10" style="position: relative;">
                    <span class="flex-row" style="justify-content: center;">
                        <div style="display: flex; margin-top: -2px; width: 14px; border: 1px solid black;">
                            <i style="font-size: 12px;" class="el-icon-check"></i>
                        </div>
                        &nbsp;
                        <span style="margin-left: 2px;font-size: 15px">{{ '符合' }}&nbsp;&nbsp;&nbsp;</span>
                    </span>
                    <span class="flex-row" style="justify-content: center;">
                        <div style="display: flex; width: 14px; margin-top: 0px; height: 14px; border: 1px solid black;">
                            <i style="font-size: 12px;">&nbsp;</i>
                        </div>
                        &nbsp;
                        <span style="font-size: 15px" >{{ '不符合' }}</span>
                    </span>
                </td>
                <td colspan="10" style="position: relative;">
                    <span class="flex-row" style="justify-content: center;">
                        <div style="display: flex; margin-top: -2px; width: 14px; border: 1px solid black;">
                            <i style="font-size: 12px;" class="el-icon-check"></i>
                        </div>
                        &nbsp;
                        <span style="margin-left: 2px;font-size: 15px">{{ '符合' }}&nbsp;&nbsp;&nbsp;</span>
                    </span>
                    <span class="flex-row" style="justify-content: center;">
                        <div style="display: flex; width: 14px; margin-top: 0px; height: 14px; border: 1px solid black;">
                            <i style="font-size: 12px;">&nbsp;</i>
                        </div>
                        &nbsp;
                        <span style="font-size: 15px" >{{ '不符合' }}</span>
                    </span>
                </td> -->
            </tr>
            <tr height="44">
                <td colspan="10">配合比</td>
                <td colspan="30">
                    <div class="flex-row" style="justify-content: space-around;">
                        <span class="flex-row" style="justify-content: center;">
                        <div style="display: flex; margin-top: -2px; width: 14px; border: 1px solid black;">
                            <i style="font-size: 12px;" class="el-icon-check"></i>
                        </div>
                        &nbsp;
                            <span style="margin-left: 2px;font-size: 15px">{{ '符合' }}&nbsp;&nbsp;&nbsp;</span>
                        </span>
                    <span class="flex-row" style="justify-content: center;">
                        <div style="display: flex; margin-top: -2px; width: 14px; height: 14px; border: 1px solid black;">
                            <i style="font-size: 12px;">&nbsp;</i>
                        </div>
                        &nbsp;
                        <span style="font-size: 15px" >{{ '不符合' }}</span>
                    </span>
                    </div>
                </td>
                <td colspan="10">施工配合比</td>
                <td colspan="50">
                    <div class="flex-row" style="justify-content: space-around;">
                        <span class="flex-row" style="justify-content: center;">
                            <!-- <i v-if="phbhistoryList && phbhistoryList?.length > 0" style="font-size: 20px;" class="el-icon-check"></i> -->
                        <div style="display: flex; margin-top: -2px; width: 14px; border: 1px solid black;">
                            <i style="font-size: 12px;" class="el-icon-check"></i>
                        </div>
                        &nbsp;
                            <span style="font-size: 15px">{{ '经调整' }}</span>
                        </span>
                        <span class="flex-row" style="justify-content: center;">
                            <!-- <i v-if="phbhistoryList == null || phbhistoryList?.length == 0" style="font-size: 20px;" class="el-icon-check"></i> -->
                            
                    <span class="flex-row" style="justify-content: center;">
                        <div style="display: flex; margin-top: -2px; width: 14px; height: 14px; border: 1px solid black;">
                            <i style="font-size: 12px;">&nbsp;</i>
                        </div>
                        &nbsp;
                        <span style="font-size: 15px" >{{ '未经调整' }}</span>
                    </span>
                        </span>
                    </div>
                </td>
            </tr>
            <tr height="44">
                <td colspan="30">搅拌机型号：强制式搅拌机</td>
                <td colspan="20">计量方式：电子秤</td>
                <td colspan="50">运输方式：搅拌车</td>
            </tr>
            <tr height="44">
                <td colspan="10">混凝土性能</td>
                <td colspan="10">坍落度（mm）</td>
                <td colspan="10">粘聚性</td>
                <td colspan="10">保水性</td>
                <td colspan="30">混凝土强度</td>
                <td colspan="10">抗渗</td>
                <td colspan="10">抗折</td>
                <td colspan="10">氯离子含量</td>
            </tr>
            <tr height="44">
                <td colspan="10">设计</td>
                <td colspan="10">{{ openAppraisalMixProportion?.proportionTld?.qz?.replace('mm', '') |isNull }} ± {{ openAppraisalMixProportion?.proportionTld?.hz?.replace('mm', '')  |isNull }}</td>
                <td colspan="10">{{ openAppraisalMixProportion?.proportionZjx | isNull}}</td>
                <td colspan="10">{{ openAppraisalMixProportion?.proportionBsx | isNull }}</td>
                <!-- 混泥土强度 -->
                <td colspan="10">试件编号</td>
                <td colspan="10">fcu28(Mpa)</td>
                <td colspan="10">达到设计强度(%)</td>
                <!-- 抗渗编号 -->
                <td colspan="10">{{rwdextraPrintInfo?.kstzypbh | isNull}}</td>
                <!-- （抗折编号） -->
                <td colspan="10">{{rwdextraPrintInfo?.kztzypbh | isNull}}</td>
                <!-- 氯离子含量 -->
                <td colspan="10">{{rwdextraPrintInfo?.llzhlypbh | isNull}}</td>
            </tr>
            <tr height="44">
                <td colspan="10">实测</td>
                <td colspan="10">{{ rwdextraPrintInfo?.sctld | isNull }}</td>
                <td colspan="10">{{ rwdextraPrintInfo?.sczjx | isNull }}</td>
                <td colspan="10">{{ rwdextraPrintInfo?.scbsx | isNull }}</td>
                <!-- 混泥土强度 -->
                <td colspan="10">{{hntObjInfo?.ypbh | isNull}}</td>
                <td colspan="10">{{getShowResult(hntObjInfo?.zsbzqd, true)}}</td>
                <td colspan="10">{{getShowResult(hntObjInfo?.ddsjqd, false)}}</td>
                <!-- 抗渗结论 -->
                <td colspan="10">{{getKangShenJieLun(rwdextraPrintInfo?.kstzjl) }}</td>
                <!-- 抗折结论 -->
                <td colspan="10">{{rwdextraPrintInfo?.kztzjl | isNull}}</td>
                <!-- 氯离子 -->
                <td colspan="10">{{rwdextraPrintInfo?.llzhljl | isNull}}</td>
            </tr>
            <tr height="44">
                <td colspan="10">鉴定意见</td>
                <td colspan="90" style="text-align: left;">&nbsp;&nbsp;&nbsp;&nbsp;该砼符合设计要求</td>
            </tr>
        </table>



        <table class="lab-sign" style="border: none; font-size: 16px; table-layout:fixed;">
            <tr height="60" style="border: none;">
            <td style="border: none;">
                <div class="sign-user">
                批准：
                </div>
            </td>
            <td style="border: none;">
                <div class="sign-user">
                复核：
                </div></td>
            <td style="border: none;">
                <div class="sign-user">
                鉴定：
                </div></td>
            </tr>
        </table>
        </div>

    </div>
</template>

<script>
import { hntTaskMixed } from './hntTaskMixed';
import {calcEquation, cpEvenRound, div, mul} from "@/utils/calculate.js"

export default {
    mixins: [hntTaskMixed],
    props: {
        companyName: {
            type: String,
            default: {}
        },
        openAppraisal: {
            type: Object,
            default: {}
        },
        companyInfo:{
            type: Object,
            default:{}
        },
        ksInfo:{
            type: Object,
            default:{}
        },
        kpkyInfo:{
            type: Object,
            default:{}
        },
        xnbgInfo:{
            type: Object,
            default:{}
        },
        rwdextraPrintInfo:{
            type: Object,
            default:{}

        }
        
    },
    data() {
        return {
            tldObj: {},
            zjxObj: {},
            bsxObj: {},
            kyqdObj: {},
            ksObj: {},
            llzObj: {},
            phbSn: {},
            phbWater: {},
            phbKzf: {},
            phbFmh: {},
            phbXgl: {},
            phbCgl: {},
            phbWjj1: {},
            phbWjj2: {},
            phbWcl1: {},
            phbWcl2: {},
            openAppraisalMixProportion:{},
            hntObjInfo:{},
            afterPhbDtoList:[],
            cailiaoNameList:[],
            dtoList:[],
            nameColspanWidth:10,
            lastColspanWidth:10,
            factoryList:[],
            cglHsl:'',
            xglHsl:'',
            kyInfo:{},

        }
    },
    methods:{

    getShowString(check, str){
        console.log('check:',check);
        console.log('getShowString:',str);
        if(check == '' || check == '0' || !check || check == '---' || str == '' || !str) {
            return '---';
        }
        return str;

    },

        showYearNoStr(no, creatTime){
            console.log('creatTime：', creatTime);
            let timeList = creatTime?.split('-');
            let year = '';
            if(timeList && timeList.length > 0){
                year = timeList[0].slice(-2);
                console.log('year：', year);
            }
            if(this.isEmpty(no)) return '---';
            return year + no;

        },
        getShortName(obj, config){
            let cjjc = obj?.cjjc;
            let cj = obj?.cj;
            let gysmcjc = obj?.gysjc;
            let gysmc = obj?.gys;
            var showName = cjjc;
            var result = '---';

            if(config == 1){
                if(this.isEmpty(cjjc) && !this.isEmpty(cj)){
                    showName = cj
                } 
            } else {
                if(!this.isEmpty(gysmcjc)){
                    showName = gysmcjc
                } else {
                    showName = gysmc
                }
            }
            result = this.isEmpty(showName) ? '---' : showName;
            return result;
        },

        getFactoryStr(obj){
            // 1-厂家 2-供应商
            const snConfig = this.companyInfo?.snConfig || 1;
            const fmhConfig = this.companyInfo?.fmhConfig || 1;
            const kzfConfig = this.companyInfo?.kzfConfig || 1;
            const xglConfig = this.companyInfo?.xglConfig || 1;
            const cglConfig = this.companyInfo?.cglConfig || 1;
            const wjjConfig = this.companyInfo?.wjjConfig || 1;
            const wcl1Config = this.companyInfo?.wcl1Config || 1;
            const wcl2Config = this.companyInfo?.wcl2Config || 1;
            
            if(obj.newName == '水'){
                return '---';
            }
            if(obj.newName == '水泥'){
                return this.getShortName(obj, snConfig);
                // return snConfig == 2 ? this.phbSn?.gys : this.phbSn?.cj;
            }
            if(obj.newName == '粉煤灰'){
                return this.getShortName(obj, fmhConfig);
            }
            if(obj.newName == '矿渣粉'){
                return this.getShortName(obj, kzfConfig);
                // return kzfConfig == 2 ? this.phbKzf?.gys : this.phbKzf?.cj;
            }
            if(obj.newName == '石'){
                console.log('----this.phbCgl:', this.phbCgl);
                console.log('----cglConfig:', cglConfig);
                console.log('----this.companyInfo:', this.companyInfo);
                return this.getShortName(obj, cglConfig);
                // return cglConfig == 2 ? this.phbCgl?.gys : this.phbCgl?.cj;
            }
            if(obj.newName == '砂'){
                return this.getShortName(obj, xglConfig);
                // return xglConfig == 2 ? this.phbXgl?.gys : this.phbXgl?.cj;
            }
            if(obj.newName == '外加剂'){
                return this.getShortName(obj, wjjConfig);
                // return wjjConfig == 2 ? this.phbWjj1?.gys : this.phbWjj1?.cj;
            }
            if(obj.newName == '外掺料1'){
                return this.getShortName(obj, wcl1Config);
                // return wcl1Config == 2 ? this.phbWcl1?.gys : this.phbWcl1?.cj;
            }
            if(obj.newName == '外掺料2'){
                return this.getShortName(obj, wcl2Config);
                // return wcl2Config == 2 ? this.phbWcl2?.gys : this.phbWcl2?.cj;
            }
            return '---';

        },
        
        getWlGGStr(obj){
            console.log('原材料厂家：', obj);
            var result = '---';
            let name = obj?.wlmc || '';
            let clmc = obj?.clmc || '';
            // 外掺料1
            if(clmc.includes('膨胀剂')){
                result = obj?.wlmc  + obj?.wlgg;
                // 外掺料2
            }else if(clmc.includes('纤维')){
                result = obj?.clgg;
            }else if(name.includes('外加剂') || name.includes('水泥')){
                result = obj?.wlgg ;
            } else if(name.includes('石') || name.includes('砂')){
                var resp = obj?.clmc + obj?.clgg;
                result = name.includes('石') ? resp.replace('mm', '') : resp;
            } else {
                result = obj?.clgg;
            
            }
            if(this.isEmpty(result)) result = '---'
            return result;


        },

        getShowResult(str, isPjz){
            var result = this.isEmpty(str) ? '---' : str;
            let index = 0;
            if(isPjz && str == '0.00'){
                index = 1;
                result = '---';
            }
            if(!isPjz && str == '0'){
                result = '---'
            }
            return cpEvenRound(result, isPjz ? 1 : 0);

        },
        getReportTimeStr(ksObj, kyObj){
            let ksReportData = ksObj?.experimentInfo?.reportDate || '';
            let kyReportData = kyObj?.experimentInfo?.reportDate || '';
            if(!this.isEmpty(ksReportData)){
                return this.getTimeStr(ksReportData);
            }
            if(!this.isEmpty(kyReportData)){
                return this.getTimeStr(kyReportData);
            }
            return '---';
        },

        getTimeStr(time){
            if(!this.isEmpty(time)){
                return time.length > 10 ? time?.substring(0,10) : time
            }
            return '---';
        },
        getResultStr(objJson){
            var result = '/'
            var hh = objJson?.hh || '0';
            var yb = objJson?.yb || '0';
            var fcc = objJson?.fcc || '0';
            if(hh == '1'){
                result = '良好'
            } else if(yb == '1'){
                result = '一般'
            } else if(fcc == '1'){
                result = '非常差'
            }

            return result;

        },
        getKangShenJieLun(value){
            if(this.isEmpty(value) || value == '不合格') return '---';
            return value;
        },

        isEmpty(val) {
            if (typeof val === "boolean") {
                return false;
            }
            if (typeof val === "number") {
                return false;
            }
            if (val instanceof Array) {
                if (val.length === 0) return true;
            } else if (val instanceof Object) {
                if (JSON.stringify(val) === "{}" || val == null ) return true;
            } else {
                if (
                val === "null" ||
                val == null ||
                val === "undefined" ||
                val === undefined ||
                val === ""
                )
                return true;
                return false;
            }
            return false;
        },
        getYongLiangInt(str, name=''){
            if(this.isEmpty(str)) return '---'
            if(name.includes('外')) return str;
            return cpEvenRound(str, 0);
        },
        getMeiFangShiJi(obj, intVale, index){
            var result = '---';
            let name = obj?.cllx || '';
            let clgg = obj?.clgg || '';
            // 水的调整用量 = 水的生产用量 - (其他材料的含水量之和)
            // 其他材料的含水量=生产用量乘以含水率
            if(clgg.includes('水')){
                var totalZl = 0;
                var  shuiList = [];
                this.afterPhbDtoList.forEach((item, idx)=>{
                    let cllx = item?.cllx || '';
                    if(cllx === '水泥' || !cllx.includes('水')){
                        // totalZl = totalZl + parseFloat(item?.tzhmpfyl || '0') * parseFloat(item?.hsl || '0') / 100
                        // 这行代码和上面的其实是一样的，只是下面代码精度更高
                        totalZl = totalZl + mul(parseFloat(item?.tzhmpfyl || '0'), div(parseFloat(item?.hsl || '0'), 100))
                        // if(cllx === '水1') {
                        //     shui1 = item?.tzhmpfyl || '0'
                        // }
                    } else {
                        // shui = item?.tzhmpfyl || '0'
                        var obj = {
                            index: idx,
                            shui: item?.tzhmpfyl || '0'
                        }
                        shuiList.push(obj);
                    }
                });
                

                console.log('*********************PHB:', obj.mpfyl,'&&&&&&&&&', totalZl);
                result = calcEquation([
                    {
                        v: obj.tzhmpfyl,
                    },
                    {
                        k: '-',
                        v: totalZl,
                    },
                    ], 0);
                    // name === '水1'
                if(name !== '水泥' && name.includes('水')){
                    var shuiObj = shuiList.find(item => item.index === index);
                    const shui1 = shuiObj?.shui || '0';
                    const shui = shuiList
                        .filter(item => item.index < index)
                        .reduce((total, item) => total + Number(item.shui), 0);
                    console.log('结果数据-----：', shui, '------', shui1, '------', totalZl);
                    console.log('水数组：', shuiList);
                    console.log('当前坐标水：', shuiObj);
                    // parseFloat(shui) <= 0 ? result = 0 : result = parseFloat(shui) + parseFloat(shui1);
                    result = parseFloat(shui) < parseFloat(totalZl) ?  parseFloat(shui) + parseFloat(shui1) - parseFloat(totalZl) : parseFloat(shui1);
                    if(parseFloat(result) <= 0) {
                        result = 0
                    }
                    console.log('计算后的数据-------：', result);
                    if(parseFloat(result) === 0) {
                        result = '---';
                    } else {
                        result = Math.round(result).toString();

                    }
                }


            }
            // if(clgg == '清水'){
            //     var totalZl = 0;
            //     let shui = '';
            //     let shui1 = '';
            //     this.dtoList.forEach((item)=>{
            //         let cllx = item?.cllx || '';
            //         if(cllx !== '水'){
            //             // totalZl = totalZl + parseFloat(item?.tzhmpfyl || '0') * parseFloat(item?.hsl || '0') / 100
            //             // 这行代码和上面的其实是一样的，只是下面代码精度更高
            //             totalZl = totalZl + mul(parseFloat(item?.tzhmpfyl || '0'), div(parseFloat(item?.hsl || '0'), 100))
            //             if(cllx === '水1') {
            //             shui1 = item?.tzhmpfyl || '0'
            //         }
            //         } else {
            //             shui = item?.tzhmpfyl || '0'
            //         }
            //     });
            //     console.log('*********************PHB:', obj.tzhmpfyl,'&&&&&&&&&', totalZl);
            //     result = calcEquation([
            //         {
            //             v: obj.tzhmpfyl,
            //         },
            //         {
            //             k: '-',
            //             v: totalZl,
            //         },
            //         ], 0);
            //     if(name === '水1'){
            //         console.log('结果数据：', result,'-------', shui, '------', shui1);
            //         result = parseFloat(shui) < totalZl ?  result + parseFloat(shui1) : parseFloat(shui1)
            //     }

            // }
            /*if(clgg == '清水'){
                var totalZl = 0;
                this.dtoList.forEach((item)=>{

                    let cllx = item?.cllx || '';
                    if(cllx !== '水'){
                        console.log('*********************KPJD对象:', obj.hsl,'材料规格', item?.hsl);
                        totalZl = totalZl + parseFloat(item?.tzhmpfyl || '0') * parseFloat(item?.hsl || '0') / 100
                    }
                });
                console.log('*********************KPJD:', obj.tzhmpfyl,'&&&&&&&&&', totalZl);
                result = calcEquation([
                    {
                        v: obj.tzhmpfyl,
                    },
                    {
                        k: '-',
                        v: totalZl,
                    },
                    ], 0);

            } */else if(name.includes('外加剂')){
                let hsl = obj?.hsl || 0;
                let tzhmpfyl = obj?.tzhmpfyl || '';
                result = hsl == 0 ? tzhmpfyl : parseFloat(tzhmpfyl) * (1+parseFloat(hsl) / 100) ;
            } else {
                let hsl = obj?.hsl || 0;
                let tzhmpfyl = obj?.tzhmpfyl || '';
                let secondNum = 1+parseFloat(hsl || 0) / 100;
                result = hsl == 0 ? tzhmpfyl : calcEquation([
                    {
                        v: tzhmpfyl,
                    },
                    {
                        k: '*',
                        v: secondNum,
                    },
                    ], 0);
            }
            
            let resultNum = this.isEmpty(result) ? '---' : result;
            if(resultNum != '---' && intVale){
                resultNum = this.getYongLiangInt(result);
            }
            return resultNum;

        },
        getKSDJResult(val1, val2){
            if(this.isEmpty(val2) || this.isEmpty(val1)) {
                return '---';
            }
            let list = val2.split("|");
            var checkVal = ''
            if(list.length > 4){
                checkVal = list[3];
            }
            if((val1.includes('P') || val1.includes('p')) && checkVal.includes('P') || checkVal.includes('p')){
                let ksdj1 = val1.slice(1);
                let ksdj2 = checkVal.slice(1);
                return parseInt(ksdj1) >= parseInt(ksdj2) ? '合格' : '不合格'
                
            }
            return '---';

        }
    },
    mounted() {
        this.kyInfo = {};
        if(this.kyInfoList?.length > 0){
            this.kyInfo = this.kyInfoList[0];
        }
        var dtoList = this.openAppraisal?.phbhistory?.afterPhbDtoList || [];
        this.dtoList = dtoList;
        console.log('配合比dtoList：', dtoList);
        if(dtoList.length > 0){
            dtoList.forEach((item, index)=>{
                var name = item?.cllx || '';
                var clmc = item?.clmc || ''
                if(name == '粗骨料'){
                    name = '石';
                    let hsl = !item?.hsl ? '---' : cpEvenRound(item?.hsl, 1);
                    if(hsl !== '---'){
                        this.cglHsl = this.cglHsl + '/' + hsl;
                    } else {
                        this.cglHsl = '---'
                    }
                }
                if(name == '细骨料'){
                    name = '砂';
                    let hsl = !item?.hsl ? '---' : cpEvenRound(item?.hsl, 1);
                    if(hsl !== '---'){
                        this.xglHsl = this.xglHsl + '/' + hsl;
                    } else {
                        this.xglHsl = '---'
                    }
                    
                }
                if(clmc.includes('纤维')){
                    name = '外掺料2'
                }

                if(clmc.includes('膨胀剂')){
                    name = '外掺料1'
                }
                item.newName = name || '---';
                this.cailiaoNameList.push(name || '---');
            });
            this.cglHsl = this.cglHsl.slice(1)
            this.xglHsl = this.xglHsl.slice(1)
        } else {
            this.cailiaoNameList = ['水', '水泥', '粉煤灰', '矿渣粉', '石', '砂', '外加剂', '外参料1'];
            this.dtoList = ['', '', '', '', '', '', '', ''];
        }


        this.nameColspanWidth = Math.floor(90 / this.cailiaoNameList.length);
        this.lastColspanWidth = 90 - this.nameColspanWidth * (this.cailiaoNameList.length - 1)
        this.openAppraisalMixProportion = this.rwdextraPrintInfo?.mixProportionInfo || {}
        if (this.openAppraisalMixProportion && this.openAppraisalMixProportion.proportionMaterial) {
            let proportionMaterial = this.openAppraisalMixProportion.proportionMaterial || {};

            this.phbSn = proportionMaterial.sn || {};
            this.phbWater = proportionMaterial.water || {};
            this.phbKzf = proportionMaterial.kzf || {};
            this.phbFmh = proportionMaterial.fmh || {};
            this.phbXgl = proportionMaterial.xgl || {};
            this.phbCgl = proportionMaterial.cgl || {};
            this.phbWjj1 = proportionMaterial.wjj1 || {};
            this.phbWjj2 = proportionMaterial.wjj2 || {};
            this.phbWcl1 = proportionMaterial.wcl1 || {};
            this.phbWcl2 = proportionMaterial.wcl2 || {};

            this.afterPhbDtoList.push(this.phbSn)
            this.afterPhbDtoList.push(this.phbWater)
            this.afterPhbDtoList.push(this.phbKzf)
            this.afterPhbDtoList.push(this.phbFmh)
            this.afterPhbDtoList.push(this.phbXgl)
            this.afterPhbDtoList.push(this.phbCgl)
            this.afterPhbDtoList.push(this.phbWjj1)
            this.afterPhbDtoList.push(this.phbWjj2)
            this.afterPhbDtoList.push(this.phbWcl1)
            this.afterPhbDtoList.push(this.phbWcl2)
        }

        if(this.rwdextraPrintInfo && this.rwdextraPrintInfo.kyInfoList && this.rwdextraPrintInfo.kyInfoList.length > 0){
            this.hntObjInfo = this.rwdextraPrintInfo.kyInfoList[0]

        } else {
            this.hntObjInfo = {}

        }
        try {
            let list = this.ksInfo?.experimentDetailList || [];
            if (list) {
                list.map(item => {
                    let testProjectCode = item?.testProjectCode || ''
                     // 抗渗
                     if(testProjectCode == 'CONCRETE_PARAM_KSDJ'){
                        this.ksObj = item?.objJson || {}
                    }
                    
                });
            }
        
        } catch (error) {
            this.ksObj = {};
        }
        try {
            let list = this.xnbgInfo?.experimentDetailList || [];
            if (list) {
                list.map(item => {
                    let testProjectCode = item?.testProjectCode || ''
                     // 抗渗
                     if(testProjectCode == 'CONCRETE_PARAM_XNBG_LLZHL'){
                        this.llzObj = item?.objJson || {}
                    }
                    
                });
            }
        
        } catch (error) {
            this.llzObj = {};
        }
        try {
            let list = this.kpkyInfo?.experimentDetailList || [];
            if (list) {
                list.map(item => {
                    let testProjectCode = item?.testProjectCode || ''
                    // 坍落度
                    if(testProjectCode == 'CONCRETE_PARAM_MCTLD'){
                        this.tldObj = item?.objJson || {}
                    }
                    // 黏聚性
                    if(testProjectCode == 'CONCRETE_PARAM_ZJX'){
                        this.zjxObj = item?.objJson || {}
                    }
                    // 保水性
                    if(testProjectCode == 'CONCRETE_PARAM_BSX'){
                        this.bsxObj = item?.objJson || {}
                    }
                    // 抗压强度
                    if(testProjectCode == 'CONCRETE_PARAM_KYQD'){
                        this.kyqdObj = item?.objJson || {}
                    }
                     // 抗渗
                     if(testProjectCode == 'CONCRETE_PARAM_KSDJ'){
                        this.ksObj = item?.objJson || {}
                    }
                    
                });
            }
        
        } catch (error) {
            this.tldObj = {};
            this.zjxObj = {};
            this.bsxObj = {};
            this.ksObj = {};
        }
    },
}
</script>

<style lang="scss" scoped>
@import '../../print.css';
.border {
    border: 3px solid black; /* 设置边框粗细 */
}

.border th, .border td{
    border: 2px solid black; /* 设置边框粗细 */
}
</style>