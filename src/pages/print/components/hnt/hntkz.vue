<!--
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-05-22 00:03:51
 * @LastEditors: huaze.fan <EMAIL>
 * @LastEditTime: 2025-03-21 15:38:58
 * @FilePath: /quality_center_web/src/pages/print/components/hntks.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div class="portrait">
        <div class="tip-view">
            <div class="tip-label" style="margin-top: 40px;">C-14b-0903</div>
        </div>
      <div class="company-label" style="font-size: 30px; font-weight:700;">{{companyName}}</div>
      <div class="title-label" style="font-size: 46px; line-height: 60px;">混凝土抗折强度检测报告</div>
      <div align="center" class="lab-report">

      <div class="lab-sub-title">
        <div class="lab-sub-title-container" style="margin-top: 80px; margin-left: 140px;">第1页{{'&nbsp&nbsp&nbsp&nbsp'}}共1页</div>
        <div class="lab-sub-title-container">
          <div class="lab-sample-id" style=" display: flex; margin-top: 35px;"><span style="width: 140px;">生产任务单号:</span><span>{{ rwdextraObj?.frwno | isNull }} </span></div>
          <div class="lab-report-id" style="margin-top: 10px; display: flex;"><span style="width: 140px;">报告编号:</span><span>{{ experimentInfo?.reportNo | isNull }} </span></div>
        </div>
      </div>

      <table  border="1"  class="border" style="margin-top: 10px; font-size: 16px; table-layout: fixed;">
          <tr height="40">
            <td colspan="3">施工单位</td>
            <td colspan="21" left>{{ erpRwdextra?.buildNameNew | isNull }}</td>
          </tr>
          <tr height="40">
            <td colspan="3">工程名称</td>
            <td colspan="21" left>{{ erpRwdextra?.projectNameNew | isNull }}</td>
          </tr>
          <tr height="40">
            <td colspan="3">工程部位</td>
            <td colspan="21" left>{{ rwdextraObj?.fjzbw | isNull }}</td>
          </tr>
          <tr height="40">
            <td colspan="3">强度等级</td> 
            <td colspan="5" left>{{ experimentInfo?.materialsSpecs | isNull }}</td>
            <td colspan="3">生产方量</td>
            <td colspan="5" left>{{ erpRwdextra?.scquantity }}</td>
            <td colspan="3">稠度要求</td>
            <td colspan="5" left>({{ rwdextraObj?.ftld | isNull  }})mm</td>
          </tr>
          <tr height="40">
            <td colspan="3">配合比编号</td>
            <td colspan="13" left>{{ rwdextraObj?.fphbNo | isNull }}</td>
            <td colspan="3">报告日期</td>
            <td colspan="5" left>{{ experimentInfo?.reportDate | momentDate }}</td>
          </tr>
        </table>

        <table  border="1"  class="border" style="font-size: 16px; table-layout: fixed;">
          <tr height="40">
            <td colspan="3">样品编号</td>
            <td colspan="5" left>{{ experimentInfo?.sampleId | shxhNull  }}</td>
            <td colspan="3">设计抗折强度</td>
            <td colspan="5" left>{{ getResultStr() }}</td>
            <td colspan="3">样品规格</td>
            <td colspan="5" left style="font-size: 15px;">{{ getSJCC(kzInfoObj?.sjcc) }}</td>
          </tr>
          <tr height="40">
            <td colspan="3">工程部位</td>
            <td colspan="13" left>{{ rwdextraObj?.fjzbw | isNull }}</td>
            <td colspan="3">稠度/mm</td>
            <td colspan="5" left>{{ '/' }}</td>
          </tr>
          <tr height="40">
            <td colspan="3">成型日期</td>
            <td colspan="3">检测日期</td>
            <td colspan="2">龄期/d</td>
            <td colspan="2">养护</br>条件</td>
            <td colspan="2">支座间距离/mm</td>
            <td colspan="2">破坏荷载/kN</td>
            <td colspan="2">抗折强度/MPa</td>
            <td colspan="3">平均强度/MPa</td>
            <td colspan="3">折合标准试块强度/MPa</td>
            <td colspan="2">达到强度设计/%</td>
          </tr>
          <tr height="40">
            <td colspan="3">{{ experimentInfo?.moldingTime | momentDate }}</td>
            <td colspan="3">{{ kzInfoObj?.jcrq | momentDate }}</td>
            <td colspan="2">{{ kzInfoObj?.lq | isNull }}</td>
            <td colspan="2">{{ experimentInfo?.curingMode | isNull }}</td>
            <!-- 支座间距离/mm -->
            <td colspan="2">{{ getZhijiaSpace() }}</td>
            <td colspan="2">
              <div style="line-height: 30px; width: 100%; border-bottom: black solid 1px;">{{ getKangZhe28Dinfo(0, 'phhz') }}</div>
              <div style="line-height: 30px; width: 100%; border-bottom: black solid 1px;">{{ getKangZhe28Dinfo(1, 'phhz') }}</div>
              <div style="line-height: 30px; width: 100%">{{ getKangZhe28Dinfo(2, 'phhz') }}</div>
            </td>
            <td colspan="2">
              <div style="line-height: 30px; width: 100%; border-bottom: black solid 1px;">{{ getKangZhe28Dinfo(0, 'kzqd') }}</div>
              <div style="line-height: 30px; width: 100%; border-bottom: black solid 1px;">{{ getKangZhe28Dinfo(1, 'kzqd') }}</div>
              <div style="line-height: 30px; width: 100%;">{{ getKangZhe28Dinfo(2, 'kzqd') }}</div>
            </td>
            <td colspan="3">{{ kzInfoObj?.pjz | isNull }}</td>
            <td colspan="3">{{ kzInfoObj?.zsbzqd | isNull }}</td>
            <td colspan="2">{{ getIntNum(kzInfoObj?.ddsjqd) }}</td>
          </tr>
        </table>

        <table  border="1"  class="border" style="font-size: 16px; table-layout: fixed;">
          <tr height="40">
            <td colspan="3">样品编号</td>
            <td colspan="5" left>{{ '/' }}</td>
            <td colspan="3">设计抗折强度</td>
            <td colspan="5" left>{{ '/' }}</td>
            <td colspan="3">样品规格</td>
            <td colspan="5" left>{{ '/' }}</td>
          </tr>
          <tr height="40">
            <td colspan="3">工程部位</td>
            <td colspan="13" left>{{ '/' }}</td>
            <td colspan="3">稠度/mm</td>
            <td colspan="5" left>{{ '/' }}</td>
          </tr>
          <tr height="40">
            <td colspan="3">成期日期</td>
            <td colspan="3">检测日期</td>
            <td colspan="2">龄期/d</td>
            <td colspan="2">养护</br>条件</td>
            <td colspan="2">支座间距离/mm</td>
            <td colspan="2">破坏荷载/kN</td>
            <td colspan="2">抗折强度/MPa</td>
            <td colspan="3">平均强度/MPa</td>
            <td colspan="3">折合标准试块强度/MPa</td>
            <td colspan="2">达到强度设计/%</td>
          </tr>
          <tr height="40">
            <td colspan="3">{{ '/' }}</td>
            <td colspan="3">{{ '/' }}</td>
            <td colspan="2">{{ '/' }}</td>
            <td colspan="2">{{ '/' }}</td>
            <td colspan="2">{{ '/' }}</td>
            <td colspan="2">
              <div style="line-height: 30px; width: 100%; border-bottom: black solid 1px;">{{ '/' }}</div>
              <div style="line-height: 30px; width: 100%; border-bottom: black solid 1px;">{{ '/' }}</div>
              <div style="line-height: 30px; width: 100%">{{ '/' }}</div>
            </td>
            <td colspan="2">
              <div style="line-height: 30px; width: 100%; border-bottom: black solid 1px;">{{ '/' }}</div>
              <div style="line-height: 30px; width: 100%; border-bottom: black solid 1px;">{{ '/' }}</div>
              <div style="line-height: 30px; width: 100%;">{{ '/' }}</div>
            </td>
            <td colspan="3">{{ '/' }}</td>
            <td colspan="3">{{ '/' }}</td>
            <td colspan="2">{{ '/' }}</td>
          </tr>
        </table>

        
        <table  border="1"  class="border" style="font-size: 16px; table-layout: fixed;">
          <tr height="40">
            <td colspan="3">样品编号</td>
            <td colspan="5" left>{{ '/' }}</td>
            <td colspan="3">设计抗折强度</td>
            <td colspan="5" left>{{ '/' }}</td>
            <td colspan="3">样品规格</td>
            <td colspan="5" left>{{ '/' }}</td>
          </tr>
          <tr height="40">
            <td colspan="3">工程部位</td>
            <td colspan="13" left>{{ '/' }}</td>
            <td colspan="3">稠度/mm</td>
            <td colspan="5" left>{{ '/' }}</td>
          </tr>
          <tr height="40">
            <td colspan="3">成期日期</td>
            <td colspan="3">检测日期</td>
            <td colspan="2">龄期/d</td>
            <td colspan="2">养护</br>条件</td>
            <td colspan="2">支座间距离/mm</td>
            <td colspan="2">破坏荷载/kN</td>
            <td colspan="2">抗折强度/MPa</td>
            <td colspan="3">平均强度/MPa</td>
            <td colspan="3">折合标准试块强度/MPa</td>
            <td colspan="2">达到强度设计/%</td>
          </tr>
          <tr height="40">
            <td colspan="3">{{ '/' }}</td>
            <td colspan="3">{{ '/' }}</td>
            <td colspan="2">{{ '/' }}</td>
            <td colspan="2">{{ '/' }}</td>
            <td colspan="2">{{ '/' }}</td>
            <td colspan="2">
              <div style="line-height: 30px; width: 100%; border-bottom: black solid 1px;">{{ '/' }}</div>
              <div style="line-height: 30px; width: 100%; border-bottom: black solid 1px;">{{ '/' }}</div>
              <div style="line-height: 30px; width: 100%">{{ '/' }}</div>
            </td>
            <td colspan="2">
              <div style="line-height: 30px; width: 100%; border-bottom: black solid 1px;">{{ '/' }}</div>
              <div style="line-height: 30px; width: 100%; border-bottom: black solid 1px;">{{ '/' }}</div>
              <div style="line-height: 30px; width: 100%;">{{ '/' }}</div>
            </td>
            <td colspan="3">{{ '/' }}</td>
            <td colspan="3">{{ '/' }}</td>
            <td colspan="2">{{ '/' }}</td>
          </tr>
        </table>

        <table  border="1"  class="border" style="font-size: 16px; table-layout: fixed;">
          <tr height="40">
            <td colspan="3">检测方法</td>
            <td colspan="11" left>{{ experimentInfo?.experimentGist | isNull }}</td>
            <td colspan="3">评定依据</td>
            <td colspan="7" left>{{ experimentInfo?.judgeGist | isNull }}</td>
          </tr>
          <tr height="40">
            <td colspan="3">备注</td>
            <td colspan="21" left>{{ experimentInfo?.remark | isNull }}</td>
          </tr>
        </table>

        <table class="lab-sign" style="border: none;">
          <tr height="40" style="border: none;">
            <td style="border: none;"><div class="sign-user">检测报告专用章：</div></td>
            <td style="border: none;">
              <div class="sign-user">
                批准：
                <!-- <img
                v-if="true"
                src=""
                width="150" height="50" /> -->
              </div>
            </td>
            <td style="border: none;">
              <div class="sign-user">
                审核：
                <!-- <img
                v-if="true"
                src=""
                width="150" height="50" /> -->
              </div>
            </td>
            <td style="border: none;">
              <div class="sign-user">
                检测：
                <!-- <img
                v-if="true"
                src=""
                width="150" height="50" /> -->
              </div>
            </td>
          </tr>
        </table>
      </div>
    </div>
</template>

<script>
import { hntMixed } from './hntMixed';
import {cpEvenRound} from "@/utils/calculate.js"
export default {
    mixins: [hntMixed],
    props: {
        companyName: {
            type: String,
            default: ""
        },
        kzInfo: {
            type: Object,
            default:{}
        }
    },
    data(){
      return {
        kzInfoObj:{},
      }
    },
    mounted() {
      if(this.kzInfo?.experimentDetailList?.length > 0){
        this.kzInfo?.experimentDetailList.map(item => {
          if (item.testProjectCode === 'CONCRETE_PARAM_KZQD') {
              // 抗折强度
              this.kzInfoObj = item.objJson || {};
          }
        });
          
     } 

  },
  methods: {
    getKangZhe28Dinfo(index, key){
      var kz28d = this.kzInfoObj?.kz28d;
      if (kz28d?.length > index) {
        return kz28d[index][key];
      }
      return '/';
    },

    getZhijiaSpace(){
      // let sjcc = this.kzInfoObj?.sjcc || '';
      // let juli = '/';
      // let parts = sjcc.split('*');
      // console.log('计算间距数组：', parts);
      // if (parts?.length > 0) {
      //   juli = parts[parts.length - 1];
      // }
      // let res = juli.replace('㎜', '');
      // return res;
      let kz28d = this.kzInfoObj?.kz28d || [];
      if (kz28d.length == 0) return '---';
      
      let obj = kz28d[0];
      let zj = obj?.zj || '';
      if(zj == '') return '---';
      return this.getIntNum(zj);
      

        
      

    },
    getSJCC(sjcc){
          if(!sjcc) return '/';
          return sjcc.replace(/\*/g, "×")
        },

        getIntNum(str){
            if(this.isEmpty(str)) return '---'
            return cpEvenRound(str, 0);
        },

      isEmpty(val) {
            if (typeof val === "boolean") {
                return false;
            }
            if (typeof val === "number") {
                return false;
            }
            if (val instanceof Array) {
                if (val.length === 0) return true;
            } else if (val instanceof Object) {
                if (JSON.stringify(val) === "{}" || val == null ) return true;
            } else {
                if (
                val === "null" ||
                val == null ||
                val === "undefined" ||
                val === undefined ||
                val === ""
                )
                return true;
                return false;
            }
            return false;
        },

    getResultStr(){
      let conversionFactor = this.experimentInfo?.conversionFactor || '';
      let lowerCaseStr = conversionFactor.toLowerCase();
      return lowerCaseStr.replace('z', '') + 'Mpa';
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../../print.css';

@import '../../print.css';
.border {
    border: 3px solid black; /* 设置边框粗细 */
}
.border th, .border td{
    border: 2px solid black; /* 设置边框粗细 */
}
</style>