<!--
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-05-22 00:03:51
 * @LastEditors: huaze.fan <EMAIL>
 * @LastEditTime: 2025-03-21 15:38:43
 * @FilePath: /quality_center_web/src/pages/print/components/hntks.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div class="portrait">
        <!-- <div class="tip-view" :style="{marginTop: pageNum>1 ? '80px': '0px'}">
            <div class="tip-label">C-06b-0903</div>
        </div> -->
        <!-- <div class="company-label" style="font-size: 30px; font-weight:700;">{{companyName}}</div>
        <div class="title-label" style="font-size: 46px; line-height: 60px;">混凝土抗压强度检测报告</div> -->
        <div align="center" class="lab-report">

        <!-- <div class="lab-sub-title" style="align-items: end;">
          <div class="lab-sub-title-container" style="margin-bottom: 0px; margin-left: 140px;"></div>
                <div class="lab-sub-title-container">
                  <div class="lab-sample-id" style="margin-top: 40px; display: flex;"><span style="width: 140px;">生产任务单号:</span><span>{{ erpRwdextra?.rwdextraExtend?.produceIndexId | isNull }} </span></div>
                  <div class="lab-report-id" style="margin-top: 15px; display: flex;"><span style="width: 140px;">报告编号:</span><span>{{ kyInfoObj?.reportNo | isNull }} </span></div>
            </div>
        </div> -->
        <!-- <table  border="1"  class="border" style="margin-top: 15px; font-size: 16px; table-layout: fixed;">
          <tr height="38">
            <td colspan="5">施工单位</td>
            <td colspan="33" left>{{ erpRwdextra?.buildNameNew | isNull }}</td>
          </tr>
          <tr height="38">
            <td colspan="5">工程名称</td>
            <td colspan="33" left>{{ erpRwdextra?.projectNameNew | isNull }}</td>
          </tr>
          <tr height="38">
            <td colspan="5">工程部位</td>
            <td colspan="33" left>{{ rwdextraObj?.fjzbw | isNull }}</td>
          </tr>
          <tr height="38">
            <td colspan="5">强度等级</td>
            <td colspan="6" left>{{ kyInfoObj?.materialsSpecs | isNull }}</td>
            <td colspan="4">生产方量</td>
            <td colspan="13">{{ erpRwdextra?.scquantity | isNull }}</td>
            <td colspan="4">稠度要求</td>
            <td colspan="6" left>{{ getChouDuYaoQiu(rwdextraObj?.ftld) }}</td>
          </tr>
          <tr height="38">
            <td colspan="5">配合比编号</td>
            <td colspan="23" left>{{ rwdextraObj?.fphbNo | isNull }}</td>
            <td colspan="4">报告日期</td>
            <td colspan="6" left>{{ getReportDate(kyInfoObj?.reportDate) }}</td>
          </tr>
        </table> -->

        <!-- 第一页数据 -->
        <template v-for="(item, idx) in pageDataList">
            <!-- 分割线条 -->
            <table v-if="idx != 0" :key="idx">
                <tr :height="'40px'"></tr>
            </table>

            <div class="tip-view" :style="{marginTop:  '0px'}">
            <div class="tip-label">C-06b-0903</div>
        </div>
        <div class="company-label" style="font-size: 30px; font-weight:700;">{{companyName}}</div>
        <div class="title-label" style="font-size: 46px; line-height: 60px;">混凝土抗压强度检测报告</div>
        <div class="lab-sub-title" style="align-items: end;">
          <div class="lab-sub-title-container" style="margin-bottom: 0px; margin-left: 140px;"></div>
                <div class="lab-sub-title-container">
                  <div class="lab-sample-id" style="margin-top: 40px; display: flex;"><span style="width: 140px;">生产任务单号:</span><span>{{ erpRwdextra?.rwdextraExtend?.produceIndexId | isNull }} </span></div>
                  <div class="lab-report-id" style="margin-top: 15px; display: flex;"><span style="width: 140px;">报告编号:</span><span>{{ kyInfoObj?.reportNo | isNull }} </span></div>
            </div>
        </div>
          <div :style="{ marginLeft: '120px', 'width': '100%', marginTop:'-20px', 'textAlign': 'left'}">第{{idx + 1}}页 共{{pageDataList.length}}页</div>
        <table  border="1"  class="border" style="margin-top: 15px; font-size: 16px; table-layout: fixed;">
          <tr height="38">
            <td colspan="5">施工单位</td>
            <td colspan="33" left>{{ erpRwdextra?.buildNameNew | isNull }}</td>
          </tr>
          <tr height="38">
            <td colspan="5">工程名称</td>
            <td colspan="33" left>{{ erpRwdextra?.projectNameNew | isNull }}</td>
          </tr>
          <tr height="38">
            <td colspan="5">工程部位</td>
            <td colspan="33" left>{{ rwdextraObj?.fjzbw | isNull }}</td>
          </tr>
          <tr height="38">
            <td colspan="5">强度等级</td>
            <td colspan="6" left>{{ kyInfoObj?.materialsSpecs | isNull }}</td>
            <td colspan="4">生产方量</td>
            <td colspan="13">{{ erpRwdextra?.scquantity | isNull }}</td>
            <td colspan="4">稠度要求</td>
            <td colspan="6" left>{{ getChouDuYaoQiu(rwdextraObj?.ftld) }}</td>
          </tr>
          <tr height="38">
            <td colspan="5">配合比编号</td>
            <td colspan="23" left>{{ rwdextraObj?.fphbNo | isNull }}</td>
            <td colspan="4">报告日期</td>
            <td colspan="6" left>{{ getReportDate(kyInfoObj?.reportDate) }}</td>
          </tr>
        </table>
          <table border="1" class="border" style="font-size: 16px; table-layout: fixed;" :key="idx">
            <template v-for="(kyqdInfo, index) in item">
              <tr height="38" :key="index+'tableList'">
                <td colspan="5">样品编号</td>
                <td colspan="6" left>{{ experimentPageInfoList[idx][index]?.sampleId | shxhNull }}</td>
                <td colspan="4">样品规格</td>  
                <td colspan="14" left>{{ getSJCC(kyqdInfo?.sjcc) }}</td>
                <!-- <td colspan="14" left>{{ experimentPageInfoList[idx][index]?.materialsSpecs | isNull }}</td> -->
                <td colspan="4">稠度/mm</td>
                <td colspan="6" left>{{ '---' }}</td>
              </tr>
              <tr height="38">
                <td colspan="5">成型日期</td>
                <td colspan="6" left>{{ experimentPageInfoList[idx][index]?.moldingTime | momentDate }}</td>
                <td colspan="4">龄期/d</td>
                <td colspan="4">{{ kyqdInfo?.lq | isNull }}</td>
                <td colspan="4" >检测日期</td>
                <td colspan="6" left>{{ experimentPageInfoList[idx][index]?.jcrq | momentDate }}</td>
                <td colspan="4" width="30">代表数量</td>
                <td colspan="6" width="30" left>{{ getDaiBiaoShuLiang(experimentPageInfoList[idx][index]?.behalfNumber) }}m³</td>
              </tr>
              <tr height="38">
                <td colspan="5">养护条件</td>
                <td colspan="6" nowrap="nowrap">破坏荷载/kN</td>
                <td colspan="6" nowrap="nowrap">抗压强度/MPa</td>
                <td colspan="6" nowrap="nowrap">强度代表值/MPa</td>
                <td colspan="10" nowrap="nowrap">折合标准试块强度/MPa</td>
                <td colspan="6" nowrap="nowrap">达到设计强度/%</td>
              </tr>
              <tr height="38">
                <td colspan="5" rowspan="3">标准养护</td>
                <!-- 破坏荷载 -->
                <td colspan="6">{{getResultStr(kyqdInfo?.qd28d[0]?.hz, 1)}}</td>
                <!-- 抗压强度 -->
                <td colspan="6">{{getResultStr(kyqdInfo?.qd28d[0]?.qd, 1)}}</td>
                <!-- 强度代表值 -->
                <td colspan="6" rowspan="3">{{getResultStr(kyqdInfo?.pjz28d , 1)}}</td>
                <!-- 折合标准试块强度 zhbzskqd -->
                <td colspan="10" rowspan="3">{{getResultStr( kyqdInfo?.zsbzqd28d, 1)}}</td>
                <!-- 达到设计强度 -->
                <td colspan="6" rowspan="3">{{getResultStr( kyqdInfo?.fyxs28d, 0)}}</td>
              </tr>
              <tr height="38">
                <td colspan="6">{{getResultStr(kyqdInfo?.qd28d[1]?.hz, 1)}}</td>
                <td colspan="6">{{getResultStr(kyqdInfo?.qd28d[1]?.qd, 1)}}</td>
              </tr>
              <tr height="38">
                <td colspan="6">{{getResultStr(kyqdInfo?.qd28d[2]?.hz, 1)}}</td>
                <td colspan="6">{{getResultStr(kyqdInfo?.qd28d[2]?.qd, 1)}}</td>
              </tr>
              <tr height="38">
                <td colspan="5">备注</td>
                <td colspan="34" left>{{ kyInfoObj?.remark || '' }}</td>
              </tr>
              <!-- 分割线 -->
              <tr v-if="index != item?.length-1" height="15">
              </tr>
            </template>
        </table>
        <table border="1" class="border" style="font-size: 16px; table-layout: fixed;">
          <tr height="38">
            <td colspan="5">检测方法</td>
            <td colspan="20" center>{{ kyInfoObj?.experimentGist | isNull }}</td>
            <td colspan="5">评定依据</td>
            <td colspan="8" center>{{ kyInfoObj?.judgeGist | isNull }}</td>
          </tr>
          <tr height="70">
            <td colspan="5">备注</td>
            <td colspan="33" left>{{ '' }}</td>
          </tr>
        </table>

        <table class="lab-sign" style="border: none;">
          <tr height="38" style="border: none;">
            <td style="border: none;"><div class="sign-user">检测机构专用章：</div></td>
            <td style="border: none;">
              <div class="sign-user">
                批准：
              </div>
            </td>
            <td style="border: none;">
              <div class="sign-user">
                审核：
              </div>
            </td>
            <td style="border: none;">
              <div class="sign-user">
                检测：
              </div>
            </td>
          </tr>
        </table>
        <!-- <div v-if="idx != pageDataList.length-1" style="margin-top: 10px; font-size: 14px; font-weight: 400; text-align: right">共{{pageNum}}页 当前第{{idx+1}}页</div> -->
        </template>
        

        <!-- <table border="1" class="border" style="font-size: 16px; table-layout: fixed;">
          <tr height="38">
            <td colspan="5">检测方法</td>
            <td colspan="20" center>{{ kyInfoObj?.experimentGist | isNull }}</td>
            <td colspan="5">评定依据</td>
            <td colspan="8" center>{{ kyInfoObj?.judgeGist | isNull }}</td>
          </tr>
          <tr :height="pageNum==1 ? 70 : 100">
            <td colspan="5">备注</td>
            <td colspan="33" left>{{ '' }}</td>
          </tr>
        </table>

        <table class="lab-sign" style="border: none;">
          <tr height="38" style="border: none;">
            <td style="border: none;"><div class="sign-user">检测机构专用章：</div></td>
            <td style="border: none;">
              <div class="sign-user">
                批准：
              </div>
            </td>
            <td style="border: none;">
              <div class="sign-user">
                审核：
              </div>
            </td>
            <td style="border: none;">
              <div class="sign-user">
                检测：
              </div>
            </td>
          </tr>
        </table> -->
        <!-- <div style="margin-top: 0px; font-size: 14px; font-weight: 400; text-align: right">共{{pageNum}}页 当前第{{pageNum}}页</div> -->


        
      </div>
    </div>
</template>

<script>
import { hntMixed } from './hntMixed';
import {cpEvenRound} from "@/utils/calculate.js"
import {calcEquation} from "@/utils/calculate.js"
export default {
    mixins: [hntMixed],

    props: {
        companyName: {
            type: String,
            default: ""
        },
        subItem: {
            type: Object,
            default: {}
        }
    },

    filters: {
      zhbzskqd: function(value) {
        let arr = [
          {
            v: value.pjz28d,
          },{
            k: '*',
            v: value.zsxs28d,
          }
        ]
        let res = calcEquation(arr, 2)
        return res;
      },
    },
    data(){
      return {
        pageNum:1,
        kyInfoList:[],
        pageDataList:[],
        kyInfoObj:{},
        experimentInfoList: [],
        experimentPageInfoList: [],
      }
    },

    mounted() {
      
        if(this.subItem?.kyInfoList?.length > 0){
          this.kyInfoObj = this.subItem?.kyInfoList[0].experimentInfo;
          console.log('任务单抗压数据：', this.kyInfoObj);
          this.subItem?.kyInfoList?.map(item => {
            item?.experimentDetailList?.map(obj => {
                if (obj.testProjectCode === 'CONCRETE_PARAM_KYQD') {
                    // 抗压强度
                    this.kyInfoList.push(obj.objJson || {});
                    this.experimentInfoList.push(item.experimentInfo || {});
                }
            })

            });
            if(this.kyInfoList.length <=3){
              this.pageNum = 1;
              while (this.kyInfoList.length < 3) {
                this.kyInfoList.push({"qd28d":[{},{},{}]});
                this.experimentInfoList.push({});
              }
              this.pageDataList.push(this.kyInfoList);
              this.experimentPageInfoList.push(this.experimentInfoList);
            } else {
              var listLength = this.kyInfoList.length - 3;
              let result = Math.floor(listLength / 3);
              if (listLength % 3!== 0) {
                result++;
              }
              var firstList = [];
              for(let j = 0; j < 3; j++){
                const obj = this.kyInfoList[j];
                firstList.push(obj);
              }
              var originalArray = this.kyInfoList.slice(3)
              var dataList = this.convertArray(originalArray, 3);
              this.pageDataList = [firstList, ...dataList];
              this.pageNum = result + 1;

              // 
              var listLength1 = this.experimentInfoList.length - 3;
              let result1 = Math.floor(listLength1 / 3);
              if (listLength1 % 3!== 0) {
                result1++;
              }
              var firstList1 = [];
              for(let j = 0; j < 3; j++){
                const obj1 = this.experimentInfoList[j];
                firstList1.push(obj1);
              }
              var originalArray1 = this.experimentInfoList.slice(3)
              var dataList1 = this.convertArray(originalArray1, 3);
              this.experimentPageInfoList = [firstList1, ...dataList1];

            }

            console.log('任务单抗压强度列表：',this.pageDataList);
            console.log('任务单抗压强度列表1：',this.experimentPageInfoList);
       } else {
        // 默认3条数据
        // Array.from({ length: 3 }).forEach(() => {
        //     this.kyInfoList.push({ "qd28d": [{}, {}, {}] });
        // });
        // this.pageDataList.push(this.kyInfoList);
        this.kyInfoObj = this.subItem?.experimentInfo || {};

        this.subItem?.experimentDetailList?.map(item => {
                if (item.testProjectCode === 'CONCRETE_PARAM_KYQD') {
                    // 抗压强度
                    this.kyInfoList.push(item.objJson || {});
                    this.experimentInfoList.push(item.experimentInfo || {});
                }
            });
            if(this.kyInfoList.length <=3){
              this.pageNum = 1;
              while (this.kyInfoList.length < 3) {
                this.kyInfoList.push({"qd28d":[{},{},{}]});
                this.experimentInfoList.push({});
              }
              this.pageDataList.push(this.kyInfoList);
              this.experimentPageInfoList.push(this.experimentInfoList);
            } else {
              var listLength = this.kyInfoList.length - 3;
              let result = Math.floor(listLength / 5);
              if (listLength % 5!== 0) {
                result++;
              }
              var firstList = [];
              for(let j = 0; j < 3; j++){
                const obj = this.kyInfoList[j];
                firstList.push(obj);
              }
              var originalArray = this.kyInfoList.slice(3)
              var dataList = this.convertArray(originalArray, 5);
              this.pageDataList = [firstList, ...dataList];
              this.pageNum = result + 1;


              // 
              var listLength1 = this.experimentInfoList.length - 3;
              let result1 = Math.floor(listLength1 / 5);
              if (listLength1 % 5!== 0) {
                result1++;
              }
              var firstList1 = [];
              for(let j = 0; j < 3; j++){
                const obj1 = this.experimentInfoList[j];
                firstList1.push(obj1);
              }
              var originalArray1 = this.experimentInfoList.slice(3)
              var dataList1 = this.convertArray(originalArray1, 5);
              this.experimentPageInfoList = [firstList1, ...dataList1];
            }
        console.log('台账抗压数据：', this.subItem);
        console.log('台账抗压强度列表：',this.pageDataList);
       }

    },
    methods:{

      getDaiBiaoShuLiang(str){
        if(!str) return '---';
        // .replace('t', 'm³')
        return str;

      },

      getChouDuYaoQiu(str){
            if(!str) return '---';
            try {
                var strList = str.split('±');
                return strList.length > 0 ? strList[0]+'mm' + '±' + strList[1]+'mm' : str;
            } catch (error) {
                return str;
            }
            
        },

        getSJCC(sjcc){
          if(!sjcc) return '---';
          return sjcc.replace(/(\d+)\*/g, '$1mm ‌× ').replace(/(\d+)$/, '$1mm');
        },
        getReportDate(time){
          if(!time) return '---';
          if(time.length > 10) {
            var date = time.substring(0, 10);
            return date.replace(/-/g, "/");
          }
          return time.replace(/-/g, "/");
        },

      getResultStr(value,index){
        if (value == undefined || value == null || value === '')  return '---';
        return cpEvenRound(value || '', index)
      },
      // 将数组 originalArray 装换成每个元素是长度是 subLength 的二位数组
      convertArray(originalArray, subLength) {
        const result = [];
        var flage = false;
          for (let i = 0; i < originalArray.length; i += subLength) {
            const subArray = originalArray.slice(i, i + subLength);
            // 补充不足4个元素的情况
            while (subArray.length < subLength) {
              flage = true
              subArray.push({"qd28d":[{},{},{}]});
            }
            result.push(subArray);
          }
          if(!flage && originalArray.length % subLength != 0){
            var list = [];
            for(let i = 0; i<subLength; i++){
              list.push({"qd28d":[{},{},{}]});
            }
            result.push(list);
          }
          return result;
        }
      
    }
}
</script>

<style lang="scss" scoped>
@import '../../print.css';
.border {
    border: 3px solid black; /* 设置边框粗细 */
}
.border th, .border td{
    border: 2px solid black; /* 设置边框粗细 */
}
</style>