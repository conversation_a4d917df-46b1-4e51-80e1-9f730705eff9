<!--
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-05-22 00:03:51
 * @LastEditors: huaze.fan <EMAIL>
 * @LastEditTime: 2025-03-21 15:38:29
 * @FilePath: /quality_center_web/src/pages/print/components/hntks.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div class="portrait">
        <div class="tip-view" style="margin-top: 30px;">
            <div class="tip-label">C-05b-0903</div>
        </div>
        <div class="company-label" style="font-size: 30px; margin-top: 20px; font-weight:700;">{{companyName}}</div>
        <div class="title-label" style="font-size: 46px; line-height: 60px;">混凝土抗渗性能检测报告</div>
        <div align="center" class="lab-report">

        <div class="lab-sub-title">
            <div class="lab-sub-title-container" style="margin-top: 80px; margin-left: 140px;">第 1 页  共 1 页</div>
                <div class="lab-sub-title-container">
          <div class="lab-sample-id" style=" display: flex; margin-top: 35px;"><span style="width: 140px;">生产任务单号:</span><span>{{ erpRwdextra?.rwdextraExtend?.produceIndexId | isNull }} </span></div>
          <div class="lab-report-id" style="margin-top: 10px; display: flex;"><span style="width: 140px;">报告编号:</span><span>{{ experimentInfo?.reportNo | isNull }} </span></div>
        
                  <!-- <div class="lab-sample-id">生产任务单号:<span>{{ rwdextraObj?.frwno | isNull }} </span></div>
                  <div class="lab-report-id" style="margin-top: 8px;">报告编号:<span>{{ experimentInfo?.reportNo | isNull }} </span></div> -->
            </div>
        </div>

        <table  border="1"  class="border" style="margin-top: 10px; font-size: 17px; table-layout: fixed;">
          <tr height="40">
            <td colspan="5">施工单位</td>
            <td colspan="31" left>{{ erpRwdextra?.buildNameNew | isNull }}</td>
          </tr>
          <tr height="40">
            <td colspan="5">工程名称</td>
            <td colspan="31" left>{{ erpRwdextra?.projectNameNew | isNull }}</td>
          </tr>
          <tr height="40">
            <td colspan="5">强度等级</td>
            <td colspan="7" left>{{ experimentInfo?.materialsSpecs | isNull }}</td>
            <td colspan="5">抗渗等级</td>
            <td colspan="7" left>{{ experimentInfo?.ksdj | isNull }}</td>
            <td colspan="5" width="30">生产方量</td>
            <td colspan="7" left>{{ erpRwdextra?.fhquantity }}</td>
          </tr>
          <tr height="40">
            <td colspan="5">稠度要求</td>
            <td colspan="7" left>{{ getChouDuYaoQiu(rwdextraObj?.ftld) }}</td>
            <td colspan="5">配合比编号</td>
            <td colspan="7" left>{{ rwdextraObj?.fphbNo | isNull }}</td>
            <td colspan="5">报告日期</td>
            <td colspan="7" left>{{ experimentInfo?.reportDate | momentDate }}</td>
          </tr>
        </table>
        <table  border="1" class="border" style="font-size: 17px; margin-top: 38px; table-layout: fixed;">
            <tr height="40">
            <td colspan="5">样品编号</td>
            <td colspan="7" left>{{ shxhSynchronizedata?.sampleId | shxhNull }}</td>
            <td colspan="5">成型日期</td>
            <td colspan="7" left>{{ experimentInfo?.moldingTime | momentDate }}</td>
            <td colspan="5">稠度/mm</td>
            <td colspan="7" left>{{ '---' }}</td>
            </tr>
            <tr height="40">
            <td colspan="5">检测日期</td>
            <td colspan="19" left>{{ getShowTimeString(ksdjInfo?.ksInfo || [])}}</td>
            <td colspan="5">龄期/d</td>
            <td colspan="7" left>{{ ksdjInfo?.lq | isNull }}</td>
            </tr>
            <tr height="40">
                <td colspan="5">养护条件</td>
                <td colspan="2">序号</td>
                <td colspan="4">结束水压<br />/MPa</td>
                <td colspan="4">结束水压下<br />持续时间</td>
                <td colspan="4">样品渗<br />透情况</td>
                <td colspan="2">序号</td>
                <td colspan="4">结束水压<br />MPa</td>
                <td colspan="4">结束水压下<br />持续时间</td>
                <td colspan="4">样品渗<br />透情况</td>
                <td colspan="3">检测结论</td>
            </tr>
            <tr height="40">
                <!-- 养护条件 -->
                <td colspan="5" rowspan="3">{{ experimentInfo?.curingMode | isNull }}</td>
                <td colspan="2">1</td>
                <!-- 核对到这里 -->
                <!-- 结束水压 -->
                <td colspan="4">{{ isArrayNull(ksdjInfo?.ksInfo, ksdjInfo?.ksInfo?.length-1)?.syyl | isNull }}</td>
                <!-- 结束水压下持续时间 -->
                <td colspan="4">{{ '8h' }}</td>
                <!-- 样品渗透情况 -->
                <td colspan="4">{{ getShenShuiStr(isArrayNull(ksdjInfo?.ksInfo, ksdjInfo?.ksInfo?.length-1)?.sfss1) }}</td>
                <td colspan="2">4</td>
                <!-- 结束水压 -->
                <td colspan="4">{{ isArrayNull(ksdjInfo?.ksInfo, ksdjInfo?.ksInfo?.length-1)?.syyl | isNull }}</td>
                <!-- 结束水压下持续时间 -->
                <td colspan="4">{{ '8h' }}</td>
                <!-- 样品渗透情况 -->
                <td colspan="4">{{ getShenShuiStr(isArrayNull(ksdjInfo?.ksInfo, ksdjInfo?.ksInfo?.length-1)?.sfss4) }}</td>
                <!-- 检测结论 -->
                <td colspan="3" rowspan="3">{{ getShowResultStr(experimentInfo?.conclusion)  }}</td>
            </tr>
            <tr height="40">
            <td colspan="2">2</td>
            <td colspan="4">{{ isArrayNull(ksdjInfo?.ksInfo, ksdjInfo?.ksInfo?.length-1)?.syyl | isNull }}</td>
            <td colspan="4">{{ '8h' }}</td>
            <td colspan="4">{{ getShenShuiStr(isArrayNull(ksdjInfo?.ksInfo, ksdjInfo?.ksInfo?.length-1)?.sfss2) }}</td>
            <td colspan="2">5</td>
            <td colspan="4">{{ isArrayNull(ksdjInfo?.ksInfo,  ksdjInfo?.ksInfo?.length-1)?.syyl | isNull }}</td>
            <td colspan="4">{{ '8h' }}</td>
            <td colspan="4">{{ getShenShuiStr(isArrayNull(ksdjInfo?.ksInfo, ksdjInfo?.ksInfo?.length-1)?.sfss5) }}</td>
            </tr>
            <tr height="40">
            <td colspan="2">3</td>
            <td colspan="4">{{ isArrayNull(ksdjInfo?.ksInfo, ksdjInfo?.ksInfo?.length-1)?.syyl | isNull }}</td>
            <td colspan="4">{{ '8h' }}</td>
            <td colspan="4">{{ getShenShuiStr(isArrayNull(ksdjInfo?.ksInfo, ksdjInfo?.ksInfo?.length-1)?.sfss3) }}</td>
            <td colspan="2">6</td>
            <td colspan="4">{{ isArrayNull(ksdjInfo?.ksInfo,  ksdjInfo?.ksInfo?.length-1)?.syyl | isNull  }}</td>
            <td colspan="4">{{ '8h' }}</td>
            <td colspan="4">{{ getShenShuiStr(isArrayNull(ksdjInfo?.ksInfo, ksdjInfo?.ksInfo?.length-1)?.sfss6) }}</td>
            </tr>
        </table>
        <!-- 下面是占位符 -->
        <table  border="1" class="border" style="font-size: 17px; margin-top: 38px; table-layout: fixed;">
            <tr height="40">
            <td colspan="5">样品编号</td>
            <td colspan="7" left>{{ '---' }}</td>
            <td colspan="5">成型日期</td>
            <td colspan="7" left>{{ '---' }}</td>
            <td colspan="5">稠度/mm</td>
            <td colspan="7" left>{{ '---' }}</td>
            </tr>
            <tr height="40">
            <td colspan="5">检测日期</td>
            <td colspan="19" left>{{ '---' }}</td>
            <td colspan="5">龄期/d</td>
            <td colspan="7" left>{{ '---' }}</td>
            </tr>
            <tr height="40">
                <td colspan="5">养护条件</td>
                <td colspan="2">序号</td>
                <td colspan="4">结束水压<br />/MPa</td>
                <td colspan="4">结束水压下<br />持续时间</td>
                <td colspan="4">样品渗<br />透情况</td>
                <td colspan="2">序号</td>
                <td colspan="4">结束水压<br />MPa</td>
                <td colspan="4">结束水压下<br />持续时间</td>
                <td colspan="4">样品渗<br />透情况</td>
                <td colspan="3">检测结论</td>
            </tr>
            <tr height="40">
                <!-- 养护条件 -->
                <td colspan="5" rowspan="3">{{ '---' }}</td>
                <td colspan="2">1</td>
                <!-- 核对到这里 -->
                <!-- 结束水压 -->
                <td colspan="4">{{ '---' }}</td>
                <!-- 结束水压下持续时间 -->
                <td colspan="4">{{ '---' }}</td>
                <!-- 样品渗透情况 -->
                <td colspan="4">{{ '---' }}</td>
                <td colspan="2">4</td>
                <!-- 结束水压 -->
                <td colspan="4">{{ '---' }}</td>
                <!-- 结束水压下持续时间 -->
                <td colspan="4">{{ '---' }}</td>
                <!-- 样品渗透情况 -->
                <td colspan="4">{{ '---' }}</td>
                <!-- 检测结论 -->
                <td colspan="3" rowspan="3">{{ '---' }}</td>
            </tr>
            <tr height="40">
            <td colspan="2">2</td>
            <td colspan="4">{{ '---' }}</td>
            <td colspan="4">{{ '---' }}</td>
            <td colspan="4">{{ '---' }}</td>
            <td colspan="2">5</td>
            <td colspan="4">{{ '---' }}</td>
            <td colspan="4">{{ '---' }}</td>
            <td colspan="4">{{ '---' }}</td>
            </tr>
            <tr height="40">
            <td colspan="2">3</td>
            <td colspan="4">{{ '---' }}</td>
            <td colspan="4">{{ '---' }}</td>
            <td colspan="4">{{ '---' }}</td>
            <td colspan="2">6</td>
            <td colspan="4">{{ '---'  }}</td>
            <td colspan="4">{{ '---' }}</td>
            <td colspan="4">{{ '---' }}</td>
            </tr>
        </table>

        <!-- 下面是占位符 -->
        <table  border="1" class="border" style="font-size: 17px; margin-top: 38px; table-layout: fixed;">
            <tr height="40">
            <td colspan="5">样品编号</td>
            <td colspan="7" left>{{ '---' }}</td>
            <td colspan="5">成型日期</td>
            <td colspan="7" left>{{ '---' }}</td>
            <td colspan="5">稠度/mm</td>
            <td colspan="7" left>{{ '---' }}</td>
            </tr>
            <tr height="40">
            <td colspan="5">检测日期</td>
            <td colspan="19" left>{{ '---' }}</td>
            <td colspan="5">龄期/d</td>
            <td colspan="7" left>{{ '---' }}</td>
            </tr>
            <tr height="40">
                <td colspan="5">养护条件</td>
                <td colspan="2">序号</td>
                <td colspan="4">结束水压<br />/MPa</td>
                <td colspan="4">结束水压下<br />持续时间</td>
                <td colspan="4">样品渗<br />透情况</td>
                <td colspan="2">序号</td>
                <td colspan="4">结束水压<br />MPa</td>
                <td colspan="4">结束水压下<br />持续时间</td>
                <td colspan="4">样品渗<br />透情况</td>
                <td colspan="3">检测结论</td>
            </tr>
            <tr height="40">
                <!-- 养护条件 -->
                <td colspan="5" rowspan="3">{{ '---' }}</td>
                <td colspan="2">1</td>
                <!-- 核对到这里 -->
                <!-- 结束水压 -->
                <td colspan="4">{{ '---' }}</td>
                <!-- 结束水压下持续时间 -->
                <td colspan="4">{{ '---' }}</td>
                <!-- 样品渗透情况 -->
                <td colspan="4">{{ '---' }}</td>
                <td colspan="2">4</td>
                <!-- 结束水压 -->
                <td colspan="4">{{ '---' }}</td>
                <!-- 结束水压下持续时间 -->
                <td colspan="4">{{ '---' }}</td>
                <!-- 样品渗透情况 -->
                <td colspan="4">{{ '---' }}</td>
                <!-- 检测结论 -->
                <td colspan="3" rowspan="3">{{ '---' }}</td>
            </tr>
            <tr height="40">
            <td colspan="2">2</td>
            <td colspan="4">{{ '---' }}</td>
            <td colspan="4">{{ '---' }}</td>
            <td colspan="4">{{ '---' }}</td>
            <td colspan="2">5</td>
            <td colspan="4">{{ '---' }}</td>
            <td colspan="4">{{ '---' }}</td>
            <td colspan="4">{{ '---' }}</td>
            </tr>
            <tr height="40">
            <td colspan="2">3</td>
            <td colspan="4">{{ '---' }}</td>
            <td colspan="4">{{ '---' }}</td>
            <td colspan="4">{{ '---' }}</td>
            <td colspan="2">6</td>
            <td colspan="4">{{ '---'  }}</td>
            <td colspan="4">{{ '---' }}</td>
            <td colspan="4">{{ '---' }}</td>
            </tr>
        </table>
        
        <table  border="1"  class="border" style="font-size: 17px; margin-top: 38px; table-layout: fixed;">
            <tr height="40">
            <td colspan="5">检测方法</td>
            <td colspan="18" left>{{ experimentInfo?.experimentGist | isNull }}</td>
            <td colspan="5">评定依据</td>
            <td colspan="8" left>{{ experimentInfo?.judgeGist | isNull }}</td>
            </tr>
            <tr height="100">
            <td colspan="5">备注</td>
            <td colspan="31" left>{{ experimentInfo?.remark | isNull }}</td>
            </tr>
        </table>

        <table class="lab-sign" style="border: none; margin-top: 10px">
            <tr height="40" style="border: none;">
            <td style="border: none;"><div class="sign-user">检测报告专用章：</div></td>
            <td style="border: none;">
                <div class="sign-user">
                批准：
                <!-- <img
                v-if="true"
                src=""
                width="150" height="50" /> -->
                </div>
            </td>
            <td style="border: none;">
                <div class="sign-user">
                审核：
                <!-- <img
                v-if="true"
                src=""
                width="150" height="50" /> -->
                </div>
            </td>
            <td style="border: none;">
                <div class="sign-user">
                检测：
                <!-- <img
                v-if="true"
                src=""
                width="150" height="50" /> -->
                </div>
            </td>
            </tr>
        </table>
        </div>
    </div>
</template>

<script>
import { hntMixed } from './hntMixed';
import moment from 'moment';

export default {
    mixins: [hntMixed],
    props: {
        companyName: {
            type: String,
            default: ""
        },
    },
    methods: {

isEmpty(val) {
      if (typeof val === "boolean") {
          return false;
      }
      if (typeof val === "number") {
          return false;
      }
      if (val instanceof Array) {
          if (val.length === 0) return true;
      } else if (val instanceof Object) {
          if (JSON.stringify(val) === "{}" || val == null ) return true;
      } else {
          if (
          val === "null" ||
          val == null ||
          val === "undefined" ||
          val === undefined ||
          val === ""
          )
          return true;
          return false;
      }
      return false;
  },

      getShowResultStr(str){
        var res = str;
        if(this.isEmpty(str)) return '--';
        const parts = str.split(/[:;：；]/);
        const filteredParts = parts.filter(part => part.includes('合格'));
        if(filteredParts.length > 0) {
            res = filteredParts[0];
        }
        // if(str.includes(':')){
        //   res = str.replace(/[:]/g, '');
        // }
        // if(res.includes('：')){
        //     res = res.replace(/[：]/g, '');
        // }
        // if(res.includes('。')){
        //     res = res.replace(/[。]/g, '');
        // }
        // if(res.includes('；')){
        //     res = res.replace(/[；]/g, '');
        // }
        // if(res.includes(';')){
        //     res = res.replace(/[;]/g, '');
        // }
        return res;

      },

        getShenShuiStr(value){
            if (!value){
                return '---';
            }
            if(value == '是') {
                return '渗水'
            };
            if(value == '否'){
                return '未渗水'
            };
            return '---';
        },

        formatDate(value) {
          if (value) return moment(value).format("YYYY-MM-DD");
          return "";
        },
        getChouDuYaoQiu(str){
            if(!str) return '---';
            try {
                var strList = str.split('±');
                return strList.length > 0 ? strList[0]+'mm' + '±' + strList[1]+'mm' : str;
            } catch (error) {
                return str;
            }
            
        },
        getShowTimeString(list) {
            if(list.length == 0){
                return '---';
            }
            let maxObj = list[0];
            let minObj = list[0];

            for (let i = 1; i < list.length; i++) {
               const current = list[i];
                if (parseFloat(current.syyl) > parseFloat(maxObj.syyl)) {
                    maxObj = current;
                }
                if (parseFloat(current.syyl) < parseFloat(minObj.syyl)) {
                    minObj = current;
                }
            }
            var start = minObj?.jysj || '';
            var end = maxObj?.jysj || '';
            var str = '---';
            if(start) {
                str = this.formatDate(start);
            }
            if(end) {
                str = str + '至' + this.formatDate(end);
            }
            var timeList = str.split('至');
            if(timeList.length > 0){
                var first = timeList[0];
                var last = timeList[1];
                // 将日期字符串转换为 Date 对象
                const date1 = new Date(first);
                const date2 = new Date(last);
                // 比较日期大小
                if (date1 > date2) {
                    str = last + '至' + first;
                } else if (date1 == date2){
                    str = first;
                }
            }

            return str;
        }
    }
}
</script>

<style lang="scss" scoped>
@import '../../print.css';
// .border {
//     border: 3px solid black; /* 设置边框粗细 */
// }
// .border th, .border td{
//     border: 2px solid black; /* 设置边框粗细 */
// }
</style>