<!--
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-05-22 00:03:51
 * @LastEditors: huaze.fan <EMAIL>
 * @LastEditTime: 2025-03-21 15:38:43
 * @FilePath: /quality_center_web/src/pages/print/components/hntks.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div class="portrait">
        <div align="center" class="lab-report">

        <!-- 数据 -->
        <template v-for="(item, idx) in kyPageDataList">
          <template v-for="(obj, index) in item">
            <!-- 分割线条 v-if="idx != 0 && index != 0" -->
            <table v-if="!(idx == 0 && index == 0)" :key="(idx + index).toString()">
                <tr :height="'60px'"></tr>
            </table>
            <div class="tip-view">
              <div class="tip-label">C-06b-0903</div>
            </div>
        <div class="company-label" style="font-size: 30px; font-weight:700;">{{companyName}}</div>
        <div class="title-label" style="font-size: 46px; line-height: 60px;">混凝土抗压强度检测报告</div>
        <div class="lab-sub-title" style="align-items: end;">
          <div class="lab-sub-title-container" style="margin-bottom: 0px; margin-left: 140px;"></div>
                <div class="lab-sub-title-container">
                  <div class="lab-sample-id" style="margin-top: 40px; display: flex;"><span style="width: 140px;">生产任务单号:</span><span>{{ erpRwdextra?.rwdextraExtend?.produceIndexId | isNull }} </span></div>
                  <div class="lab-report-id" style="margin-top: 15px; display: flex;"><span style="width: 140px;">报告编号:</span><span>{{ kyMapKeys[idx] | isNull }} </span></div>
            </div>
        </div>

        <div :style="{ marginLeft: '120px', 'width': '100%', marginTop:'-20px', 'textAlign': 'left'}">第{{index + 1}}页 共{{item.length}}页</div>
        <table  border="1"  class="border" style="margin-top: 15px; font-size: 16px; table-layout: fixed;">
          <tr height="38">
            <td colspan="5">施工单位</td>
            <td colspan="33" left>{{ erpRwdextra?.buildNameNew | isNull }}</td>
          </tr>
          <tr height="38">
            <td colspan="5">工程名称</td>
            <td colspan="33" left>{{ erpRwdextra?.projectNameNew | isNull }}</td>
          </tr>
          <tr height="38">
            <td colspan="5">工程部位</td>
            <td colspan="33" left>{{ rwdextraObj?.fjzbw | isNull }}</td>
          </tr>
          <tr height="38">
            <td colspan="5">强度等级</td>
            <td colspan="6" left>{{ obj[0]?.materialsSpecs | isNull }}</td>
            <td colspan="4">生产方量</td>
            <td colspan="13">{{ erpRwdextra?.scquantity | isNull }}</td>
            <td colspan="4">稠度要求</td>
            <td colspan="6" left>{{ getChouDuYaoQiu(rwdextraObj?.ftld) }}</td>
          </tr>
          <tr height="38">
            <td colspan="5">配合比编号</td>
            <td colspan="23" left>{{ rwdextraObj?.fphbNo | isNull }}</td>
            <td colspan="4">报告日期</td>
            <td colspan="6" left>{{ getReportDate(obj[0]?.reportDate) }}</td>
          </tr>
        </table>
          <table border="1" class="border" style="font-size: 16px; table-layout: fixed;" :key="idx">
            <template v-for="(kyqdInfo, index) in obj">
              <tr height="38" :key="index+'tableList'">
                <td colspan="5">样品编号</td>
                <td colspan="6" left>{{ kyqdInfo?.sampleID | shxhNull }}</td>
                <td colspan="4">样品规格</td>  
                <td colspan="14" left>{{ getSJCC(kyqdInfo?.objJson?.sjcc) }}</td>
                <td colspan="4">稠度/mm</td>
                <td colspan="6" left>{{ '---' }}</td>
              </tr>
              <tr height="38">
                <td colspan="5">成型日期</td>
                <td colspan="6" left>{{ kyqdInfo?.moldingDate | momentDate }}</td>
                <td colspan="4">龄期/d</td>
                <td colspan="4">{{ kyqdInfo?.objJson?.lq | isNull }}</td>
                <td colspan="4" >检测日期</td>
                <td colspan="6" left>{{ kyqdInfo?.objJson?.jcrq | momentDate }}</td>
                <td colspan="4" width="30">代表数量</td>
                <td colspan="6" width="30" left>{{ getDaiBiaoShuLiang(obj[0]?.behalfNumber) }}</td>
              </tr>
              <tr height="38">
                <td colspan="5">养护条件</td>
                <td colspan="6" nowrap="nowrap">破坏荷载/kN</td>
                <td colspan="6" nowrap="nowrap">抗压强度/MPa</td>
                <td colspan="6" nowrap="nowrap">强度代表值/MPa</td>
                <td colspan="10" nowrap="nowrap">折合标准试块强度/MPa</td>
                <td colspan="6" nowrap="nowrap">达到设计强度/%</td>
              </tr>
              <tr height="38">
                <td colspan="5" rowspan="3">标准养护</td>
                <td colspan="6">{{getResultStr(kyqdInfo?.objJson?.qd28d[0]?.hz, 1)}}</td>
                <td colspan="6">{{getResultStr(kyqdInfo?.objJson?.qd28d[0]?.qd, 1)}}</td>
                <td colspan="6" rowspan="3">{{getResultStr(kyqdInfo?.objJson?.pjz28d , 1)}}</td>
                <td colspan="10" rowspan="3">{{getResultStr( kyqdInfo?.objJson?.zsbzqd28d, 1)}}</td>
                <td colspan="6" rowspan="3">{{getResultStr( kyqdInfo?.objJson?.fyxs28d, 0)}}</td>
              </tr>
              <tr height="38">
                <td colspan="6">{{getResultStr(kyqdInfo?.objJson?.qd28d[1]?.hz, 1)}}</td>
                <td colspan="6">{{getResultStr(kyqdInfo?.objJson?.qd28d[1]?.qd, 1)}}</td>
              </tr>
              <tr height="38">
                <td colspan="6">{{getResultStr(kyqdInfo?.objJson?.qd28d[2]?.hz, 1)}}</td>
                <td colspan="6">{{getResultStr(kyqdInfo?.objJson?.qd28d[2]?.qd, 1)}}</td>
              </tr>
              <tr height="38">
                <td colspan="5">备注</td>
                <td colspan="34" left>{{ obj?.remark || '' }}</td>
              </tr>
              <tr v-if="index != obj?.length-1" height="15">
              </tr>
            </template>
        </table>
        <table border="1" class="border" style="font-size: 16px; table-layout: fixed;">
          <tr height="38">
            <td colspan="5">检测方法</td>
            <td colspan="20" center>{{ obj?.experimentGist | isNull }}</td>
            <td colspan="5">评定依据</td>
            <td colspan="8" center>{{ obj?.judgeGist | isNull }}</td>
          </tr>
          <tr height="70">
            <td colspan="5">备注</td>
            <td colspan="33" left>{{ '' }}</td>
          </tr>
        </table>

        <table class="lab-sign" style="border: none;">
          <tr height="38" style="border: none;">
            <td style="border: none;"><div class="sign-user">检测机构专用章：</div></td>
            <td style="border: none;">
              <div class="sign-user">
                批准：
              </div>
            </td>
            <td style="border: none;">
              <div class="sign-user">
                审核：
              </div>
            </td>
            <td style="border: none;">
              <div class="sign-user">
                检测：
              </div>
            </td>
          </tr>
        </table>

          </template>
            
        </template>
        
      </div>
    </div>
</template>

<script>
import { hntMixed } from './hntMixed';
import {cpEvenRound} from "@/utils/calculate.js"
export default {
    mixins: [hntMixed],

    props: {
        companyName: {
            type: String,
            default: ""
        },
        subItem: {
            type: Object,
            default: {}
        }
    },

    data(){
      return {
        kyInfoList:[],
        kyInfoObj:{},
        pageDataList:[],
        experimentInfoList: [],
        experimentPageInfoList: [],
        // 新抗压试验
        kyMapKeys:[],
        kyMapValues:[],
        kyPageDataList:[],

      }
    },

    mounted() {
      this.subItem.kyMapInfo = {
                    "HKY202507529": [
                        {
                            "reportDate": "2025-06-27 00:00:00",
                            "materialsSpecs": "水下C30",
                            "experimentGist": "GB/T 50081-2019",
                            "judgeGist": "",
                            "moldingDate": "2025-05-30 11:01:13",
                            "behalfNumber": 194.5,
                            "remark": null,
                            "objJson": {
                                "sjcc": "100*100*100",
                                "lq": "28",
                                "zsbzqd28d": "45.099998474121094",
                                "jcrq": "2025-06-27",
                                "qd28d": [
                                    {
                                        "hz": "629.2999877929688",
                                        "qd": "62.900001525878906",
                                        "index": "1"
                                    },
                                    {
                                        "hz": "474.79998779296875",
                                        "qd": "47.5",
                                        "index": "2"
                                    },
                                    {
                                        "hz": "439.70001220703125",
                                        "qd": "44.0",
                                        "index": "3"
                                    }
                                ],
                                "zsxs28d": "",
                                "pjz7d": "",
                                "dxjl7d": "",
                                "pjz28d": "47.5",
                                "zsbzqd7d": "",
                                "qd7d": [
                                    {
                                        "hz": "",
                                        "qd": "",
                                        "index": "1"
                                    },
                                    {
                                        "hz": "",
                                        "qd": "",
                                        "index": "2"
                                    },
                                    {
                                        "hz": "",
                                        "qd": "",
                                        "index": "3"
                                    }
                                ],
                                "fyxs7d": "",
                                "dxjl28d": "",
                                "fyxs28d": "129.0",
                                "zsxs7d": ""
                            },
                            "sampleID": "2508002625"
                        }
                    ]
                }
      if(this.subItem?.kyMapInfo) {
        this.kyMapValues = Object.values(this.subItem?.kyMapInfo);
      }
      // 报告编号对应二维数组
        if(this.kyMapValues?.length > 0){
          var newPageData = [];
          // 最外层数据
          this.kyMapValues.forEach((item, index)=>{
            var keyList = Object.keys(this.subItem?.kyMapInfo);
            var keyStr = keyList[index] == 'isNull' ? '' : keyList[index];
            var newKyInfoList = [];
            // item 是个数组
            item.forEach((obj, idx)=>{
              newKyInfoList.push(obj);
            });

          if(newKyInfoList.length <=3){
              while (newKyInfoList.length < 3) {
                newKyInfoList.push({
                  "reportNo":'',
                  "sampleID": "",
                  "materialsSpecs":'',
                  "experimentGist":'',
                  "judgeGist":'',
                  "moldingDate": "",
                  "behalfNumber": '',
                  "remark": '',
                  "reportDate":'',
                  "objJson":{ 
                    "qd28d":[{},{},{}],
                    "zsxs28d": "",
                    "pjz7d": "",
                    "dxjl7d": "",
                    "pjz28d": "",
                    "zsbzqd7d": "",
                    "fyxs7d": "",
                    "dxjl28d": "",
                    "fyxs28d": "",
                    "zsxs7d": "",
                    "sjcc": "",
                    "lq": "",
                    "zsbzqd28d": "",
                    "jcrq": "",
                  } 
                });
              }
              this.kyMapKeys.push(keyStr);
              this.kyPageDataList.push([newKyInfoList]);
            } else {
              this.kyPageDataList.push(this.convertArray(newKyInfoList, 3));
              this.kyMapKeys.push(keyStr);
            }
          });
          
          console.log('任务单抗压强度列表：', this.kyPageDataList);
          console.log('任务单报告编号：', this.kyMapKeys);
       } else {
        // 返回的数据为空
        var newKyInfoList = [];
        for(let i = 0; i < 3; i++) {
          newKyInfoList.push({
                  "sampleID": "",
                  "materialsSpecs":'',
                  "experimentGist":'',
                  "judgeGist":'',
                  "moldingDate": "",
                  "behalfNumber": '',
                  "remark": '',
                  "reportDate":'',
                  "objJson":{ 
                    "qd28d":[{},{},{}],
                    "zsxs28d": "",
                    "pjz7d": "",
                    "dxjl7d": "",
                    "pjz28d": "",
                    "zsbzqd7d": "",
                    "fyxs7d": "",
                    "dxjl28d": "",
                    "fyxs28d": "",
                    "zsxs7d": "",
                    "sjcc": "",
                    "lq": "",
                    "zsbzqd28d": "",
                    "jcrq": "",
                  } 
                });
        } 
        console.log('返回的数据为空：', newKyInfoList);
        this.kyPageDataList.push(this.convertArray(newKyInfoList, 3));
        this.kyMapKeys.push('');
          console.log('空数据列表：', this.kyPageDataList);
          console.log('空数据报告编号：', this.kyMapKeys);
       }

    },
    methods:{

      getDaiBiaoShuLiang(str){
        if(!str) return '---';
        return str + 'm³';

      },

      getChouDuYaoQiu(str){
            if(!str) return '---';
            try {
                var strList = str.split('±');
                return strList.length > 0 ? strList[0]+'mm' + '±' + strList[1]+'mm' : str;
            } catch (error) {
                return str;
            }
            
        },

        getSJCC(sjcc){
          if(!sjcc) return '---';
          return sjcc.replace(/(\d+)\*/g, '$1mm ‌× ').replace(/(\d+)$/, '$1mm');
        },
        getReportDate(time){
          if(!time) return '---';
          if(time.length > 10) {
            var date = time.substring(0, 10);
            return date.replace(/-/g, "/");
          }
          return time.replace(/-/g, "/");
        },

      getResultStr(value,index){
        if (value == undefined || value == null || value === '')  return '---';
        return cpEvenRound(value || '', index)
      },
      // 将数组 originalArray 装换成每个元素是长度是 subLength 的二位数组
      convertArray(originalArray, subLength) {
        const result = [];
        var flage = false;
          for (let i = 0; i < originalArray.length; i += subLength) {
            const subArray = originalArray.slice(i, i + subLength);
            // 补充不足4个元素的情况
            while (subArray.length < subLength) {
              flage = true
              subArray.push({
                  "sampleID": "",
                  "materialsSpecs":'',
                  "experimentGist":'',
                  "judgeGist":'',
                  "moldingDate": "",
                  "behalfNumber": '',
                  "remark": '',
                  "reportDate":'',
                  "objJson":{ 
                    "qd28d":[{},{},{}],
                    "zsxs28d": "",
                    "pjz7d": "",
                    "dxjl7d": "",
                    "pjz28d": "",
                    "zsbzqd7d": "",
                    "fyxs7d": "",
                    "dxjl28d": "",
                    "fyxs28d": "",
                    "zsxs7d": "",
                    "sjcc": "",
                    "lq": "",
                    "zsbzqd28d": "",
                    "jcrq": "",
                  } 
                });
            }
            result.push(subArray);
          }
          if(!flage && originalArray.length % subLength != 0){
            var list = [];
            for(let i = 0; i<subLength; i++){
              list.push({
                  "sampleID": "",
                  "materialsSpecs":'',
                  "experimentGist":'',
                  "judgeGist":'',
                  "moldingDate": "",
                  "behalfNumber": '',
                  "remark": '',
                  "reportDate":'',
                  "objJson":{ 
                    "qd28d":[{},{},{}],
                    "zsxs28d": "",
                    "pjz7d": "",
                    "dxjl7d": "",
                    "pjz28d": "",
                    "zsbzqd7d": "",
                    "fyxs7d": "",
                    "dxjl28d": "",
                    "fyxs28d": "",
                    "zsxs7d": "",
                    "sjcc": "",
                    "lq": "",
                    "zsbzqd28d": "",
                    "jcrq": "",
                  } 
                });
            }
            result.push(list);
          }
          return result;
        }
      
    }
}
</script>

<style lang="scss" scoped>
@import '../../print.css';
.border {
    border: 3px solid black; /* 设置边框粗细 */
}
.border th, .border td{
    border: 2px solid black; /* 设置边框粗细 */
}
</style>