<!--
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-05-22 00:03:51
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-08-28 00:13:00
 * @FilePath: /quality_center_web/src/pages/print/components/hntks.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div class="portrait" style="font-size: 14px">
        <template v-for="(itemObj, index) in pageTableList" >
            <!-- 分割线条 -->
            <table v-if="index != 0">
                <tr height="90"></tr>
            </table>
            <div class="company-label":key="index.toString()" :style="{fontSize: '40px', fontWeight:'700', marginTop: index == 0 ? '50px' : '50px'}">{{companyName}}</div>
            <div class="title-label" style="font-size: 46px; line-height: 60px;">混凝土配合比调整通知</div>
            <div align="center" class="lab-report">
                <div class="lab-sub-title">
                    <div class="top-title-container">
                      <div class="lab-sample-id" style="font-size: 15px; font-weight: 400;">生产日期:<span>{{ getTimeStr(erpRwdextra?.plantime)}} </span></div>
                      <div class="lab-report-id" style="font-size: 15px; font-weight: 400;">任务单编号:<span>{{ rwdextraObj?.frwno | isNull }} </span></div>
                      <div class="lab-report-id" style="font-size: 15px; font-weight: 400;">配合比通知单编号:<span>{{showYearNoStr(erpRwdextra?.rwdextraExtend?.mixProportionNo, erpRwdextra?.rwdextraExtend?.createTime)}}</span></div>
                    </div>
                </div>
                <table  border="1" class="border" style="margin-bottom: 5px; margin-top: 10px;">
                    <tr height="40">
                        <td colspan="5" style="width: 18%; font-weight: 400; font-weight: 400;">用户单位</td>
                        <td colspan="15" style="width: 40%;font-weight: 400;">{{ erpRwdextra?.buildNameNew | isNull }}</td>
                        <td colspan="4" style="width: 13%;font-weight: 400;">配比编号</td>
                        <td colspan="5" style="width: 13%;font-weight: 400;">{{ rwdextraObj?.fphbNo | isNull }}</td>
                        <td colspan="4" style="width: 13%;font-weight: 400;">每盘容积</td>
                        <td colspan="5">/</td>
                    </tr>
                    <tr height="40">
                        <td colspan="5" style="width: 18%;font-weight: 400;">工程名称</td>
                        <td colspan="15" style="width: 40%;font-weight: 400;">{{ rwdextraObj?.fgcmc | isNull }}</td>
                        <td colspan="4" style="width: 13%;font-weight: 400;">强度等级</td>
                        <td colspan="5" style="width: 13%;font-weight: 400;">{{ mixProportion?.proportionQddj | isNull }}</td>
                        <td colspan="4" style="width: 13%;font-weight: 400;">坍落度(mm)</td>
                        <td colspan="5" style="width: 13%;font-weight: 400;">{{ getTLDValue(mixProportion?.proportionTld?.qz, mixProportion?.proportionTld?.hz) }}</td>
                    </tr>
                    <tr height="40">
                        <td colspan="5" style="width: 18%;font-weight: 400;">浇筑部位</td>
                        <td colspan="15" style="width: 40%;font-weight: 400;">{{ rwdextraObj?.fjzbw | isNull }}</td>
                        <td colspan="4" style="width: 13%;font-weight: 400;">水胶比</td>
                        <td colspan="5" style="width: 13%;font-weight: 400;">{{ mixProportion?.printSjb | isNull }}</td>
                        <td colspan="4" style="width: 13%;font-weight: 400;">砂率(%)</td>
                        <td colspan="5" style="width: 13%;font-weight: 400;">{{ mixProportion?.printSl | isNull  }}</td>
                    </tr>
                    <tr height="40">
                        <td rowspan="6" colspan="1" style="width: 5%;font-weight: 400;">配<br />合<br />比</td>
                        <td colspan="4" style="width: 13%;font-weight: 400;">原材料</td>
                        <template v-for="(item, index) in topNameList" >
                            <td v-if="index != topNameList.length-1 || topNameList.length == 1 " :style="{width:topNameWidth,fontWeight: 400}" :colspan="topColWidth" :key="index" >
                            {{item | isNull}}
                            </td>
                            <td v-else  :style="{width:topNameWidth,fontWeight: 400}" :colspan="lastTopColWidth" :key="item">
                            {{item | isNull}}
                            </td>
                        </template>
                    </tr>
                    <tr height="40">
                        <td colspan="4" style="width: 13%;font-weight: 400;">规格</td>
                        <template  v-for="(item, index) in afterPhbDtoList" >
                            <td v-if="index != afterPhbDtoList.length-1 || afterPhbDtoList.length == 1 "  :style="{width:topNameWidth,fontWeight: 400}" :colspan="topColWidth" :key="index" >
                                {{getWlGGStr(item)}} 
                            </td>
                            <td v-else :style="{width:topNameWidth,fontWeight: 400}" :colspan="lastTopColWidth" :key="item">
                                {{getWlGGStr(item)}} 
                            </td>
                        </template>
                    </tr>
                    <tr height="40">
                        <td colspan="4" style="width: 13%;font-weight: 400;">每方配料</td>
                        <template  v-for="(item, index) in afterPhbDtoList" >
                            <td v-if="index != afterPhbDtoList.length-1 || afterPhbDtoList.length == 1 "  :style="{width:topNameWidth,fontWeight: 400}" :colspan="topColWidth" :key="index" >
                                {{ getYongLiangInt(item?.mpfyl, item?.cllx) }} 
                            </td>
                            <td v-else  :style="{width:topNameWidth,fontWeight: 400}" :colspan="lastTopColWidth" :key="item">
                                {{getYongLiangInt(item?.mpfyl, item?.cllx)}} 
                            </td>
                        </template>
                    </tr>
                    <tr height="40">
                        <td colspan="4" style="width: 13%;font-weight: 400;">含水率(%)</td>
                        <template  v-for="(item, index) in afterPhbDtoList" >
                            <td v-if="index != afterPhbDtoList.length-1 || afterPhbDtoList.length == 1 "  :style="{width:topNameWidth,fontWeight: 400}" :colspan="topColWidth" :key="index" >
                                {{item?.hsl | isNull}} 
                            </td>
                            <td v-else  :style="{width:topNameWidth,fontWeight: 400}" :colspan="lastTopColWidth" :key="item">
                                {{item?.hsl | isNull}} 
                            </td>
                        </template>
                    </tr>
                    <tr height="40">
                        <td colspan="4" style="width: 13%;font-weight: 400;">每方实际</td>
                        <template  v-for="(item, index) in afterPhbDtoList" >
                            <td v-if="index != afterPhbDtoList.length-1 || afterPhbDtoList.length == 1 "  :style="{width:topNameWidth,fontWeight: 400}" :colspan="topColWidth" :key="index" >
                                {{getMeiFangShiJi(item, afterPhbDtoList, index)}} 
                            </td>
                            <td v-else  :style="{width:topNameWidth,fontWeight: 400}" :colspan="lastTopColWidth" :key="item">
                                {{getMeiFangShiJi(item, afterPhbDtoList, index)}} 
                            </td>
                        </template>
                    </tr>
                </table>
                <table class="lab-sign" style="border: none; margin-top: 0; font-size: 15px;">
                    <tr height="40" style="border: none;">
                    <td style="border: none;">
                        <div class="sign-user" style="font-weight: 400;">
                        搅拌时间(秒)：{{mixProportion?.proportionJbsj | isNull}}
                        </div>
                    </td>
                    <td style="border: none;">
                        <div class="sign-user" style="font-weight: 400;">
                        试验员：
                        </div>
                    </td>
                    <td style="border: none;">
                        <div class="sign-user" style="justify-content: center;font-weight: 400;">
                        复核员：
                        </div>
                    </td>
                    </tr>
                </table>
            <!-- 混凝土配合比调整记录： -->
            <div style="margin-top: 0px; font-size: 20px; font-weight: 400; text-align: left">混凝土配合比调整记录：</div>
            <table border="1" class="border" style="margin-bottom: 20px; margin-top: 10px; font-size: 14px">
                <tr height="40">
                    <td rowspan="3" colspan="2" style="width: 20%;font-weight: 400;">调整时间</td>
                    <td colspan="21" style="font-weight: 400;">调整后的每方配合比（Kg）</td>
                </tr>
                <tr height="40">
                    <template  v-for="(item, index) in bottomNameList" >
                            <td :style="{width: bottomNameWidth,fontWeight: 400}" v-if="index != bottomNameList.length-1 || bottomNameList.length == 1 " :colspan="bottomColWidth" :key="index" >
                                {{item | isNull}}
                            </td>
                            <td :style="{width: bottomNameWidth,fontWeight: 400}" v-else :colspan="lastBottomColWidth" :key="item">
                                {{item | isNull}}
                            </td>
                        </template>

                    <td colspan="1" rowspan="2" style="width: 10%;font-weight: 400;">试验员</td>
                </tr>
                <tr height="40">
                    <template  v-for="(item, index) in afterPhbList" >
                            <td :style="{width: bottomNameWidth,fontWeight: 400}" v-if="index != afterPhbList.length-1 || afterPhbList.length == 1 " :colspan="bottomColWidth" :key="index" >
                                 {{getWlGGStr(item)}} 
                            </td>
                            <td :style="{width: bottomNameWidth,fontWeight: 400}" v-else :colspan="lastBottomColWidth" :key="item">
                                 {{getWlGGStr(item)}} 
                            </td>
                        </template>
                </tr>
                <!-- 循环展示数据 -->
                <template v-for="(phbhistory, idxNum) in itemObj">
                    <tr height="40" style="width: 100%;" :key="idxNum.toString()">
                    <template>
                        <td colspan="1" rowspan="3" style="width: 10%;font-weight: 400">{{phbhistory.createtime | isNull}} <br/>【{{getTableStr(phbhistory?.mixtable)}}】</td>
                        <td colspan="1" style="width: 10%;font-weight: 400;">生产用量</td>
                        <template v-for="(obj, idx) in phbhistory.afterPhbDtoList">
                            <td :style="{width: bottomNameWidth,fontWeight: 400}" v-if="idx != phbhistory.afterPhbDtoList.length-1 || phbhistory.afterPhbDtoList.length == 1 " :colspan="bottomColWidth" :key="idx" >
                                {{obj?.tzhmpfyl | isNull}}
                            </td>
                            <td :style="{width: bottomNameWidth,fontWeight: 400}" v-else :colspan="lastBottomColWidth" :key="obj">
                                {{obj?.tzhmpfyl | isNull}}
                            </td>
                        </template>
                    </template>
                    <td colspan="1" rowspan="3" style="width: 10%;"></td>
                </tr>
                <tr height="40" style="width: 100%;" :key="idxNum.toString()">
                    <template>
                        <td colspan="1" style="width: 10%;font-weight: 400;">含水率(%)</td>
                        <template v-for="(obj, idx) in phbhistory.afterPhbDtoList">
                            <td :style="{width: bottomNameWidth,fontWeight: 400}" v-if="idx != phbhistory.afterPhbDtoList.length-1 || phbhistory.afterPhbDtoList.length == 1 " :colspan="bottomColWidth" :key="idx" >
                                {{obj?.hsl | isNull}}
                            </td>
                            <td :style="{width: bottomNameWidth,fontWeight: 400}" v-else :colspan="lastBottomColWidth" :key="obj">
                                {{obj?.hsl | isNull}}
                            </td>
                        </template>
                    </template>

                </tr>
                <tr height="40" :key="idxNum.toString()">
                    <template>
                        <td colspan="1" style="width: 10%;font-weight: 400;">调整用量</td>
                        <template v-for="(obj, idx) in phbhistory.afterPhbDtoList">
                            <td :style="{width: bottomNameWidth,fontWeight: 400}" v-if="idx != phbhistory.afterPhbDtoList.length-1 || phbhistory.afterPhbDtoList.length == 1 " :colspan="bottomColWidth" :key="idx" >
                                {{getMeiFangShiJi(obj, phbhistory.afterPhbDtoList, idx)}}
                            </td>
                            <td :style="{width: bottomNameWidth,fontWeight: 400}" v-else :colspan="lastBottomColWidth" :key="obj">
                                {{getMeiFangShiJi(obj, phbhistory.afterPhbDtoList, idx)}}
                            </td>
                        </template>
                    </template>
                </tr>
                </template>
                <tr height="120">
                    <td colspan="2" style="font-weight: 400;">备&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;注</td>
                    <td colspan="22" style="width: 70%;font-weight: 400;" left></td>
                </tr>
            </table>
            <div style="margin-top: 0px; font-size: 14px; font-weight: 400;font-weight: 400; text-align: right">共{{pageTableList.length}}页 当前第{{index + 1}}页</div>
        </div>
        </template>
            
        
    </div>

    
</template>

<script>
import {calcEquation, cpEvenRound, div, mul} from "@/utils/calculate.js"
import { hntTaskMixed } from './hntTaskMixed';

export default {
    mixins: [hntTaskMixed],
    props: {
        companyName: {
            type: String,
            default: ""
        },
        indexNum:{
            type: Number,
            default:0
        },
        // 包含调整时间的list
        phbhistoryList: {
            type: Array,
            default: () => { return [] }
        },
        // 配合比调整通知上面部分数据list 
        afterPhbDtoList: {
            type: Array,
            default: () => { return [] }
        }
    },

    data() {
        return {

            phbhistoryListCount:0,
            pageTableList:[],
            phbhistoryObj: {},
            topNameList:['粉煤灰','水','水泥','外加剂','石','砂','矿渣粉'],
            topNameWidth:'82%',
            topColWidth:33,
            lastTopColWidth:3,
            bottomNameWidth:'70%',
            // 调整记录
            bottomNameList:['粉煤灰','水','水泥','外加剂','石','砂','矿渣粉'],
            afterPhbList: [],
            lastBottomColWidth:3,
            bottomColWidth:21,
            isLastCol:false,

        }
    },

    mounted() {
        // this.phbhistoryList = [];
        this.handelPageList();

        this.topColWidth = ((33 / (this.topNameList.length)));
        this.lastTopColWidth = 33 - (this.topColWidth) * (this.topNameList.length - 1);
        this.topNameWidth = (82 / this.topNameList.length) + '%'
        this.bottomColWidth = ((21 / (this.bottomNameList.length)));
        this.lastBottomColWidth = 21 - (this.bottomColWidth) * (this.bottomNameList.length - 1);
        this.bottomNameWidth = (70 / this.bottomNameList.length) + '%'
        if (this.phbhistoryList.length > 0) {
            this.phbhistoryListCount = this.phbhistoryList.length % 2 == 0 ? this.phbhistoryList.length : this.phbhistoryList.length + 1;
            this.bottomNameList = [];
            this.phbhistoryObj = this.phbhistoryList[0];
            this.afterPhbList = this.phbhistoryObj?.afterPhbDtoList;
            let phbDtoList = this.phbhistoryObj?.afterPhbDtoList || []
            phbDtoList.forEach((item, index)=>{
                var name = item?.cllx || '';
                var clmc = item?.clmc || ''
                if(name == '粗骨料'){
                    name = '石'
                }
                if(name == '细骨料'){
                    name = '砂'
                }
                if(clmc.includes('纤维')){
                    name = '外掺料2'
                }

                if(clmc.includes('膨胀剂')){
                    name = '外掺料1'
                }
                this.bottomNameList.push(name || '/');
                console.log('底部' + item);
            });
            this.bottomNameWidth = (70 / this.bottomNameList.length) + '%'
            this.bottomColWidth = ((21 / (this.bottomNameList.length)));
            console.log('底部表格宽度：', this.bottomNameWidth);
            console.log('底部表格宽度col：', this.bottomColWidth);
            this.lastBottomColWidth = 21 - (this.bottomColWidth) * (this.bottomNameList.length - 1);
            if(this.lastBottomColWidth % 1 !== 0){
                this.lastBottomColWidth = parseInt(this.lastBottomColWidth) + 1
            }

            console.log('底部表格宽度：', this.bottomNameWidth);
            console.log('底部表格宽度col：', this.bottomColWidth);
            console.log('底部表格宽度last：', this.lastBottomColWidth);
            if( this.bottomNameList.length == 1) {
                this.bottomColWidth = 21
                this.bottomNameWidth = '70%'
                this.lastBottomColWidth = 0
            }
        } else {

            
            this.bottomNameList.forEach((item, index)=>{
                this.afterPhbList.push({
                        "clmc": item,
                        "clgg": "",
                        "wlmc": "",
                        "wlgg": "",
                        "hsl": "",
                        "mpfyl": "",
                        "tzhmpfyl": ''
                    })
            });
        }


        if (this.afterPhbDtoList.length > 0) {
            this.topNameList = [];
            this.afterPhbDtoList.forEach((item, index)=>{
                console.log('头部' + item);
                var name = item?.cllx || '';
                var clmc = item?.clmc || ''
                if(name == '粗骨料'){
                    name = '石'
                }
                if(name == '细骨料'){
                    name = '砂'
                }
                if(clmc.includes('纤维')){
                    name = '外掺料2'
                }
                if(clmc.includes('膨胀剂')){
                    name = '外掺料1'
                }
                this.topNameList.push(name || '/');
            });
            this.topNameWidth = (82 / this.topNameList.length) + '%'
            this.topColWidth = (33 / (this.topNameList.length));
            this.lastTopColWidth = 33 - (this.topColWidth) * (this.topNameList.length - 1);
            if(this.lastTopColWidth % 1 !== 0){
                this.lastTopColWidth = parseInt(this.lastTopColWidth) + 1
            }
            if( this.topNameList.length == 1) {
                this.topNameWidth = '82%'
                this.topColWidth = 33
                this.lastTopColWidth = 0
            }
        } else {

            this.topNameList.forEach((item, index)=>{
                this.afterPhbDtoList.push({
                        "clgg": "",
                        "wlmc": "",
                        "wlgg": "",
                        "hsl": "",
                        "mpfyl": "",
                        "tzhmpfyl": ''
                    })
            })
        }

    },
    methods:{
        showYearNoStr(no, creatTime){
            console.log('creatTime：', creatTime);
            let timeList = creatTime?.split('-');
            let year = '';
            if(timeList && timeList.length > 0){
                year = timeList[0].slice(-2);
                console.log('year：', year);
            }
            if(this.isEmpty(no)) return '---';
            return year + no;

        },
      // 将数组 originalArray 装换成每个元素是长度是 subLength 的二位数组
      convertArray(originalArray, subLength, defaultObj) {
        const result = [];
        var flage = false;
        let leg = originalArray.length > 0 ? originalArray.length : 1
          for (let i = 0; i < leg; i += subLength) {
            const subArray = originalArray.slice(i, i + subLength);
            // 补充不足4个元素的情况
            while (subArray.length < subLength) {
              flage = true
              subArray.push(defaultObj);
            }
            result.push(subArray);
          }
          if(!flage && originalArray.length % subLength != 0){
            var list = [];
            for(let i = 0; i<subLength; i++){
              list.push(defaultObj);
            }
            result.push(list);
          }
          return result;
        },

        handelPageList(){
            let defaultObj = {
                "clmc": "",
                "clgg": "",
                "wlmc": "",
                "wlgg": "",
                "hsl": "",
                "mpfyl": "",
                "tzhmpfyl": ''
            }
            let dtoList = [];
            if(this.phbhistoryList.length > 0){
                let defaultObjList = this.phbhistoryList[0].afterPhbDtoList;
                if(defaultObjList.length > 0){
                    defaultObjList.forEach((obj)=>{
                        let allKeys = Object.keys(obj);
                        defaultObj = {}
                        allKeys.forEach((item)=>{
                            defaultObj[item] = '';
                        });
                        dtoList.push(defaultObj);

                    })
                    
                    
                }
            } else {
                Array.from({ length: 7 }).forEach((_, index) => {
                    let allKeys = Object.keys(defaultObj);
                    allKeys.forEach((item)=>{
                        defaultObj[item] = '';
                    });
                    dtoList.push(defaultObj);
                });
            }
            console.log('通知书页面数据：',this.phbhistoryList);
            // this.phbhistoryList.pop();
            // let list = [...this.phbhistoryList, ...this.phbhistoryList];
            this.pageTableList = this.convertArray(this.phbhistoryList?.length > 0 ? this.phbhistoryList : [], 5, {afterPhbDtoList:dtoList});

            console.log('pageTableList：',this.pageTableList);
        
        },

        getYongLiangInt(str, cllx){
            if(this.isEmpty(str)) return '---'
            return cllx.includes('外加剂') ? str : calcEquation([
              {
                v: str,
              },
              {
                k: '+',
                v: '0',
              },
            ], 0);
        },
        changeLastCol(index, length){
            this.isLastCol = index == length;
        },

        getTableStr(str){
            if(this.isEmpty(str) || str == '0'){
                return '*';
            }
            return str + '拌台';
        },

        getMeiFangShiJi(obj, afterPhbDtoList, index){
            var result = '---';
            let name = obj?.cllx || '';
            let clgg = obj?.clgg || '';

            if(this.isEmpty(obj.tzhmpfyl)){
                return result;
            }
            // 水的调整用量 = 水的生产用量 - (其他材料的含水量之和)
            // 其他材料的含水量=生产用量乘以含水率
            if(clgg.includes('水')){
                var totalZl = 0;
                var  shuiList = [];
                afterPhbDtoList.forEach((item, idx)=>{
                    let cllx = item?.cllx || '';
                    if(cllx === '水泥' || !cllx.includes('水')){
                        // totalZl = totalZl + parseFloat(item?.tzhmpfyl || '0') * parseFloat(item?.hsl || '0') / 100
                        // 这行代码和上面的其实是一样的，只是下面代码精度更高
                        totalZl = totalZl + mul(parseFloat(item?.tzhmpfyl || '0'), div(parseFloat(item?.hsl || '0'), 100))
                        // if(cllx === '水1') {
                        //     shui1 = item?.tzhmpfyl || '0'
                        // }
                    } else {
                        // shui = item?.tzhmpfyl || '0'
                        var obj = {
                            index: idx,
                            shui: item?.tzhmpfyl || '0'
                        }
                        shuiList.push(obj);
                    }
                });
                

                console.log('*********************PHB:', obj.mpfyl,'&&&&&&&&&', totalZl);
                result = calcEquation([
                    {
                        v: obj.tzhmpfyl,
                    },
                    {
                        k: '-',
                        v: totalZl,
                    },
                    ], 0);
                    // name === '水1'
                if(name !== '水泥' && name.includes('水')){
                    var shuiObj = shuiList.find(item => item.index === index);
                    const shui1 = shuiObj.shui;
                    const shui = shuiList
                        .filter(item => item.index < index)
                        .reduce((total, item) => total + Number(item.shui), 0);
                    console.log('结果数据-----：', shui, '------', shui1, '------', totalZl);
                    console.log('水数组：', shuiList);
                    console.log('当前坐标水：', shuiObj);
                    // parseFloat(shui) <= 0 ? result = 0 : result = parseFloat(shui) + parseFloat(shui1);
                    result = parseFloat(shui) < parseFloat(totalZl) ?  parseFloat(shui) + parseFloat(shui1) - parseFloat(totalZl) : parseFloat(shui1);
                    if(parseFloat(result) <= 0) {
                        result = 0
                    }
                    console.log('计算后的数据-------：', result);
                    result = Math.round(result).toString();
                }


            } else if(name.includes('外加剂')){
                let hsl = obj?.hsl || 0;
                let tzhmpfyl = obj?.tzhmpfyl || '';
                result = hsl == 0 ? tzhmpfyl : parseFloat(tzhmpfyl) * (1+parseFloat(hsl) / 100) ;
            } else {
                let hsl = obj?.hsl || 0;
                let tzhmpfyl = obj?.tzhmpfyl || '';
                let secondNum = 1+parseFloat(hsl || 0) / 100;
                result = hsl == 0 ? tzhmpfyl : calcEquation([
                    {
                        v: tzhmpfyl,
                    },
                    {
                        k: '*',
                        v: secondNum,
                    },
                    ], 0);
            }
            return this.isEmpty(result) ? '---' : result;

        },
        getTLDValue(val1, val2){
            let tld1 = val1.replace(new RegExp('mm', 'g'), '');
            let tld2 = val2.replace(new RegExp('mm', 'g'), '');
            return tld1 + '±' + tld2
        },

        getTimeStr(time){
            if(!this.isEmpty(time)){
                return time.length > 10 ? time?.substring(0,10) : time
            }
            return '---';
        },
        getWlGGStr(obj){
            var result = '---';
            let name = obj?.wlmc || '';
            let clmc = obj?.clmc || '';
            // 外掺料1
            if(clmc.includes('膨胀剂')){
                result = obj?.wlmc  + obj?.wlgg;
                if(this.isEmpty(result)) result = '---'
                return result;
                // 外掺料2
            }
            if(clmc.includes('纤维')){
                result = obj?.clgg;

                if(this.isEmpty(result)) result = '---'
                return result;
            }
            if(name.includes('外加剂') || name.includes('水泥')){
                result = obj?.wlgg ;

                if(this.isEmpty(result)) result = '---'
                return result;
            } 
            if(name.includes('石') || name.includes('砂')){
                var resp = obj?.clmc + obj?.clgg;
                result = name.includes('石') ? resp.replace('mm', '') : resp;
                if(this.isEmpty(result)) result = '---'
                return result;
            } 
                result = obj?.clgg;
            
            if(this.isEmpty(result)) result = '---'
            return result;


        },

        isEmpty(val) {
            if (typeof val === "boolean") {
                return false;
            }
            if (typeof val === "number") {
                return false;
            }
            if (val instanceof Array) {
                if (val.length === 0) return true;
            } else if (val instanceof Object) {
                if (JSON.stringify(val) === "{}" || val == null ) return true;
            } else {
                if (
                val === "null" ||
                val == null ||
                val === "undefined" ||
                val === undefined ||
                val === ""
                )
                return true;
                return false;
            }
            return false;
            
        }
        
    }
}
</script>

<style lang="scss">
@import '../../print.css';
.top-title-container{
    display: flex;
    justify-content: space-between;
    width: 100%;
    margin-top: 50px;
}
.div-with-right-border {
    display: flex;
    text-align: center;
    justify-content: center;
    border-right: 1.8px solid #000000; /* 2px宽的黑色实线右边框 */
}
</style>