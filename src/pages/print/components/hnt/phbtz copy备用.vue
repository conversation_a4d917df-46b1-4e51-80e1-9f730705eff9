<!--
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-05-22 00:03:51
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2024-10-27 20:47:20
 * @FilePath: /quality_center_web/src/pages/print/components/hntks.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div class="portrait" style="font-size: 14px; margin-top: 80px;">
            <div class="company-label" :style="{fontSize: '40px', fontWeight:'700', marginTop: indexNum > 0 ? '100px' : 0}">{{companyName}}</div>
            <div class="title-label" style="font-size: 46px; line-height: 60px;">混凝土配合比调整通知</div>
            <div align="center" class="lab-report">
                <div class="lab-sub-title">
                    <div class="top-title-container">
                            <div class="lab-sample-id" style="font-size: 15px; font-weight: 400;">生产日期:<span>{{ getTimeStr(erpRwdextra?.plantime)}} </span></div>
  
                        <div class="lab-report-id" style="font-size: 15px; font-weight: 400;">任务单编号:<span>{{ rwdextraObj?.frwno | isNull }} </span></div>
                        <div class="lab-report-id" style="font-size: 15px; font-weight: 400;">配合比通知单编号:<span>{{showYearNoStr(erpRwdextra?.rwdextraExtend?.mixProportionNo)}}</span></div>
                    </div>
                </div>

                <table  border="1" class="border" style="margin-bottom: 5px; margin-top: 10px;">
                    <tr height="42">
                        <td colspan="5" style="width: 18%; font-weight: 400; font-weight: 400;">用户单位</td>
                        <td colspan="15" style="width: 40%;font-weight: 400;">{{ erpRwdextra?.buildNameNew | isNull }}</td>
                        <td colspan="4" style="width: 13%;font-weight: 400;">配比编号</td>
                        <td colspan="5" style="width: 13%;font-weight: 400;">{{ rwdextraObj?.fphbNo | isNull }}</td>
                        <td colspan="4" style="width: 13%;font-weight: 400;">每盘容积</td>
                        <td colspan="5">/</td>
                    </tr>
                    <tr height="42">
                        <td colspan="5" style="width: 18%;font-weight: 400;">工程名称</td>
                        <td colspan="15" style="width: 40%;font-weight: 400;">{{ rwdextraObj?.fgcmc | isNull }}</td>
                        <td colspan="4" style="width: 13%;font-weight: 400;">强度等级</td>
                        <td colspan="5" style="width: 13%;font-weight: 400;">{{ mixProportion?.proportionQddj | isNull }}</td>
                        <td colspan="4" style="width: 13%;font-weight: 400;">坍落度(mm)</td>
                        <td colspan="5" style="width: 13%;font-weight: 400;">{{ getTLDValue(mixProportion?.proportionTld?.qz, mixProportion?.proportionTld?.hz) }}</td>
                    </tr>
                    <tr height="42">
                        <td colspan="5" style="width: 18%;font-weight: 400;">浇筑部位</td>
                        <td colspan="15" style="width: 40%;font-weight: 400;">{{ rwdextraObj?.fjzbw | isNull }}</td>
                        <td colspan="4" style="width: 13%;font-weight: 400;">水胶比</td>
                        <td colspan="5" style="width: 13%;font-weight: 400;">{{ mixProportion?.printSjb | isNull }}</td>
                        <td colspan="4" style="width: 13%;font-weight: 400;">砂率(%)</td>
                        <td colspan="5" style="width: 13%;font-weight: 400;">{{ mixProportion?.printSl | isNull  }}</td>
                    </tr>
                    <tr height="42">
                        
                        <td rowspan="6" colspan="1" style="width: 5%;font-weight: 400;">配<br />合<br />比</td>
                        <td colspan="4" style="width: 13%;font-weight: 400;">原材料</td>
                        <template v-for="(item, index) in topNameList" >
                            <td v-if="index != topNameList.length-1 || topNameList.length == 1 " :style="{width:topNameWidth,fontWeight: 400}" :colspan="topColWidth" :key="index" >
                            {{item | isNull}}
                            </td>
                            <td v-else  :style="{width:topNameWidth,fontWeight: 400}" :colspan="lastTopColWidth" :key="item">
                            {{item | isNull}}
                            </td>
                        </template>
                    </tr>
                    <tr height="42">
                        <td colspan="4" style="width: 13%;font-weight: 400;">规格</td>
                        <template  v-for="(item, index) in afterPhbDtoList" >
                            <td v-if="index != afterPhbDtoList.length-1 || afterPhbDtoList.length == 1 "  :style="{width:topNameWidth,fontWeight: 400}" :colspan="topColWidth" :key="index" >
                                {{getWlGGStr(item)}} 
                            </td>
                            <td v-else :style="{width:topNameWidth,fontWeight: 400}" :colspan="lastTopColWidth" :key="item">
                                {{getWlGGStr(item)}} 
                            </td>
                        </template>
                    </tr>
                    <tr height="42">
                        <td colspan="4" style="width: 13%;font-weight: 400;">每方配料</td>
                        <template  v-for="(item, index) in afterPhbDtoList" >
                            <td v-if="index != afterPhbDtoList.length-1 || afterPhbDtoList.length == 1 "  :style="{width:topNameWidth,fontWeight: 400}" :colspan="topColWidth" :key="index" >
                                {{ getYongLiangInt(item?.mpfyl, item?.cllx) }} 
                            </td>
                            <td v-else  :style="{width:topNameWidth,fontWeight: 400}" :colspan="lastTopColWidth" :key="item">
                                {{getYongLiangInt(item?.mpfyl, item?.cllx)}} 
                            </td>
                        </template>
                    </tr>
                    <tr height="42">
                        <td colspan="4" style="width: 13%;font-weight: 400;">含水率(%)</td>
                        <template  v-for="(item, index) in afterPhbDtoList" >
                            <td v-if="index != afterPhbDtoList.length-1 || afterPhbDtoList.length == 1 "  :style="{width:topNameWidth,fontWeight: 400}" :colspan="topColWidth" :key="index" >
                                {{item?.hsl | isNull}} 
                            </td>
                            <td v-else  :style="{width:topNameWidth,fontWeight: 400}" :colspan="lastTopColWidth" :key="item">
                                {{item?.hsl | isNull}} 
                            </td>
                        </template>
                    </tr>
                    <tr height="42">
                        <td colspan="4" style="width: 13%;font-weight: 400;">每方实际</td>
                        <template  v-for="(item, index) in afterPhbDtoList" >
                            <td v-if="index != afterPhbDtoList.length-1 || afterPhbDtoList.length == 1 "  :style="{width:topNameWidth,fontWeight: 400}" :colspan="topColWidth" :key="index" >
                                {{getMeiFangShiJi(item)}} 
                            </td>
                            <td v-else  :style="{width:topNameWidth,fontWeight: 400}" :colspan="lastTopColWidth" :key="item">
                                {{getMeiFangShiJi(item)}} 
                            </td>
                        </template>
                    </tr>
                </table>
                <table class="lab-sign" style="border: none; margin-top: 0; font-size: 15px;">
                    <tr height="42" style="border: none;">
                    <td style="border: none;">
                        <div class="sign-user" style="font-weight: 400;">
                        搅拌时间(秒)：{{mixProportion?.proportionJbsj | isNull}}
                        </div>
                    </td>
                    <td style="border: none;">
                        <div class="sign-user" style="font-weight: 400;">
                        试验员：
                        </div>
                    </td>
                    <td style="border: none;">
                        <div class="sign-user" style="justify-content: center;font-weight: 400;">
                        复核员：
                        </div>
                    </td>
                    </tr>
                </table>
        
            <div style="margin-top: 0px; font-size: 20px; font-weight: 400; text-align: left">混凝土配合比调整记录：</div>
            <!-- 混凝土配合比调整记录 -->
            <table border="1" class="border" style="margin-bottom: 20px; margin-top: 10px; font-size: 14px">
                <tr height="42">
                    <td rowspan="3" colspan="2" style="width: 20%;font-weight: 400;">调整时间</td>
                    <td colspan="21" style="font-weight: 400;">调整后的每方配合比（Kg）</td>
                </tr>
                <tr height="42">
                    <template  v-for="(item, index) in bottomNameList" >
                            <td :style="{width: bottomNameWidth,fontWeight: 400}" v-if="index != bottomNameList.length-1 || bottomNameList.length == 1 " :colspan="bottomColWidth" :key="index" >
                                {{item | isNull}}
                            </td>
                            <td :style="{width: bottomNameWidth,fontWeight: 400}" v-else :colspan="lastBottomColWidth" :key="item">
                                {{item | isNull}}
                            </td>
                        </template>

                    <td colspan="1" rowspan="2" style="width: 10%;font-weight: 400;">试验员</td>
                </tr>
                <tr height="42">
                    <template  v-for="(item, index) in afterPhbList" >
                            <td :style="{width: bottomNameWidth,fontWeight: 400}" v-if="index != afterPhbList.length-1 || afterPhbList.length == 1 " :colspan="bottomColWidth" :key="index" >
                                 <!-- {{item?.wlgg | isNull}} -->
                                 {{getWlGGStr(item)}} 
                            </td>
                            <td :style="{width: bottomNameWidth,fontWeight: 400}" v-else :colspan="lastBottomColWidth" :key="item">
                                 <!-- {{item?.wlgg | isNull}} -->
                                 {{getWlGGStr(item)}} 
                            </td>
                        </template>
                  
                </tr>
                <!-- 下面是合并单元 -->
                <template v-for="(phbhistory, idxNum) in firstPageDataList">
                    <tr height="42" style="width: 100%;" :key="idxNum.toString()">
                    <template>
                        <td colspan="1" rowspan="3" style="width: 10%;font-weight: 400">{{phbhistory.createtime | isNull}}<br/>【{{getTableStr(phbhistory?.mixtable)}}】</td>
                        <td colspan="1" style="width: 10%;font-weight: 400;">生产用量</td>
                        <template v-for="(obj, idx) in phbhistory.afterPhbDtoList">
                            <td :style="{width: bottomNameWidth,fontWeight: 400}" v-if="idx != phbhistory.afterPhbDtoList.length-1 || phbhistory.afterPhbDtoList.length == 1 " :colspan="bottomColWidth" :key="idx" >
                                {{obj?.tzhmpfyl | isNull}}
                            </td>
                            <td :style="{width: bottomNameWidth,fontWeight: 400}" v-else :colspan="lastBottomColWidth" :key="obj">
                                {{obj?.tzhmpfyl | isNull}}
                            </td>
                        </template>
                    </template>
                    <td colspan="1" rowspan="3" style="width: 10%;"></td>
                </tr>
                <tr height="42" style="width: 100%;" :key="idxNum.toString()">
                    <template>
                        <td colspan="1" style="width: 10%;font-weight: 400;">含水率(%)</td>
                        <template v-for="(obj, idx) in phbhistory.afterPhbDtoList">
                            <td :style="{width: bottomNameWidth,fontWeight: 400}" v-if="idx != phbhistory.afterPhbDtoList.length-1 || phbhistory.afterPhbDtoList.length == 1 " :colspan="bottomColWidth" :key="idx" >
                                {{obj?.hsl | isNull}}
                            </td>
                            <td :style="{width: bottomNameWidth,fontWeight: 400}" v-else :colspan="lastBottomColWidth" :key="obj">
                                {{obj?.hsl | isNull}}
                            </td>
                        </template>
                    </template>

                </tr>
                <tr height="42" :key="idxNum.toString()">
                    <template>
                        <td colspan="1" style="width: 10%;font-weight: 400;">调整用量</td>
                        <template v-for="(obj, idx) in phbhistory.afterPhbDtoList">
                            <td :style="{width: bottomNameWidth,fontWeight: 400}" v-if="idx != phbhistory.afterPhbDtoList.length-1 || phbhistory.afterPhbDtoList.length == 1 " :colspan="bottomColWidth" :key="idx" >
                                {{getMeiFangShiJi(obj)}}
                            </td>
                            <td :style="{width: bottomNameWidth,fontWeight: 400}" v-else :colspan="lastBottomColWidth" :key="obj">
                                {{getMeiFangShiJi(obj)}}
                            </td>
                        </template>
                    </template>
                </tr>
                </template>
                <!-- 下面是空数据 -->
                <template  v-if="firstPageDataList?.length <= 4 && firstPageDataList.length != 0">
                    <template v-for="idxNum in (4 - firstPageDataList.length)">
                        <tr height="42" style="width: 100%;" :key="idxNum.toString()">
                            <template>
                                <td colspan="1" rowspan="3" style="width: 10%;">{{''}}</td>
                                <td colspan="1" style="width: 10%;font-weight: 400;">生产用量</td>
                                <template v-for="idx in bottomNameList?.length">
                                    <td :style="{width: bottomNameWidth,fontWeight: 400}" v-if="idx != bottomNameList?.length || bottomNameList?.length == 1 " :colspan="bottomColWidth" :key="idx" >
                                        {{''}}
                                    </td>
                                    <td :style="{width: bottomNameWidth,fontWeight: 400}" v-else :colspan="lastBottomColWidth" :key="idx">
                                        {{''}}
                                    </td>
                                </template>
                            </template>
                            <td colspan="1" rowspan="3" style="width: 10%;"></td>
                        </tr>
                        <tr height="42" style="width: 100%;" :key="idxNum.toString()">
                            <template>
                                <td colspan="1" style="width: 10%;font-weight: 400;">含水率(%)</td>
                                <template v-for="idx in bottomNameList?.length">
                                    <td :style="{width: bottomNameWidth,fontWeight: 400}" v-if="idx != bottomNameList?.length || bottomNameList?.length == 1 " :colspan="bottomColWidth" :key="idx" >
                                        {{''}}
                                    </td>
                                    <td :style="{width: bottomNameWidth,fontWeight: 400}" v-else :colspan="lastBottomColWidth" :key="idx">
                                        {{''}}
                                    </td>
                                </template>
                            </template>

                        </tr>
                        <tr height="42" :key="idxNum.toString()">
                            <template>
                                <td colspan="1" style="width: 10%;font-weight: 400;">调整用量</td>
                                <template v-for="idx in bottomNameList?.length">
                                    <td :style="{width: bottomNameWidth,fontWeight: 400}" v-if="idx != bottomNameList?.length || bottomNameList?.length == 1 " :colspan="bottomColWidth" :key="idx" >
                                        {{''}}
                                    </td>
                                    <td :style="{width: bottomNameWidth,fontWeight: 400}" v-else :colspan="lastBottomColWidth" :key="idx">
                                        {{''}}
                                    </td>
                                </template>
                            </template>
                        </tr>

                    </template>
                </template>
                <template v-if="firstPageDataList?.length == 0">
                    <tr height="42" v-for="index in 12 " :key="index">
                        <td style="width: 10%" colspan="2" rowspan="3" v-if="(index-1) % 3 == 0">{{ '' }}</td>
                        <template v-for="(obj, idx) in bottomNameList.length">
                            <td :style="{width: bottomNameWidth,fontWeight: 400}" v-if="idx != bottomNameList.length" :colspan="bottomColWidth" :key="idx" >
                            {{''}}
                            </td>
                            <td :style="{width: bottomNameWidth,fontWeight: 400}" v-else :colspan="lastBottomColWidth" :key="obj">
                            {{''}}
                            </td>
                        </template>
                        <td style="width: 10%" colspan="2" rowspan="3" v-if="(index-1) % 3 == 0">{{ '' }}</td>
                    </tr>

                </template>
                
                <tr height="120" v-if="firstPageDataList.length <= 4">
                    <td colspan="2" style="font-weight: 400;">备&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;注</td>
                    <td colspan="22" style="width: 70%;font-weight: 400;" left></td>
                </tr>
            </table>
            <div style="margin-top: 0px; font-size: 14px; font-weight: 400;font-weight: 400; text-align: right">共{{pageNum}}页 当前第1页</div>

            <!-- 分割线条 -->
            <table v-if="phbhistoryList.length >= 5 && secondPageDataList?.length != 0">
                <tr height="60"></tr>
            </table>

            <!-- 第二页 table -->
            <table v-if="phbhistoryList.length >= 5 && secondPageDataList?.length != 0" border="1" class="border" style="margin-bottom: 20px; font-size: 14px; margin-top: 10px">
                <tr height="42">
                    <td rowspan="3" colspan="2" style="width: 20%;font-weight: 400;">调整时间</td>
                    <td colspan="21" style="font-weight: 400;">调整后的每方配合比（Kg）</td>
                </tr>
                <tr height="42">
                    <template  v-for="(item, index) in bottomNameList" >
                            <td :style="{width: bottomNameWidth,fontWeight: 400}" v-if="index != bottomNameList.length-1 || bottomNameList.length == 1 " :colspan="bottomColWidth" :key="index" >
                                {{item | isNull}}
                            </td>
                            <td :style="{width: bottomNameWidth,fontWeight: 400}" v-else :colspan="lastBottomColWidth" :key="item">
                                {{item | isNull}}
                            </td>
                        </template>

                    <td colspan="1" rowspan="2" style="width: 10%;font-weight: 400;">试验员</td>
                </tr>
                <tr height="42">
                    <template  v-for="(item, index) in afterPhbList" >
                            <td :style="{width: bottomNameWidth,fontWeight: 400}" v-if="index != afterPhbList.length-1 || afterPhbList.length == 1 " :colspan="bottomColWidth" :key="index" >
                                 <!-- {{item?.wlgg | isNull}} -->
                                 {{getWlGGStr(item)}} 
                            </td>
                            <td :style="{width: bottomNameWidth,fontWeight: 400}" v-else :colspan="lastBottomColWidth" :key="item">
                                 <!-- {{item?.wlgg | isNull}} -->
                                 {{getWlGGStr(item)}} 
                            </td>
                        </template>
                  
                </tr>
                <template v-for="(phbhistory, idxNum) in secondPageDataList">
                    <tr height="42" style="width: 100%;" :key="idxNum.toString()">
                    <template>
                        <td colspan="1" rowspan="3" style="width: 10%;">{{phbhistory?.createtime | isNull}}<br/>【{{getTableStr(phbhistory?.mixtable)}}】</td>
                        <td colspan="1" style="width: 10%;font-weight: 400;">生产用量</td>
                        <template v-for="(obj, idx) in phbhistory.afterPhbDtoList">
                            <td :style="{width: bottomNameWidth,fontWeight: 400}" v-if="idx != phbhistory.afterPhbDtoList.length-1 || phbhistory.afterPhbDtoList.length == 1 " :colspan="bottomColWidth" :key="idx" >
                                {{obj?.tzhmpfyl | isNull}}
                            </td>
                            <td :style="{width: bottomNameWidth,fontWeight: 400}" v-else :colspan="lastBottomColWidth" :key="obj">
                                {{obj?.tzhmpfyl | isNull}}
                            </td>
                        </template>
                    </template>
                    <td colspan="1" rowspan="3" style="width: 10%;"></td>
                </tr>
                <tr height="42" style="width: 100%;" :key="idxNum.toString()">
                    <template>
                        <td colspan="1" style="width: 10%;font-weight: 400;">含水率(%)</td>
                        <template v-for="(obj, idx) in phbhistory.afterPhbDtoList">
                            <td :style="{width: bottomNameWidth,fontWeight: 400}" v-if="idx != phbhistory.afterPhbDtoList.length-1 || phbhistory.afterPhbDtoList.length == 1 " :colspan="bottomColWidth" :key="idx" >
                                {{obj?.hsl | isNull}}
                            </td>
                            <td :style="{width: bottomNameWidth,fontWeight: 400}" v-else :colspan="lastBottomColWidth" :key="obj">
                                {{obj?.hsl | isNull}}
                            </td>
                        </template>
                    </template>

                </tr>
                <tr height="42" :key="idxNum.toString()">
                    <template>
                        <td colspan="1" style="width: 10%;font-weight: 400;">调整用量</td>
                        <template v-for="(obj, idx) in phbhistory.afterPhbDtoList">
                            <td :style="{width: bottomNameWidth,fontWeight: 400}" v-if="idx != phbhistory.afterPhbDtoList.length-1 || phbhistory.afterPhbDtoList.length == 1 " :colspan="bottomColWidth" :key="idx" >
                                {{getMeiFangShiJi(obj)}}
                            </td>
                            <td :style="{width: bottomNameWidth,fontWeight: 400}" v-else :colspan="lastBottomColWidth" :key="obj">
                                {{getMeiFangShiJi(obj)}}
                            </td>
                        </template>
                    </template>
                </tr>
                </template>
                <!-- 空数据 -->
                <template  v-if="secondPageDataList?.length <= 9 && secondPageDataList.length != 0">
                    <template v-for="idxNum in (9 - secondPageDataList.length)">
                        <tr height="42" style="width: 100%;" :key="idxNum.toString()">
                            <template>
                                <td colspan="1" rowspan="3" style="width: 10%;">{{''}}</td>
                                <td colspan="1" style="width: 10%;font-weight: 400;">生产用量</td>
                                <template v-for="idx in bottomNameList?.length">
                                    <td :style="{width: bottomNameWidth,fontWeight: 400}" v-if="idx != bottomNameList?.length || bottomNameList?.length == 1 " :colspan="bottomColWidth" :key="idx" >
                                        {{''}}
                                    </td>
                                    <td :style="{width: bottomNameWidth,fontWeight: 400}" v-else :colspan="lastBottomColWidth" :key="idx">
                                        {{''}}
                                    </td>
                                </template>
                            </template>
                            <td colspan="1" rowspan="3" style="width: 10%;"></td>
                        </tr>
                        <tr height="42" style="width: 100%;" :key="idxNum.toString()">
                            <template>
                                <td colspan="1" style="width: 10%;font-weight: 400;">含水率(%)</td>
                                <template v-for="idx in bottomNameList?.length">
                                    <td :style="{width: bottomNameWidth,fontWeight: 400}" v-if="idx != bottomNameList?.length || bottomNameList?.length == 1 " :colspan="bottomColWidth" :key="idx" >
                                        {{''}}
                                    </td>
                                    <td :style="{width: bottomNameWidth,fontWeight: 400}" v-else :colspan="lastBottomColWidth" :key="idx">
                                        {{''}}
                                    </td>
                                </template>
                            </template>

                        </tr>
                        <tr height="42" :key="idxNum.toString()">
                            <template>
                                <td colspan="1" style="width: 10%;font-weight: 400;">调整用量</td>
                                <template v-for="idx in bottomNameList?.length">
                                    <td :style="{width: bottomNameWidth,fontWeight: 400}" v-if="idx != bottomNameList?.length || bottomNameList?.length == 1 " :colspan="bottomColWidth" :key="idx" >
                                        {{''}}
                                    </td>
                                    <td :style="{width: bottomNameWidth,fontWeight: 400}" v-else :colspan="lastBottomColWidth" :key="idx">
                                        {{''}}
                                    </td>
                                </template>
                            </template>
                        </tr>

                    </template>
                </template>
                <tr height="120" v-if="secondPageDataList.length <= 9 && phbhistoryList.length >= 5 && secondPageDataList?.length != 0">
                    <td colspan="2" style="font-weight: 400;">备&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;注</td>
                    <td colspan="22" style="width: 70%;font-weight: 400;" left></td>
                </tr>
            </table>

            <div v-if="phbhistoryList.length >= 5 && secondPageDataList?.length != 0" style="margin-top: 0px; font-size: 14px; font-weight: 400; text-align: right;">共{{pageNum}}页 当前第2页</div>
            <!-- 分割线条 -->
            <table v-if="phbhistoryList.length >= 15 && thirdPageDataList?.length != 0">
                <tr height="90"></tr>
            </table>
            <!-- 第三页 table -->
            <table v-if="phbhistoryList.length >= 15 && thirdPageDataList?.length != 0" border="1" class="border" style="margin-bottom: 20px; font-size: 14px; margin-top: 10px">
                <tr height="42">
                    <td rowspan="3" colspan="2" style="width: 20%;font-weight: 400;">调整时间</td>
                    <td colspan="21" style="font-weight: 400;">调整后的每方配合比（Kg）</td>
                </tr>
                <tr height="42">
                    <template  v-for="(item, index) in bottomNameList" >
                            <td :style="{width: bottomNameWidth,fontWeight: 400}" v-if="index != bottomNameList.length-1 || bottomNameList.length == 1 " :colspan="bottomColWidth" :key="index" >
                                {{item | isNull}}
                            </td>
                            <td :style="{width: bottomNameWidth,fontWeight: 400}" v-else :colspan="lastBottomColWidth" :key="item">
                                {{item | isNull}}
                            </td>
                        </template>

                    <td colspan="1" rowspan="2" style="width: 10%;font-weight: 400;">试验员</td>
                </tr>
                <tr height="42">
                    <template  v-for="(item, index) in afterPhbList" >
                            <td :style="{width: bottomNameWidth,fontWeight: 400}" v-if="index != afterPhbList.length-1 || afterPhbList.length == 1 " :colspan="bottomColWidth" :key="index" >
                                 <!-- {{item?.wlgg | isNull}} -->
                                 {{getWlGGStr(item)}} 
                            </td>
                            <td :style="{width: bottomNameWidth,fontWeight: 400}" v-else :colspan="lastBottomColWidth" :key="item">
                                 <!-- {{item?.wlgg | isNull}} -->
                                 {{getWlGGStr(item)}} 
                            </td>
                        </template>
                  
                </tr>
                <template v-for="(phbhistory, idxNum) in thirdPageDataList">
                    <tr height="42" style="width: 100%;" :key="idxNum.toString()">
                    <template>
                        <td colspan="1" rowspan="3" style="width: 10%;font-weight: 400">{{phbhistory.createtime | isNull}} <br/>【{{getTableStr(phbhistory?.mixtable)}}】</td>
                        <td colspan="1" style="width: 10%;font-weight: 400;">生产用量</td>
                        <template v-for="(obj, idx) in phbhistory.afterPhbDtoList">
                            <td :style="{width: bottomNameWidth,fontWeight: 400}" v-if="idx != phbhistory.afterPhbDtoList.length-1 || phbhistory.afterPhbDtoList.length == 1 " :colspan="bottomColWidth" :key="idx" >
                                {{obj?.tzhmpfyl | isNull}}
                            </td>
                            <td :style="{width: bottomNameWidth,fontWeight: 400}" v-else :colspan="lastBottomColWidth" :key="obj">
                                {{obj?.tzhmpfyl | isNull}}
                            </td>
                        </template>
                    </template>
                    <td colspan="1" rowspan="3" style="width: 10%;"></td>
                </tr>
                <tr height="42" style="width: 100%;" :key="idxNum.toString()">
                    <template>
                        <td colspan="1" style="width: 10%;font-weight: 400;">含水率(%)</td>
                        <template v-for="(obj, idx) in phbhistory.afterPhbDtoList">
                            <td :style="{width: bottomNameWidth,fontWeight: 400}" v-if="idx != phbhistory.afterPhbDtoList.length-1 || phbhistory.afterPhbDtoList.length == 1 " :colspan="bottomColWidth" :key="idx" >
                                {{obj?.hsl | isNull}}
                            </td>
                            <td :style="{width: bottomNameWidth,fontWeight: 400}" v-else :colspan="lastBottomColWidth" :key="obj">
                                {{obj?.hsl | isNull}}
                            </td>
                        </template>
                    </template>

                </tr>
                <tr height="42" :key="idxNum.toString()">
                    <template>
                        <td colspan="1" style="width: 10%;font-weight: 400;">调整用量</td>
                        <template v-for="(obj, idx) in phbhistory.afterPhbDtoList">
                            <td :style="{width: bottomNameWidth,fontWeight: 400}" v-if="idx != phbhistory.afterPhbDtoList.length-1 || phbhistory.afterPhbDtoList.length == 1 " :colspan="bottomColWidth" :key="idx" >
                                {{getMeiFangShiJi(obj)}}
                            </td>
                            <td :style="{width: bottomNameWidth,fontWeight: 400}" v-else :colspan="lastBottomColWidth" :key="obj">
                                {{getMeiFangShiJi(obj)}}
                            </td>
                        </template>
                    </template>
                </tr>
                </template>
                <!-- 空数据 -->
                <template  v-if="thirdPageDataList?.length <= 9 && thirdPageDataList.length != 0">
                    <template v-for="idxNum in (9 - thirdPageDataList.length)">
                        <tr height="42" style="width: 100%;" :key="idxNum.toString()">
                            <template>
                                <td colspan="1" rowspan="3" style="width: 10%;">{{''}}</td>
                                <td colspan="1" style="width: 10%;font-weight: 400;">生产用量</td>
                                <template v-for="idx in bottomNameList?.length">
                                    <td :style="{width: bottomNameWidth,fontWeight: 400}" v-if="idx != bottomNameList?.length || bottomNameList?.length == 1 " :colspan="bottomColWidth" :key="idx" >
                                        {{''}}
                                    </td>
                                    <td :style="{width: bottomNameWidth,fontWeight: 400}" v-else :colspan="lastBottomColWidth" :key="idx">
                                        {{''}}
                                    </td>
                                </template>
                            </template>
                            <td colspan="1" rowspan="3" style="width: 10%;"></td>
                        </tr>
                        <tr height="42" style="width: 100%;" :key="idxNum.toString()">
                            <template>
                                <td colspan="1" style="width: 10%;font-weight: 400;">含水率(%)</td>
                                <template v-for="idx in bottomNameList?.length">
                                    <td :style="{width: bottomNameWidth,fontWeight: 400}" v-if="idx != bottomNameList?.length || bottomNameList?.length == 1 " :colspan="bottomColWidth" :key="idx" >
                                        {{''}}
                                    </td>
                                    <td :style="{width: bottomNameWidth,fontWeight: 400}" v-else :colspan="lastBottomColWidth" :key="idx">
                                        {{''}}
                                    </td>
                                </template>
                            </template>

                        </tr>
                        <tr height="42" :key="idxNum.toString()">
                            <template>
                                <td colspan="1" style="width: 10%;font-weight: 400;">调整用量</td>
                                <template v-for="idx in bottomNameList?.length">
                                    <td :style="{width: bottomNameWidth,fontWeight: 400}" v-if="idx != bottomNameList?.length || bottomNameList?.length == 1 " :colspan="bottomColWidth" :key="idx" >
                                        {{''}}
                                    </td>
                                    <td :style="{width: bottomNameWidth,fontWeight: 400}" v-else :colspan="lastBottomColWidth" :key="idx">
                                        {{''}}
                                    </td>
                                </template>
                            </template>
                        </tr>

                    </template>
                </template>

                <!-- <template v-if="thirdPageDataList?.length == 0">
                    <tr height="42" v-for="index in 27 " :key="index">
                        <td style="width: 10%" colspan="2" rowspan="3" v-if="(index-1) % 3 == 0">{{ '' }}</td>
                        <template v-for="(obj, idx) in bottomNameList.length">
                            <td :style="{width: bottomNameWidth}" v-if="idx != bottomNameList.length" :colspan="bottomColWidth" :key="idx" >
                            {{''}}
                            </td>
                            <td :style="{width: bottomNameWidth}" v-else :colspan="lastBottomColWidth" :key="obj">
                            {{''}}
                            </td>
                        </template>
                        <td style="width: 10%" colspan="2" rowspan="3" v-if="(index-1) % 3 == 0">{{ '' }}</td>
                    </tr>

                </template>
                
                <tr height="120" v-if="secondPageDataList.length <= 10 && phbhistoryList.length <= 24">
                    <td colspan="2">备&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;注</td>
                    <td colspan="22" style="width: 70%;" left></td>
                </tr> -->
                <tr height="120" v-if="secondPageDataList.length <= 10 && phbhistoryList.length <= 24 && thirdPageDataList?.length != 0">
                    <td colspan="2" style="font-weight: 400;">备&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;注</td>
                    <td colspan="22" style="width: 70%;font-weight: 400;" left></td>
                </tr>
            </table>
            <div v-if="phbhistoryList.length >= 15 && thirdPageDataList?.length != 0" style="margin-top: 0px; font-size: 14px; font-weight: 400; text-align: right;">共{{pageNum}}页 当前第3页</div>

            <!-- 分割线条 -->
            <table v-if="phbhistoryList.length >= 25 && fourPageDataList?.length != 0">
                <tr height="90"></tr>
            </table>
            <!-- 第四页 table -->
            <table v-if="phbhistoryList.length >= 25 && fourPageDataList?.length != 0" border="1" class="border" style="margin-bottom: 20px; font-size: 14px; margin-top: 10px">
                <tr height="42">
                    <td rowspan="3" colspan="2" style="width: 20%;font-weight: 400;">调整时间</td>
                    <td colspan="21" style="font-weight: 400;">调整后的每方配合比（Kg）</td>
                </tr>
                <tr height="42">
                    <template  v-for="(item, index) in bottomNameList" >
                            <td :style="{width: bottomNameWidth,fontWeight: 400}" v-if="index != bottomNameList.length-1 || bottomNameList.length == 1 " :colspan="bottomColWidth" :key="index" >
                                {{item | isNull}}
                            </td>
                            <td :style="{width: bottomNameWidth,fontWeight: 400}" v-else :colspan="lastBottomColWidth" :key="item">
                                {{item | isNull}}
                            </td>
                        </template>

                    <td colspan="1" rowspan="2" style="width: 10%;font-weight: 400;">试验员</td>
                </tr>
                <tr height="42">
                    <template  v-for="(item, index) in afterPhbList" >
                            <td :style="{width: bottomNameWidth,fontWeight: 400}" v-if="index != afterPhbList.length-1 || afterPhbList.length == 1 " :colspan="bottomColWidth" :key="index" >
                                 <!-- {{item?.wlgg | isNull}} -->
                                 {{getWlGGStr(item)}} 
                            </td>
                            <td :style="{width: bottomNameWidth,fontWeight: 400}" v-else :colspan="lastBottomColWidth" :key="item">
                                 <!-- {{item?.wlgg | isNull}} -->
                                 {{getWlGGStr(item)}} 
                            </td>
                        </template>
                  
                </tr>
                <template v-for="(phbhistory, idxNum) in fourPageDataList">
                    <tr height="42" style="width: 100%;" :key="idxNum.toString()">
                    <template>
                        <td colspan="1" rowspan="3" style="width: 10%;font-weight: 400">{{phbhistory.createtime | isNull}}<br/>【{{getTableStr(phbhistory?.mixtable)}}】</td>
                        <td colspan="1" style="width: 10%;font-weight: 400;">生产用量</td>
                        <template v-for="(obj, idx) in phbhistory.afterPhbDtoList">
                            <td :style="{width: bottomNameWidth,fontWeight: 400}" v-if="idx != phbhistory.afterPhbDtoList.length-1 || phbhistory.afterPhbDtoList.length == 1 " :colspan="bottomColWidth" :key="idx" >
                                {{obj?.tzhmpfyl | isNull}}
                            </td>
                            <td :style="{width: bottomNameWidth,fontWeight: 400}" v-else :colspan="lastBottomColWidth" :key="obj">
                                {{obj?.tzhmpfyl | isNull}}
                            </td>
                        </template>
                    </template>
                    <td colspan="1" rowspan="3" style="width: 10%;"></td>
                </tr>
                <tr height="42" style="width: 100%;" :key="idxNum.toString()">
                    <template>
                        <td colspan="1" style="width: 10%;font-weight: 400;">含水率(%)</td>
                        <template v-for="(obj, idx) in phbhistory.afterPhbDtoList">
                            <td :style="{width: bottomNameWidth,fontWeight: 400}" v-if="idx != phbhistory.afterPhbDtoList.length-1 || phbhistory.afterPhbDtoList.length == 1 " :colspan="bottomColWidth" :key="idx" >
                                {{obj?.hsl | isNull}}
                            </td>
                            <td :style="{width: bottomNameWidth,fontWeight: 400}" v-else :colspan="lastBottomColWidth" :key="obj">
                                {{obj?.hsl | isNull}}
                            </td>
                        </template>
                    </template>

                </tr>
                <tr height="42" :key="idxNum.toString()">
                    <template>
                        <td colspan="1" style="width: 10%;font-weight: 400;">调整用量</td>
                        <template v-for="(obj, idx) in phbhistory.afterPhbDtoList">
                            <td :style="{width: bottomNameWidth,fontWeight: 400}" v-if="idx != phbhistory.afterPhbDtoList.length-1 || phbhistory.afterPhbDtoList.length == 1 " :colspan="bottomColWidth" :key="idx" >
                                {{getMeiFangShiJi(obj)}}
                            </td>
                            <td :style="{width: bottomNameWidth,fontWeight: 400}" v-else :colspan="lastBottomColWidth" :key="obj">
                                {{getMeiFangShiJi(obj)}}
                            </td>
                        </template>
                    </template>
                </tr>
                </template>
                <!-- 空数据 -->
                <template  v-if="fourPageDataList?.length <= 9 && fourPageDataList.length != 0">
                    <template v-for="idxNum in (9 - fourPageDataList.length)">
                        <tr height="42" style="width: 100%;" :key="idxNum.toString()">
                            <template>
                                <td colspan="1" rowspan="3" style="width: 10%;">{{''}}</td>
                                <td colspan="1" style="width: 10%;font-weight: 400;">调整用量</td>
                                <template v-for="idx in bottomNameList?.length">
                                    <td :style="{width: bottomNameWidth,fontWeight: 400}" v-if="idx != bottomNameList?.length || bottomNameList?.length == 1 " :colspan="bottomColWidth" :key="idx" >
                                        {{''}}
                                    </td>
                                    <td :style="{width: bottomNameWidth,fontWeight: 400}" v-else :colspan="lastBottomColWidth" :key="idx">
                                        {{''}}
                                    </td>
                                </template>
                            </template>
                            <td colspan="1" rowspan="3" style="width: 10%;"></td>
                        </tr>
                        <tr height="42" style="width: 100%;" :key="idxNum.toString()">
                            <template>
                                <td colspan="1" style="width: 10%;font-weight: 400;">含水率(%)</td>
                                <template v-for="idx in bottomNameList?.length">
                                    <td :style="{width: bottomNameWidth,fontWeight: 400}" v-if="idx != bottomNameList?.length || bottomNameList?.length == 1 " :colspan="bottomColWidth" :key="idx" >
                                        {{''}}
                                    </td>
                                    <td :style="{width: bottomNameWidth,fontWeight: 400}" v-else :colspan="lastBottomColWidth" :key="idx">
                                        {{''}}
                                    </td>
                                </template>
                            </template>

                        </tr>
                        <tr height="42" :key="idxNum.toString()">
                            <template>
                                <td colspan="1" style="width: 10%;font-weight: 400;">生产用量</td>
                                <template v-for="idx in bottomNameList?.length">
                                    <td :style="{width: bottomNameWidth,fontWeight: 400}" v-if="idx != bottomNameList?.length || bottomNameList?.length == 1 " :colspan="bottomColWidth" :key="idx" >
                                        {{''}}
                                    </td>
                                    <td :style="{width: bottomNameWidth,fontWeight: 400}" v-else :colspan="lastBottomColWidth" :key="idx">
                                        {{''}}
                                    </td>
                                </template>
                            </template>
                        </tr>

                    </template>
                </template>

                <!-- <template v-if="fourPageDataList?.length == 0">
                    <tr height="42" v-for="index in 27 " :key="index">
                        <td style="width: 10%" colspan="2" rowspan="3" v-if="(index-1) % 3 == 0">{{ '' }}</td>
                        <template v-for="(obj, idx) in bottomNameList.length">
                            <td :style="{width: bottomNameWidth}" v-if="idx != bottomNameList.length" :colspan="bottomColWidth" :key="idx" >
                            {{''}}
                            </td>
                            <td :style="{width: bottomNameWidth}" v-else :colspan="lastBottomColWidth" :key="obj">
                            {{''}}
                            </td>
                        </template>
                        <td style="width: 10%" colspan="2" rowspan="3" v-if="(index-1) % 3 == 0">{{ '' }}</td>
                    </tr>

                </template>
                
                <tr height="120" v-if="phbhistoryList.length >= 25">
                    <td colspan="2">备&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;注</td>
                    <td colspan="22" style="width: 70%;" left></td>
                </tr> -->
                <tr height="120" v-if="phbhistoryList.length >= 25 && fourPageDataList?.length != 0">
                    <td colspan="2" style="font-weight: 400;">备&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;注</td>
                    <td colspan="22" style="width: 70%;font-weight: 400;" left></td>
                </tr>
            </table>
            <div v-if="phbhistoryList.length >= 25 && fourPageDataList?.length != 0" style="margin-top: 0px; font-size: 14px; font-weight: 400;font-weight: 400; text-align: right;">共{{pageNum}}页 当前第4页</div>
        </div>
        
    </div>
    
</template>

<script>
import {calcEquation} from "@/utils/calculate.js"
import { hntTaskMixed } from './hntTaskMixed';

export default {
    mixins: [hntTaskMixed],
    props: {
        companyName: {
            type: String,
            default: ""
        },
        indexNum:{
            type: Number,
            default:0
        },
        // 包含调整时间的list
        phbhistoryList: {
            type: Array,
            default: () => { return [] }
        },
        // 配合比调整通知上面部分数据list 
        afterPhbDtoList: {
            type: Array,
            default: () => { return [] }
        }
    },

    data() {
        return {

            phbhistoryListCount:0,

            firstPageDataList:[],
            secondPageDataList:[],
            thirdPageDataList:[],
            fourPageDataList:[],
            pageNum: 1,

            phbhistoryObj: {},
            topNameList:['粉煤灰','水','水泥','外加剂','石','砂','矿渣粉'],
            topNameWidth:'82%',

            topColWidth:33,
            lastTopColWidth:3,
            bottomNameWidth:'70%',

            // 调整记录
            bottomNameList:['粉煤灰','水','水泥','外加剂','石','砂','矿渣粉'],
            afterPhbList: [],
            lastBottomColWidth:3,
            bottomColWidth:21,
            isLastCol:false,

        }
    },

    mounted() {
        this.handelPageList();

        this.topColWidth = ((33 / (this.topNameList.length)));
        this.lastTopColWidth = 33 - (this.topColWidth) * (this.topNameList.length - 1);
        this.topNameWidth = (82 / this.topNameList.length) + '%'
        this.bottomColWidth = ((21 / (this.bottomNameList.length)));
        this.lastBottomColWidth = 21 - (this.bottomColWidth) * (this.bottomNameList.length - 1);
        this.bottomNameWidth = (70 / this.bottomNameList.length) + '%'
        if (this.phbhistoryList.length > 0) {
            this.phbhistoryListCount = this.phbhistoryList.length % 2 == 0 ? this.phbhistoryList.length : this.phbhistoryList.length + 1;
            this.bottomNameList = [];
            this.phbhistoryObj = this.phbhistoryList[0];
            this.afterPhbList = this.phbhistoryObj?.afterPhbDtoList;
            let phbDtoList = this.phbhistoryObj?.afterPhbDtoList || []
            phbDtoList.forEach((item, index)=>{
                var name = item?.cllx || '';
                var clmc = item?.clmc || ''
                if(name == '粗骨料'){
                    name = '石'
                }
                if(name == '细骨料'){
                    name = '砂'
                }
                if(clmc.includes('纤维')){
                    name = '外掺料2'
                }

                if(clmc.includes('膨胀剂')){
                    name = '外掺料1'
                }
                this.bottomNameList.push(name || '/');
                console.log('底部' + item);
            });
            this.bottomNameWidth = (70 / this.bottomNameList.length) + '%'
            this.bottomColWidth = ((21 / (this.bottomNameList.length)));
            console.log('底部表格宽度：', this.bottomNameWidth);
            console.log('底部表格宽度col：', this.bottomColWidth);
            this.lastBottomColWidth = 21 - (this.bottomColWidth) * (this.bottomNameList.length - 1);
            if(this.lastBottomColWidth % 1 !== 0){
                this.lastBottomColWidth = parseInt(this.lastBottomColWidth) + 1
            }

            console.log('底部表格宽度：', this.bottomNameWidth);
            console.log('底部表格宽度col：', this.bottomColWidth);
            console.log('底部表格宽度last：', this.lastBottomColWidth);
            if( this.bottomNameList.length == 1) {
                this.bottomColWidth = 21
                this.bottomNameWidth = '70%'
                this.lastBottomColWidth = 0
            }
        } else {

            
            this.bottomNameList.forEach((item, index)=>{
                this.afterPhbList.push({
                        "clmc": item,
                        "clgg": "",
                        "wlmc": "",
                        "wlgg": "",
                        "hsl": "",
                        "mpfyl": "",
                        "tzhmpfyl": ''
                    })
            });
        }


        if (this.afterPhbDtoList.length > 0) {
            this.topNameList = [];
            this.afterPhbDtoList.forEach((item, index)=>{
                console.log('头部' + item);
                var name = item?.cllx || '';
                var clmc = item?.clmc || ''
                if(name == '粗骨料'){
                    name = '石'
                }
                if(name == '细骨料'){
                    name = '砂'
                }
                if(clmc.includes('纤维')){
                    name = '外掺料2'
                }
                if(clmc.includes('膨胀剂')){
                    name = '外掺料1'
                }
                this.topNameList.push(name || '/');
            });
            this.topNameWidth = (82 / this.topNameList.length) + '%'
            this.topColWidth = (33 / (this.topNameList.length));
            this.lastTopColWidth = 33 - (this.topColWidth) * (this.topNameList.length - 1);
            if(this.lastTopColWidth % 1 !== 0){
                this.lastTopColWidth = parseInt(this.lastTopColWidth) + 1
            }
            if( this.topNameList.length == 1) {
                this.topNameWidth = '82%'
                this.topColWidth = 33
                this.lastTopColWidth = 0
            }
        } else {

            this.topNameList.forEach((item, index)=>{
                this.afterPhbDtoList.push({
                        "clgg": "",
                        "wlmc": "",
                        "wlgg": "",
                        "hsl": "",
                        "mpfyl": "",
                        "tzhmpfyl": ''
                    })
            })
        }

    },
    methods:{
        showYearNoStr(no, creatTime){
            console.log('creatTime：', creatTime);
            let timeList = creatTime?.split('-');
            let year = '';
            if(timeList && timeList.length > 0){
                year = timeList[0].slice(-2);
                console.log('year：', year);
            }
            if(this.isEmpty(no)) return '---';
            return year + no;

        },

        handelPageList(){

            if(this.phbhistoryList.length <= 5){
                // 第一页
                this.firstPageDataList = this.phbhistoryList
                // this.pageNum = this.phbhistoryList.length == 5 ? 2 : 1
                this.pageNum =  1
            } else {
                // 第二页
                this.pageNum = 2
                this.firstPageDataList = this.phbhistoryList.slice(0, 5);
                // 创建原数组的副本
                const array = [...this.phbhistoryList];
                array.splice(0, 5);
                if(array.length <= 10){
                    this.secondPageDataList = array;
                    // this.pageNum = array.length < 10 ? 2 : 3
                } else {
                    // 第三页
                    this.pageNum = 3;
                    this.secondPageDataList = array.slice(0, 10);
                    // 创建原数组的副本
                    const thirdList = [...array];
                    thirdList.splice(0, 10);
                    if(thirdList.length <= 10){
                        // this.pageNum = thirdList.length < 10 ? 3 : 4
                        this.thirdPageDataList = thirdList;
                    } else {
                        this.pageNum = 4;
                        this.thirdPageDataList = thirdList.slice(0, 10);
                        this.fourPageDataList = thirdList.splice(0, 10)

                    }
                }
            }
        },

        getYongLiangInt(str, cllx){
            if(this.isEmpty(str)) return '/'
            return cllx.includes('外加剂') ? str : calcEquation([
              {
                v: str,
              },
              {
                k: '+',
                v: '0',
              },
            ], 0);
        },
        changeLastCol(index, length){
            this.isLastCol = index == length;
        },

        getMeiFangShiJiFirst(obj){
            var result = '/';
            let name = obj?.cllx || '';
            let clgg = obj?.clgg || '';
            if(clgg == '清水'){
                var totalZl = 0;
                this.afterPhbDtoList.forEach((item)=>{
                    let cllx = item?.cllx || '';
                    if(cllx !== '水'){
                        totalZl = totalZl + parseFloat(item?.dyphbtzhmpfyl || '0') * parseFloat(item?.hsl || '0') / 100
                    }
                });
                let resp = calcEquation([
                    {
                        v: obj.dyphbtzhmpfyl,
                    },
                    {
                        k: '-',
                        v: totalZl,
                    },
                    ], 0);
                    result = resp == 0 ? '/' : resp

            } else if(name.includes('外加剂')){
                let hsl = obj?.hsl || 0;
                let dyphbtzhmpfyl = obj?.dyphbtzhmpfyl || '';
                result = hsl == 0 ? dyphbtzhmpfyl : parseFloat(dyphbtzhmpfyl) * (1+parseFloat(hsl) / 100) ;
            } else {
                let hsl = obj?.hsl || 0;
                let dyphbtzhmpfyl = obj?.dyphbtzhmpfyl || '';
                let secondNum = 1+parseFloat(hsl || 0) / 100;
                result = hsl == 0 ? dyphbtzhmpfyl : calcEquation([
                    {
                        v: dyphbtzhmpfyl,
                    },
                    {
                        k: '*',
                        v: secondNum,
                    },
                    ], 0);
            }
            return this.isEmpty(result) ? '/' : result;

        },

        getTableStr(str){
            if(this.isEmpty(str) || str == '0'){
                return '*';
            }
            return str + '拌台';
        },

        getMeiFangShiJi(obj){
            var result = '/';
            let name = obj?.cllx || '';
            let clgg = obj?.clgg || '';

            if(this.isEmpty(obj.tzhmpfyl)){
                return result;
            }
            if(clgg == '清水'){
                var totalZl = 0;
                this.afterPhbDtoList.forEach((item)=>{
                    let cllx = item?.cllx || '';
                    if(cllx !== '水'){
                        totalZl = totalZl + parseFloat(item?.tzhmpfyl || '0') * parseFloat(item?.hsl || '0') / 100
                    }
                });
                result = calcEquation([
                    {
                        v: obj.tzhmpfyl,
                    },
                    {
                        k: '-',
                        v: totalZl,
                    },
                    ], 0);

            } else if(name.includes('外加剂')){
                let hsl = obj?.hsl || 0;
                let tzhmpfyl = obj?.tzhmpfyl || '';
                result = hsl == 0 ? tzhmpfyl : parseFloat(tzhmpfyl) * (1+parseFloat(hsl) / 100) ;
            } else {
                let hsl = obj?.hsl || 0;
                let tzhmpfyl = obj?.tzhmpfyl || '';
                let secondNum = 1+parseFloat(hsl || 0) / 100;
                result = hsl == 0 ? tzhmpfyl : calcEquation([
                    {
                        v: tzhmpfyl,
                    },
                    {
                        k: '*',
                        v: secondNum,
                    },
                    ], 0);
            }
            return this.isEmpty(result) ? '/' : result;

        },
        getTLDValue(val1, val2){
            let tld1 = val1.replace(new RegExp('mm', 'g'), '');
            let tld2 = val2.replace(new RegExp('mm', 'g'), '');
            return tld1 + '±' + tld2
        },

        getTimeStr(time){
            if(!this.isEmpty(time)){
                return time.length > 10 ? time?.substring(0,10) : time
            }
            return '/';
        },
        getWlGGStr(obj){
            var result = '/';
            let name = obj?.wlmc || '';
            let clmc = obj?.clmc || '';
            // 外掺料1
            if(clmc.includes('膨胀剂')){
                result = obj?.wlmc  + obj?.wlgg;
                if(this.isEmpty(result)) result = '/'
                return result;
                // 外掺料2
            }
            if(clmc.includes('纤维')){
                result = obj?.clgg;

                if(this.isEmpty(result)) result = '/'
                return result;
            }
            if(name.includes('外加剂') || name.includes('水泥')){
                result = obj?.wlgg ;

                if(this.isEmpty(result)) result = '/'
                return result;
            } 
            if(name.includes('石') || name.includes('砂')){
                var resp = obj?.clmc + obj?.clgg;
                result = name.includes('石') ? resp.replace('mm', '') : resp;
                if(this.isEmpty(result)) result = '/'
                return result;
            } 
                result = obj?.clgg;
            
            if(this.isEmpty(result)) result = '/'
            return result;


        },

        isEmpty(val) {
            if (typeof val === "boolean") {
                return false;
            }
            if (typeof val === "number") {
                return false;
            }
            if (val instanceof Array) {
                if (val.length === 0) return true;
            } else if (val instanceof Object) {
                if (JSON.stringify(val) === "{}" || val == null ) return true;
            } else {
                if (
                val === "null" ||
                val == null ||
                val === "undefined" ||
                val === undefined ||
                val === ""
                )
                return true;
                return false;
            }
            return false;
            
        }
        
    }
}
</script>

<style lang="scss">
@import '../../print.css';
.top-title-container{
    display: flex;
    justify-content: space-between;
    width: 100%;
    margin-top: 50px;
}
.div-with-right-border {
    display: flex;
    text-align: center;
    justify-content: center;
    border-right: 1.8px solid #000000; /* 2px宽的黑色实线右边框 */
}
</style>