/*
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-08-15 23:10:31
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2024-10-27 18:48:19
 * @FilePath: /quality_center_web/src/pages/print/components/hntMix.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import moment from 'moment';
export const hntMixed = {
    props: {
        experimentInfo: {
            type: Object,
            default: () => { return {} }
        },
        experimentDetailList: {
            type: Array,
            default: () => { return [] }
        },
        shxhSynchronizedataList: {
            type: Array,
            default: () => { return [] }
        },
        rwdextraList: {
            type: Array,
            default: () => { return [] }
        },
        supplyTaskList: {
            type: Array,
            default: () => { return [] }
        },
    },
    filters: {
        isNull: function (value) {
            if (value == undefined || value == null || value === '') return '---'
            
            return value;
        },
        shxhNull: function (value) {
            if (value == undefined || value == null || value === '') return '---'
            
            return value;
        },
        momentDate: function(value) {
            if (value) return moment(value).format("YYYY-MM-DD");
            return "---";
        },
    },
    data() {
        return {
            shxhSynchronizedata: this.shxhSynchronizedataList && this.shxhSynchronizedataList.length > 0 ? this.shxhSynchronizedataList[0] : {},
            supplyTask: this.supplyTaskList && this.supplyTaskList.length > 0 ? this.supplyTaskList[0] : {},
            kyqdInfo: {}, //抗压强度
            ksdjInfo: {}, //抗渗等级
            ldxInfo: {}, //流动性
            bsxInfo: {},   // 保水性
            njxInfo: {}, // 粘聚性
            dldInfo: {}, // 目测坍落度
            xndldInfo: {}, // 性能报告-坍落度
            hqlInfo: {}, // 含气量
            cnsjInfo: {}, // 初凝时间
            znsjInfo: {}, // 终凝时间
            llzInfo: {}, // 氯离子含量
            mslInfo: {}, // 泌水率
            kzdInfo: {}, // 扩展度
            kzqdInfo: {}, // 抗折强度

            rwdextraObj: {}, // 任务单信息
            erpRwdextra: {},
        }
    },
    mounted() {
        if (this.experimentDetailList && this.experimentDetailList.length > 0) {
            this.experimentDetailList.map(item => {
                if (item.testProjectCode === 'CONCRETE_PARAM_KYQD') {
                    // 抗压强度
                    this.kyqdInfo = item.objJson || {};
                }else if (item.testProjectCode === 'CONCRETE_PARAM_KSDJ') {
                    // 抗渗等级
                    this.ksdjInfo = item.objJson || {};
                }else if (item.testProjectCode === 'CONCRETE_PARAM_HYX') {
                    // 流动性
                    this.ldxInfo = item.objJson || {};
                }else if (item.testProjectCode === 'CONCRETE_PARAM_BSX') {
                    // 保水性
                    this.bsxInfo = item.objJson || {};
                }else if (item.testProjectCode === 'CONCRETE_PARAM_ZJX') {
                    // 粘聚性
                    this.njxInfo = item.objJson || {};
                }else if (item.testProjectCode === 'CONCRETE_PARAM_MCTLD') {
                    // 目测坍落度
                    this.dldInfo = item.objJson || {};
                }else if (item.testProjectCode === 'CONCRETE_PARAM_XNBG_TLD') {
                    // 性能报告-坍落度
                    this.xndldInfo = item.objJson || {};
                }else if (item.testProjectCode === 'CONCRETE_PARAM_XNBG_HQL') {
                    // 含气量
                    this.hqlInfo = item.objJson || {};
                }else if (item.testProjectCode === 'CONCRETE_PARAM_XNBG_CNSJ') {
                    // 初凝时间
                    this.cnsjInfo = item.objJson || {};
                }else if (item.testProjectCode === 'CONCRETE_PARAM_XNBG_ZNSJ') {
                    // 终凝时间
                    this.znsjInfo = item.objJson || {};
                }else if (item.testProjectCode === 'CONCRETE_PARAM_XNBG_LLZHL') {
                    // 氯离子含量
                    this.llzInfo = item.objJson || {};
                }else if (item.testProjectCode === 'CONCRETE_PARAM_XNBG_MSL') {
                    // 泌水率
                    this.mslInfo = item.objJson || {};
                }else if (item.testProjectCode === 'CONCRETE_PARAM_XNBG_KZD') {
                    // 扩展度
                    this.kzdInfo = item.objJson || {};
                }else if (item.testProjectCode === 'CONCRETE_PARAM_KZQD') {
                    // 抗折强度
                    this.kzqdInfo = item.objJson || {};
                }
            });
        }

        if (this.rwdextraList && this.rwdextraList.length > 0) {
            this.rwdextraObj = this.rwdextraList[0].rwdextraInfo;
            this.erpRwdextra = this.rwdextraList[0];
        }
    },

    methods: {
        isArrayNull(list, index) {
            if (list && list.length > index) {
                return list[index];
            }
            return undefined;
        }
    },
}