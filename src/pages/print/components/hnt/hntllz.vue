<!--
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-05-22 00:03:51
 * @LastEditors: huaze.fan <EMAIL>
 * @LastEditTime: 2025-03-21 15:39:19
 * @FilePath: /quality_center_web/src/pages/print/components/hntks.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div class="portrait">
        <div class="tip-view">
            <div class="tip-label" style="margin-top: 120px;">C-39b-0907</div>
        </div>
        <div class="company-label" style="font-size: 30px; margin-top: 20px; font-weight:700;">{{companyName}}</div>
        <div class="title-label" style="font-size: 46px; line-height: 60px;">混凝土氯离子检测报告</div>
        <div align="center" class="lab-report">

        <div class="lab-sub-title">
            <div class="lab-sub-title-container" style="margin-top: 80px; margin-left: 140px;">第 1 页  共 1 页</div>
                <div style="align-content: end">
          <div class="lab-sample-id" style=" display: flex; margin-top: 35px;"><span style="width: 100px;">委托编号:</span><span>{{ shxhSynchronizedata?.consignId | isNull }} </span></div>
          <div class="lab-report-id" style="margin-top: 10px; display: flex;"><span style="width: 100px;">报告编号:</span><span>{{ experimentInfo?.reportNo | isNull }} </span></div>
            </div>
        </div>

        <!-- <div class="lab-sub-title">
            <div class="lab-sub-title-container" style="margin-top: 40px; margin-left: 140px;">第 1 页  共 1 页</div>
            <div class="lab-sub-title-container">
            <div class="lab-sample-id">委托编号:<span>{{ shxhSynchronizedata?.consignId | isNull }} </span></div>
            <div class="lab-report-id">报告编号:<span>{{ experimentInfo?.reportNo | isNull }} </span></div>
            </div>
        </div> -->
        <table  border="1" class="border" style="margin-bottom: 20px;">
            <tbody><tr height="50">
            <td width="240">委托部门</td>
            <td colspan="10" left>{{ experimentInfo?.experimentDept | isNull }}</td>
            </tr>
            <tr height="50">
            <td width="240">委托日期</td>
            <td left>{{ experimentInfo?.entrustTime | momentDate }}</td>
            <td width="240">报告日期</td>
            <td colspan="5" left>{{ experimentInfo?.reportDate | momentDate }}</td>
            </tr>
            </tbody>
        </table>

        <table  border="1" class="border" >
            <tr height="50">
                <td width="100" colspan="1">样品编号</td>
                <td colspan="2" left>{{ shxhSynchronizedata?.sampleId | shxhNull }}</td>
                <td colspan="1">样品名称</td>
                <td colspan="2" left>{{ experimentInfo?.materialsName | isNull }}</td>
                <td colspan="1">种类级别</td>
                <td colspan="2" left>{{ experimentInfo?.sampleLevel | isNull }}</td>
            </tr>
            <tr height="50">
                <td width="100" colspan="1" >强度等级</td>
                <td colspan="2" left>{{ experimentInfo?.sampleLevel | isNull }}</td>
                <td colspan="1">代表数量</td>
                <td colspan="2" left>{{ experimentInfo?.behalfNumber | isNull }}m³</td>
                <td colspan="1">检测日期</td>
                <td colspan="2" left>{{ llzInfo?.jcrq | momentDate }}</td>
            </tr>
            <tr height="50">
                <td width="100" colspan="1">生产单位</td>
                <td colspan="4" left>{{ companyName | isNull }}</td>
                <td width="100" colspan="1">备案证号</td>
                <td colspan="4" left>{{ experimentInfo?.certificateNo | shxhNull }}</td>
            </tr>
            <tr height="50">
                <td width="140" colspan="2">工程部位</td>
                <td colspan="8" left>{{ rwdextraObj?.fjzbw | isNull }}</td>
            </tr>
            <tr height="50">
                <td width="140" colspan="2">评定依据</td>
                <td colspan="8" left>{{ experimentInfo?.judgeGist | isNull }}</td>
            </tr>
            <tr height="50">
                <td width="140" colspan="2">检测方法</td>
                <td colspan="8" left>{{ experimentInfo?.experimentGist | isNull }}</td>
            </tr>
            <tr height="50">
                <td colspan="2">参数名称</td>
                <td colspan="3">技术要求</td>
                <td colspan="3">检测值</td>
                <td colspan="1">单项结果</td>
            </tr>
            <tr height="50">
                <td width="140" colspan="2">坍落度/mm</td>
                <td colspan="3">160±30</td>
                <td colspan="3">{{ xndldInfo?.jcz | isNull }}</td>
                <td colspan="1">{{ xndldInfo?.dxjl | isNull }}</td>
            </tr>
            <tr height="50">
                <td width="140" colspan="2">含气量/%</td>
                <td colspan="3">-</td>
                <td colspan="3">{{ hqlInfo?.jcz | isNull }}</td>
                <td colspan="1">{{ hqlInfo?.dxjl | isNull }}</td>
            </tr>
            <tr height="50">
                <td width="140" colspan="2">初凝时间/min</td>
                <td colspan="3">-</td>
                <td colspan="3">{{ cnsjInfo?.jcz | isNull }}</td>
                <td colspan="1">{{ cnsjInfo?.dxjl | isNull }}</td>
            </tr>
            <tr height="50">
                <td width="140" colspan="2">终凝时间/min</td>
                <td colspan="3">-</td>
                <td colspan="3">{{ znsjInfo?.jcz | isNull }}</td>
                <td colspan="1">{{ znsjInfo?.dxjl | isNull }}</td>
            </tr>
            <!-- <tr height="50">
                <td width="140" colspan="2">表观密度/(kg/m³)</td>
                <td colspan="3">2360</td>
                <td colspan="3">{{ '/' }}</td>
                <td colspan="1">{{ '/' }}</td>
            </tr> -->
            <tr height="50">
                <td width="140" colspan="2">氯离子含量/%</td>
                <td colspan="3">≤0.06</td>
                <td colspan="3">{{ llzInfo?.synInfo?.llzhl | isNull }}</td>
                <td colspan="1">{{ llzInfo?.dxjl | isNull }}</td>
            </tr>
            <tr height="50">
                <td width="140" colspan="2">泌水率/%</td>
                <td colspan="3">-</td>
                <td colspan="3">{{ mslInfo?.jcz | isNull }}</td>
                <td colspan="1">{{ mslInfo?.dxjl | isNull }}</td>
            </tr>
            <!-- <tr height="50">
                <td width="140" colspan="2">泌水量/(ml/mm2)</td>
                <td colspan="3">-</td>
                <td colspan="3">{{ '/' }}</td>
                <td colspan="1">{{ '/' }}</td>
            </tr> -->
            <tr height="50">
                <td width="160" colspan="2">检查结论</td>
                <td colspan="7" left>{{ experimentInfo?.conclusion || '' }}</td>
            </tr>
        </table>

        <table  border="1" class="border" style=" border-collapse:collapse; font-size: 16px; margin-top: 20px; table-layout:fixed;">
            <tbody>
            <tr height="100">
            <td colspan="8">备注</td>
            <td colspan="21" left>{{ experimentInfo?.remark | isNull }}</td>
            </tr>
            </tbody>
        </table>

        <table class="lab-sign" style="border: none;">
            <tr height="45" style="border: none;">
            <td style="border: none;"><div class="sign-user">检测报告专用章：</div></td>
            <td style="border: none;">
                <div class="sign-user">
                批准：
                <!-- <img
                v-if="true"
                src=""
                width="150" height="50" /> -->
                </div>
            </td>
            <td style="border: none;">
                <div class="sign-user">
                审核：
                <!-- <img
                v-if="true"
                src=""
                width="150" height="50" /> -->
                </div>
            </td>
            <td style="border: none;">
                <div class="sign-user">
                检测：
                <!-- <img
                v-if="true"
                src=""
                width="150" height="50" /> -->
                </div>
            </td>
            </tr>
        </table>

        </div>
    </div>
</template>

<script>
import { hntMixed } from './hntMixed';

export default {
    mixins: [hntMixed],
    props: {
        data: {
            type: Object,
            default: () => { return {} }
        },
        companyName: {
            type: String,
            default: ""
        },
    },
}
</script>

<style lang="scss" scoped>
@import '../../print.css';
.border {
    border: 3px solid black; /* 设置边框粗细 */
}
.border th, .border td{
    border: 2px solid black; /* 设置边框粗细 */
}
</style>