/*
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-08-15 23:10:31
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-04-15 22:48:05
 * @FilePath: /quality_center_web/src/pages/print/components/hntMix.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import moment from 'moment';
export const hntTaskMixed = {
    props: {
        mixProportion: {
            type: Object,
            default: () => { return {} }
        },
        mixExperimentInfo:{
            type: Object,
            default:() => { return {} }
        },
        printVerifyRecordOtherInfo: {
            type: Object,
            default: () => { return {} }
        },
        shxhSynchronizedataList: {
            type: Array,
            default: () => { return [] }
        },
        rwdextraList: {
            type: Array,
            default: () => { return [] }
        },
        supplyTaskList: {
            type: Array,
            default: () => { return [] }
        },
        kyInfoList: {
            type: Array,
            default: () => { return [] }
        },
        phbhistoryList: {
            type: Array,
            default: () => { return [] }
        }
    },
    filters: {
        isNull: function (value) {
            if (value == undefined || value == null || value === '') return '---'
            
            return value;
        },
        shxhNull: function (value) {
            if (value == undefined || value == null || value === '') return '---'
            
            return value;
        },
        momentDate: function(value) {
            if (value) return moment(value).format("YYYY-MM-DD");
            return "/";
        },
    },
    data() {
        return {
            shxhSynchronizedata: this.shxhSynchronizedataList && this.shxhSynchronizedataList.length > 0 ? this.shxhSynchronizedataList[0] : {},
            supplyTask: this.supplyTaskList && this.supplyTaskList.length > 0 ? this.supplyTaskList[0] : {},
            rwdextraObj: {}, // 任务单信息
            erpRwdextra: {},
            proportionMaterial: {}, // 原材料信息
            sn: {},
            water: {},
            kzf: {},
            fmh: {},
            xgl: {},
            cgl: {},
            wjj1: {},
            wjj2: {},
            wcl1: {},
            wcl2: {},
        }
    },
    mounted() {
        if (this.rwdextraList && this.rwdextraList.length > 0) {
            this.rwdextraObj = this.rwdextraList[0].rwdextraInfo;
            this.erpRwdextra = this.rwdextraList[0];
        }
        
        if (this.mixProportion && this.mixProportion.proportionMaterial) {
            this.proportionMaterial = this.mixProportion.proportionMaterial || {};
            this.sn = this.proportionMaterial.sn || {};
            this.water = this.proportionMaterial.water || {};
            this.kzf = this.proportionMaterial.kzf || {};
            this.fmh = this.proportionMaterial.fmh || {};
            this.xgl = this.proportionMaterial.xgl || {};
            this.cgl = this.proportionMaterial.cgl || {};
            this.wjj1 = this.proportionMaterial.wjj1 || {};
            this.wjj2 = this.proportionMaterial.wjj2 || {};
            this.wcl1 = this.proportionMaterial.wcl1 || {};
            this.wcl2 = this.proportionMaterial.wcl2 || {};
        }
        
        this.printVerifyRecordOtherInfoHandle();
    },

    methods: {
        isArrayNull(list, index) {
            if (list && list.length > index) {
                return list[index];
            }
            return '/';
        },

        printVerifyRecordOtherInfoHandle() {
            let vRInfo = this.printVerifyRecordOtherInfo || {};
            let snInfo = vRInfo.snInfo || {};
            let waterInfo = vRInfo.waterInfo || {};
            let kzfInfo = vRInfo.kzf1Info || {};
            let fmhInfo = vRInfo.fmh1Info || {};
            let xglInfo = vRInfo.xglInfo || {};
            let cglInfo = vRInfo.cglInfo || {};
            let wjj1Info = vRInfo.wjj1Info || {};
            let wjj2Info = vRInfo.wjj2Info || {};
            let wcl1Info = vRInfo.wcl1Info || {};
            let wcl2Info = vRInfo.wcl2Info || {};

            // reportId 复试编号  certificateNo  质保书编号
            if (snInfo.cj) {
                this.sn.cj = snInfo.cj;
            }
            // if (snInfo.certificateNo) {
            //     this.sn.certificateNo = snInfo.certificateNo;
            // }
            if (snInfo.reportId) {
                this.sn.xhbgbh = snInfo.reportId;
            }

            if (waterInfo.cj) {
                this.water.cj = waterInfo.cj;
            }
            // if (waterInfo.certificateNo) {
            //     this.water.certificateNo = waterInfo.certificateNo;
            // }
            if (waterInfo.reportId) {
                this.water.xhbgbh = waterInfo.reportId;
            }

            if (kzfInfo.cj) {
                this.kzf.cj = kzfInfo.cj;
            }
            // if (kzfInfo.certificateNo) {
            //     this.kzf.certificateNo = kzfInfo.certificateNo;
            // }
            if (kzfInfo.reportId) {
                this.kzf.xhbgbh = kzfInfo.reportId;
            }

            if (xglInfo.cj) {
                this.xgl.cj = xglInfo.cj;
            }
            if (xglInfo.reportId) {
                this.xgl.xhbgbh = xglInfo.reportId;
            }
            // if (xglInfo.certificateNo) {
            //     this.xgl.certificateNo = xglInfo.certificateNo;
            // }
            
            // if (cglInfo.certificateNo) {
            //     this.cgl.certificateNo = cglInfo.certificateNo;
            // }
            
            if (cglInfo.reportId) {
                this.cgl.xhbgbh = cglInfo.reportId;
            }
            if (cglInfo.cj) {
                this.cgl.cj = cglInfo.cj;
            }

            if (fmhInfo.cj) {
                this.fmh.cj = fmhInfo.cj;
            }
            if (fmhInfo.reportId) {
                this.fmh.xhbgbh = fmhInfo.reportId;
            }
            // if (fmhInfo.certificateNo) {
            //     this.fmh.certificateNo = fmhInfo.certificateNo;
            // }
            
            if (wjj1Info.cj) {
                this.wjj1.cj = wjj1Info.cj;
            }
            if (wjj1Info.reportId) {
                this.wjj1.xhbgbh = wjj1Info.reportId;
            }
            // if (wjj1Info.certificateNo) {
            //     this.wjj1.certificateNo = wjj1Info.certificateNo;
            // }

            if (wjj2Info.cj) {
                this.wjj2.cj = wjj2Info.cj;
            }
            if (wjj2Info.reportId) {
                this.wjj2.xhbgbh = wjj2Info.reportId;
            }
            // if (wjj2Info.certificateNo) {
            //     this.wjj2.certificateNo = wjj2Info.certificateNo;
            // }

            if (wcl1Info.cj) {
                this.wcl1.cj = wcl1Info.cj;
            }
            if (wcl1Info.reportId) {
                this.wcl1.xhbgbh = wcl1Info.reportId;
            }
            // if (wcl1Info.certificateNo) {
            //     this.wcl1.certificateNo = wcl1Info.certificateNo;
            // }

            if (wcl2Info.cj) {
                this.wcl2.cj = wcl2Info.cj;
            }
            if (wcl2Info.reportId) {
                this.wcl2.xhbgbh = wcl2Info.reportId;
            }
            // if (wcl2Info.certificateNo) {
            //     this.wcl2.certificateNo = wcl2Info.certificateNo;
            // }
            
        }
    },
}