<!--
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-05-22 00:03:51
 * @LastEditors: huaze.fan <EMAIL>
 * @LastEditTime: 2025-03-21 15:36:02
 * @FilePath: /quality_center_web/src/pages/print/components/hntks.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div class="portrait">
        <div class="tip-view" style="margin-top: 130px;">
            <div class="tip-label">C-39b-0907</div>
        </div>
        <div class="company-label" style="line-height: 36px;font-weight: 900; font-size: 28px;">{{companyName}}</div>
        <div class="title-label" style="line-height: 36px;font-weight: 900; font-size: 36px;">粉煤灰检测报告</div>
        <div align="center" class="lab-report">

        <div class="lab-sub-title" style="margin-top: 40px;">
            <div class="lab-sub-title-container" style="margin-top: 40px; margin-left: 180px;">第1页{{'&nbsp&nbsp&nbsp&nbsp'}}共1页</div>
            <div class="lab-sub-title-container">
                <div class="lab-sample-id">委托编号:<span>{{ shxhSynchronizedata?.consignId | isNull }} </span></div>
                <div class="lab-report-id" style="margin-top: 16px;">报告编号:<span>{{ experimentInfo?.reportNo | isNull }} </span></div>
            </div>
        </div>
        <table border="1" class="border" style="margin-bottom: 20px; table-layout: fixed; margin-top: 15px;">
            <tbody>
            <tr height="40">
                <td colspan="4">委托部门</td>
                <!-- <td colspan="20" left>{{ experimentInfo?.experimentDept | isNull }}</td> -->
                <td colspan="20" left>{{ '---' }}</td>
            </tr>
            <tr height="40">
                <td colspan="4">委托日期</td>
                <td left colspan="8">{{ experimentInfo?.entrustTime | momentDate }}</td>
                <td colspan="4">报告日期</td>
                <td left colspan="8">{{ experimentInfo?.reportDate | momentDate }}</td>
            </tr>
            </tbody>
        </table>

        <table  border="1" class="border" style="margin-bottom: 20px; table-layout: fixed; margin-top: 30px;">
            <tr height="40">
                <td colspan="4">样品编号</td>
                <td colspan="4" left>{{ shxhSynchronizedata?.sampleId | shxhNull }}</td>
                <td colspan="3">品种规格</td>
                <td colspan="3" left>{{ getResultString(experimentInfo?.materialsSpecs, 'pzgg') }}</td>
                <!-- <td colspan="3" left>{{ experimentInfo?.materialsSpecs | isNull }}</td> -->
                <td colspan="3">等级</td>
                <td left colspan="7">{{  getResultString(experimentInfo?.materialsSpecs, 'dj') }}</td>
                <!-- <td left colspan="7">{{ experimentInfo?.sampleLevel | isNull }}</td> -->
            </tr>
            <tr height="40">
                <td colspan="4">样品名称</td>
                <td colspan="10" left>{{ experimentInfo?.materialsName | isNull }}</td>
                <td colspan="3">备案证号</td>
                <td left colspan="7" nowrap="nowrap">{{ experimentInfo?.certificateNo | shxhNull }}</td>
            </tr>
            <tr height="40">
                <td colspan="4">生产单位</td>
                <td colspan="10" left>{{ getShowCJ(experimentInfo, companyInfo?.fmhConfig) }}</td>
                <td colspan="3">代表数量</td>
                <td left colspan="7">{{ experimentInfo?.behalfNumber | isNull }}t</td>
            </tr>
            <tr height="40">
                <td colspan="4">检测日期</td>
                <td colspan="10" left>{{ experimentInfo?.jcrq | isNull }}</td>
                <td colspan="3">批号</td>
                <td left colspan="7">{{ getBatchString(shxhSynchronizedata?.sampleId) }}</td>
                <!-- <td left colspan="7">{{ experimentInfo?.batch | isNull }}</td> -->
            </tr>
            <tr height="40">
                <td colspan="4">评定依据</td>
                <td colspan="20" left>{{ experimentInfo?.judgeGist | isNull }}</td>
            </tr>
            <tr height="40">
                <td colspan="4">检测方法</td>
                <td colspan="20" left>{{ experimentInfo?.experimentGist | isNull }}</td>
            </tr>
            <tr height="40">
                <td colspan="5" rowspan="2">检测项目</td>
                <td colspan="6">F类</td>
                <td colspan="6">C类</td>
                <td rowspan="2" colspan="7">检测结果</td>
            </tr>
            <tr height="40">
                <td colspan="2">I级</td>
                <td colspan="2">Ⅱ级</td>
                <td colspan="2">Ⅲ级</td>
                <td colspan="2">I级</td>
                <td colspan="2">Ⅱ级</td>
                <td colspan="2">Ⅲ级</td>
            </tr>
            <tr height="40">
                <td colspan="5">细度/%</td>
                <td colspan="2">≤12.0</td>
                <td colspan="2">≤30.0</td>
                <td colspan="2">≤45.0</td>
                <td colspan="2">≤12.0</td>
                <td colspan="2">≤30.0</td>
                <td colspan="2">≤45.0</td>
                <td colspan="7">{{ xdInfo?.jzhxddbz | isNull }}</td>
            </tr>
            <tr height="40">
                <td colspan="5">需水量比/%</td>
                <td colspan="2">≤95</td>
                <td colspan="2">≤105</td>
                <td colspan="2">≤115</td>
                <td colspan="2">≤95</td>
                <td colspan="2">≤105</td>
                <td colspan="2">≤115</td>
                <td colspan="7">{{ getIntNum(xslbInfo?.xslb) }}</td>
            </tr>
            <tr height="40">
                <td colspan="5">烧失量/%</td>
                <td colspan="2">≤5.0</td>
                <td colspan="2">≤8.0</td>
                <td colspan="2">≤15.0</td>
                <td colspan="2">≤5.0</td>
                <td colspan="2">≤8.0</td>
                <td colspan="2">≤15.0</td>
                <td colspan="7">{{ sslInfo?.sslpjz | isNull }}</td>
            </tr>
            <tr height="40">
                <td colspan="5">含水量/%</td>
                <td colspan="12">≤1.0</td>
                <td  colspan="7">{{ hslInfo?.hsl | isNull }}</td>
            </tr>
            <tr height="40">
                <td colspan="5">三氧化硫/%</td>
                <td colspan="12">≤3.0</td>
                <td colspan="7">{{ syhlInfo?.pjz | isNull }}</td>
            </tr>
            <tr height="40">
                <td colspan="5">游离氧化钙/%</td>
                <td colspan="6">≤1.0</td>
                <td colspan="6">≤4.0</td>
                <td colspan="7">{{ ylgInfo?.pjz | isNull }}</td>
            </tr>
            <tr height="40">
                <td colspan="5">安定性/mm</td>
                <td colspan="12">≤5.0</td>
                <td colspan="7">{{ adxInfo?.lsf?.pjz | isNull }}</td>
            </tr>
            <tr height="40">
                <td colspan="5">碱含量</td>
                <td colspan="12">---</td>
                <td colspan="7">---</td>
            </tr>
            <tr height="40">
            <td colspan="5">放射性</td>
            <td colspan="12">---</td>
            <td colspan="7">---</td>
            </tr>
            <tr height="40">
                <td colspan="5">均匀性</td>
                <td colspan="12">---</td>
                <td colspan="7">---</td>
            </tr>
            <tr height="40">
                <td colspan="5">检测结论</td>
                <td colspan="19" left>{{ getShowResultStr(experimentInfo?.conclusion)}}</td>
            </tr>
        </table>

        <table  border="1" class="border" style="margin-top: 20px; table-layout: fixed;">
            <tbody>
            <tr height="70">
            <td colspan="5">备注</td>
            <td colspan="19" left>{{ experimentInfo?.remark | isNull }}</td>
            </tr>
            </tbody>
        </table>

        <table class="lab-sign" style="border: none; margin-top: 5px">
            <tr height="40" style="border: none;">
            <td style="border: none;"><div class="sign-user">检测报告专用章：</div></td>
            <td style="border: none;">
                <div class="sign-user">
                批准：
                <!-- <img
                v-if="true"
                src=""
                width="150" height="50" /> -->
                </div>
            </td>
            <td style="border: none;">
                <div class="sign-user">
                审核：
                <!-- <img
                v-if="true"
                src=""
                width="150" height="50" /> -->
                </div>
            </td>
            <td style="border: none;">
                <div class="sign-user">
                检测：
                <!-- <img
                v-if="true"
                src=""
                width="150" height="50" /> -->
                </div>
            </td>
            </tr>
        </table>

        </div>
    </div>
</template>

<script>
import moment from 'moment';
import {cpEvenRound} from "@/utils/calculate.js"
export default {
    props: {
        companyName: {
            type: String,
            default: ""
        },
        mixExperimentInfo:{
            type: Object,
            default: () => { return {} }
        },
        mixProportion: {
            type: Object,
            default: () => { return {} }
        },
        companyInfo: {
            type: Object,
            default: () => { return {} }
        },
    },

    filters: {
        momentDate: function(value) {
          if (value) return moment(value).format("YYYY-MM-DD");
          return "---";
        },
        isNull: function (value) {
            if (value == undefined || value == null || value === '') return '---'
            
            return value;
        },
        shxhNull: function (value) {
            if (value == undefined || value == null || value === '') return '---'
            
            return value;
        }
    },

    data() {
        return {
            shxhSynchronizedata: {},
            xdInfo: {}, //细度
            sslInfo: {}, //烧失量
            xslbInfo: {}, //需水量比
            hslInfo: {},   // 含水率
            syhlInfo: {}, // 三氧化硫
            ylgInfo: {}, // 游离钙
            adxInfo: {}, // 安定性
            hxzbInfo: {}, // 活性指标
            experimentInfo:{},
            experimentDetailList:[],
            fmh:{},
        }
    },

    methods: {
        getIntNum(str){
            if(this.isEmpty(str)) return '---'
            return cpEvenRound(str, 0);
        },

        getShowCJ(obj, config){
            let cj = obj?.factory;
            let gysmc = obj?.supplyCompanyName;
            var showName = cj;
            var result = '/';

            if(config == 1){
                if(!this.isEmpty(cj)){
                    showName = cj
                } 
            } else {
                if(!this.isEmpty(gysmc)){
                    showName = gysmc
                }
            }
            result = this.isEmpty(showName) ? '/' : showName;
            return result;
        },

      isEmpty(val) {
            if (typeof val === "boolean") {
                return false;
            }
            if (typeof val === "number") {
                return false;
            }
            if (val instanceof Array) {
                if (val.length === 0) return true;
            } else if (val instanceof Object) {
                if (JSON.stringify(val) === "{}" || val == null ) return true;
            } else {
                if (
                val === "null" ||
                val == null ||
                val === "undefined" ||
                val === undefined ||
                val === ""
                )
                return true;
                return false;
            }
            return false;
        },
      getShowResultStr(str){
        var res = str;
        if(this.isEmpty(str)) return '--';
        if(str.includes(':')){
          res = str.split(':').slice(1).join(':');
        }
        if(str.includes('：')){
          res = str.split('：').slice(1).join('：');
        }
        if(res.includes('。')){
          res = res.split('。')[0] + '。';
        }
        return res.replace(';', '').replace('；', '');
      },
        getResultString(value, type){
            if(!value) return '--';
            if(type == 'pzgg') {
                return value.substring(0,2);
            } 
            return value.substring(2,value.length);

        },
        getBatchString(value){
            if(!value) return '--';
            if(value.length < 3) return '--';
            return value.substring(value.length-3, value.length);
        }

    },

    mounted() {

        if(this.mixExperimentInfo) {
            let objInfo = this.mixExperimentInfo?.fmhInfo || {};
            if(objInfo && objInfo.shxhSynchronizedataList && objInfo.shxhSynchronizedataList.length > 0){
                this.shxhSynchronizedata = objInfo.shxhSynchronizedataList[0];
            }
            this.experimentInfo = objInfo?.experimentInfo || {}
                if (objInfo.experimentDetailList && objInfo.experimentDetailList.length > 0) {
                    objInfo.experimentDetailList.map(item => {
                        if (item.testProjectCode === 'FLY_ASK_PARAM_FMH_SSL') {
                            // 烧失量
                            this.sslInfo = item.objJson || {};
                        }else if (item.testProjectCode === 'FLY_ASK_PARAM_FMH_XSLB') {
                            // 需水量比
                            this.xslbInfo = item.objJson || {};
                        }else if (item.testProjectCode === 'FLY_ASK_PARAM_FMH_HSL') {
                            // 含水率
                            this.hslInfo = item.objJson || {};
                        }else if (item.testProjectCode === 'FLY_ASK_PARAM_FMH_XD') {
                            // 细度
                            this.xdInfo = item.objJson || {};
                        }else if (item.testProjectCode === 'FLY_ASK_PARAM_FMH_SYHL') {
                            // 三氧化硫
                            this.syhlInfo = item.objJson || {};
                        }else if (item.testProjectCode === 'FLY_ASK_PARAM_FMH_YLYHG') {
                            // 游离钙
                            this.ylgInfo = item.objJson || {};
                        }else if (item.testProjectCode === 'FLY_ASK_PARAM_FMH_ADX') {
                            // 安定性
                            this.adxInfo = item.objJson || {};
                        }else if (item.testProjectCode === 'FLY_ASK_PARAM_FMH_HXZS') {
                            // 活性指标
                            this.hxzbInfo = item.objJson || {};
                        }
                });
            }
        }

        if (this.mixProportion && this.mixProportion.proportionMaterial) {
            var proportionMaterial = this.mixProportion.proportionMaterial || {};
            this.fmh = proportionMaterial.fmh || {};
        }

    },
}
</script>

<style lang="scss" scoped>
@import '../print.css';

.border {
    border: 3px solid black; /* 设置边框粗细 */
}
.border th, .border td{
    border: 2px solid black; /* 设置边框粗细 */
}
</style>