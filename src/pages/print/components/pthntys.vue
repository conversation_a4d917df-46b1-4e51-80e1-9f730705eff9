<!--
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-05-22 00:03:51
 * @LastEditors: huaze.fan <EMAIL>
 * @LastEditTime: 2025-03-21 15:36:58
 * @FilePath: /quality_center_web/src/pages/print/components/hntks.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="portrait">
    <div class="tip-view" style="margin-top: 40px;">
      <div class="tip-label">C-12a-0806</div>
    </div>
    <div class="company-label" style="line-height: 36px;font-weight: 900; font-size: 28px;">{{companyName}}</div>
    <div class="title-label" style="line-height: 36px;font-weight: 900; font-size: 36px;">普通混凝土用砂检测报告</div>
    <div align="center" class="lab-report">
    <div class="lab-sub-title" style="margin-top: 30px;">
        <div class="lab-sub-title-container" style="margin-top: 40px; margin-left: 140px;">第1页{{'&nbsp&nbsp&nbsp&nbsp'}}共1页</div>
        <div class="lab-sub-title-container">
          <div class="lab-sample-id">委托编号:<span>{{ shxhSynchronizedata?.consignId | isNull }} </span></div>
          <div class="lab-report-id" style="margin-top: 15px;">报告编号:<span>{{ experimentInfo?.reportNo | isNull }} </span></div>
        </div>
      </div>

      <!-- <div class="lab-sub-title" style="margin-top: 40px;">
        <div class="lab-sub-title-container" style="margin-top: 40px; margin-left: 140px;"></div>
        <div class="lab-sub-title-container">
          <div class="lab-sample-id">委托编号:<span>{{ shxhSynchronizedata?.consignId | isNull }} </span></div>
          <div class="lab-report-id" style="margin-top: 15px;">报告编号:<span>{{ experimentInfo?.reportNo | isNull }} </span></div>
        </div>
      </div> -->
      <table border="1" class="border" style="margin-bottom: 20px; table-layout: fixed; margin-top: 15px">
        <tbody>
          <tr height="40">
            <td colspan="3">委托部门</td>
            <!-- <td colspan="21" left>{{ experimentInfo?.experimentDept | isNull }}</td> -->
            <td colspan="21" left>{{ '---' }}</td>
          </tr>
          <tr height="40">
            <td colspan="3">委托日期</td>
            <td left colspan="14">{{ experimentInfo?.entrustTime | momentDate }}</td>
            <td colspan="3">报告日期</td>
            <td left colspan="4">{{ experimentInfo?.reportDate | momentDate }}</td>
          </tr>
        </tbody>
      </table>

      <table border="1" class="border" style="margin-top: 30px; table-layout: fixed;">
        <tr height="40">
          <td colspan="3">样品编号</td>
          <td colspan="4" left>{{ shxhSynchronizedata?.sampleId | shxhNull }}</td>
          <td colspan="3">样品名称</td>
          <td colspan="4" left>{{ experimentInfo?.materialsName | isNull }}</td>
          <td colspan="3">样品规格</td>
          <td colspan="7" left>{{ experimentInfo?.materialsSpecs | isNull }}</td>
        </tr>
        <tr height="40">
          <td colspan="3">生产单位</td>
          <td colspan="11" left>{{getShowCJ(experimentInfo, companyInfo?.xglConfig) }}</td>
          <td colspan="3">备案证号</td>
          <td colspan="7" left>{{ experimentInfo?.certificateNo | shxhNull }}</td>
        </tr>
        <tr height="40">
          <td colspan="3">检测日期</td>
          <td colspan="11" left>{{ getShuiniJCRQ(experimentInfo?.jcrq) }}</td>
          <td colspan="3">代表数量</td>
          <td colspan="7" left>{{ experimentInfo?.behalfNumber | isNull }}t</td>
        </tr>
        <tr height="40">
          <td colspan="3">评定依据</td>
          <td colspan="11" left>{{ experimentInfo?.judgeGist | isNull }}</td>
          <td colspan="3">检测方法</td>
          <td colspan="7" left>{{ experimentInfo?.experimentGist | isNull }}</td>
        </tr>
        <tr height="40">
          <td colspan="4">公称直径/mm</td>
          <td colspan="2">10.0</td>
          <td colspan="2">5.00</td>
          <td colspan="2">2.50</td>
          <td colspan="2">1.25</td>
          <td colspan="2">0.630</td>
          <td colspan="2">0.315</td>
          <td colspan="2">0.160</td>
          <td colspan="2">筛底</td>
          <td colspan="4">所属级配区</td>
        </tr>
         <tr height="40">
          <td rowspan="3" colspan="2">标准颗粒级配区</td>
          <td colspan="2">I区</td>
          <td colspan="2">---</td>
          <td colspan="2" style="font-size: 15px;">10~0</td>
          <td colspan="2" style="font-size: 15px;">35~5</td>
          <td colspan="2" style="font-size: 15px;">65~35</td>
          <td colspan="2" style="font-size: 15px;">85~71</td>
          <td colspan="2" style="font-size: 15px;">95~80</td>
          <td colspan="2" style="font-size: 15px;">100~90</td>
          <td colspan="2">---</td>
          <td colspan="4" rowspan="3">{{ sfxInfo?.dxjl | isNull }}</td>
        </tr>
        <tr height="40">
          <td colspan="2">Ⅱ区</td>
          <td colspan="2">---</td>
          <td colspan="2" style="font-size: 15px;">10~0</td>
          <td colspan="2" style="font-size: 15px;">25~0</td>
          <td colspan="2" style="font-size: 15px;">50~10</td>
          <td colspan="2" style="font-size: 15px;">70~41</td>
          <td colspan="2" style="font-size: 15px;">92~70</td>
          <td colspan="2" style="font-size: 15px;">100~90</td>
          <td colspan="2">---</td>
          <!-- <td colspan="2">{{ sfxInfo?.sfxInfo[0] | sfxInfoSkgjzj }}</td>
          <td colspan="3">0</td>
          <td colspan="3">0</td>
          <td colspan="3">0</td>
          <td colspan="3">{{ sfxInfo?.sfxInfo[0] | sfxFjsypjz }}</td>
          <td colspan="3">{{ sfxInfo?.sfxInfo[0] | sfxLjsypjz }}</td>
          <td width="50">2</td>
          <td colspan="2">表观密度/(kg/m³)</td>
          <td colspan="8">{{ experimentDetailObj?.FINE_AGGREGATE_PARAM_XGL_BGMD?.pjz | isNull }}</td> -->
        </tr>
        <tr height="40">
          <td colspan="2">Ⅲ区</td>
          <td colspan="2">---</td>
          <td colspan="2" style="font-size: 15px;">10~0</td>
          <td colspan="2" style="font-size: 15px;">15~0</td>
          <td colspan="2" style="font-size: 15px;">25~0</td>
          <td colspan="2" style="font-size: 15px;">40~16</td>
          <td colspan="2" style="font-size: 15px;">85~55</td>
          <td colspan="2" style="font-size: 15px;">100~90</td>
          <td colspan="2">---</td>
          <!-- <td colspan="2">{{ sfxInfo?.sfxInfo[1] | sfxInfoSkgjzj }}</td>
          <td colspan="3">10~0</td>
          <td colspan="3">10~0</td>
          <td colspan="3">10~0</td>
          <td colspan="3">{{ sfxInfo?.sfxInfo[1] | sfxFjsypjz }}</td>
          <td colspan="3">{{ sfxInfo?.sfxInfo[1] | sfxLjsypjz }}</td>
          <td>3</td>
          <td colspan="2">堆积密度/( kg/m³)</td>
          <td colspan="8">{{ experimentDetailObj?.FINE_AGGREGATE_PARAM_XGL_DJMD?.pjz | isNull }}</td> -->
        </tr>
        <tr height="40">
          <td colspan="4">分计筛余/%</td>
          <td colspan="2">---</td>
          <td colspan="2" style="">---</td>
          <td colspan="2">---</td>
          <td colspan="2">---</td>
          <td colspan="2">---</td>
          <td colspan="2">---</td>
          <td colspan="2">---</td>
          <td colspan="2">---</td>
          <td colspan="4">细度模数</td>
          <!-- <td colspan="2">{{ sfxInfo?.sfxInfo[2] | sfxInfoSkgjzj }}</td>
          <td colspan="3">35~5</td>
          <td colspan="3">25~0</td>
          <td colspan="3">15~0</td>
          <td colspan="3">{{ sfxInfo?.sfxInfo[2] | sfxFjsypjz }}</td>
          <td colspan="3">{{ sfxInfo?.sfxInfo[2] | sfxLjsypjz }}</td>
          <td>4</td>
          <td colspan="2">紧密密度/(kg/m³)</td>
          <td colspan="8">{{ experimentDetailObj?.FINE_AGGREGATE_PARAM_XGL_JMMD?.pjz | isNull }}</td> -->
        </tr>
        <tr height="40">
          <td colspan="4">累计筛余/%</td>
          <td colspan="2">0</td>
          <td colspan="2">{{getCpEvenRound(sfxInfo?.sfxInfo[0]?.ljsyppjz, 0)}}</td>
          <td colspan="2">{{getCpEvenRound(sfxInfo?.sfxInfo[1]?.ljsyppjz, 0)}}</td>
          <td colspan="2">{{getCpEvenRound(sfxInfo?.sfxInfo[2]?.ljsyppjz, 0)}}</td>
          <td colspan="2">{{getCpEvenRound(sfxInfo?.sfxInfo[3]?.ljsyppjz, 0)}}</td>
          <td colspan="2">{{getCpEvenRound(sfxInfo?.sfxInfo[4]?.ljsyppjz, 0)}}</td>
          <td colspan="2">{{getCpEvenRound(sfxInfo?.sfxInfo[5]?.ljsyppjz, 0)}}</td>
          <td colspan="2">{{getCpEvenRound(sfxInfo?.sfxInfo[6]?.ljsyppjz, 0)}}</td>
          <td colspan="4">{{getCpEvenRound(experimentDetailObj?.FINE_AGGREGATE_PARAM_XGL_SFX?.xdmspjz, 1)}}</td>
        </tr>
        <tr height="40">
          <td colspan="6">检测项目</td>
          <td colspan="6">检测值</td>
          <td colspan="6">检测项目</td>
          <td colspan="6">检测值</td>
          <!-- <td colspan="2">{{ sfxInfo?.sfxInfo[4] | sfxInfoSkgjzj }}</td>
          <td colspan="3">85~71</td>
          <td colspan="3">70~41</td>
          <td colspan="3">40~16</td>
          <td colspan="3">{{ sfxInfo?.sfxInfo[4] | sfxFjsypjz }}</td>
          <td colspan="3">{{ sfxInfo?.sfxInfo[4] | sfxLjsypjz }}</td>
          <td>6</td>
          <td colspan="2">泥块含量/%</td>
          <td colspan="8">{{ experimentDetailObj?.FINE_AGGREGATE_PARAM_XGL_NKHL?.pjz | isNull }}</td> -->
        </tr>
        <tr height="40">
          <td colspan="6" style="text-align: left;">表观密度/(kg/m³)</td>
          <td colspan="6">{{experimentDetailObj?.FINE_AGGREGATE_PARAM_XGL_BGMD?.pjz | isNull}}</td>
          <td colspan="6" style="text-align: left;">堆积密度/(kg/m³)</td>
          <td colspan="6">{{ experimentDetailObj?.FINE_AGGREGATE_PARAM_XGL_DJMD?.pjz | isNull }}</td>
          <!-- <td colspan="2">{{ sfxInfo?.sfxInfo[5] | sfxInfoSkgjzj }}</td>
          <td colspan="3">95~80</td>
          <td colspan="3">92~70</td>
          <td colspan="3">85~55</td>
          <td colspan="3">{{ sfxInfo?.sfxInfo[5] | sfxFjsypjz }}</td>
          <td colspan="3">{{ sfxInfo?.sfxInfo[5] | sfxLjsypjz }}</td>
          <td>7</td>
          <td colspan="2">云母含量%</td>
          <td colspan="8">{{ experimentDetailObj?.FINE_AGGREGATE_PARAM_XGL_YMHL?.pjz | isNull }}</td> -->
        </tr>
        <tr height="40">
          <td colspan="6" style="text-align: left;">紧密密度/(kg/m³)</td>
          <td colspan="6">{{ experimentDetailObj?.FINE_AGGREGATE_PARAM_XGL_JMMD?.pjz | isNull }}</td>
          <td colspan="6" style="text-align: left;">石粉含量(含泥量)/%</td>
          <td colspan="6">{{ experimentDetailObj?.FINE_AGGREGATE_PARAM_XGL_HNL?.pjz | isNull }}</td>
          <!-- <td colspan="2">{{ sfxInfo?.sfxInfo[6] | sfxInfoSkgjzj }}</td>
          <td colspan="3">100~90</td>
          <td colspan="3">100~90</td>
          <td colspan="3">100~90</td>
          <td colspan="3">{{ sfxInfo?.sfxInfo[6] | sfxFjsypjz }}</td>
          <td colspan="3">{{ sfxInfo?.sfxInfo[6] | sfxLjsypjz }}</td>
          <td>8</td>
          <td colspan="2">压碎值指标/%</td>
          <td colspan="8">{{ experimentDetailObj?.FINE_AGGREGATE_PARAM_XGL_YSZZB?.pjz | isNull }}</td> -->
        </tr>
        <tr height="40">
          <td colspan="6" style="text-align: left;">泥块含量/%</td>
          <td colspan="6">{{ experimentDetailObj?.FINE_AGGREGATE_PARAM_XGL_NKHL?.pjz | isNull }}</td>
          <td colspan="6" style="text-align: left;">云母含量/%</td>
          <td colspan="6">{{ experimentDetailObj?.FINE_AGGREGATE_PARAM_XGL_YMHL?.pjz | isNull }}</td>
          <!-- <td colspan="2">{{ sfxInfo?.sfxInfo[7] | sfxInfoSkgjzj }}</td>
          <td colspan="3">--</td>
          <td colspan="3">--</td>
          <td colspan="3">--</td>
          <td colspan="3">{{ sfxInfo?.sfxInfo[7] | sfxFjsypjz }}</td>
          <td colspan="3">{{ sfxInfo?.sfxInfo[7] | sfxLjsypjz }}</td>
          <td>9</td>
          <td colspan="2">MB值(亚甲蓝法)/(g/kg)</td>
          <td colspan="8">{{ experimentDetailObj?.FINE_AGGREGATE_PARAM_XGL_HNL?.syjgInfo?.mbz | isNull }}</td> -->
        </tr>
        <tr height="40">
          <td colspan="6" style="text-align: left;">压碎值指标/%</td>
          <td colspan="6">{{ getCpEvenRound(experimentDetailObj?.FINE_AGGREGATE_PARAM_XGL_YSZZB?.zyszb, 1) | isSLNull }}</td>
          <td colspan="6" style="text-align: left;" nowrap="nowrap">MB值(亚甲蓝法)/(g/kg)</td>
          <td colspan="6">{{ experimentDetailObj?.FINE_AGGREGATE_PARAM_XGL_HNL?.syjgInfo?.mbz | isNull }}</td>
          <!-- <td colspan="3" rowspan="2">所属级配区</td>
          <td colspan="15" rowspan="2" left>{{ sfxInfo?.dxjl | isNull }}</td>
          <td>10</td>
          <td colspan="2">贝壳含量/%</td>
          <td colspan="8">{{ experimentDetailObj?.FINE_AGGREGATE_PARAM_XGL_BKHL?.pjz | isNull }}</td> -->
        </tr>
        <tr height="40">
          <td colspan="6" style="text-align: left;">贝壳含量/%</td>
          <td colspan="6">{{ experimentDetailObj?.FINE_AGGREGATE_PARAM_XGL_BKHL?.pjz | isNull }}</td>
          <td colspan="6" style="text-align: left;">氯离子含量/%</td>
          <td colspan="6">{{ experimentDetailObj?.FINE_AGGREGATE_PARAM_XGL_LLZHL?.pjz | isNull }}</td>
          <!-- <td>11</td>
          <td colspan="2">氯离子含量%</td>
          <td colspan="8">{{ experimentDetailObj?.FINE_AGGREGATE_PARAM_XGL_LLZHL?.pjz | isNull }}</td> -->
        </tr>
        <tr height="40">
          <td colspan="6" style="text-align: left;">---</td>
          <td colspan="6">---</td>
          <td colspan="6" style="text-align: left;">---</td>
          <td colspan="6">---</td>
          <!-- <td colspan="3" rowspan="2">细度模数</td>
          <td colspan="15" rowspan="2" left>{{ experimentDetailObj?.FINE_AGGREGATE_PARAM_XGL_SFX?.xdmspjz | isNull }}</td>
          <td>12</td>
          <td colspan="2">含水率/%</td>
          <td colspan="8">{{ experimentDetailObj?.FINE_AGGREGATE_PARAM_XGL_HSL?.pjz | isNull }}</td> -->
        </tr>
        <tr height="40">
          <td colspan="6" style="text-align: left;">---</td>
          <td colspan="6">---</td>
          <td colspan="6" style="text-align: left;">---</td>
          <td colspan="6">---</td>
        </tr>

        <tr height="40">
          <td colspan="6" style="text-align: left;">---</td>
          <td colspan="6">---</td>
          <td colspan="6" style="text-align: left;">---</td>
          <td colspan="6">---</td>
        </tr>

        <tr height="40">
          <td colspan="6" style="text-align: left;">---</td>
          <td colspan="6">---</td>
          <td colspan="6" style="text-align: left;">---</td>
          <td colspan="6">---</td>
        </tr>
        <tr height="80">
          <td colspan="3">检测结论</td>
          <td colspan="21" left>{{ getShowResultStr(experimentInfo?.conclusion) }}</td>
        </tr>
      </table>

      <table border="1" class="border" style="margin-top: 20px;">
        <tbody>
          <tr height="70">
            <td width="150">备注</td>
            <td colspan="10" left>{{ experimentInfo?.remark | isNull }}</td>
          </tr>
        </tbody>
      </table>
      <table class="lab-sign" style="border: none;">
        <tr height="40" style="border: none;">
          <td style="border: none;">
            <div class="sign-user">检测报告专用章：</div>
          </td>
          <td style="border: none;">
            <div class="sign-user">
              批准/职务：
              <!-- <img v-if="true" src="" width="150" height="50" /> -->
            </div>
          </td>
          <td style="border: none;">
            <div class="sign-user">
              审核：
              <!-- <img v-if="true" src="" width="150" height="50" /> -->
            </div>
          </td>
          <td style="border: none;">
            <div class="sign-user">
              检测：
              <!-- <img v-if="true" src="" width="150" height="50" /> -->
            </div>
          </td>
        </tr>
      </table>


    </div>
  </div>

</template>

<script>
import {calcEquation} from "@/utils/calculate.js"
import {cpEvenRound} from "@/utils/calculate.js"
import moment from "moment";
  export default {
    props: {
      taskInfo: {
        type: Object,
        default: () => {
          return {}
        }
      },
      companyName: {
        type: String,
        default: ""
      },
      mixExperimentInfo:{
          type: Object,
          default: () => { return {} }
      },
        mixProportion: {
            type: Object,
            default: () => { return {} }
        },
        companyInfo: {
            type: Object,
            default: () => { return {} }
        },
    },
    watch: {
      taskInfo: {
        handler(newValue, oldValue) {
          const {
            mixExperimentInfo,
          } = newValue;
          let objInfo = mixExperimentInfo?.xglInfo || {};
          if(objInfo && objInfo.shxhSynchronizedataList && objInfo.shxhSynchronizedataList.length > 0){
              this.shxhSynchronizedata = objInfo.shxhSynchronizedataList[0];
          }
          this.experimentInfo = objInfo?.experimentInfo || {}
          this.experimentDetailObj = {}
          if (objInfo.experimentDetailList && objInfo.experimentDetailList.length > 0) {
              objInfo.experimentDetailList.forEach(item =>{
                this.experimentDetailObj[item.testProjectCode] = item.objJson
                if (item.testProjectCode == 'FINE_AGGREGATE_PARAM_XGL_NKHL') {
                  this.nkInfo = item.objJson;
                }else if (item.testProjectCode == 'FINE_AGGREGATE_PARAM_XGL_SFX') {
                  let sfxInfo = new Array(8);
                  if (item.objJson.sfxInfo) {
                    item.objJson.sfxInfo.map((item, index) => {
                      sfxInfo[index] = item;
                    });
                  }
                  this.sfxInfo = {
                    ...item.objJson,
                    sfxInfo: sfxInfo
                  };
                  console.log('筛分析数据：',this.sfxInfo)
                }else if (item.testProjectCode == 'FINE_AGGREGATE_PARAM_XGL_HSL') {
                  this.hslInfo = item.objJson;
                }else if (item.testProjectCode == 'FINE_AGGREGATE_PARAM_XGL_HNL') {
                  this.hnlInfo = item.objJson;
                }else if (item.testProjectCode == 'FINE_AGGREGATE_PARAM_XGL_YMHL') {
                  this.ymInfo = item.objJson;
                }else if (item.testProjectCode == 'FINE_AGGREGATE_PARAM_XGL_YSZZB') {
                  this.ysInfo = item.objJson;
                }else if (item.testProjectCode == 'FINE_AGGREGATE_PARAM_XGL_DJMD') {
                  this.djInfo = item.objJson;
                }else if (item.testProjectCode == 'FINE_AGGREGATE_PARAM_XGL_LLZHL') {
                  this.lvzInfo = item.objJson;
                }else if (item.testProjectCode == 'FINE_AGGREGATE_PARAM_XGL_BGMD') {
                  this.bgmdInfo = item.objJson;
                }else if (item.testProjectCode == 'FINE_AGGREGATE_PARAM_XGL_BKHL') {
                  this.bkhkInfo = item.objJson;
                }else if (item.testProjectCode == 'FINE_AGGREGATE_PARAM_XGL_JMMD') {
                  this.jmmdInfo = item.objJson;
                }else if (item.testProjectCode == 'FINE_AGGREGATE_PARAM_XGL_MCHSL') {
                  this.mchslInfo = item.objJson;
                }else if (item.testProjectCode == 'FINE_AGGREGATE_PARAM_XGL_MCHNL') {
                  this.mchjInfo = item.objJson;
                }else if (item.testProjectCode == 'FINE_AGGREGATE_PARAM_XGL_MCHNKL') {
                  this.mcnkInfo = item.objJson;
                }
              })
          }
          
        },
        deep: true,
        immediate: true
      },
    },
    data() {
      return {
        experimentInfo: {},
        experimentDetailObj: {},
        shxhSynchronizedata: {},
        nkInfo: {}, // 泥块含量
        sfxInfo: {
          sfxInfo: []
        }, // 筛分析
        hslInfo: {}, // 含水率
        hnlInfo: {}, // 含泥量
        ymInfo: {}, // 云母含量
        ysInfo: {}, // 压碎指标
        djInfo: {}, // 堆积密度
        lvzInfo: {}, // 氯离子
        bgmdInfo: {}, // 表观密度
        bkhkInfo: {}, // 贝壳含量
        jmmdInfo: {}, // 紧密密度
        mchslInfo: {}, // 目测含水率
        mchjInfo: {}, // 目测含泥
        mcnkInfo: {}, // 目测泥块含量,
        ys:{},
      }
    },
    mounted(){
      if (this.mixProportion && this.mixProportion.proportionMaterial) {
            var proportionMaterial = this.mixProportion.proportionMaterial || {};
            this.ys = proportionMaterial.xgl || {};
        }
    },
    methods: {
      getShowCJ(obj, config){
            let cj = obj?.factory;
            let gysmc = obj?.supplyCompanyName;
            var showName = cj;
            var result = '/';

            if(config == 1){
                if(!this.isEmpty(cj)){
                    showName = cj
                } 
            } else {
                if(!this.isEmpty(gysmc)){
                    showName = gysmc
                }
            }
            result = this.isEmpty(showName) ? '/' : showName;
            return result;
        },


        getShuiniJCRQ(value){
            if(!value) return '---';
            return value.replace('~', '至');
        },

      isEmpty(val) {
            if (typeof val === "boolean") {
                return false;
            }
            if (typeof val === "number") {
                return false;
            }
            if (val instanceof Array) {
                if (val.length === 0) return true;
            } else if (val instanceof Object) {
                if (JSON.stringify(val) === "{}" || val == null ) return true;
            } else {
                if (
                val === "null" ||
                val == null ||
                val === "undefined" ||
                val === undefined ||
                val === ""
                )
                return true;
                return false;
            }
            return false;
        },
      getCpEvenRound(str, index){
            if(this.isEmpty(str)) return '/'
            return cpEvenRound(str, index);
        },

      getShowResultStr(str){
        var res = str;
        if(this.isEmpty(str)) return '--';
        if(str.includes(':')){
          res = str.split(':').slice(1).join(':');
        }
        if(str.includes('：')){
          res = str.split('：').slice(1).join('：');
        }
        if(res.includes('。')){
          res = res.split('。')[0] + '。';
        }
        return res.replace(';', '').replace('；', '');

      },
    },

    filters: {
      momentDate: function(value) {
        if (value) return moment(value).format("YYYY-MM-DD");
        return "---";
      },
      isNull: function(value) {
        if (value == undefined || value == null || value === '') return '---'

        return value;
      },
      // 存在斜线也算 ---
      isSLNull: function(value) {
        if (value == undefined || value == null || value === ''|| value === '/') return '---'

        return value;
      },
      shxhNull: function(value) {
        if (value == undefined || value == null || value === '') return '---'

        return value;
      },

      sfxInfoSkgjzj: function(value) {
        if (value) return value.skgjzj || '---';
        return '--';
      },
      
      // 计算分级筛余
      sfxFjsypjz: function(value) {
        if (value) {
          let arr = [
            {
              v: value.fjsy1 || 0,
            },{
              k: '+',
              v: value.fjsy2 || 0,
            },{
              k: '/',
              v: 2,
            }
          ]

          return calcEquation(arr, 2);
        }
        return "/"
      },
      // 计算累计筛余
      sfxLjsypjz: function(value) {
        if (value) {
          let arr = [
            {
              v: value.ljsy1 || 0,
            },{
              k: '+',
              v: value.ljsy2 || 0,
            },{
              k: '/',
              v: 2,
            }
          ]

          return calcEquation(arr, 2);
        }
        return "/"
      }
    },
  }
</script>

<style lang="scss" scoped>
  @import '../print.css';

.border {
    border: 3px solid black; /* 设置边框粗细 */
}
.border th, .border td{
    border: 2px solid black; /* 设置边框粗细 */
}
</style>