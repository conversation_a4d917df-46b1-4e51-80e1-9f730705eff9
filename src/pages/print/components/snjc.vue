<!--
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-05-22 00:03:51
 * @LastEditors: huaze.fan <EMAIL>
 * @LastEditTime: 2025-03-21 15:37:40
 * @FilePath: /quality_center_web/src/pages/print/components/hntks.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div class="portrait">
        <div class="tip-view" style="margin-top: 8px;">
            <div class="tip-label">C-15b-0907</div>
        </div>
        <div class="company-label" style="line-height: 36px;font-weight: 900; margin-top: 25px; font-size: 28px;">{{companyName}}</div>
        <div class="title-label" style="line-height: 36px;font-weight: 900; font-size: 36px;">水泥检测报告</div>
        <div align="center" class="lab-report" style="margin-top: 10px;">
            <div class="lab-sub-title">
                <div class="lab-sub-title-container" style="margin-top: 40px; margin-left: 140px;">第1页{{'&nbsp&nbsp&nbsp&nbsp'}}共1页</div>
                    <div class="lab-sub-title-container">
                    <div class="lab-sample-id">委托编号:<span>{{ snReportObjInfo?.consignId | isNull }} </span></div>
                    <div class="lab-report-id" style="margin-top: 16px;">报告编号:<span>{{ experimentInfo?.reportNo | isNull }} </span></div>
                </div>
            </div>
            <table border="1" class="border" style="margin-bottom: 20px; table-layout: fixed; margin-top: 16px;">
                <tbody>
                    <tr height="40">
                        <td colspan="3">委托部门</td>
                        <!-- <td colspan="21" left>{{ experimentInfo?.experimentDept | isNull }}</td> -->
                        <td colspan="21" left>{{ '---' }}</td>
                    </tr>
                    <tr height="40">
                        <td colspan="3">委托日期</td>
                        <td left colspan="9">{{ experimentInfo?.entrustTime | momentDate }}</td>
                        <td colspan="3">报告日期</td>
                        <td left colspan="9">{{ experimentInfo?.reportDate | momentDate }}</td>
                    </tr>
                </tbody>
            </table>

            <table border="1" class="border" style="margin-bottom: 20px; table-layout: fixed; margin-top: 30px;">
                <tr height="40">
                    <td colspan="3">样品编号</td>
                    <td colspan="4" left>{{ snReportObjInfo?.sampleId | isNull }}</td>
                    <td colspan="3">样品名称 </td>
                    <td colspan="7" left>{{ experimentInfo?.materialsName | isNull }}</td>
                    <!-- <td colspan="3">材料简称</td>
                    <td colspan="4" left>{{ experimentInfo?.materialsSpecs | isNull }}</td> -->
                    <td colspan="3">产品代号</td>
                    <td colspan="4" left>{{ experimentInfo?.materialAbbreviation | isNull }}</td>
                </tr>
                <tr height="40">
                    <td colspan="3">生产单位</td>
                    <td colspan="14" left>{{ getShowCJ(experimentInfo, companyInfo?.fmhConfig) }}</td>
                    <td colspan="3">代表数量</td>
                    <td colspan="4" left>{{ experimentInfo?.behalfNumber | isNull }}t</td>
                </tr>
                <tr height="40">
                    <td colspan="3">批号</td>
                    <td left colspan="4">{{ getBatchString(snReportObjInfo?.sampleId) }}</td>
                    <!-- <td colspan="4" left>{{ experimentInfo?.batch | isNull }}</td> -->
                    <td colspan="3">备案证号</td>
                    <td colspan="7" left>{{ experimentInfo?.certificateNo | shxhNull }}</td>
                    <!-- <td colspan="3">强度等级</td>
                    <td colspan="4" left>{{ experimentInfo?.sampleLevel | isNull }}</td> -->
                    <td colspan="3">强度等级</td>
                    <td colspan="4" left>{{ experimentInfo?.materialsSpecs | isNull }}</td>
                </tr>
                <tr height="40">
                    <td colspan="3">评定依据</td>
                    <td colspan="21" left>{{ experimentInfo?.judgeGist | isNull }}</td>
                </tr>
                <tr height="40">
                    <td colspan="3">检测方法</td>
                    <td colspan="21" left>{{ experimentInfo?.experimentGist | isNull }}</td>
                </tr>
                <tr height="40">
                    <td colspan="3">检测日期</td>
                    <!-- 需要计算排序 -->
                    <td colspan="21" left>{{ getShuiniJCRQ(experimentInfo?.jcrq) }}</td>
                </tr>
                <tr height="40">
                    <td colspan="6">检测参数</td>
                    <td colspan="12">
                        <div style="display: flex;">
                            <span style="width: 50%; text-align: center;">标准值</span>
                            <span style="width: 50%; text-align: center;">检测值</span>
                        </div>
                    </td>
                    <td colspan="6">单项结果</td>
                </tr>
                <tr height="40">
                    <td colspan="6">细度(--- μm筛析法)</td>
                    <td colspan="6">≥---%</td>
                    <td colspan="6">{{ xdInfo?.pjz | isNull }}%</td>
                    <td colspan="6">{{ xdInfo?.sfhg | isNull }}</td>
                </tr>
                <tr height="40">
                    <td colspan="6">比表面积</td>
                    <td colspan="6">≥{{ '--' }} ㎡/kg</td>
                    <td colspan="6">{{ bbmjInfo?.pjz | isNull }}㎡/kg</td>
                    <td colspan="6">{{ bbmjInfo?.dxjl | isNull }}</td>
                </tr>
                <tr height="40">
                    <td colspan="6">初凝时间</td>
                    <td colspan="6">不得早于45min</td>
                    <td colspan="6">{{ njsjInfo?.jgjs ? njsjInfo?.jgjs?.cnsj || '/' : '/' }}min</td>
                    <td colspan="6">{{ njsjInfo?.jgjs ? njsjInfo?.jgjs?.cnsjjl || '/' : '/' }}</td>
                </tr>
                <tr height="40">
                    <td colspan="6">终凝时间</td>
                    <td colspan="6">不得迟于600min</td>
                    <td colspan="6">{{ njsjInfo?.jgjs ? njsjInfo?.jgjs?.znsj || '/' : '/' }}min</td>
                    <td colspan="6">{{ njsjInfo?.jgjs ? njsjInfo?.jgjs?.znsjjl || '/' : '/' }}</td>
                </tr>
                <tr height="40">
                    <td colspan="6">安定性</td>
                    <td colspan="6">必须合格</td>
                    <td colspan="6">{{ adInfo?.sbf?.jl }}</td>
                    <td colspan="6">{{ adInfo?.sbf?.jl }}</td>
                </tr>
                <tr height="40">
                    <td colspan="6">标准稠度用水量</td>
                    <td colspan="6">---</td>
                    <td colspan="6">{{ getCpEvenRound(bzcdInfo?.bzcdysl) }}%</td>
                    <td colspan="6">{{ bzcdInfo?.dxjl | isNull }}</td>
                </tr>
                <tr height="40">
                    <td colspan="6">氯离子(质量分数)</td>
                    <td colspan="6">≤0.06%</td>
                    <td colspan="6">--</td>
                    <td colspan="6">--</td>
                </tr>
                <tr height="40">
                    <td rowspan="2" colspan="3">检测龄期</td>
                    <td colspan="9">抗折强度/MPa</td>
                    <td colspan="12">抗压强度/MPa</td>
                </tr>
                <tr height="40">
                    <td colspan="2">标准值</td>
                    <td colspan="2">检测值</td>
                    <td colspan="2">平均值</td>
                    <td colspan="3">单项结果</td>

                    <td colspan="2">标准值</td>
                    <td colspan="4">检测值</td>
                    <td colspan="3">平均值</td>
                    <td colspan="3">单项结果</td>
                </tr>
                <tr height="40">
                    <td rowspan="3" colspan="3">3d</td>
                    <td colspan="2" rowspan="3">≥{{standardList[0]}}</td>
                    <td colspan="2">{{ kzqdsyInfo3d(qdInfo?.kzqdsy3d, 0).kzqd | isNull }}</td>
                    <td rowspan="3" colspan="2">{{ qdInfo?.kzqdsy3d?.pjz | isNull }}</td>
                    <td rowspan="3" colspan="3">{{ qdInfo?.kzqdsy3d?.dxjl | isNull }}</td>
                    <td rowspan="3" colspan="2">≥{{standardList[1]}}</td>
                    <td colspan="2">{{ kyqdsyInfo3d(qdInfo?.kyqdsy3d, 0).kyqd | isNull }}</td>
                    <td colspan="2">{{ kyqdsyInfo3d(qdInfo?.kyqdsy3d, 1).kyqd | isNull }}</td>
                    <td colspan="3" rowspan="3">{{ qdInfo?.kyqdsy3d?.pjz | isNull }}</td>
                    <td rowspan="3" colspan="3">{{ qdInfo?.kyqdsy3d?.dxjl | isNull }}</td>
                </tr>
                <tr height="40">
                    <td colspan="2">{{ kzqdsyInfo3d(qdInfo?.kzqdsy3d, 1).kzqd | isNull }}</td>
                    <td colspan="2">{{ kyqdsyInfo3d(qdInfo?.kyqdsy3d, 2).kyqd | isNull }}</td>
                    <td colspan="2">{{ kyqdsyInfo3d(qdInfo?.kyqdsy3d, 3).kyqd | isNull }}</td>
                </tr>
                <tr height="40">
                    <td colspan="2">{{ kzqdsyInfo3d(qdInfo?.kzqdsy3d, 2).kzqd | isNull }}</td>
                    <td colspan="2">{{ kyqdsyInfo3d(qdInfo?.kyqdsy3d, 4).kyqd | isNull }}</td>
                    <td colspan="2">{{ kyqdsyInfo3d(qdInfo?.kyqdsy3d, 5).kyqd | isNull }}</td>
                </tr>
                <tr height="40">
                    <td rowspan="3" colspan="3">28d</td>
                    <td colspan="2" rowspan="3">≥{{standardList[2]}}</td>
                    <td colspan="2">{{ kzqdsyInfo28d(qdInfo?.kzqdsy28d, 0).kzqd | isNull }}</td>
                    <td rowspan="3" colspan="2">{{ qdInfo?.kzqdsy28d?.pjz | isNull }}</td>
                    <td rowspan="3" colspan="3">{{ qdInfo?.kzqdsy28d?.dxjl | isNull }}</td>
                    <td rowspan="3" colspan="2">≥{{standardList[3]}}</td>
                    <td colspan="2">{{ kyqdsyInfo28d(qdInfo?.kyqdsy28d, 0).kyqd | isNull }}</td>
                    <td colspan="2">{{ kyqdsyInfo28d(qdInfo?.kyqdsy28d, 1).kyqd | isNull }}</td>
                    <td colspan="3" rowspan="3">{{ qdInfo?.kyqdsy28d?.pjz | isNull }}</td>
                    <td rowspan="3" colspan="3">{{ qdInfo?.kyqdsy28d?.dxjl | isNull }}</td>
                </tr>
                <tr height="40">
                    <td colspan="2">{{ kzqdsyInfo28d(qdInfo?.kzqdsy28d, 1).kzqd | isNull }}</td>
                    <td colspan="2">{{ kyqdsyInfo28d(qdInfo?.kyqdsy28d, 2).kyqd | isNull }}</td>
                    <td colspan="2">{{ kyqdsyInfo28d(qdInfo?.kyqdsy28d, 3).kyqd | isNull }}</td>
                </tr>
                <tr height="40">
                    <td colspan="2">{{ kzqdsyInfo28d(qdInfo?.kzqdsy28d, 2).kzqd | isNull }}</td>
                    <td colspan="2">{{ kyqdsyInfo28d(qdInfo?.kyqdsy28d, 4).kyqd | isNull }}</td>
                    <td colspan="2">{{ kyqdsyInfo28d(qdInfo?.kyqdsy28d, 5).kyqd | isNull }}</td>
                </tr>
                <tr height="60">
                    <td colspan="3">检测结论</td>
                    <td colspan="21" left>{{ getShowResultStr(experimentInfo?.conclusion) }}</td>
                </tr>
            </table>

            <table border="1" class="border" style="margin-top: 20px; table-layout: fixed;">
                <tbody>
                <tr height="70">
                <td colspan="3">备注</td>
                <td colspan="21" left>{{ experimentInfo?.remark | isNull }}</td>
                </tr>
                </tbody>
            </table>
            <table class="lab-sign" style="border: none; margin-top: 5px">
                <tr height="40" style="border: none;">
                <td style="border: none;"><div class="sign-user">检测报告专用章：</div></td>
                <td style="border: none;">
                    <div class="sign-user">
                    批准：
                    <!-- <img
                    v-if="true"
                    src=""
                    width="150" height="50" /> -->
                    </div>
                </td>
                <td style="border: none;">
                    <div class="sign-user">
                    审核：
                    <!-- <img
                    v-if="true"
                    src=""
                    width="150" height="50" /> -->
                    </div>
                </td>
                <td style="border: none;">
                    <div class="sign-user">
                    检测：
                    <!-- <img
                    v-if="true"
                    src=""
                    width="150" height="50" /> -->
                    </div>
                </td>
                </tr>
            </table>
        </div>
    </div>
</template>

<script>
import moment from "moment";
import {cpEvenRound} from "@/utils/calculate.js"
export default {
    props: {
        companyName: {
            type: String,
            default: ""
        },
        mixExperimentInfo:{
            type: Object,
            default: () => { return {} }
        },
        mixProportion: {
            type: Object,
            default: () => { return {} }
        },
        companyInfo: {
            type: Object,
            default: () => { return {} }
        },
    },

    filters: {
        momentDate: function(value) {
            if (value) return moment(value).format("YYYY-MM-DD");
            return "---";
        },
        isNull: function (value) {
            if (value == undefined || value == null || value === '') return '---'
            
            return value;
        },
        shxhNull: function (value) {
            if (value == undefined || value == null || value === '') return '---'
            
            return value;
        },
    },

    data() {
        return {
            supplyTask: this.supplyTaskList && this.supplyTaskList.length > 0 ? this.supplyTaskList[0] : {},
            xdInfo: {}, //细度
            bbmjInfo: {}, //比表面积
            njsjInfo: {}, //凝结时间
            adInfo: {
                sbf: {}
            },   // 安定性
            bzcdInfo: {}, // 标准稠度
            qdInfo: {}, // 强度
            // 水泥对象
            experimentInfo:{},
            // 水泥报告对象
            snReportObjInfo:{},
            experimentDetailList:[],
            sn:{},
            // 标准值list
            standardList:['---', '---', '---', '---'],
            
        }
    },

    mounted() {
        if (this.mixProportion && this.mixProportion.proportionMaterial) {
            var proportionMaterial = this.mixProportion.proportionMaterial || {};
            this.sn = proportionMaterial.sn || {};
        }

        if(this.mixExperimentInfo) {
            let objInfo = this.mixExperimentInfo?.snInfo || {};
            if(objInfo && objInfo.shxhSynchronizedataList && objInfo.shxhSynchronizedataList.length > 0){
                this.snReportObjInfo = objInfo.shxhSynchronizedataList[0];
            }
            this.experimentInfo = objInfo?.experimentInfo || {}
                if (objInfo.experimentDetailList && objInfo.experimentDetailList.length > 0) {
                    objInfo.experimentDetailList.map(item => {
                    if (item.testProjectCode === 'CEMENT_PARAM_BZCD') {
                        // 标准稠度
                        this.bzcdInfo = item.objJson || {};
                    }else if (item.testProjectCode === 'CEMENT_PARAM_NJSJ') {
                        // 凝结时间
                        this.njsjInfo = item.objJson || {};
                    }else if (item.testProjectCode === 'CEMENT_PARAM_ADX') {
                        // 安定性
                        this.adInfo = item.objJson || {};
                    }else if (item.testProjectCode === 'CEMENT_PARAM_XD') {
                        // 细度
                        this.xdInfo = item.objJson || {};
                    }else if (item.testProjectCode === 'CEMENT_PARAM_BBMJ') {
                        // 比表面积
                        this.bbmjInfo = item.objJson || {};
                    }else if (item.testProjectCode === 'CEMENT_PARAM_QDCD') {
                        // 强度
                        this.qdInfo = item.objJson || {};
                    }

                console.log('水泥打印数据',objInfo.experimentDetailList)
                });
            }
        }
        // 获取水泥标准值
        this.getAccordingToHand();
        
    },

    methods: {
        getShuiniJCRQ(value){
            if(!value) return '---';
            return value.replace('~', '至');
        },
        getShowCJ(obj, config){
            let cj = obj?.factory;
            let gysmc = obj?.supplyCompanyName;
            var showName = cj;
            var result = '/';

            if(config == 1){
                if(!this.isEmpty(cj)){
                    showName = cj
                } 
            } else {
                if(!this.isEmpty(gysmc)){
                    showName = gysmc
                }
            }
            result = this.isEmpty(showName) ? '/' : showName;
            return result;
        },
        
    getAccordingToHand(){
     var params = {
        testProjectCode: "CEMENT_PARAM_QDCD",
        materialAbbreviation : this.experimentInfo?.materialAbbreviation,
        materialsName : this.experimentInfo?.materialsName,
        materialsSpec : this.experimentInfo?.materialsSpecs
      }
      this.$api.getAccordingTo(params, this).then(res =>{
        if(res.data?.list?.length > 0){
            let o = res.data.list[0].objJson;
            let objJson = JSON.parse(o);
            console.log('标准值：', objJson);
            var list = []
            list[0] = this.getCpEvenRound(objJson?.kz3d?.min) || '--';
            list[1] = this.getCpEvenRound(objJson?.ky3d?.min) || '--';
            list[2] = this.getCpEvenRound(objJson?.kz28d?.min) || '--';
            list[3] = this.getCpEvenRound(objJson?.ky28d?.min) || '--';
            this.standardList = list;
        }
      })
    },

isEmpty(val) {
      if (typeof val === "boolean") {
          return false;
      }
      if (typeof val === "number") {
          return false;
      }
      if (val instanceof Array) {
          if (val.length === 0) return true;
      } else if (val instanceof Object) {
          if (JSON.stringify(val) === "{}" || val == null ) return true;
      } else {
          if (
          val === "null" ||
          val == null ||
          val === "undefined" ||
          val === undefined ||
          val === ""
          )
          return true;
          return false;
      }
      return false;
  },
getShowResultStr(str){
  var res = str;
  if(this.isEmpty(str)) return '--';
        if(str.includes(':')){
          res = str.split(':').slice(1).join(':');
        }
        if(str.includes('：')){
          res = str.split('：').slice(1).join('：');
        }
        if(res.includes('。')){
          res = res.split('。')[0] + '。';
        }
        return res.replace(';', '').replace('；', '');

      },
        getBatchString(value){
            if(!value) return '--';
            if(value.length < 3) return '--';
            return value.substring(value.length-3, value.length);
        },
        // 3d抗折
        kzqdsyInfo3d: function (value, index) {
            try {
                if (!value) return {};
                if (value.kzqdsyInfo) {
                    if (index != undefined) {
                        if ((value.kzqdsyInfo.length - 1) >= index) {
                            let item = value.kzqdsyInfo[index];
                            return item;
                        }
                    }else{
                        return value.kzqdsyInfo;
                    }
                }
                
                return {};
            } catch (error) {
                return {};
            }
        },
        // 28d抗折
        kzqdsyInfo28d: function (value, index) {
            try {
                if (!value) return {};
                if (value.kzqdsyInfo) {
                    if (index != undefined) {
                        if ((value.kzqdsyInfo.length - 1) >= index) {
                            let item = value.kzqdsyInfo[index];
                            return item;
                        }
                    }else{
                        return value.kzqdsyInfo;
                    }
                }
                
                return {};
            } catch (error) {
                return {};
            }
        },
        getCpEvenRound(str){
            if(this.isEmpty(str)) return '--'
            return cpEvenRound(str, 1);
        },
        isEmpty(val) {
            if (typeof val === "boolean") {
                return false;
            }
            if (typeof val === "number") {
                return false;
            }
            if (val instanceof Array) {
                if (val.length === 0) return true;
            } else if (val instanceof Object) {
                if (JSON.stringify(val) === "{}" || val == null ) return true;
            } else {
                if (
                val === "null" ||
                val == null ||
                val === "undefined" ||
                val === undefined ||
                val === ""
                )
                return true;
                return false;
            }
            return false;
        },

        // 3d抗压
        kyqdsyInfo3d: function (value, index) {
            try {
                if (!value) return {};
                if (value.kyqdsyInfo) {
                    if (index != undefined) {
                        if ((value.kyqdsyInfo.length - 1) >= index) {
                            let item = value.kyqdsyInfo[index];
                            return item;
                        }
                    }else{
                        return value.kyqdsyInfo;
                    }
                }
                
                return {};
            } catch (error) {
                return {};
            }
        },
        // 28d抗压
        kyqdsyInfo28d: function (value, index) {
            try {
                if (!value) return {};
                if (value.kyqdsyInfo) {
                    if (index != undefined) {
                        if ((value.kyqdsyInfo.length - 1) >= index) {
                            let item = value.kyqdsyInfo[index];
                            return item;
                        }
                    }else{
                        return value.kyqdsyInfo;
                    }
                }
                
                return {};
            } catch (error) {
                return {};
            }
        },
    }
}
</script>

<style lang="scss" scoped>
@import '../print.css';
.border {
    border: 3px solid black; /* 设置边框粗细 */
}
.border th, .border td{
    border: 2px solid black; /* 设置边框粗细 */
}
</style>