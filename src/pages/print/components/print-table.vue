<!--
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-09-22 13:25:32
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2024-09-22 15:29:23
 * @FilePath: /quality_center_web/src/pages/print/components/print-table.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script>
import { Table } from "element-ui";
export default {
    name: 'PrintTable',
    //继承table组件，并扩展
    extends: Table,
    mounted() {
        this.$nextTick(() => {
            let thead = document.querySelector(".el-table__header thead");
            let theadNew = thead.cloneNode(true);
            let body = document.querySelector(".el-table__body");
            body.appendChild(theadNew);
            // 删除原始的 el-table__header-wrapper
            let header = document.querySelector(".el-table__header-wrapper");
            header.remove();
        });
    },
};
</script>

<style lang="scss" scoped>

</style>