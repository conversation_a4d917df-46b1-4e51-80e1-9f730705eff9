<!--
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-05-22 00:03:51
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2024-10-22 23:02:23
 * @FilePath: /quality_center_web/src/pages/print/components/hntks.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div class="portrait">
        <div class="tip-view" style="margin-top: 40px;">
            <div class="tip-label">C-40b-0907</div>
        </div>
        <div class="company-label" style="line-height: 36px;font-weight: 900; font-size: 28px;">{{companyName}}</div>
        <div class="title-label" style="line-height: 36px;font-weight: 900; font-size: 36px;">粒化高炉矿渣微粉检测报告</div>
        <div align="center" class="lab-report">
        <div class="lab-sub-title" style="margin-top: 50px;">
            <div class="lab-sub-title-container" style="margin-top: 50px; margin-left: 140px;">第1页{{'&nbsp&nbsp&nbsp&nbsp'}}共1页</div>
                <div class="lab-sub-title-container">
                <div class="lab-sample-id">委托编号:<span>{{ shxhSynchronizedata?.consignId | isNull }} </span></div>
                <div class="lab-report-id" style="margin-top: 10px;">报告编号:<span>{{ experimentInfo?.reportNo | isNull }} </span></div>
            </div>
        </div>
        <table border="1" class="border" style="margin-bottom: 20px; margin-top: 10px; table-layout: fixed;">
            <tbody>
                <tr height="40">
                    <td colspan="3">委托部门</td>
                    <!-- <td colspan="21" left>{{ experimentInfo?.experimentDept | isNull }}</td> -->
                    <td colspan="21" left>{{ '---' }}</td>
                </tr>
                <tr height="40">
                    <td colspan="3">委托日期</td>
                    <td left  colspan="12">{{ experimentInfo?.entrustTime | momentDate }}</td>
                    <td colspan="3">报告日期</td>
                    <td left  colspan="6">{{ experimentInfo?.reportDate | momentDate }}</td>
                </tr>
            </tbody>
        </table>
        <table  border="1" class="border" style="table-layout: fixed;margin-top: 30px;">
            <tr height="40">
                <td colspan="3">样品编号</td>
                <td colspan="12" left>{{ shxhSynchronizedata?.sampleId | shxhNull }}</td>
                <td colspan="3">种类级别</td>
                <td left colspan="6">{{ experimentInfo?.materialsSpecs | isNull }}</td>
                <!-- <td left colspan="6">{{ experimentInfo?.sampleLevel | isNull }}</td> -->
            </tr>
            <tr height="40">
                <td colspan="3">样品名称</td>
                <td colspan="12" left>{{ experimentInfo?.materialsName | isNull }}</td>
                <td colspan="3">备案证号</td>
                <td left colspan="6" nowrap="nowrap">{{ experimentInfo?.certificateNo | shxhNull }}</td>
            </tr>
            <tr height="40">
                <td colspan="3">生产单位</td>
                <td colspan="12" left>{{ getShowCJ(experimentInfo, companyInfo?.fmhConfig) }}</td>
                <td colspan="3">代表数量</td>
                <td left colspan="6">{{ experimentInfo?.behalfNumber | isNull }}t</td>
            </tr>
            <tr height="40">
                <td colspan="3">检测日期</td>
                <td colspan="21" left>{{ experimentInfo?.jcrq | isNull }}</td>
            </tr>
            <tr height="40">
                <td colspan="3">评定依据</td>
                <td colspan="21" left>{{ experimentInfo?.judgeGist | isNull }}</td>
            </tr>
            <tr height="40">
                <td colspan="3">检测方法</td>
                <td colspan="21" left>{{ experimentInfo?.experimentGist | isNull }}</td>
            </tr>
            <tr height="40">
                <td colspan="7" rowspan="2">检测项目</td>
                <td colspan="12">技术指标</td>
                <td rowspan="2" colspan="5">检测值</td>
            </tr>
            <tr height="40">
                <td colspan="4">S105</td>
                <td colspan="4">S95</td>
                <td colspan="4">S75</td>
            </tr>
            <tr height="40">
                <td colspan="7">密度ρ /(g/cm³)</td>
                <td colspan="12">≥2.8</td>
                <td colspan="5">{{ mdInfo?.pjz | isNull }}</td>
            </tr>
            <tr height="40">
                <td colspan="7">比表面积/(㎡/kg)</td>
                <td colspan="4">≥500</td>
                <td colspan="4">≥400</td>
                <td colspan="4">≥300</td>
                <td colspan="5">{{ bbmjInfo?.pjz | isNull }}</td>
            </tr>
            <tr height="40">
                <td colspan="5" rowspan="2">活性指数/%</td>
                <td colspan="2">7d</td>
                <td colspan="4">≥95</td>
                <td colspan="4">≥70</td>
                <td colspan="4">≥55</td>
                <td colspan="5">{{ hxzbInfo?.length > 1 ? (hxzbInfo[0]?.hxzs || '/') : '/' }}</td>
            </tr>
            <tr height="40">
                <td colspan="2">28d</td>
                <td colspan="4">≥105</td>
                <td colspan="4">≥95</td>
                <td colspan="4">≥75</td>
                <td colspan="5">{{ hxzbInfo?.length > 1 ? (hxzbInfo[1]?.hxzs || '/') : '/' }}</td>
            </tr>
            <tr height="40">
                <td colspan="7">流动度比/%</td>
                <td colspan="12">≥95</td>
                <td colspan="5">{{ ldxInfo?.syjg | isNull }}</td>
            </tr>
            <tr height="40">
                <td colspan="7">含水量/%</td>
                <td colspan="12">≤1.0</td>
                <td colspan="5">{{ hslInfo?.hsl | isNull }}</td>
            </tr>
            <tr height="40">
                <td colspan="7">烧失量/%</td>
                <td colspan="12">---</td>
                <td colspan="5">{{ sslInfo?.sslpjz | isNull }}</td>
            </tr>
            <tr height="40" v-if="syhlInfo?.sylzlfs">
                <td colspan="7">三氧化硫</td>
                <td colspan="12">≤4.0</td>
                <td colspan="5">{{ syhlInfo?.sylzlfs | isNull }}</td>
            </tr>

            <!-- 空数据 -->
            <tr height="40">
                <td colspan="7">---</td>
                <td colspan="12">---</td>
                <td colspan="5">---</td>
            </tr>

            
            <tr height="40">
                <td colspan="7">---</td>
                <td colspan="12">---</td>
                <td colspan="5">---</td>
            </tr>

            <tr height="40">
                <td colspan="7">---</td>
                <td colspan="12">---</td>
                <td colspan="5">---</td>
            </tr>

            <tr height="40">
                <td colspan="7">---</td>
                <td colspan="12">---</td>
                <td colspan="5">---</td>
            </tr>

            <tr height="40">
                <td colspan="7">---</td>
                <td colspan="12">---</td>
                <td colspan="5">---</td>
            </tr>

            <tr height="40">
                <td colspan="7">---</td>
                <td colspan="12">---</td>
                <td colspan="5">---</td>
            </tr>
            <!-- 空数据 -->

            <tr height="40">
                <td colspan="3">检测结论</td>
                <td colspan="21" left>{{ getShowResultStr(experimentInfo?.conclusion) }}</td>
            </tr>
        </table>

        <table  border="1" class="border" style="margin-top: 20px; table-layout: fixed;">
            <tbody>
            <tr height="100">
            <td colspan="3">备注</td>
            <td colspan="21" left>{{ experimentInfo?.remark | isNull }}</td>
            </tr>
            </tbody>
        </table>
        <table class="lab-sign" style="border: none; margin-top: 5px;">
            <tr height="40" style="border: none;">
            <td style="border: none;"><div class="sign-user">检测报告专用章：</div></td>
            <td style="border: none;">
                <div class="sign-user">
                批准：
                <!-- <img
                v-if="true"
                src=""
                width="150" height="50" /> -->
                </div>
            </td>
            <td style="border: none;">
                <div class="sign-user">
                审核：
                <!-- <img
                v-if="true"
                src=""
                width="150" height="50" /> -->
                </div>
            </td>
            <td style="border: none;">
                <div class="sign-user">
                检测：
                <!-- <img
                v-if="true"
                src=""
                width="150" height="50" /> -->
                </div>
            </td>
            </tr>
        </table>
        </div>
    </div>
</template>

<script>
import moment from 'moment';
export default {
    props: {
        
        mixExperimentInfo:{
            type: Object,
            default: () => { return {} }
        },
        companyName: {
            type: String,
            default: ""
        },
        mixProportion: {
            type: Object,
            default: () => { return {} }
        },
        companyInfo: {
            type: Object,
            default: () => { return {} }
        },
    },

    filters: {
        momentDate: function(value) {
          if (value) return moment(value).format("YYYY-MM-DD");
          return "---";
        },
        isNull: function (value) {
            if (value == undefined || value == null || value === '') return '---'
            
            return value;
        },
        shxhNull: function (value) {
            if (value == undefined || value == null || value === '') return '---'
            
            return value;
        }
    },

    data() {
        return {
            mdInfo: {}, //密度
            bbmjInfo: {}, //比表面积
            ldxInfo: {}, //流动性
            hxzbInfo: [{},{}],   // 活性指标
            hslInfo: {}, // 含水量
            sslInfo: {}, // 强度
            syhlInfo: {}, // 三氧化硫
            // 对象
            experimentInfo:{},
            // 报告对象
            shxhSynchronizedata:{},
            experimentDetailList:[],
            kzf:{},
        }
    },
    methods: {

        getShowCJ(obj, config){
            let cj = obj?.factory;
            let gysmc = obj?.supplyCompanyName;
            var showName = cj;
            var result = '/';

            if(config == 1){
                if(!this.isEmpty(cj)){
                    showName = cj
                } 
            } else {
                if(!this.isEmpty(gysmc)){
                    showName = gysmc
                }
            }
            result = this.isEmpty(showName) ? '/' : showName;
            return result;
        },

        isEmpty(val) {
            if (typeof val === "boolean") {
                return false;
            }
            if (typeof val === "number") {
                return false;
            }
            if (val instanceof Array) {
                if (val.length === 0) return true;
            } else if (val instanceof Object) {
                if (JSON.stringify(val) === "{}" || val == null ) return true;
            } else {
                if (
                val === "null" ||
                val == null ||
                val === "undefined" ||
                val === undefined ||
                val === ""
                )
                return true;
                return false;
            }
            return false;
        },
      getShowResultStr(str){
        var res = str;
        if(this.isEmpty(str)) return '--';
        if(str.includes(':')){
          res = str.split(':').slice(1).join(':');
        }
        if(str.includes('：')){
          res = str.split('：').slice(1).join('：');
        }
        if(res.includes('。')){
          res = res.split('。')[0] + '。';
        }
        return res.replace(';', '').replace('；', '');

      },
    },

    mounted() {
        if (this.mixProportion && this.mixProportion.proportionMaterial) {
            var proportionMaterial = this.mixProportion.proportionMaterial || {};
            this.kzf = proportionMaterial.kzf || {};
        }

        if(this.mixExperimentInfo) {
            let objInfo = this.mixExperimentInfo?.kzfInfo || {};
            if(objInfo && objInfo.shxhSynchronizedataList && objInfo.shxhSynchronizedataList.length > 0){
                this.shxhSynchronizedata = objInfo.shxhSynchronizedataList[0];
            }
            this.experimentInfo = objInfo?.experimentInfo || {}
                if (objInfo.experimentDetailList && objInfo.experimentDetailList.length > 0) {
                    console.log('检测值对象', objInfo.experimentDetailList)
                    objInfo.experimentDetailList.map(item => {
                        if (item.testProjectCode === 'SLAG_PARAM_KZF_YPMD') {
                            // 密度
                            this.mdInfo = item.objJson || {};
                        }else if (item.testProjectCode === 'SLAG_PARAM_KZF_LDDB') {
                            // 流动性
                            this.ldxInfo = item.objJson || {};
                        }else if (item.testProjectCode === 'SLAG_PARAM_KZF_HXZS') {
                            // 活性指标
                            this.hxzbInfo = item.objJson.hxzsInfoList || [{},{}];
                        }else if (item.testProjectCode === 'SLAG_PARAM_KZF_HSL') {
                            // 含水量
                            this.hslInfo = item.objJson || {};
                        }else if (item.testProjectCode === 'SLAG_PARAM_KZF_BBMJ') {
                            // 比表面积
                            this.bbmjInfo = item.objJson || {};
                        }else if (item.testProjectCode === 'SLAG_PARAM_KZF_SSL') {
                            // 烧失量
                            this.sslInfo = item.objJson || {};
                        }else if (item.testProjectCode === 'SLAG_PARAM_KZF_SYHL') {
                            // 三氧化硫
                            this.syhlInfo = item.objJson || {};
                        }
                    });
            }
        }

    },
}
</script>

<style lang="scss" scoped>
@import '../print.css';

.border {
    border: 3px solid black; /* 设置边框粗细 */
}
.border th, .border td{
    border: 2px solid black; /* 设置边框粗细 */
}
</style>