<!--
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-05-22 00:03:51
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2024-10-22 23:12:20
 * @FilePath: /quality_center_web/src/pages/print/components/hntks.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="portrait">
    <div class="tip-view" style="margin-top: 55px;">
      <div class="tip-label">C-01a-0806</div>
    </div>
    <div class="company-label" style="line-height: 36px;font-weight: 900; font-size: 28px;">{{companyName}}</div>
    <div class="title-label" style="line-height: 36px;font-weight: 900; font-size: 36px;">外加剂检测报告</div>
    <div align="center" class="lab-report">

      <div class="lab-sub-title" style="margin-top: 40px; ">
        <div class="lab-sub-title-container" style="margin-top: 40px; margin-left: 140px;">第1页{{'&nbsp&nbsp&nbsp&nbsp'}}共1页</div>
        <div class="lab-sub-title-container">
          <div class="lab-sample-id">委托编号:<span>{{ shxhSynchronizedata?.consignId | isNull }} </span></div>
          <div class="lab-report-id" style="margin-top: 15px;">报告编号:<span>{{ experimentInfo?.reportNo | isNull }} </span></div>
        </div>
      </div>
      <table border="1" class="border" style="margin-bottom: 20px; margin-top: 15px; table-layout: fixed;">
        <tbody>
          <!-- 32 -->
          <tr height="40">
            <td colspan="4">委托部门</td>
            <!-- <td colspan="28" left>{{ experimentInfo?.experimentDept | isNull }}</td> -->
            <td colspan="28" left>{{ '---' }}</td>
          </tr>
          <tr height="40">
            <td colspan="4">委托日期</td>
            <td left colspan="18">{{ experimentInfo?.entrustTime | momentDate }}</td>
            <td colspan="4">报告日期</td>
            <td  colspan="6">{{ experimentInfo?.reportDate | momentDate }}</td>
          </tr>
        </tbody>
      </table>

      <table border="1" class="border" style="margin-bottom: 20px; margin-top: 30px; table-layout: fixed;">
        <tr height="40">
          <td colspan="4">样品编号</td>
          <td colspan="6" left>{{ shxhSynchronizedata?.sampleId | shxhNull }}</td>
          <td colspan="4">样品名称</td>
          <td colspan="8" left>{{ experimentInfo?.materialsName | isNull }}</td>
          <td colspan="4">种类级别</td>
          <td colspan="6" left>---</td>
        </tr>
        <tr height="40">
          <td colspan="4">强度等级</td>
          <td left colspan="6">{{ experimentInfo?.materialsSpecs | isNull }}</td>
          <td colspan="4">代表数量</td>
          <td colspan="4" width="140" left>{{ experimentInfo?.behalfNumber | isNull }}t</td>
          <td colspan="4">检测日期</td>
          <td colspan="10" left>{{ experimentInfo?.jcrq | isNull }}</td>
        </tr>
        <tr height="40">
          <td colspan="4">生产单位</td>
          <td colspan="14" left>{{ getShowCJ(experimentInfo, companyInfo?.fmhConfig) }}</td>
          <td colspan="4">备案证号</td>
          <td colspan="10" left>{{ experimentInfo?.certificateNo | shxhNull }}</td>
        </tr>
        <tr height="40">
          <td colspan="4">工程部位</td>
          <td colspan="28" left>{{ '--' }}</td>
        </tr>
        <tr height="40">
          <td colspan="4">评定依据</td>
          <td colspan="28" left>{{ experimentInfo?.judgeGist | isNull }}</td>
        </tr>
        <tr height="80">
          <td colspan="4">检测方法</td>
          <td colspan="28" left>{{ experimentInfo?.experimentGist | isNull }}</td>
        </tr>
        <tr height="40">
          <td colspan="9">参数名称</td>
          <td colspan="9">技术要求</td>
          <td colspan="9">检测值</td>
          <td colspan="5">单项结果</td>
        </tr>
        <!-- <tr height="40" v-for="item in experimentDetailList" :key="item?.id">
          <td colspan="2">{{ item?.testProjectName }}</td>
          <td colspan="2">{{ item?.testProjectName }}</td>
          <td colspan="2">{{ item?.objJson?.jg }}</td>
          <td colspan="2">{{ item?.objJson?.dxjl }}</td>
        </tr> -->
        <tr height="40" v-for="item in showListInfo" :key="item.title+item.val2">
          <td colspan="9">{{item.title}}</td>
          <td colspan="9">{{item.val1}}</td>
          <!-- <td colspan="9">{{item.val2}}</td>
          <td colspan="5">{{item.val3}}</td> -->
          <td v-if="item.title != '凝结时间之差/min'" colspan="9">{{item.val2}}</td>
          <td v-else colspan="9">
            <span v-if="item.val2">初凝：{{ item.val2 }}</span>
            <span v-if="item.val3">&nbsp&nbsp终凝：{{ item.val3 }}</span>
          </td>
          <td v-if="item.title != '凝结时间之差/min'" colspan="5">{{item.val3}}</td>
          <td v-else colspan="5">
            {{item.val4}}
          </td>
        </tr>
        <!-- <tr height="40">
          <td colspan="9">减水率/%</td>
          <td colspan="9">≥25</td>
          <td colspan="9">{{ getResultStr(jslInfo?.pjz, 2) }}</td>
          <td colspan="5">{{ jslInfo?.dxjl | isNull }}</td>
        </tr>
        <tr height="40">
          <td colspan="9">泌水率比/%</td>
          <td colspan="9">≤70</td>
          <td colspan="9">{{ getResultStr(mslInfo?.qslb, 2) }}</td>
          <td colspan="5">{{ mslInfo?.dxjl | isNull }}</td>
        </tr>
        <tr height="40">
          <td colspan="9">凝结时间之差/min</td>
          <td colspan="9">＞+90</td>
          <td colspan="9">
            <span v-if="njsjInfo?.cnsjzc">初凝：{{ njsjInfo?.cnsjzc }}</span>
            <span v-if="njsjInfo?.znsjzc">终凝：{{ njsjInfo?.znsjzc }}</span>
          </td>
          <td colspan="5">{{ getResult(njsjInfo?.cndxjl, njsjInfo?.zndxjl)}}</td>
        </tr>
        <tr height="40">
          <td colspan="9">坍落度1h经时变化量/%</td>
          <td colspan="9">≤60</td>
          <td colspan="9">{{ tldInfo?.sjhntObject?.h1bhlpjz | isNull }}</td>
          <td colspan="5">{{ tldInfo?.dxjl | isNull }}</td>
        </tr>
        <tr height="40">
          <td colspan="9">含固量/%</td>
          <td colspan="9">15.30~18.70</td>
          <td colspan="9">{{ getResultStr(hglInfo?.pjhgl, 2) }}</td>
          <td colspan="5">{{ hglInfo?.dxjl | isNull }}</td>
        </tr>
        <tr height="40">
          <td colspan="9">密度/g/cm³</td>
          <td colspan="9">1.05±0.02</td>
          <td colspan="9">{{ getResultStr(mdInfo?.jmmdInfo?.mdpjz, 2) }}</td>
          <td colspan="5">{{ mdInfo?.dxjl | isNull }}</td>
        </tr>
        <tr height="40">
          <td colspan="9">PH值</td>
          <td colspan="9">7±4</td>
          <td colspan="9">{{ getResultStr(phInfo?.pjph, 1) }}</td>
          <td colspan="5">{{ phInfo?.dxjl | isNull }}</td>
        </tr> -->
        <tr height="40">
          <td colspan="9"></td>
          <td colspan="9"></td>
          <td colspan="9"></td>
          <td colspan="5"></td>
        </tr>
        <tr height="40">
          <td colspan="4">检测结论</td>
          <td colspan="28" left>{{ getShowResultStr(experimentInfo?.conclusion) }}</td>
        </tr>
      </table>

      <table border="1" class="border" style="margin-top: 20px; table-layout: fixed;">
        <tbody>
          <tr height="240">
            <td colspan="4">备&nbsp;&nbsp;&nbsp;&nbsp;注</td>
            <td colspan="28" left>{{ experimentInfo?.remark | isNull }}</td>
          </tr>
        </tbody>
      </table>

      <table class="lab-sign" style="border: none;">
        <tr height="40" style="border: none;">
          <td style="border: none;">
            <div class="sign-user">检测机构专用章：</div>
          </td>
          <td style="border: none;">
            <div class="sign-user">
              批准/职务：
              <!-- <img v-if="true" src="" width="150" height="40" /> -->
            </div>
          </td>
          <td style="border: none;">
            <div class="sign-user">
              审核：
              <!-- <img v-if="true" src="" width="150" height="40" /> -->
            </div>
          </td>
          <td style="border: none;">
            <div class="sign-user">
              检测：
              <!-- <img v-if="true" src="" width="150" height="40" /> -->
            </div>
          </td>
        </tr>
      </table>


    </div>
  </div>
</template>

<script>
import {cpEvenRound} from "@/utils/calculate.js"
import moment from 'moment';
  export default {
    props: {
      taskInfo: {
        type: Object,
        default: () => {
          return {}
        }
      },
      companyName: {
        type: String,
        default: ""
      },
        mixProportion: {
            type: Object,
            default: () => { return {} }
        },
        companyInfo: {
            type: Object,
            default: () => { return {} }
        },
    },
    watch: {
      taskInfo:{
        handler(newValue, oldValue) {
          const {
            mixExperimentInfo,
          } = newValue;
          let objInfo = mixExperimentInfo?.wjj1Info || {};
          if(objInfo && objInfo.shxhSynchronizedataList && objInfo.shxhSynchronizedataList.length > 0){
              this.shxhSynchronizedata = objInfo.shxhSynchronizedataList[0];
          }

          this.experimentInfo = objInfo?.experimentInfo || {}
          this.experimentDetailObj = {}
          if(objInfo.experimentDetailList && objInfo.experimentDetailList.length > 0){
              objInfo.experimentDetailList.map(item => {
              if (item.testProjectCode === 'CONCRETE_ADMIXTURE_PARAM_WJJ_PHZ') {
                this.phInfo = item.objJson || {};
              }else if (item.testProjectCode === 'CONCRETE_ADMIXTURE_PARAM_WJJ_HGL') {
                this.hglInfo = item.objJson || {};
              }else if (item.testProjectCode === 'CONCRETE_ADMIXTURE_PARAM_WJJ_MD') {
                this.mdInfo = item.objJson || {};
              }else if (item.testProjectCode === 'CONCRETE_ADMIXTURE_PARAM_WJJ_NJSJZC') {
                this.njsjInfo = item.objJson || {};
              }else if (item.testProjectCode === 'CONCRETE_ADMIXTURE_PARAM_WJJ_QSLB') {
                this.mslInfo = item.objJson || {};
              }else if (item.testProjectCode === 'CONCRETE_ADMIXTURE_PARAM_WJJ_TLD1HJSBHL') {
                this.tldInfo = item.objJson || {};
              }else if (item.testProjectCode === 'CONCRETE_ADMIXTURE_PARAM_WJJ_JSL') {
                this.jslInfo = item.objJson || {};
              }
            });

          }

          
        },
        deep: true,
        immediate: true
      },
    },
    data() {
      return {
        experimentInfo: {},
        experimentDetailList: [],
        shxhSynchronizedata: {},
        jslInfo: {},
        mslInfo: {},
        njsjInfo: {},
        tldInfo: {},
        hglInfo: {},
        mdInfo: {},
        phInfo: {},
        wjj:{},
        showListInfo:[],
        // 技术标准 list 
        // 依次是：减水率、泌水率、凝结时间之差、坍落度、含固量、密度、PH
        technologyList: ['≥25', '≤50', '-90~+90', '-', '15.30~18.70', '1.05±0.02', '7±4'],
      }
    },
    
    mounted() {
      console.log(this.taskInfo)
      if (this.mixProportion && this.mixProportion.proportionMaterial) {
        var proportionMaterial = this.mixProportion.proportionMaterial || {};
        this.wjj = proportionMaterial.wjj1 || {};
      }
      switch (this.experimentInfo?.materialsSpecs) {
        case '早强型':
          this.technologyList = ['≥25', '≤50', '-90~+90', '-', '15.30~18.70', '1.05±0.02', '7±4'];
          break;
        case '标准型':
          this.technologyList = ['≥25', '≤60', '-90~+120', '80', '15.30~18.70', '1.05±0.02', '7±4'];
          break;
        case '缓凝型':
          this.technologyList = ['≥25', '≤70', '＞+90', '60', '15.30~18.70', '1.05±0.02', '7±4'];
          break;
      
        default:
          break;
      }

      this.initDataList();
      console.log('组装后的数据：', this.showListInfo);
    },
    methods:{
      getCpEvenRound(str, index){
            if(this.isEmpty(str)) return '--'
            return cpEvenRound(str, index);
        },
      initDataList(){
        // 减水率
        if(!this.isEmpty(this.jslInfo?.pjz)) {
          var obj = {
            title:'减水率/%',
            val1: this.technologyList[0],
            val2: this.getResultStr(this.jslInfo?.pjz, 0),
            val3: this.jslInfo?.dxjl,
          }
          this.showListInfo.push(obj);
        }

        // 沁水率比 分为 早强型、标准型、缓凝型
        if(!this.isEmpty(this.mslInfo?.qslb)) {
          console.log('-----:',this.getResultStr(this.mslInfo?.qslb, 2));
          var obj = {
            title:'泌水率比/%',
            val1: this.technologyList[1],
            val2: this.getResultStr(this.mslInfo?.qslb, 0),
            val3: this.mslInfo?.dxjl,
          }
          this.showListInfo.push(obj);
        }
        // 凝结时间之差/min  分为 早强型、标准型、缓凝型
        if(!this.isEmpty(this.njsjInfo?.cnsjzc) || !this.isEmpty(this.njsjInfo?.znsjzc)) {
          var obj = {
            title:'凝结时间之差/min',
            val1: this.technologyList[2],
            val2: this.njsjInfo?.cnsjzc,
            val3: this.njsjInfo?.znsjzc,
            val4: this.getResult(this.njsjInfo?.cndxjl, this.njsjInfo?.zndxjl),
          }
          this.showListInfo.push(obj);
        }

        // 坍落度1h经时变化量/%  分为 早强型、标准型、缓凝型
        if(!this.isEmpty(this.tldInfo?.sjhntObject?.h1bhlpjz)) {
          var obj = {
            title:'坍落度1h经时变化量/%',
            val1: this.technologyList[3] == '-' ? '-' : '≤' + this.technologyList[3],
            val2: this.tldInfo?.sjhntObject?.h1bhlpjz,
            val3: this.getResultTld(this.tldInfo?.sjhntObject?.h1bhlpjz)
          }
          this.showListInfo.push(obj);
        }
        // 含固量/%
        if(!this.isEmpty(this.hglInfo?.pjhgl)) {
          let res = this.hglInfo?.sccjkzz1 + '±' + this.hglInfo?.sccjkzz2;
          let minVal = Number(this.hglInfo?.sccjkzz1) - Number(this.hglInfo?.sccjkzz2);
          let maxVal = Number(this.hglInfo?.sccjkzz1) + Number(this.hglInfo?.sccjkzz2);
          res = this.getCpEvenRound(minVal, 2) + '~' + this.getCpEvenRound(maxVal, 2);
          this.technologyList[4] = res;
          var obj = {
            title:'含固量/%',
            // val1: this.technologyList[4],
            val1: res,
            val2: this.getResultStr(this.hglInfo?.pjhgl, 2),
            val3: this.hglInfo?.dxjl,
          }
          this.showListInfo.push(obj);
        }

        // 密度/g/cm³
        if(!this.isEmpty(this.mdInfo?.jmmdInfo?.mdpjz)) {
          let res = this.mdInfo?.sccjkzz1 + '±' + this.mdInfo?.sccjkzz2;
          this.technologyList[5] = res;
          var obj = {
            title:'密度/g/cm³',
            // val1: this.technologyList[5],
            val1: res,
            val2: this.getResultStr(this.mdInfo?.jmmdInfo?.mdpjz, 2),
            val3: this.mdInfo?.dxjl,
          }
          this.showListInfo.push(obj);
        }
        // PH值
        if(!this.isEmpty(this.phInfo?.pjph)) {
          let res = this.phInfo?.sccjkzz1 + '±' + this.phInfo?.sccjkzz2;
          this.technologyList[6] = res;
          var obj = {
            title:'PH值',
            // val1: this.technologyList[6],
            val1: res,
            val2: this.getResultStr(this.phInfo?.pjph, 1),
            val3: this.phInfo?.dxjl,
          }
          this.showListInfo.push(obj);
        }

        const itemToFill = {
          title: '',
          val1: '',
          val2: '',
          val3: ''
        };

        if (this.showListInfo.length < 7) {
          const numToFill = 7 - this.showListInfo.length;
          for (let i = 0; i < numToFill; i++) {
            this.showListInfo.push({...itemToFill });
          }
        }
      },

      getResultTld(){
        var res = this.technologyList[3];
        if(this.isEmpty(res) || res == '-') return '合格';
        try {
          var resNum = Number(res);
          var checkNum = Number(this.tldInfo?.sjhntObject?.h1bhlpjz);
          return checkNum <= resNum ? '合格' : '不合格';
        } catch(error){
          return '合格';
        }
        
      },
      
      getShowCJ(obj, config){
            let cj = obj?.factory;
            let gysmc = obj?.supplyCompanyName;
            var showName = cj;
            var result = '/';

            if(config == 1){
                if(!this.isEmpty(cj)){
                    showName = cj
                } 
            } else {
                if(!this.isEmpty(gysmc)){
                    showName = gysmc
                }
            }
            result = this.isEmpty(showName) ? '/' : showName;
            return result;
        },
      

      isEmpty(val) {
            if (typeof val === "boolean") {
                return false;
            }
            if (typeof val === "number") {
                return false;
            }
            if (val instanceof Array) {
                if (val.length === 0) return true;
            } else if (val instanceof Object) {
                if (JSON.stringify(val) === "{}" || val == null ) return true;
            } else {
                if (
                val === "null" ||
                val == null ||
                val === "undefined" ||
                val === undefined ||
                val === ""
                )
                return true;
                return false;
            }
            return false;
        },
      getShowResultStr(str){
        console.log('检测结论：', str);
        var res = str;
        if(this.isEmpty(str)) return '--';
        if(str.includes(':')){
          res = str.split(':').slice(1).join(':');
        }
        if(str.includes('：')){
          res = str.split('：').slice(1).join('：');
        }
        if(res.includes('。')){
          res = res.split('。')[0] + '。';
        }
        return res.replace(';', '').replace('；', '');

      },
      getResultStr(value,index){
        if (value == undefined || value == null || value === '') return '--';
        return cpEvenRound(value || '', index)
      },
      getResult(cn, zn){
        if(cn == '合格' && zn == '合格'){
          return '合格';
        }
        if(cn == '不合格' || zn == '不合格'){
          return '不合格';
        }
        return '--';
      }
    },
    filters: {
        momentDate: function(value) {
          if (value) return moment(value).format("YYYY-MM-DD");
          return "---";
        },
        isNull: function (value) {
          if (value == undefined || value == null || value === '') return '---'
            
          return value;
        },
        shxhNull: function (value) {
          if (value == undefined || value == null || value === '') return '---'
            
          return value;
        }
    },
  }
</script>

<style lang="scss" scoped>
  @import '../print.css';
  .border {
    border: 3px solid black; /* 设置边框粗细 */
}
.border th, .border td{
    border: 2px solid black; /* 设置边框粗细 */
}
</style>