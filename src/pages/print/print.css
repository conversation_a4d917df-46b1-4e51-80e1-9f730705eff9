.portrait {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    font-family: MES-Song;
    font-weight: 400;
    font-size: 16px;
    line-height: 22px;
    letter-spacing: 2px;
}
.tip-view {
    display: flex;
    justify-content: flex-end;
    min-width: 950px;
}

.tip-label {
    border-width: 3px;
    border-style: solid;
    border-color: black;
    padding: 8px;
    font-weight: bold;
    /* font-family: SimHei; */
}
table {
    width: 950px;
    border-collapse: collapse;
    border-width: 1.8px;
    border-style: solid;
    border-color: black;
}
table tr td {
    text-align: center;
    word-wrap:break-word;
    border-width: 1.8px;
    border-style: solid;
    border-color: black;
}

.lab-sign td,
table td[left]{
    text-align: left;
    padding-left: 6px;
}

.lab-sign td,
table td[right]{
    text-align: right;
}

.lab-sub-title {
    margin-top: 10px;
    max-width: 1050px;
    display: flex;
    justify-content: space-between;
}

.lab-report-id,
.lab-sample-id {
    text-align: left;
    margin: 0;
}

.lab-report-id > span,
.lab-sample-id > span {
    min-width: 100px;
}

.lab-report table {
    margin-top: 25px;
}

.lab-bottom {
    /* max-width: 1050px; */
    display: flex;
    justify-content: right;
}

.lab-top {
    /* max-width: 1050px; */
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
}
.lab-mix-ycl {
    height: 400px; padding-left: 10px; padding-top: 10px; width: 600px;
}

.lab-mix-ycl >div {
    display: flex; justify-content: space-between;
    margin-bottom: 10px;
}
.lab-mix-ycl >div >span {
    display: inline-block;
    width: 100px;
}
.lab-mix-ycl >div >span:last-child {
    flex: 1;
}

.title-label {
    width: 950px;
    text-align: center;
    font-size:32px;
    font-weight: bold;
    padding-top: 20px;
}
.company-label {
    width: 950px;
    text-align: center;
    font-size:25px;
    padding-top: 10px;
}
.btn-position {
    position: absolute;
    right: 100px;
    top: 30px;
}
.sign-user {
    display: flex;
    min-height: 40px;
}

/* 
竖屏（纵向）A4 纸：
•	宽度：794 px
•	高度：1123 px
横屏（横向）A4 纸：
•	宽度：1123 px
•	高度：794 px
 */