<!--
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-05-21 23:47:00
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-04-13 16:13:14
 * @FilePath: /quality_center_web/src/pages/print/printContent.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div style="width: 100%; height: 100%; display: flex; padding-left: 40px; background-color: white">
        <div class="page-content">
            <el-button class="btn-position" size="mini" type="primary" v-print="printMoreInfo" @click="printClick">打印</el-button>
            <div class="printContent" id="printMore" :style="printDivClass">
                <!-- 预拌(商品)混凝土出厂质量证明书 -->
                <template  v-if="printTypes.indexOf('a1') > -1">
                    <cchgz v-for="(item, index) in taskList" :key="index + 'cchgz'" 
                    :indexNum = index
                        :mixProportion="item?.mixProportion"
                        :printVerifyRecordOtherInfo="item?.printVerifyRecordOtherInfo"
                        :supplyTaskList="item?.supplyTaskList"
                        :companyName="item?.companyName"
                        :shxhSynchronizedataList="item?.shxhSynchronizedataList"
                        :rwdextraList="item?.rwdextraList || []"
                        :companyInfo="item?.companyInfo"
                        :mixMaterialsExperimentInfo = "item?.mixMaterialsExperimentInfo"
                        :kyInfoList="item?.kyInfoList"
                        :xnbgInfo="item?.xnbgInfo"
                        :ksInfo="item?.ksInfo"
                        :kzInfo="item?.kzInfo"
                    />
                </template>
                <!-- 预拌(商品)混凝土出厂质量证明书新版 -->
                <template  v-if="printTypes.indexOf('a1N') > -1">
                    <cchgz_new v-for="(item, index) in taskList" :key="index + 'cchgz'" 
                    :indexNum = index
                        :mixProportion="item?.mixProportion"
                        :printVerifyRecordOtherInfo="item?.printVerifyRecordOtherInfo"
                        :supplyTaskList="item?.supplyTaskList"
                        :companyName="item?.companyName"
                        :shxhSynchronizedataList="item?.shxhSynchronizedataList"
                        :rwdextraList="item?.rwdextraList || []"
                        :companyInfo="item?.companyInfo"
                        :mixMaterialsExperimentInfo = "item?.mixMaterialsExperimentInfo"
                        :kyInfoList="item?.kyInfoList"
                        :xnbgInfo="item?.xnbgInfo"
                        :ksInfo="item?.ksInfo"
                        :kzInfo="item?.kzInfo"
                        :kllzInfo = "item?.kllzInfo"
                    />
                </template>

                <!-- 配合比设计报告 -->
                <template v-if="printTypes.indexOf('a8') > -1">
                    <div class="print-sjbg-div-class" :style="printSubDivClass">
                    <hntphb v-for="(item, index) in taskList" :key="index + 'hntphb'" 
                        :indexNum = index
                        :mixProportion="item?.mixProportion"
                        :printVerifyRecordOtherInfo="item?.printVerifyRecordOtherInfo"
                        :supplyTaskList="item?.supplyTaskList"
                        :companyInfo="item?.companyInfo"
                        :shxhSynchronizedataList="item?.shxhSynchronizedataList"
                        :rwdextraList="item?.rwdextraList || []"
                        :mixMaterialsExperimentInfo = "item?.mixMaterialsExperimentInfo"
                        :mixExperimentInfo = "item?.mixExperimentInfo"
                        :companyName="item?.companyName"
                    />
                    </div>
                </template>
                
                <!-- 混凝土开盘鉴定 -->
                <template  v-if="printTypes.indexOf('a2') > -1">
                    <kpjd v-for="(item, index) in taskList" :key="index + 'kpjd'" 
                        :mixProportion="item?.mixProportion"
                        :printVerifyRecordOtherInfo="item?.printVerifyRecordOtherInfo"
                        :supplyTaskList="item?.supplyTaskList"
                        :shxhSynchronizedataList="item?.shxhSynchronizedataList"
                        :rwdextraList="item?.rwdextraList || []"
                        :companyInfo="item?.companyInfo"
                        :companyName="item?.companyName"
                        :openAppraisal="item?.openAppraisal"
                        :kpkyInfo="item?.kpkyInfo || {}"
                        :ksInfo="item?.ksInfo"
                        :kyInfoList="item?.kyInfoList"
                        :xnbgInfo="item?.xnbgInfo"
                        :phbhistoryList="item?.phbhistoryList"
                        :rwdextraPrintInfo="item?.openAppraisal?.rwdextraPrintInfo || {}"
                    />
                </template>

                <!-- 水泥检测报告 -->
                <template  v-if="printTypes.indexOf('b1') > -1">
                    <snjc v-for="(item, index) in taskList" :key="index + 'snjc'" 
                        :companyName="item?.companyName"
                        :companyInfo="item?.companyInfo"
                        :mixProportion="item?.mixProportion"
                        :mixExperimentInfo="item?.mixExperimentInfo"
                    />
                </template>
                
                <!-- 矿渣粉检测报告 -->
                <template  v-if="printTypes.indexOf('b2') > -1">
                    <lhglkzf v-for="(item, index) in taskList" :key="index + 'lhglkzf'" 
                        :companyName="item?.companyName"
                        :companyInfo="item?.companyInfo"
                        :mixProportion="item?.mixProportion"
                        :mixExperimentInfo="item?.mixExperimentInfo"
                    />
                </template>

                <!-- 粉煤灰检测报告 -->
                <template  v-if="printTypes.indexOf('b3') > -1">
                    <fmhjc v-for="(item, index) in taskList" :key="index + 'fmhjc'" 
                        :companyName="item?.companyName"
                        :companyInfo="item?.companyInfo"
                        :mixProportion="item?.mixProportion"
                        :mixExperimentInfo="item?.mixExperimentInfo"
                    />
                </template>

                <!-- 细骨料检测报告 -->
                <template  v-if="printTypes.indexOf('b4') > -1">
                    <pthntys v-for="(item, index) in taskList" :key="index + 'pthntys'" 
                        :taskInfo="item" 
                        :companyInfo="item?.companyInfo"
                        :mixProportion="item?.mixProportion"
                        :companyName="item?.companyName"
                    />
                </template>

                <!-- 粗骨料检测报告 -->
                <template  v-if="printTypes.indexOf('b5') > -1">
                    <pthntyst v-for="(item, index) in taskList" :key="index + 'pthntyst'" 
                        :taskInfo="item" 
                        :companyInfo="item?.companyInfo"
                        :mixProportion="item?.mixProportion"
                        :companyName="item?.companyName"
                    />
                </template>

                <!-- 外加剂检测报告 -->
                <template  v-if="printTypes.indexOf('b6') > -1">
                    <wjjjc v-for="(item, index) in taskList" :key="index + 'wjjjc'" 
                        :taskInfo="item" 
                        :companyInfo="item?.companyInfo"
                        :mixProportion="item?.mixProportion"
                        :companyName="item?.companyName"
                    />
                </template>

                <!-- 配合比调整通知 -->
                <template  v-if="printTypes.indexOf('a9') > -1">
                    <div class="print-tztz-div-class" :style="printSubDivClass">
                    <phbtz v-for="(item, index) in taskList" :key="index + 'phbtz'" 
                    :indexNum = index
                        :mixProportion="item?.mixProportion"
                        :supplyTaskList="item?.supplyTaskList"
                        :shxhSynchronizedataList="item?.shxhSynchronizedataList"
                        :rwdextraList="item?.rwdextraList || []"
                        :companyName="item?.companyName"
                        :phbhistoryList="item?.phbhistoryList"
                        :afterPhbDtoList="item?.phbhistory?.afterPhbDtoList || []"
                    />

                    </div>
                </template>

                <!-- 混凝土抗压强度检测报告 -->
                <template v-if="printTypes.indexOf('a3') > -1">
                    <!-- <template v-for="(item, index) in taskList">
                        <hntky v-for="(task, subIndex) in item?.kyInfoList" :key="subIndex + 'hntky'" 
                            :experimentInfo="task?.experimentInfo" 
                            :experimentDetailList="task?.experimentDetailList" 
                            :shxhSynchronizedataList="task?.shxhSynchronizedataList"
                            :rwdextraList="item?.rwdextraList || []"
                            :supplyTaskList="item?.supplyTaskList"
                            :companyName="item?.companyName"
                            
                        />
                    </template> -->
                    <hntky v-for="(item, subIndex) in taskList" :key="subIndex + 'hntky'" 
                            :subItem="item"
                            :rwdextraList="item?.rwdextraList || []"
                            :supplyTaskList="item?.supplyTaskList"
                            :companyName="item?.companyName"
                            
                        />
                </template>

                <!-- 混凝土抗压强度检测报告 -->
                <template v-if="printTypes.indexOf('a3N') > -1">
                    <hntky_new v-for="(item, subIndex) in taskList" :key="subIndex + 'hntky_new'" 
                            :subItem="item"
                            :rwdextraList="item?.rwdextraList || []"
                            :supplyTaskList="item?.supplyTaskList"
                            :companyName="item?.companyName"
                            
                        />
                </template>

                <!-- 混凝土抗渗性能检测报告 -->
                <template v-if="printTypes.indexOf('a4') > -1">
                    <hntks v-for="(task, index) in taskList" :key="index + 'hntks'" 
                        :experimentInfo="task?.ksInfo?.experimentInfo" 
                        :experimentDetailList="task?.ksInfo?.experimentDetailList" 
                        :shxhSynchronizedataList="task?.ksInfo?.shxhSynchronizedataList"
                        :rwdextraList="task?.rwdextraList || []"
                        :supplyTaskList="task?.supplyTaskList"
                        :companyName="task?.companyName"
                    />
                </template>

                <!-- 抗氯离子 -->
                <template v-if="printTypes.indexOf('a11') > -1">
                    <hntllz v-for="(task, index) in taskList" :key="index + 'hntllz'" 
                        :experimentInfo="task?.kllzInfo?.experimentInfo" 
                        :experimentDetailList="task?.kllzInfo?.experimentDetailList" 
                        :shxhSynchronizedataList="task?.kllzInfo?.shxhSynchronizedataList"
                        :rwdextraList="task?.rwdextraList || []"
                        :supplyTaskList="task?.supplyTaskList"
                        :companyName="task?.companyName"
                    />
                </template>

                <!-- 性能检测报告 -->
                <template v-if="printTypes.indexOf('a12') > -1">
                    <hntjc v-for="(task, index) in taskList" :key="index + 'hntjc'" 
                        :experimentInfo="task?.xnbgInfo?.experimentInfo" 
                        :experimentDetailList="task?.xnbgInfo?.experimentDetailList" 
                        :shxhSynchronizedataList="task?.xnbgInfo?.shxhSynchronizedataList"
                        :rwdextraList="task?.rwdextraList || []"
                        :supplyTaskList="task?.supplyTaskList"
                        :companyName="task?.companyName"
                    />
                </template>

                <!-- 混凝土抗折强度检测报告(延后) -->
                <template v-if="printTypes.indexOf('a5') > -1">
                        <hntkz v-for="(task, index) in taskList"  :key="index + 'hntkz'" 
                            :experimentInfo="task?.kzInfo?.experimentInfo" 
                            :experimentDetailList="task?.kzInfo?.experimentDetailList" 
                            :shxhSynchronizedataList="task?.shxhSynchronizedataList"
                            :rwdextraList="task?.rwdextraList"
                            :supplyTaskList="task?.supplyTaskList"
                            :kzInfo="task?.kzInfo"
                            :companyName="task?.companyName"
                        />
                    </template>
                
                <!-- 强度统计分析（延后） -->
            </div>
        </div>
    </div>
</template>

<script>
import cchgz from "./components/hnt/cchgz.vue";
import cchgz_new from "./components/hnt/cchgz_new.vue";
import kpjd from "./components/hnt/kpjd.vue";
import hntphb from "./components/hnt/hntphb.vue";
import hntbs from "./components/hnt/hntbs.vue"
import hnthssf from "./components/hnt/hnthssf.vue";
import phbtz from "./components/hnt/phbtz.vue";
import hntkz from "./components/hnt/hntkz.vue";

import lhglkzf from "./components/lhglkzf.vue";
import snjc from "./components/snjc.vue";
import fmhjc from "./components/fmhjc.vue";
import wjjjc from "./components/wjjjc.vue";
import pthntys from "./components/pthntys.vue";
import pthntyst from "./components/pthntyst.vue";

import hntks from "./components/hnt/hntks.vue";
import hntky from "./components/hnt/hntky.vue";
import hntky_new from "./components/hnt/hntky_new.vue";
import hntjc from "./components/hnt/hntjc.vue";
import hntllz from "./components/hnt/hntllz.vue";

export default {
    components: {
        cchgz,
        cchgz_new,
        kpjd,
        hntky_new,
        hntphb,
        hntbs,
        hnthssf,
        lhglkzf,
        snjc,
        fmhjc,
        wjjjc,
        pthntys,
        pthntyst,
        phbtz,
        hntks,
        hntky,
        hntjc,
        hntllz,
        hntkz
    },

    data() {
        return {
            taskIds: this.$route.query.taskIds || '',
            printTypes: this.$route.query.printType ? this.$route.query.printType.split(",") : '',
            taskList: [],
            printInfo: "",
            formType: this.$route.query.formType || '', // 数据是修改记录的时候穿 logs
            printDivClass:{
                "display": "flex",
                "flexDirection": "column",
                "alignItems": "center",
            },
            printSubDivClass: {
                "display": "flex", 
                "flexDirection": "column",
                "justifyContent": "center",
                "alignItems": "center",
            },
            printMoreInfo:{
              id:"printMore",
              beforeOpenCallback (vue) {
                console.log('打开之前')
              },
              openCallback (vue) {
                console.log('执行了打印')
              },
              closeCallback (vue) {
                console.log('关闭了打印工具')
                vue.printDivClass = {
                    "display": "flex",
                    "flexDirection": "column",
                    "alignItems": "center",
                };
                vue.printSubDivClass = {
                    "display": "flex", 
                    "flexDirection": "column",
                    "justifyContent": "center",
                    "alignItems": "center",
                };
              }
            
            }

        }
    },

    mounted() {
        // 再次打印
        if (this.formType === 'printAgain') {
            console.log('传递过来的id：',this.taskIds);
            this.$api.getExperimentPrintRecord(`id=${this.taskIds}`).then(res => {
                if (res.code == 1) {
                    this.taskList = res.data.printJson.data.list;
                    this.printInfo = res.data.printJson;
                }
            })
        } else if (this.formType === 'logs') {
            this.$api.getErpRwdPrintInfoToUpdateLog({
                updateLogIdList: this.taskIds.split(",")
            }, this).then(res => {
                if (res.code == 1) {
                    this.taskList = res.data.list;
                    this.printInfo = res;
                }
            })
        } else {
            this.$api.queryErpRwdPrintInfo({
                frwdIdList: this.taskIds.split(",")
            }, this).then(res => {
                if (res.code == 1) {
                    this.taskList = res.data.list;
                    this.printInfo = res;
                }
            })
        }
    },

    methods: {
        printClick() {
            this.printDivClass = {}
            this.printSubDivClass = {}
            this.addErpRwdPrintRecordResp(this.printInfo);
        },
        addErpRwdPrintRecordResp(printJson) {
            const frwdhList = (this.formType === 'logs' || this.formType === 'printAgain') ? this.$route.query.frwdh.split(",") : this.taskIds.split(",");
            this.$api.addErpRwdPrintRecord({
                // printJson, 
                printJsonList: printJson.data.list,
                frwdhList: frwdhList,
                printHtml: this.printTypes.join(",")
            });
        }
    }
}
</script>

<style lang="scss" scoped>

.page-content {
    background: white !important;
    width: 100%;
    height: 100%;
    overflow: auto;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
    border-collapse: collapse;
    
}
.printContent {
    font-family: MES-Song;
    font-weight: 500;
    font-size: 17px;
}
.print-sjbg-div-class {
    width: 100%; 
    margin-top: 25px;
}
.print-tztz-div-class {
    width: 100%; 
    margin-top: 15px;
}
</style>