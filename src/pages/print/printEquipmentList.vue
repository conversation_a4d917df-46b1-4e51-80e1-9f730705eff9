<!--
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-05-21 23:47:00
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2024-10-14 22:54:59
 * @FilePath: /quality_center_web/src/pages/print/printContent.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div class="page-content">
        <el-button class="btn-position" size="mini" type="primary" v-print="'printMore'" @click="printClick">打印</el-button>
        <div class="printContent" id="printMore">
            <div class="landscape">
                <table style="width: 100%;">
                    <!-- 公共拥有的头部 start -->
                    <thead style="width: 100%">
                        <tr>
                            <td style="font-weight:bold; font-size: 30px; text-align: center">
                                检 测 设 备 台 账
                            </td>
                        </tr>
                    </thead>
                    <!-- 公共拥有的头部 end -->
                    <!-- 表单内容 start -->
                    <tbody style="width: 100%;">
                        <div class="table-content">
                            <PrintTable :data="tableData" style="width: 100%;">
                                <el-table-column
                                    v-for="item in tableColumn"
                                    :key="item.prop"
                                    :prop="item.prop"
                                    :label="item.label"
                                    :formatter="item.formatter"
                                    :fixed="item.fixed"
                                    :width="item.width || ''"
                                    align="center"
                                />
                            </PrintTable>
                        </div>
                    </tbody>
                    <!-- 表单内容 end -->
                    <!-- 公共拥有的尾部 start-->
                    <tfoot>
                        <tr>
                            <td></td>
                        </tr>
                    </tfoot>
                    <!-- 公共拥有的尾部 end -->
                </table>
            </div>
        </div>
    </div>
</template>

<script>
import moment from 'moment';
import PrintTable from './components/print-table.vue';
export default {
    components: {
        PrintTable
    },

    data() {
        return {
            tableData: [],
            tableColumn: [
                {
                    label: '类别',
                    prop: 'equipmentCategory',
                },
                {
                    label: '序号',
                    prop: 'indexNumber',
                },
                {
                    label: '编号',
                    prop: 'equipmentNo',
                },{
                    label: '数量',
                    prop: 'number',
                },{
                    label: '规格型号',
                    prop: 'equipmentType',
                },{
                    label: '生产厂家',
                    prop: 'manufacturer',
                },{
                    label: '购置日期',
                    prop: 'buyTime',
                    formatter: (row) => {
                    if (!row.buyTime) {
                        return '';
                    }
                    return moment(row.buyTime).format('YYYY-MM-DD')
                    },
                },
                {
                    label: '出厂编号',
                    prop: 'factoryNumber',
                },
                {
                    label: '出厂时间',
                    prop: 'factoryTime',
                },
                {
                    label: '测量范围',
                    prop: 'technicalParameter',
                },{
                    label: '分度值',
                    prop: 'division',
                    
                },{
                    label: '精度等级',
                    prop: 'accuracyClass',
                    
                },{
                    label: '操作员',
                    prop: 'custodyUserName',
                },{
                    label: '安放地点',
                    prop: 'placementLocation',
                },{
                    label: '设备状态',
                    prop: 'equipmentStatus',
                },{
                    label: '检定周期（天）',
                    prop: 'verificationCycle',
                },{
                    label: '最近检定日期',
                    prop: 'lastDetectionTime',
                    formatter: (row) => {
                    if (!row.lastDetectionTime) {
                        return '';
                    }
                    return moment(row.lastDetectionTime).format('YYYY-MM-DD')
                    },
                },{
                    label: '备注',
                    prop: 'notes',
                },
            ],
            printInfo: ""
        }
    },

    mounted() {
        this.$api.getEquipmentPrintInfo({}, this).then(res => {
            if(res.succ){
                this.tableData = res.data.list.map((item, index) => {
                    return {
                        ...item,
                        indexNumber: index + 1
                    }
                });

                this.printInfo = JSON.stringify(res);
            }else{
                this.$message.error(res.msg || '查询失败')
            }
        })
    },

    methods: {
        addExperimentPrintRecordResp(printJson) {
            this.$api.addExperimentPrintRecord({printJson});
        },
        printClick() {
            // this.addExperimentPrintRecordResp(this.printInfo);
        },
        // addExperimentPrintRecordResp(printJson) {
        //     this.$api.addExperimentPrintRecord({printJson});
        // }
    }
}
</script>

<style lang="scss" scoped>
.page-content {
    background: white !important;
    width: 100%;
    height: 100%;
    overflow: auto;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
}
.printContent {
    width: calc(80% - 280px);
    margin-top: 20px;
    font-family: MES-Song;
    font-weight: 500;
    font-size: 17px;
}
::v-deep .el-table__body {
    table-layout: auto;
    border: 0.5px solid black !important;
    border-color: black !important;
    border-collapse: collapse;
    font-size: 8px !important;
    color: black !important;
    td {
        border: 0.5px solid  black !important;
        border-bottom: none !important;
        padding: 10px 3px;
        font-size: 16px;
    }
    th {
        border: 0.5px solid black !important;
        border-bottom: none !important;
        padding: 10px 3px;
        font-size: 16px;
    }
}
.table-content {
    margin-top: 20px;
    width: 100%;
}
.btn-position {
    position: absolute;
    right: 130px;
    top: 20px;
}
</style>