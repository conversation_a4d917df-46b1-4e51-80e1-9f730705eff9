<!--
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-05-21 23:47:00
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2024-10-26 15:46:58
 * @FilePath: /quality_center_web/src/pages/print/printContent.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div class="page-content">
        <el-button class="btn-position" size="mini" type="primary" v-print="'#printMore'" @click="printClick">打印</el-button>
        <div class="printContent" id="printMore">

            <template  v-for="(item, index) in taskList">
                <!-- 水泥检测报告 -->
                <snjc v-if="item.experimentType == 1" :key="index + 'snjc'" 
                    :companyName="item?.companyName"
                    :mixExperimentInfo="item?.mixExperimentInfo"
                />
                
                <!-- 矿渣粉检测报告 -->
                <lhglkzf v-if="item.experimentType == 3" :key="index + 'lhglkzf'" 
                        :companyName="item?.companyName"
                        :mixExperimentInfo="item?.mixExperimentInfo"
                />

                <!-- 粉煤灰检测报告 -->
                <fmhjc v-if="item.experimentType == 2" :key="index + 'fmhjc'" 
                        :companyName="item?.companyName"
                        :mixExperimentInfo="item?.mixExperimentInfo"
                />
                
                <!-- 细骨料检测报告 -->
                <pthntys v-if="item.experimentType == 5" :key="index + 'pthntys'" 
                        :taskInfo="item" 
                        :companyName="item?.companyName"
                />
                
                <!-- 粗骨料检测报告 -->
                <pthntyst v-if="item.experimentType == 4" :key="index + 'pthntyst'" 
                        :taskInfo="item" 
                        :companyName="item?.companyName"
                />
                
                <!-- 外加剂检测报告 -->
                <wjjjc v-if="item.experimentType == 6" :key="index + 'wjjjc'" 
                        :taskInfo="item" 
                        :companyName="item?.companyName"
                />
            </template>
        </div>
    </div>
</template>

<script>
import lhglkzf from "./components/lhglkzf.vue";
import snjc from "./components/snjc.vue";
import fmhjc from "./components/fmhjc.vue";
import wjjjc from "./components/wjjjc.vue";
import pthntys from "./components/pthntys.vue";
import pthntyst from "./components/pthntyst.vue";

export default {
    components: {
        lhglkzf,
        snjc,
        fmhjc,
        wjjjc,
        pthntys,
        pthntyst,
    },

    data() {
        return {
            experimentIds: this.$route.query.experimentIds || '',
            printTypes: this.$route.query.printType ? this.$route.query.printType.split(",") : '',
            taskList: [],
            printInfo: ""
        }
    },

    mounted() {
        this.$api.queryExperimentPrintInfo({
            experimentIdList: this.experimentIds.split(",")
        }, this).then(res => {
            if (res.code == 1) {
                this.taskList = res.data.list;

                this.printInfo = JSON.stringify(res);
            }
        })
    },

    methods: {
        printClick() {
            // this.addExperimentPrintRecordResp(this.printInfo);
        },
        // addExperimentPrintRecordResp(printJson) {
        //     this.$api.addExperimentPrintRecord({printJson, experimentIdList: this.experimentIds.split(",")});
        // }
    }
}
</script>

<style lang="scss" scoped>
.page-content {
    background: white !important;
    width: 100%;
    height: 100%;
    overflow: auto;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
}
.printContent {
    font-family: MES-Song;
    font-weight: 500;
    font-size: 17px;
}
</style>