<template>
  <div class="content-box">
    <div class="content flex-box flex-column">
      <div class="search-box">
        <div class="fr">
          <el-button type="primary" :disabled="loading" @click="handleSetTarget()">新增骨料含水率</el-button>
          <el-button type="primary" plain :disabled="loading" @click="resetForm()">打印骨料含水率</el-button>
        </div>
        <h3>骨料含水率</h3>
      </div>
      
      <div class="flex-item table-box">
        <div class="scroll-div">
          <el-table
            :data="tableData"
            v-loading="loading"
            style="width: 100%">
            <template v-for="item in tableColumn">
              <el-table-column v-if="item.children" v-key="item.prop" :prop="item.prop" :label="item.label" align="center">
                <el-table-column
                  v-for="separateItem in item.children"
                  :key="separateItem.prop"
                  :prop="separateItem.prop"
                  :label="separateItem.label" 
                  :align="item.align || 'center'"
                  :show-overflow-tooltip="true"
                />
              </el-table-column>
              <!-- "(item.prop === 'turnoverDay' || item.prop === 'turnoverRate' || item.prop === 'inventoryMoney') && activeMenu === '0' ? 'custom': false" -->
              <el-table-column v-else
                v-key="item.prop" 
                :prop="item.prop"
                :label="item.label"
                :align="item.align || 'center'"
                :show-overflow-tooltip="true"
                :formatter="item.formatter"
                :width="item.width"
              />
            </template>
            
            <el-table-column width="100" label="操作" align="center" key="handle" :resizable="false">
              <template slot-scope="scope">
                <el-button type="text" size="small" @click="handleSetTarget(scope.row)">修改</el-button>
                <el-button type="text" size="small" style="color: #ff0000;" @click="handleDel(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <div class="mt16 mb4">
        <Pagination
          :total="total" 
          :pageNum="pageObj.pageNum" 
          :pageSize="pageObj.pageSize" 
          @getData="initData" 
        />
      </div>
    </div>
    
    
    <DrawerForm
      :formContent="formContent"
      ref="drawerForm"
      @saveTarget="saveTarget"
    >
    </DrawerForm>
  </div>
</template>

<script>
import {moistureColumn as tableColumn} from "./config.js"
import Pagination from "@/components/Pagination/index.vue";
import DrawerForm from "@/components/drawerForm.vue";
export default {
  components: {
    Pagination,
    DrawerForm
  },
  data() {
    return {
      addApi: 'moistureContentCreate',
      delApi: 'delMoistureContent',
      updateApi: 'setMoistureContent',
      getListApi: 'getMoistureContent',
      
      loading: false,
      // 设置时间选择
      pickerOptions: {
        disabledDate(time) {
          let deadline = Date.now() - 60 * 60 * 1000;
          return time.getTime() > deadline //
        },
      },
      searchForm: {
        
      },
      tableColumn: tableColumn,
      pageObj: {
        pageNum: 1, // 页数
        pageSize: 10, // 条数
      },
      total: 1,
      tableData: [],
      
      
      activeName: 'second',
      
      formContent: [],
    };
  },
  
  created: function() {
    this.initData();
    
    this.formContent = [
      {
        type: 'datetime',
        label: '操作时间',
        prop: 'operTime',
        format: 'yyyy-MM-dd HH:mm:ss'
      },{
        type: 'input',
        label: '细骨料烘前试样重',
        prop: 'fineAggregateBefore',
      },{
        type: 'input',
        label: '细骨料烘后试样中',
        prop: 'fineAggregateAfter',
      },{
        type: 'input',
        label: '细骨料含水率',
        prop: 'fineAggregateWater',
      },{
        type: 'input',
        label: '粗骨料烘前试样重',
        prop: 'coarseAggregateBefore',
      },{
        type: 'input',
        label: '粗骨料烘后试样中',
        prop: 'coarseAggregateAfter',
      },{
        type: 'input',
        label: '粗骨料含水率',
        prop: 'coarseAggregateWater',
      },{
        type: 'input',
        label: '试验人',
        prop: 'testPerson',
      }
    ]
  },
  methods: {
    handleFilter() {
      console.log(this.searchForm)
      this.initData(1);
    },
    resetForm(){
      this.searchForm = {};
      this.initData(1);
    },
    initData(opageNum, opageSize){
      this.loading = true;
      if (opageNum) this.pageObj.pageNum = opageNum;
      if (opageSize) this.pageObj.pageSize = opageSize;
        
      const params ={
        ...this.pageObj,
        params: this.searchForm
      }
      //获取列表
      this.$api[this.getListApi](params, this).then(res => {
        this.loading = false;
        if(res.succ){
          this.tableData = res.data.list;
          this.total = res.data.total;
        }else{
          this.$message.error(res.msg || '查询失败')
        }
      })
    },
    handleDel(row){
      this.$confirm("删除后不能恢复，确定要删除吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        //删除
        this.$api[this.delApi]({
          id: row.id
        }, this).then((res) => {
          if (res.succ) {
            this.$message({
              showClose: true,
              message: "删除成功",
              type: "success",
            });
            if(this.tableData.length == 1 && this.pageObj.pageNum > 1){
              this.pageObj.pageNum = this.pageObj.pageNum -1
            }
            this.initData();
          }
        });
      }).catch(() => {
        this.$message({
          type: "info",
          message: "已取消删除",
        });
      });
    },
    
    handleSetTarget(row){
      //this.activeRow = row;
      this.$refs.drawerForm.initData(row);
    },
    getName(oVal, list){
      for(let i =0;i<list.length; i++){
        if(oVal == list[i].value){
          return list[i].label;
        }
      }
    },
    saveTarget(formData){
      if(formData.id){//修改this.activeRow
        this.$api[this.updateApi](formData, this).then((res) => {
          if (res.succ) {
            this.$message({
              showClose: true,
              message: "修改成功",
              type: "success",
            });
            this.$refs.drawerForm.handleClose();
            this.initData();
          }
        });
      }else{
        
        this.$api[this.addApi](formData, this).then((res) => {
          if (res.succ) {
            this.$message({
              showClose: true,
              message: "添加成功",
              type: "success",
            });
            this.initData();
            this.$refs.drawerForm.handleClose();
          }
        });
      }
      
    },
  },
};
</script>

<style scoped lang="scss">
  ::v-deep .el-button{
    padding-left: 13px;
    padding-right: 13px;
  }
  .el-form-item{
    margin-bottom: 8px;
  }
  ::v-deep .el-form--inline{
    .el-form-item{
      margin-right: 24px;
      margin-bottom: 0;
      &:last-child{
        margin: 0;
      }
    }
  }
  ::v-deep .el-table{
    .expanded,.expanded:hover{
      background-color: #FFFBD9;
      
    }
    .expanded + tr{
      background-color: #FFFBD9;
      td{
        background-color: #FFFBD9;
      }
    }
    
    .table-child-box{
      margin: 16px;
      padding: 16px;
      background: #FFFFFF;
    }
  }
  
  .content-box{
    padding: 16px;
  }
  .content{
    width: 100%;
    height: 100%;
    padding: 16px;
    background: #FFFFFF;
    border-radius: 16px;
  }
  
  .search-box{
    height: 56px;
    line-height: 40px;
  }
  
  .table-box{
    overflow: hidden;
  }
  
</style>