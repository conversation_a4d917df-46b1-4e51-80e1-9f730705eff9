<template>
    <div class="content-box">
        <div class="flex-box flex-column content">
            <div class="search-box flex-box">
                <el-form class="flex-item" ref="searchForm" :inline="true" :model="searchForm">
                    <el-form-item label="任务单号：">
                        <el-input v-model="searchForm.frwno" clearable
                        placeholder="请输入" 
                        style="width: 180px" 
                        />
                    </el-form-item>
                    <el-form-item label="原工程名称：">
                        <el-input v-model="searchForm.projectName" clearable
                        placeholder="请输入" 
                        style="width: 180px" 
                        />
                    </el-form-item>
                    <el-form-item label="修改后工程名称：">
                        <el-input v-model="searchForm.updateProjectName" clearable
                        placeholder="请输入" 
                        style="width: 180px" 
                        />
                    </el-form-item>
                    <el-form-item label="是否过滤历史增补记录：">
                        <el-radio-group v-model="searchForm.isAddInformation">
                            <el-radio :label="1">是</el-radio>
                            <el-radio :label="0">否</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="handleFilter">搜索</el-button>
                        <el-button type="text" icon="el-icon-refresh-right" @click="resetForm()">重置</el-button>
                    </el-form-item>
                </el-form>
                <el-button type="primary" @click="batchEditClick">批量修改</el-button>
                <el-button type="primary" @click="batchPrintClick">批量打印</el-button>
            </div>

            <!-- table 列表 -->
            <div class="flex-item overHide">
                <div class="scroll-div">
                <el-table
                    :data="tableData"                
                    :key="1"
                    @selection-change="taskSelectionChange"
                    :row-style="cellStyle"
                    >
                    <el-table-column type="selection" align="center"></el-table-column>
                    <template v-for="(item, index) in tableColumn"  >
                        <el-table-column v-if="item.prop === 'consignId'" 
                            :key="index"
                            :prop="item.prop" 
                            :label="item.label" 
                            :fixed="item.fixed" 
                            :width="item.width || ''"
                            :formatter="item.formatter"
                            :show-overflow-tooltip="true"
                            align="center" 
                        >
                            <template slot-scope="scope">
                                <span v-if="parseFloat(scope.row.scquantity || '0') > 0" style="cursor:pointer; color: #3369FF;" @click="selectExperiment(scope.row)">查看</span>
                                <!-- <span style="cursor:pointer; color: #3369FF;" @click="selectExperiment(scope.row)">查看</span> -->
                            </template>
                        </el-table-column>
                        <el-table-column
                        v-else
                        :key="item.prop"
                        :prop="item.prop" 
                        :label="item.label" 
                        :fixed="item.fixed" 
                        :width="item.width || ''"
                        :formatter="item.formatter"
                        :show-overflow-tooltip="true"
                        align="center" 
                        >
                        </el-table-column>
                    </template>
                    
                    <el-table-column fixed="right" width="140" label="操作" align="center" key="handle" :resizable="false">
                        <template slot-scope="scope">
                            <el-button type="text" size="small" @click="editMater(scope.row)">编辑</el-button>
                            <el-button type="text" size="small" @click="printSigle(scope.row)">打印</el-button>
                        </template>
                    </el-table-column>
                </el-table>
                </div>
            </div>
            <div class="mt16 mb4">
                <Pagination
                :total="total" 
                :pageNum="pageObj.pageNum" 
                :pageSize="pageObj.pageSize" 
                @getData="initData" 
                />
            </div>
        </div>
        <PrintDrawer ref="printDrawer" :show="printDrawer" @close="printDrawerClose"  @sure="getPrintType" @change="printTypeChange" />
        <div v-if="experimentNoListDialog">
            <el-dialog 
                width="800px" 
                title="" 
                :visible.sync="experimentNoListDialog" 
                @closed="experimentNoListDialog = false"
            >
                <div class="flex-row" style="font-size: 16px; font-weight: bold;">原材料台账</div>
                <div style="padding: 0px 16px;">
                    <template>
                        <el-row class="flex-row">
                            <div style="flex: 1">
                                <div class="flex-row exp-dialog-title">水泥</div>
                                <div v-if="experimentNoList.snInfoList &&  experimentNoList.snInfoList.length > 0" class="flex-row exp-dialog-cell">
                                    <div class="flex-row exp-dialog-item" v-for="(item, index) in experimentNoList.snInfoList" :key="index + '_sn'">
                                        <span>{{ item.ypbh || item.sytzno }}</span>
                                        <el-button type="text" size="mini" @click="gotoMaterialBoard(item.sytzbh, item.sytzno)">查看</el-button>
                                    </div>
                                </div>
                            </div>
                            <div style="flex: 1">
                                <div class="flex-row exp-dialog-title">粗骨料</div>
                                <div v-if="experimentNoList.cglInfoList &&  experimentNoList.cglInfoList.length > 0" class="flex-row exp-dialog-cell">
                                    <div class="flex-row exp-dialog-item" v-for="(item, index) in experimentNoList.cglInfoList" :key="index + '_cgl'">
                                        <span>{{ item.ypbh || item.sytzno }}</span>
                                        <el-button type="text" size="mini" @click="gotoMaterialBoard(item.sytzbh, item.sytzno)">查看</el-button>
                                    </div>
                                </div>
                            </div>
                        </el-row>
                        <el-row class="flex-row">
                            <div style="flex: 1">
                                <div class="flex-row exp-dialog-title">细骨料</div>
                                <div v-if="experimentNoList.xglInfoList &&  experimentNoList.xglInfoList.length > 0" class="flex-row exp-dialog-cell">
                                    <div class="flex-row exp-dialog-item" v-for="(item, index) in experimentNoList.xglInfoList" :key="index + '_xgl'">
                                        <span>{{ item.ypbh || item.sytzno }}</span>
                                        <el-button type="text" size="mini" @click="gotoMaterialBoard(item.sytzbh, item.sytzno)">查看</el-button>
                                    </div>
                                </div>
                            </div>
                            <div style="flex: 1">
                                <div class="flex-row exp-dialog-title">粉煤灰</div>
                                <div v-if="experimentNoList.fmhInfoList &&  experimentNoList.fmhInfoList.length > 0" class="flex-row exp-dialog-cell">
                                    <div class="flex-row exp-dialog-item" v-for="(item, index) in experimentNoList.fmhInfoList" :key="index + '_fmh'">
                                        <span>{{ item.ypbh || item.sytzno }}</span>
                                        <el-button type="text" size="mini" @click="gotoMaterialBoard(item.sytzbh, item.sytzno)">查看</el-button>
                                    </div>
                                </div>
                            </div>
                        </el-row>
                        <el-row class="flex-row">
                            <div style="flex: 1">
                                <div class="flex-row exp-dialog-title">矿渣粉</div>
                                <div v-if="experimentNoList.kzfInfoList &&  experimentNoList.kzfInfoList.length > 0" class="flex-row exp-dialog-cell">
                                    <div class="flex-row exp-dialog-item" v-for="(item, index) in experimentNoList.kzfInfoList" :key="index + '_kzf'">
                                        <span>{{ item.ypbh || item.sytzno }}</span>
                                        <el-button type="text" size="mini" @click="gotoMaterialBoard(item.sytzbh, item.sytzno)">查看</el-button>
                                    </div>
                                </div>
                            </div>
                            <div style="flex: 1">
                                <div class="flex-row exp-dialog-title">外加剂</div>
                                <div v-if="experimentNoList.wjjInfoList &&  experimentNoList.wjjInfoList.length > 0" class="flex-row exp-dialog-cell">
                                    <div class="flex-row exp-dialog-item" v-for="(item, index) in experimentNoList.wjjInfoList" :key="index + '_wjj'">
                                        <span>{{ item.ypbh || item.sytzno }}</span>
                                        <el-button type="text" size="mini" @click="gotoMaterialBoard(item.sytzbh, item.sytzno)">查看</el-button>
                                    </div>
                                </div>
                            </div>
                        </el-row>
                    </template>
                </div>
                <div class="flex-row" style="margin-top: 24px; font-size: 16px; font-weight: bold;">混凝土台账</div>
                <div style="padding: 0px 16px;">
                    <template>
                        <div class="flex-row exp-dialog-title" style="">抗压台账</div>
                        <div v-if="experimentNoList.kyInfoList &&  experimentNoList.kyInfoList.length > 0" class="flex-row exp-dialog-cell">
                            <div class="flex-row exp-dialog-item" v-for="(kyItem, index) in experimentNoList.kyInfoList" :key="index + '_ky'">
                                <div style="width: 180px;">
                                    <span>{{ kyItem.ypbh || kyItem.sytzno }}</span>
                                    <el-button type="text" size="mini" @click="gotoExperimentBoard(kyItem.sytzbh, kyItem.sytzno)">查看</el-button>
                                </div>
                            </div>
                        </div>
                    </template>
                    <template>
                        <div class="flex-row exp-dialog-title">抗渗台账</div>
                        <div v-if="experimentNoList.ksInfoList &&  experimentNoList.ksInfoList.length > 0" class="flex-row exp-dialog-cell">
                            <div class="flex-row exp-dialog-item"  v-for="(ksItem, index) in experimentNoList.ksInfoList" :key="index + '_ks'">
                                <span>{{ ksItem.ypbh || ksItem.sytzno }}</span>
                                <el-button type="text" size="mini" @click="gotoExperimentBoard(ksItem.sytzbh, ksItem.sytzno)">查看</el-button>
                            </div>
                        </div>
                    </template>
                    <template>
                        <div class="flex-row exp-dialog-title">抗折台账</div>
                        <div v-if="experimentNoList.kzInfoList &&  experimentNoList.kzInfoList.length > 0" class="flex-row exp-dialog-cell">
                            <div class="flex-row exp-dialog-item" v-for="(kzItem, index) in experimentNoList.kzInfoList" :key="index + '_kz'">
                                <span>{{ kzItem.ypbh || kzItem.sytzno }}</span>
                                <el-button type="text" size="mini" @click="gotoExperimentBoard(kzItem.sytzbh, kzItem.sytzno)">查看</el-button>
                            </div>
                        </div>
                    </template>
                    <template>
                        <div class="flex-row exp-dialog-title">抗氯离子台账</div>
                        <div v-if="experimentNoList.kllzInfoList &&  experimentNoList.kllzInfoList.length > 0" class="flex-row exp-dialog-cell">
                            <div class="flex-row exp-dialog-item" v-for="(kllzItem, index) in experimentNoList.kllzInfoList" :key="index + '_kllz'">
                                <span>{{ kllzItem.ypbh || kllzItem.sytzno }}</span>
                                <el-button type="text" size="mini" @click="gotoExperimentBoard(kllzItem.sytzbh, kllzItem.sytzno)">查看</el-button>
                            </div>
                        </div>
                    </template>
                </div>
            </el-dialog>
        </div>

        <div v-if="newBatchShowDialog">
            <el-dialog 
                width="600px" 
                title="批量修改" 
                :visible.sync="newBatchShowDialog" 
                @closed="newBatchShowDialog = false"
            >
                <div style="padding: 0px 16px;">
                    <el-form :model="newBatchForm" label-width="100px">
                        <!-- 工程名称和施工单位输入框 -->
                        <el-form-item label="工程名称" prop="projectName">
                            <el-input v-model="newBatchForm.projectName" placeholder="请输入工程名称" clearable></el-input>
                        </el-form-item>
                        <el-form-item label="施工单位" prop="customerName">
                            <el-input v-model="newBatchForm.customerName" placeholder="请输入施工单位" clearable></el-input>
                        </el-form-item>
                    </el-form>

                    <div class="flex-row-start" style="justify-content: flex-end; margin-top: 30px;">
                        <el-button type="primary" @click="newBatchFormSave">保存</el-button>
                        <el-button @click="newBatchShowDialog = false">取消</el-button>
                    </div>
                </div>
            </el-dialog>
        </div>

        <UpdateRwdextra 
            v-if="showUpdateRwdextraDialog" 
            ref="updateRwdextra" 
            :updateLogId="updateLogId" 
            @close="showUpdateRwdextraDialog = false"
            @saveSuccessed="updateRwdextraSaveSuccessed"
        />
    </div>
</template>  

<script>
import Pagination from "@/components/Pagination/index.vue";
import DialogForm from "@/components/dialogForm.vue";
import PrintDrawer from "./components/printDrawer.vue";
import UpdateRwdextra from "./components/updateRwdextra.vue";

export default {
    components: {
        Pagination,
        DialogForm,
        PrintDrawer,
        UpdateRwdextra,
    },
    data() {
        return {
            newBatchForm: {
                projectName: "",
                customerName: ""
            },

            newBatchShowDialog: false,
            experimentNoListDialog: false,
            experimentNoList: {
                kyInfoList: [],
                ksInfoList: [],
                kzInfoList: []
            },
            searchForm: {
                frwno: '', // 任务单号
                projectName: '', // 原工程名称
                updateProjectName:'', // 修改后工程名称
                isAddInformation: 0, // 是否过滤历史增补记录
            },
            pageObj: {
                pageNum: 1, // 页数
                pageSize: 10, // 条数
            },
            total: 1,
            // 列表数据
            tableData: [],
            // 选中的数据
            taskSelects: [],
            // 列表展示数据
            tableColumn: [
                {
                    label: '任务单号',
                    prop: 'frwno',
                    width: '120'
                },
                {
                    label: '原工程名称',
                    prop: 'projectName',
                    width: '300'
                },
                {
                    label: '修改后工程名称',
                    prop: 'updateProjectName',
                    width: '300'
                },
                {
                    label: '委托编号',
                    prop: 'consignId',
                    width: '80'
                },
                {
                    label: '配合比编号',
                    prop: 'updatePhb',
                    width: '100'
                },
                {
                    label: '客户名称',
                    prop: 'customerName',
                    width: '190'
                },
                {
                    label: '计划方量',
                    prop: 'fjhsl',
                    width: '80'
                },
                {
                    label: '完成方量',
                    prop: 'fhquantity',
                    width: '80'
                },
                {
                    label: '施工部位',
                    prop: 'fjzbw',
                    width: '240'
                },
                {
                    label: '砼品种',
                    prop: 'tpz',
                    width: '80'
                },
                // {
                //     label: '计划日期',
                //     prop: 'updateProjectName',
                //     width: '150',
                //     formatter: function(row) {
                //         if (row.updateProjectName) {
                //             return moment(row.updateProjectName).format('YYYY-MM-DD HH:mm');
                //         }
                //         return '--';
                //     }
                // },
                {
                    label: '计划日期',
                    prop: 'plantime',
                    width: '180'
                },
                {
                    label: '供货起止日期',
                    prop: 'supplyTime',
                    width: '200'
                },
                {
                    label: '增补时间',
                    prop: 'createTime',
                    width: '200'
                },
                
            ],
            // 打印菜单
            printDrawer: false,

            showUpdateRwdextraDialog: false,
            updateLogId: ''
        }
    },

    activated() {
        this.initData();
    },

    methods: {
        resetNewBatchFormData() {
            this.newBatchForm= {
                projectName: "",
                customerName: ""
            }
        },

        newBatchFormSave() {
            if (this.isEmpty(this.newBatchForm.projectName) && this.isEmpty(this.newBatchForm.customerName)) {
                this.$message.error('请输入内容');
                return;
            }
            let params = {};
            if (!this.isEmpty(this.newBatchForm.projectName)) {
                params.projectName = this.newBatchForm.projectName;
            }
            if (!this.isEmpty(this.newBatchForm.customerName)) {
                params.customerName = this.newBatchForm.customerName;
            }
            let trwdIds = [];
            trwdIds = this.taskSelects.map(item => item.id);
            this.$api.rwUpdateBatchPrintDetailRwdextra({
                updateLogIdList: trwdIds,
                ...params,
            }, this).then(res => {
                if(res.succ){
                    this.$message.success('保存成功');
                    this.newBatchShowDialog = false;
                    this.initData();
                    this.resetNewBatchFormData();
                }else{
                    this.$message.error(res.msg || '保存失败')
                }
            });
        },

        gotoExperimentBoard(sytzbh, sytzno) {
            this.experimentNoListDialog = false;
            window.location.href = `/mes-web/qualityControl/experimentBoard?id=${sytzbh}&experimentNo=${sytzno}`
        },

        gotoMaterialBoard(sytzbh, sytzno) {
            this.experimentNoListDialog = false;
            window.location.href = `/mes-web/qualityControl/materialBoard?id=${sytzbh}&experimentNo=${sytzno}`
        },

        // 查看委托编号（需要调用修改后的接口，获取修改后的委托编号信息）
        selectExperiment(row) {
            console.log(row);
            this.$api.getExperimentUpdateByFrwdh(`updateLogId=${row.id}`, this).then(res => {
                if (res.succ) {
                    this.experimentNoList = res.data;
                    this.experimentNoListDialog = true;
                }else{
                    this.$message.error(res.msg || '查询失败')
                }
            });
        },
        // table 单个 cell 的样式
        cellStyle({ row, column, rowIndex, columnIndex }) {
            if (row.isAddInformation == 1) {
                return { color: '#FF4500' }
            } else {
                return { color: '#333' }
            }
        },
        // 搜索按钮事件
        handleFilter() {
            console.log(this.searchForm)
            this.initData(1);
        },
        // 搜索条件重置
        resetForm(){
            this.searchForm = {
                fztList: [],
                plantimeDate: []
            };
            this.initData();
        },
        // 请求列表数据
        initData(opageNum, opageSize) {
            if (opageNum) this.pageObj.pageNum = opageNum;
            if (opageSize) this.pageObj.pageSize = opageSize;

            let params = {
                ...this.searchForm
            };

            this.$api.queryErpRwdUpdatePage({
                ...this.pageObj,
                params: params,
            }, this).then(res => {
                if(res.succ){
                    this.tableData = res.data.list;
                    console.log('列表数据：',this.tableData );
                    this.total = res.data.total;
                }else{
                    this.$message.error(res.msg || '查询失败')
                }
            });
        },

        // 列表选择事件
        taskSelectionChange(val) {
            this.taskSelects = val;
        },
        // 关闭打印菜单
        printDrawerClose() {
            this.printDrawer = false;
        },
        // 批量打印
        batchPrintClick() {
            if (this.taskSelects.length == 0) {
                // 弹出提示
                this.$message({
                    showClose: true,
                    message: "请选择任务单",
                    type: "warning",
                });
                return;
            }
            this.printDrawer = true;
        },

        // 批量修改
        batchEditClick() {
            if (this.taskSelects.length == 0) {
                // 弹出提示
                this.$message({
                    showClose: true,
                    message: "请选择任务单",
                    type: "warning",
                });
                return;
            }
            this.newBatchShowDialog = true;
        },
        // 单个数据打印
        printSigle(row) {
            this.taskSelects = [row];
            this.printDrawer = true;
        },
        // 获取打印类型
        getPrintType(val) {
            if (val.length == 0) {
            this.$message({
                showClose: true,
                message: "请选择打印数据表",
                type: "warning",
            });
            return;
            }
            this.printBtnClick(val.join(","))
        },

        printTypeChange(types) {
            // ["a2", "a1", "a9"] 开盘鉴定、出厂合格证和调整记录是生产方量大于0，才可以打印。
            // a8 配合比是有配比编号了，就可以打印了
            for (const element of this.taskSelects) {
                let tempTypes = [].concat(types);
                tempTypes.map(item => {
                    if (item === 'a1' && !(parseFloat(element.fhquantity || '0') > 0)) {
                        this.removeCheckItem("a1", "当前存在任务单暂未生产，暂不支持打印")
                    } else if ((item === 'a1N') && !(parseFloat(element.fhquantity || '0') > 0)) {
                        this.removeCheckItem("a1N", "当前存在任务单暂未生产，暂不支持打印")
                    }
                    else if (item === 'a9' && !(parseFloat(element.fhquantity || '0') > 0)) {
                        this.removeCheckItem("a9", "当前存在任务单暂未生产，暂不支持打印")
                    }
                    else if (item === 'a2' && !(parseFloat(element.fhquantity || '0') > 0)) {
                        this.removeCheckItem("a2", "当前存在任务单暂未生产，暂不支持打印")
                    }
                    else if (item === 'a8' && !element.rwdextraInfo.fphbNo) {
                        this.removeCheckItem("a8", "无配比信息请先下配比")
                    }
                });
            }
        },

        removeCheckItem(item, mesg) {
            this.$message({
                showClose: true,
                message: mesg,
                type: "warning",
            });
            this.$refs.printDrawer.removeCheckItem(item);
        },

        printBtnClick(types) {
            if (this.taskSelects.length == 0) {
                // 弹出提示
                this.$message({
                    showClose: true,
                    message: "请选择任务单",
                    type: "warning",
                });
                return;
            }

            let list = [];
            this.taskSelects.forEach((item)=>{
                console.log(item);
                list.push(item.id);
            })
            console.log('打印数据：', list);
            let routeData = this.$router.resolve({
                path: "/printTaskContent",
                query: {
                    taskIds: list.join(","),
                    formType: 'logs',
                    printType: types
                }
            });
            window.open(routeData.href, '_blank');
        },

        isEmpty(val) {
            if (typeof val === "boolean") {
                return false;
            }
            if (typeof val === "number") {
                return false;
            }
            if (val instanceof Array) {
                if (val.length === 0) return true;
            } else if (val instanceof Object) {
                if (JSON.stringify(val) === "{}") return true;
            } else {
                if (
                val === "null" ||
                val == null ||
                val === "undefined" ||
                val === undefined ||
                val === ""
                )
                return true;
                return false;
            }
            return false;
        },

        editMater(row) {
            this.updateLogId = row.id;
            this.showUpdateRwdextraDialog = true;
            this.$nextTick(() => {
                this.$refs.updateRwdextra.show();
            });
        },

        updateRwdextraSaveSuccessed() {
            this.initData();
        }
    },
}
</script>

<style lang="scss" scoped>
 ::v-deep .el-button{
    padding-left: 13px;
    padding-right: 13px;
  }
  .el-form-item{
    margin-bottom: 8px;
  }
  ::v-deep .el-form--inline{
    .el-form-item{
      margin-right: 24px;
      margin-bottom: 0;
      &:last-child{
        margin: 0;
      }
    }
  }
  ::v-deep .el-table{
    .expanded,.expanded:hover{
      background-color: #FFFBD9;
      
    }
    .expanded + tr{
      background-color: #FFFBD9;
      td{
        background-color: #FFFBD9;
      }
    }
    
    .table-child-box{
      margin: 16px;
      padding: 16px;
      background: #FFFFFF;
    }
  }
  
  .content-box{
    padding: 16px;
  }
  .content{
    width: 100%;
    height: 100%;
    padding: 16px;
    background: #FFFFFF;
    border-radius: 16px;
  }
  
  .search-box{
    padding-bottom: 16px;
    line-height: 40px;
  }
  .cell-state {
    .rda-task-state {
        display: inline-block;
        padding-left: 10px;
        padding-right: 10px;
        height: 18px;
        line-height: 18px;
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #FFFFFF;
        text-align: center;
        background: #1F7AFF;
        border-radius: 2px;
    }
    .dqr {
        background: #DC3290;
    }
    .ddd {
        background: #3369FF;
    }
    .dwc {
        background: #1FAE66;
    }
    .yqx {
        background: #D6D6D6;
    }
    .yjj {
        background: #ADAA00;
    }
    .ywc {
        background: #515157;
    }
  }
  .exp-dialog-title {
    background: #F2F4F5; 
    color: #6A727D; 
    font-size: 14px; 
    font-weight: 600; 
    padding: 10px 8px;
    margin-top: 20px;
  }
  .exp-dialog-item {
    height: 41px;
    justify-content: flex-start;
    word-wrap: break-word;
  }
  .exp-dialog-cell {
    justify-content: flex-start;
    flex-wrap:wrap;
  }

 ::v-deep .el-button{
    padding-left: 13px;
    padding-right: 13px;
  }
  .el-form-item{
    margin-bottom: 8px;
  }
  ::v-deep .el-form--inline{
    .el-form-item{
      margin-right: 24px;
      margin-bottom: 0;
      &:last-child{
        margin: 0;
      }
    }
  }
  ::v-deep .el-table{
    .expanded,.expanded:hover{
      background-color: #FFFBD9;
      
    }
    .expanded + tr{
      background-color: #FFFBD9;
      td{
        background-color: #FFFBD9;
      }
    }
    
    .table-child-box{
      margin: 16px;
      padding: 16px;
      background: #FFFFFF;
    }
  }
  
  .content-box{
    padding: 16px;
  }
  .content{
    width: 100%;
    height: 100%;
    padding: 16px;
    background: #FFFFFF;
    border-radius: 16px;
  }
  
  .search-box{
    padding-bottom: 16px;
    line-height: 40px;
  }
  .cell-state {
    .rda-task-state {
        display: inline-block;
        padding-left: 10px;
        padding-right: 10px;
        height: 18px;
        line-height: 18px;
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #FFFFFF;
        text-align: center;
        background: #1F7AFF;
        border-radius: 2px;
    }
    .dqr {
        background: #DC3290;
    }
    .ddd {
        background: #3369FF;
    }
    .dwc {
        background: #1FAE66;
    }
    .yqx {
        background: #D6D6D6;
    }
    .yjj {
        background: #ADAA00;
    }
    .ywc {
        background: #515157;
    }
  }
  .exp-dialog-title {
    background: #F2F4F5; 
    color: #6A727D; 
    font-size: 14px; 
    font-weight: 600; 
    padding: 10px 8px;
    margin-top: 20px;
  }
  .exp-dialog-item {
    height: 41px;
    justify-content: flex-start;
    word-wrap: break-word;
  }
  .exp-dialog-cell {
    justify-content: flex-start;
    flex-wrap:wrap;
  }
</style>