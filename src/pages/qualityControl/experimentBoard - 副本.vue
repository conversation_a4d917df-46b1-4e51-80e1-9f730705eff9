<template>
  <div class="flex-box flex-column">
    <div class="search-box flex-box">
      <el-form class="flex-item" ref="searchForm" :inline="true" :model="searchForm" :disabled="loading">
        <!-- <el-form-item label="">
        	<el-input v-model="searchForm.reportNo" clearable
            placeholder="请输入订单号、工程名称、砼品种" 
            style="width: 270px" 
          />
        </el-form-item> -->
      
      	<el-form-item label="发生日期" prop="takeEffectDate">
            
          <el-date-picker type="daterange" 
            v-model="searchForm.takeEffectDate" 
            :picker-options="pickerOptions"
            start-placeholder="开始日期" end-placeholder="结束日期" 
            format="yyyy-MM-dd" value-format="yyyy-MM-dd"
            :clearable="true" :disabled="loading" style="width: 360px"
          >
          </el-date-picker>
      	</el-form-item>
        <el-form-item label="指标类型：">
          <el-radio-group v-model="searchForm.checkType">
            <el-radio :label="1">快检</el-radio>
            <el-radio :label="2">批检</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :disabled="loading" @click="handleFilter">搜索</el-button>
          <el-button type="text" icon="el-icon-refresh-right" :disabled="loading" @click="resetForm()">重置</el-button>
        </el-form-item>
      </el-form>
      
      <el-button type="primary" @click="goDetail()">切换视图</el-button>
    </div>
    <div class="flex-item content">
      <div class="flex-box">
        
       <!-- 混凝土看板左侧列表 -->
      <div class="con-lef">
        <div v-if="diffExperimentCount > 0" @click="resetForm()" style="cursor: pointer;; text-align: center; line-height: 30px; width: 100%; background-color: #D9EAFF; margin-bottom: 8px; border-radius: 4px; color: #4F499B;">
          有<span style="color: #1D67BD; font-weight: bold;">{{ diffExperimentCount }}</span>条新的委托，点击刷新
        </div>
        <div @click="showDetail(item)" :class="{'active' : activeId == item.id}" class="g-card" v-for="(item,index) in dataListUnderway" :key="item.id">
          <p class="gc-main">{{item.experimentNo}} <span class="type-span" style="border-radius: 3px;" :style="{backgroundColor: item.checkType == '1' ? '#496BF9' : '#61A480', color: '#fff'}">{{ item.checkType == '1' ? '快检' : '批检' }}</span> <span class="fr">{{experimentStatusObj[item.experimentStatus]}}</span></p>
          <p class="gc-main pb16">
            {{experimentTypeNameList[item.experimentType]}}&nbsp;&nbsp;&nbsp;
            {{item.sampleLevel}}&nbsp;&nbsp;&nbsp;
            {{item.phb}}
          </p>
          <!-- <p>供货商：{{item.entrustPersonName}}</p> -->
          <p>委托时间：{{item.entrustTime}}</p>
          <p>代表数量：{{item.behalfNumber}}</p>
        </div>
        <p v-if="dataListUnderway.length == 0" style="text-align: center;">暂无数据</p>
        
      </div>

      <!-- 混凝土看板中间 -->
      <div class="flex-item con-center">
        <div class="cc-box">
          <h4>委托信息</h4>
          <div class="cc-info">
            <div class="flex-box">
              <div class="flex-item">
                <p class="pb16 cci-main">{{activeData.experimentNo}} <span class="type-span" style="border-radius: 3px; padding: 0px 7px; font-size: 12px; font-weight: 600;" :style="{backgroundColor: activeData.checkType == '1' ? '#496BF9' : '#61A480', color: '#fff'}">{{ activeData.checkType == '1' ? '快检' : '批检' }}</span> </p>
                <p><span>委托原因：{{activeData.entrustReasonName}}</span>
                  <span>配合比编号：{{activeData.phb}}</span>
                </p>
              </div>
              <div style="margin-top: -10px;" v-if="activeData.experimentStatus == 0">
                <el-button type="primary" :disabled="loading" @click="handleSetState('setExperimentAccept')">接受</el-button>
                <el-button type="primary" plain :disabled="loading" @click="handleSetState('setExperimentRefuse')">拒绝</el-button>
              </div>
            </div>
            
            
            <p><span>样品等级：{{activeData.sampleLevel}}</span><span>委托时间：{{activeData.entrustTime}}</span>
            <span>代表数量：{{activeData.behalfNumber}}</span>
            <span>委托人：{{activeData.entrustPersonName}}</span></p>
            <p>委托试验：{{activeData.entrustExperiment}}</p>
          </div>
          <!-- <div class="cc-info">
            <p class="pb16 cci-main">SNWT000000001</p>
            <p>委托原因：更换配合比</p>
            <p><span>样品等级：PO42.5</span><span>委托时间：2023-12-23 00:23</span></p>
            <p>委托试验：和易性检测、保水性检测、抗压强度检测</p>
          </div> -->
          <div class="cc-info ">
            <!-- flex-box -->
            <!-- <div class="flex-item"> -->
              <p class="pb16 cci-main">任务信息</p>
              <!-- <p class="cci-main">{{taskData.frwdh}}</p> -->
              <p v-for="item in taskData" :key="item.id">
                <span class="fr" style="margin: 0;height: 20px;overflow: hidden;"><el-button type="text" size="mini" @click="goTaskDetail(item)">任务单详情</el-button></span>
                <span>{{item.frwno}}</span>
                <span v-if="item.fgcmc && item.fgcmc.length < 16">{{item.fgcmc}}</span>
                <el-tooltip v-else class="item" effect="dark" :content="item.fgcmc" placement="top">
                  <span class="">{{item.fgcmc.slice(0,15)}}...</span>
                </el-tooltip>
                <span>{{item.fjzbw}}</span>
                
                <span>{{item.fhquantity}}</span>
              </p>
              
              
              
            <!-- </div>
            <div style="margin-top: 30px;">
              <el-button type="primary" :disabled="loading" @click="handleSetState('setExperimentAccept')">接受</el-button>
              <el-button type="primary" plain :disabled="loading" @click="handleSetState('setExperimentRefuse')">拒绝</el-button>
            </div> -->
          </div>
          <!-- <div class="cc-info">
            <p class="cci-main">CGRWD0000001</p>
            <p><span>样品等级：PO42.5</span><span>委托时间：2023-12-23 00:23</span></p>
            <p>委托试验：和易性检测、保水性检测、抗压强度检测</p>
          </div> -->
        </div>
        <template v-if="activeData.experimentStatus == 1 || activeData.experimentStatus == 2 || activeData.experimentStatus == 3">
          <SampleMgt ref="sampleMgt" @setSampleTotal="setSampleTotal" :id="activeId" :checkType="activeData.checkType" :experimentStatus="activeData.experimentStatus"></SampleMgt>
          <!-- 只有试验中才有试验信息 -->
          <div v-if="activeData.experimentStatus == 2 || activeData.experimentStatus == 3" class="cc-box">
            <h4>试验信息</h4>
            <div class="cc-info">
              <el-form  label-width="84px" ref="testInfoForm" :model="testInfoForm" :disabled="loading || activeData.experimentStatus === 3">
                <el-row :gutter="40">
                  <el-col :span="24">
                    <el-form-item label="检测方法：">
                      <el-input v-model="testInfoForm.experimentGist" clearable
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="判定依据：">
                      <el-input v-model="testInfoForm.judgeGist" clearable
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="仪器设备：">
                      <el-input v-model="testInfoForm.equipment" disabled clearable
                      />
                      <!-- <el-select
                        v-model="testInfoForm.equipment" 
                        filterable clearable 
                        placeholder="请选择仪器设备" 
                        style="width: 380px">
                        <el-option
                          v-for="item in equipmentOption"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value">
                        </el-option>
                      </el-select> -->
                    </el-form-item>
                  </el-col>
                  
                  <el-col :span="8">
                    <el-form-item label="成型日期：">
                      <!-- datetime  HH:mm:ss-->
                      <el-date-picker
                        type="date"
                        v-model="testInfoForm.moldingTime"
                        placeholder="选择日期"
                        format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd"
                      >
                      </el-date-picker>
                      
                      
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="环境温度：">
                      <el-input v-model="testInfoForm.temperature" clearable
                        placeholder="请输入" 
                      >
                        <template slot="append">℃</template>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="环境湿度：">
                      <el-input v-model="testInfoForm.humidness" clearable
                        placeholder="请输入" 
                      >
                        <template slot="append">%</template>
                      </el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
              
              <ExperProjectList 
                ref="formTab" 
                :sampleLevel='activeData.sampleLevel' 
                :experimentStatus="activeData.experimentStatus" 
                :sampleTotal="sampleTotal" 
                :activeId="activeId"
              ></ExperProjectList>
            </div>
            
          </div>
          <div style="margin-bottom: 20px;" v-if="activeData.experimentStatus == 2 || activeData.experimentStatus == 3" class="cc-box">
            <h4>试验结论</h4>
            <div class="cc-info">
              <el-form label-width="100px" ref="conclusionForm" :model="testInfoForm" :disabled="loading || activeData.experimentStatus === 3">
                <el-form-item label="是否合格：">
                  <el-radio-group v-model="testInfoForm.isQualified">
                    <el-radio :label="1">合格</el-radio>
                    <el-radio :label="2">不合格</el-radio>
                  </el-radio-group>
                </el-form-item>
              
                <el-form-item label="结论：">
                  <el-input v-model="testInfoForm.conclusion" clearable
                    placeholder="请输入" 
                  />
                </el-form-item>
                <el-form-item label="备注：">
                  <el-input v-model="testInfoForm.remark" clearable
                    placeholder="请输入" 
                  />
                </el-form-item>
              </el-form>
            </div>
          </div>
          <div class="center-footer" v-if="activeData.experimentStatus == 2">
            <el-button type="primary" :disabled="loading" @click="saveExperimentInfo">保存</el-button>
            <el-button type="primary" :disabled="loading" @click="handleSetState('setExperimentFinish')">完成</el-button>
          </div>
        </template>
      
      </div>

      <!-- 混凝土看板右侧列表 -->
      <div class="con-right">
        <div  @click="showDetail(item)" :class="{'active' : activeId == item.id}" class="g-card" v-for="(item,index) in dataListFinished" :key="item.id">
          <p class="gc-main">{{item.experimentNo}} <span class="type-span" style="border-radius: 3px" :style="{backgroundColor: item.checkType == '1' ? '#496BF9' : '#61A480', color: '#fff'}">{{ item.checkType == '1' ? '快检' : '批检' }}</span> <span class="fr succ">{{experimentStatusObj[item.experimentStatus]}}</span></p>
          <p class="gc-main pb16">
            {{experimentTypeNameList[item.experimentType]}}&nbsp;&nbsp;&nbsp;
            {{item.sampleLevel}}&nbsp;&nbsp;&nbsp;
            {{item.phb}}
          </p>
          <!-- <p>供货商：{{item.entrustPersonName}}</p> -->
          <p>委托时间：{{item.entrustTime}}</p>
          <p>代表数量：{{item.behalfNumber}}</p>
        </div>
        <p v-if="dataListFinished.length == 0" style="text-align: center;">暂无数据</p>
      </div>
    
      </div>
    </div>
    
    
  </div>
</template>

<script>
import moment from "@/utils/moment";
import SampleMgt from "./components/sampleMgt.vue";
import ExperProjectList from "./components/experProjectList.vue";
import getOpt from "@/common/js/getListData.js"

export default {
  components: {
    SampleMgt,
    ExperProjectList
  },
  data() {
    return {
      sfssListColumn: ["sfss1", "sfss2", "sfss3", "sfss4",  "sfss5", "sfss6"],
      loading: false,
      filePrefix: this.$store.state.loginStore.userInfo.filePrefix,
      // 设置时间选择
      pickerOptions: {
        disabledDate(time) {
          let deadline = Date.now() - 60 * 60 * 1000;
          return time.getTime() > deadline //
        },
      },
      searchForm: {
        takeEffectDate: [moment.severalDaysAgo(60), moment.today()],
      },
      dataListFinished: [],//已完成
      dataListUnderway: [],//进行中
      experimentStatusObj:{
        0: '待接收',
        1: '待取样',
        2: '试验中',
        3: '已完成',
        4: '已拒绝',
      },
      experimentTypeList:[],
      experimentTypeNameList: {},
      activeId: '',//当前选中的id
      activeData: {},//当前选中的id
      taskData: [],//任务数据
      
      //conclusionForm:{},//结论表单
      testInfoForm:{},//信息表单
      
      sampleTotal: 0,
      equipmentOption: [],
      totalExperimentCount: 0,
      diffExperimentCount: 0, // 有几条未更新
    };
  },
  
  created: function() {
    this.initData();
    this.$api.getDictValue({
      dictCode: 'MASTERIAL_TYPE'
    }, this).then(res => {
      this.experimentTypeList = res.data.list
      this.experimentTypeNameList = {};
      res.data.list.forEach(item => {
        this.experimentTypeNameList[item.dictValueCode] = item.dictValueName
      })
    })
    
    this.getequipmentOption();
    
  },
  methods: {
    async getequipmentOption(){
      this.equipmentOption = await getOpt.getEquipmentAll(this)
    },
    //抽样数量
    setSampleTotal(val){
      this.sampleTotal = val;
    },
    //保存
    saveExperimentInfo(){
      const odata = [...this.$refs.formTab.quickData, ...this.$refs.formTab.projectList];
      let oQuickForm = this.$refs.formTab.quickForm;
      
      let oSsjList= this.$refs.formTab.ssjlInfoList;
      let oSsjListIndex = -1;
      let ossksdj;
      if(oSsjList && oSsjList.length > 0 && this.$refs.formTab.projectList.length > 0){
        for(let i=0;i<oSsjList.length; i++){
          if(!oSsjList[i].syyl || oSsjList[i].syyl <= 0){
            this.$message.error(`第${i + 1}行，试验压力需是大于0的数字`);
            return false;
          }
          if(!oSsjList[i].jysj){
            this.$message.error(`第${i + 1}行，请填写加压时间`);
            return false;
          }
          
          let total = 0;
          for(let j =0; j < this.sfssListColumn.length; j++){
            if(oSsjList[i][this.sfssListColumn[j]] == '是'){
              total = total + 1;
            }
          }
          if(total >= 3){
            oSsjListIndex = i;
            ossksdj = oSsjList[i].syyl * 10 - 1;
            break;
          }
        }
        
        if(oSsjListIndex > -1){
          //剩余不要
          oSsjList = oSsjList.slice(0, oSsjListIndex * 1 + 1)
        }
      }
      
      for(let i = 0; i< odata.length; i++){
        let objJson = {}
        let objImg = ""
        if(odata[i].testProjectName === '流动性' || odata[i].testProjectName === '保水性' || odata[i].testProjectName === '粘聚性'|| item.testProjectName === '坍落度' 
                || item.testProjectName === '目测砂率' ){//快检odata[i].checkType == 1
          objJson = oQuickForm[odata[i].testProjectCode].objJson;
          if(odata[i].testProjectName === '流动性' || odata[i].testProjectName === '保水性' || odata[i].testProjectName === '粘聚性'|| item.testProjectName === '坍落度' 
                || item.testProjectName === '目测砂率' ){
            const oVal = objJson.val;
            for(let item in objJson){
              if(item != 'val'){
                objJson[item] = 0;
                if(item == oVal){
                  objJson[item] = 1;
                }
              }
              
            }
          }
          if(this.$refs.formTab.quickForm.img){
            objImg = this.$refs.formTab.quickForm.img.map(item => {
              if(item.url){
                return item.url.replace(this.filePrefix, '')
              }else{
                return item.replace(this.filePrefix, '');
              }
            })
            objImg = objImg.join(',');
          }
        }else if(odata[i].testProjectCode === 'CONCRETE_PARAM_KYQD'){
          objJson = this.$refs.formTab.searchForm6;
          if(this.$refs.formTab.searchForm6.img){
            objImg = this.$refs.formTab.searchForm6.img.map(item => {
              if(item.url){
                return item.url.replace(this.filePrefix, '')
              }else{
                return item.replace(this.filePrefix, '');
              }
            })
            objImg = objImg.join(',');
          }
        }else if(odata[i].testProjectCode === 'CONCRETE_PARAM_KSDJ'){
          objJson = this.$refs.formTab.searchForm7;
          if(ossksdj){
            objJson.ssksdj = 'P' + parseInt(ossksdj);
          }else{
            objJson.ssksdj = '';
          }
          //处理赋值表格数据
          objJson.ksInfo = oSsjList;
          //处理图片
          if(this.$refs.formTab.searchForm7.img){
            objImg = this.$refs.formTab.searchForm7.img.map(item => {
              if(item.url){
                return item.url.replace(this.filePrefix, '');
              }else{
                return item.replace(this.filePrefix, '');
              }
            })
            objImg = objImg.join(',');
          }
        }
        this.saveTabData(odata[i],objJson,objImg)
      }
      
      //保存实现信息和结论
      let oTestInfoForm = JSON.parse(JSON.stringify(this.testInfoForm))
      if(oTestInfoForm.moldingTime && oTestInfoForm.moldingTime.length === 10){
        oTestInfoForm.moldingTime = oTestInfoForm.moldingTime + ' 00:00:01'
      }
      
      this.$api.setExperiment(oTestInfoForm).then(res =>{
        if(res.succ){
          this.$message.success("保存成功")
        }
      })
    },
    saveTabData(odata,objJson, objImg){
      this.$api.setExperimentDetail({
        id: odata.id,
        experimentId: this.activeId,
        checkType: odata.checkType,//1-快检 2-批检 == 1 ? 1 : 2
        testProjectCode: odata.testProjectCode,
        testProjectName: odata.testProjectName,
        objJson, 
        objImg
      }, this).then(res => {
        if(res.succ){
          //this.$message.success("保存成功")
        }else{
          this.$message.error(res.msg || '保存失败')
        }
      })
    },
    //试验信息
    
    //试验结论
    
    
    handleFilter() {
      console.log(this.searchForm)
      this.initData();
      
    },
    resetForm(){
      this.searchForm = {
        takeEffectDate: [moment.severalDaysAgo(60), moment.today()],
      };
      this.initData();
    },
    initData(activeId){//更新列表，update
      this.loading = true;
      let params = JSON.parse(JSON.stringify(this.searchForm))
      if (!this.isEmpty(this.searchForm.takeEffectDate)) {
        params.startTime = this.searchForm.takeEffectDate[0]
          ? this.searchForm.takeEffectDate[0]
          : "";
        params.endTime = this.searchForm.takeEffectDate[1]
          ? this.searchForm.takeEffectDate[1]
          : "";
      }
      delete params.takeEffectDate
      this.diffExperimentCount = 0;
      //获取列表
      this.$api.getExperimentListAll(params, this).then(res => {
        this.loading = false;
        if(res.succ){
          this.dataListFinished = [];
          this.dataListUnderway = [];
          this.totalExperimentCount = res.data.list.length;

          res.data.list.forEach(item =>{
             //0-待接收  1-待取样  2-试验中;  3-已完成 4-已拒绝
            if(item.experimentStatus == 3 || item.experimentStatus == 4){
              this.dataListFinished.push(item)
            }else{
              this.dataListUnderway.push(item)
            }
          })
          if(activeId){
            this.showDetail({
              id: activeId
            })
          }else if(this.$route.query.id){
            this.showDetail({
              id: this.$route.query.id
            })
            //this.activeId = 
          }else if(this.dataListUnderway.length > 0){
            this.showDetail(this.dataListUnderway[0])
          }else if(this.dataListFinished.length > 0){
            this.showDetail(this.dataListFinished[0])
          }
          
        }else{
          this.$message.error(res.msg || '查询失败')
        }
      })

      this.pollingExperimentAllCount(params)
    },
    
    pollingExperimentAllCount(parma) {
      if (!this.pollTimer) {
        this.pollTimer = setInterval(() => {
          this.getExperimentAllCountResp(parma)
        }, 60000);
      }
    },
    // 查询总数
    getExperimentAllCountResp(parma) {
      this.$api.getExperimentAllCount(parma, this).then(res => {
        if(res.succ){
          this.diffExperimentCount = parseInt(res.data.totalCount) - parseInt(this.totalExperimentCount);
        }
      })
    },
    
    showDetail(row){
      this.activeId = row.id;
      this.getExperimentById(row.id);
      this.getTaskInfo(row.id);
    },
    //获取详情
    getExperimentById(id){
      this.$api.getExperimentById(`id=${id}`, this).then(res => {
        this.loading = false;
        if(res.succ){
          this.activeData = res.data;
          this.testInfoForm = JSON.parse(JSON.stringify(res.data))
          if(!this.testInfoForm.equipment){
            //获取仪器设备
            this.getEquipmentData()
          }
          //获取最新温湿度
          if(!res.data.temperature || !res.data.humidness){
            this.getHumitureData();
          }
          if(res.data.experimentStatus == 1 || res.data.experimentStatus == 2 || res.data.experimentStatus == 3){
            this.$nextTick(()=>{
              this.$refs.sampleMgt.initData();
            })
          }
          if(res.data.experimentStatus == 2 || res.data.experimentStatus == 3){
            this.$nextTick(()=>{
              this.$refs.formTab.clearData();
              this.$refs.formTab.setExperimentProject()
            })
          }
        }else{
          this.$message.error(res.msg || '查询失败')
        }
      })
    },
    //获取最新温湿度
    getHumitureData(){
      console.log(this.testInfoForm.dictValueCode)
      this.$api.getHumitureData({}, this).then(res => {
        if(res.succ){
          if(!this.testInfoForm.temperature){
            this.testInfoForm.temperature = res.data.temperature
          }
          if(!this.testInfoForm.humidness){
            this.testInfoForm.humidness = res.data.humidness
          }
        }else{
          this.$message.error(res.msg || '查询失败')
        }
      })
    },
    //获取仪器设备
    getEquipmentData(){
      console.log(this.testInfoForm.dictValueCode)
      this.$api.getEquipmentData(`testProjectType=CONCRETE`, this).then(res => {
        if(res.succ){
          let oName = res.data.list.map(item => item.equipmentName)
          this.testInfoForm.equipment = oName.join('、');
          
        }else{
          this.$message.error(res.msg || '查询失败')
        }
      })
    },
    
    //获取任务信息
    getTaskInfo(id){
      this.$api.getTaskInfo(`experimentId=${id}`, this).then(res => {
        if(res.succ){
          this.taskData = res.data
        }else{
          this.$message.error(res.msg || '查询失败')
        }
      })
    },
    
    
    
    isEmpty(val) {
      if (typeof val === "boolean") {
        return false;
      }
      if (typeof val === "number") {
        return false;
      }
      if (val instanceof Array) {
        if (val.length === 0) return true;
      } else if (val instanceof Object) {
        if (JSON.stringify(val) === "{}") return true;
      } else {
        if (
          val === "null" ||
          val == null ||
          val === "undefined" ||
          val === undefined ||
          val === ""
        )
          return true;
        return false;
      }
      return false;
    },
    
    //接受，完成 拒绝
    handleSetState(type){
      let tip = ''
      if(type == 'setExperimentAccept'){
        tip = '确定接收： ‘' + this.activeData.experimentNo + '’ 吗？'
      }else if(type == 'setExperimentFinish'){
        tip = '确定完成： ‘' + this.activeData.experimentNo + '’ 吗？'
      }else if(type == 'setExperimentRefuse'){
        tip = '确定拒绝： ‘' + this.activeData.experimentNo + '’ 吗？'
      }
      
      this.$confirm(tip, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.$api[type]({
          id: this.activeId
        }).then((res) => {
          if (res.succ) {
            this.$message({
              showClose: true,
              message: "操作成功",
              type: "success",
            });
            
            if(type == 'setExperimentAccept'){
              this.activeData.experimentStatus = 1;
            }else if(type == 'setExperimentFinish'){
              this.activeData.experimentStatus = 3;
            }else if(type == 'setExperimentRefuse'){
              this.activeData.experimentStatus = 4;
            }

            this.initData(this.activeId);

            // this.showDetail({
            //   id: this.activeId
            // })
          }else{
            this.$message({
              showClose: true,
              message: res.msg,
              type: "error",
            });
          }
        });
      }).catch((rej) => {
        console.log(rej)
        this.$message({
          type: "info",
          message: "已取消",
        });
      });
    },
    handleClick(tab,event){
      console.log(tab,event,this.activeName)
      
    },
    goDetail(){
      this.$router.push({
        path: '/qualityControl/panelList',
        query:{
          materialType : '7'
        }
      })
    },
    
    goTaskDetail(row){
      this.$router.push({
        path: '/qualityControl/panelListDetail',
        query: {
          taskId: row.id,
          experimentId: this.activeId,
        }
      })
    },
    
    add(a, b) {
    	    var c, d, e;
          var that = this;
    	    try {
    	        c = a.toString().split(".")[1].length;
    	    } catch (f) {
    	        c = 0;
    	    }
    	    try {
    	        d = b.toString().split(".")[1].length;
    	    } catch (f) {
    	        d = 0;
    	    }
    	    return e = Math.pow(10, Math.max(c, d)), (that.mul(a, e) + that.mul(b, e)) / e;
    },
    sub(a, b) {
    	    var c, d, e;
          var that = this;
    	    try {
    	        c = a.toString().split(".")[1].length;
    	    } catch (f) {
    	        c = 0;
    	    }
    	    try {
    	        d = b.toString().split(".")[1].length;
    	    } catch (f) {
    	        d = 0;
    	    }
    	    return e = Math.pow(10, Math.max(c, d)), (that.mul(a, e) - that.mul(b, e)) / e;
    },
    mul(a, b) {
        var c = 0,
            d = a.toString(),
            e = b.toString();
        try {
            c += d.split(".")[1].length;
        } catch (f) {}
        try {
            c += e.split(".")[1].length;
        } catch (f) {}
        return Number(d.replace(".", "")) * Number(e.replace(".", "")) / Math.pow(10, c);
    },
    div(a, b) {
        var that = this;
        var c, d, e = 0,
            f = 0;
        try {
            e = a.toString().split(".")[1].length;
        } catch (g) {}
        try {
            f = b.toString().split(".")[1].length;
        } catch (g) {}
        return c = Number(a.toString().replace(".", "")), d = Number(b.toString().replace(".", "")), that.mul(c / d, Math.pow(10, f - e));
    },
    //原保存
    saveExperimentInfo222(){
      const odata = [...this.$refs.formTab.quickData, ...this.$refs.formTab.projectList];
      let oQuickForm = this.$refs.formTab.quickForm;
      
      let oSsjList= this.$refs.formTab.ssjlInfoList;
      let oSsjListIndex = -1;
      let ossksdj;
      if(oSsjList && oSsjList.length > 0 && this.$refs.formTab.projectList.length > 0){
        for(let i=0;i<oSsjList.length; i++){
          if(!oSsjList[i].syyl || oSsjList[i].syyl <= 0){
            this.$message.error(`第${i + 1}行，试验压力需是大于0的数字`);
            return false;
          }
          if(!oSsjList[i].jysj){
            this.$message.error(`第${i + 1}行，请填写加压时间`);
            return false;
          }
          
          let total = 0;
          for(let j =0; j < oSsjList[i].sfssList.length; j++){
            for (let item in oSsjList[i].sfssList[j]) {
              if(oSsjList[i].sfssList[j][item] == '是'){
                total = total + 1;
              }
            }
          }
          if(total >= 3){
            oSsjListIndex = i;
            ossksdj = oSsjList[i].syyl * 10 - 1;
            break;
          }
        }
        
        if(oSsjListIndex > -1){
          //剩余不要
          oSsjList = oSsjList.slice(0, oSsjListIndex * 1 + 1)
        }
      }
      
      
      
      
      for(let i = 0; i< odata.length; i++){
        let objJson = {}
        let objImg = ""
        if(odata[i].checkType == 1){//快检
          objJson = oQuickForm[odata[i].testProjectCode].objJson;
          if(odata[i].testProjectName === '流动性' || odata[i].testProjectName === '保水性' || odata[i].testProjectName === '粘聚性'|| item.testProjectName === '坍落度' 
                || item.testProjectName === '目测砂率' ){
            const oVal = objJson.val;
            for(let item in objJson){
              if(item != 'val'){
                objJson[item] = 0;
                if(item == oVal){
                  objJson[item] = 1;
                }
              }
              
            }
          }
          if(this.$refs.formTab.quickForm.img){
            objImg = this.$refs.formTab.quickForm.img.map(item => {
              if(item.url){
                return item.url.replace(this.filePrefix, '')
              }else{
                return item.replace(this.filePrefix, '');
              }
            })
            objImg = objImg.join(',');
          }
          
        }else if(odata[i].testProjectCode === 'CONCRETE_PARAM_KYQD'){
          objJson = this.$refs.formTab.searchForm6;
          if(this.$refs.formTab.searchForm6.img){
            objImg = this.$refs.formTab.searchForm6.img.map(item => {
              if(item.url){
                return item.url.replace(this.filePrefix, '')
              }else{
                return item.replace(this.filePrefix, '');
              }
            })
            objImg = objImg.join(',');
          }
        }else if(odata[i].testProjectCode === 'CONCRETE_PARAM_KSDJ'){
          objJson = this.$refs.formTab.searchForm7;
          if(ossksdj){
            objJson.ssksdj = 'P' + parseInt(ossksdj);
          }else{
            objJson.ssksdj = '';
          }
          
          //objJson.ssjlInfoList = this.$refs.formTab.ssjlInfoList
          if(this.$refs.formTab.searchForm7.img){
            objImg = this.$refs.formTab.searchForm7.img.map(item => {
              if(item.url){
                return item.url.replace(this.filePrefix, '');
              }else{
                return item.replace(this.filePrefix, '');
              }
            })
            objImg = objImg.join(',');
          }
        }
        this.saveTabData(odata[i],objJson,objImg)
      }
      //批检请求
      if(this.$refs.formTab.projectList.length > 0){
        // let oSsjlInfoOriginal= JSON.parse(JSON.stringify(this.$refs.formTab.ssjlInfoOriginal));
        // let oSsjlInfoList= this.$refs.formTab.ssjlInfoList;
        // //设置抗渗的提交数据
        // for(let i = 0; i<oSsjlInfoOriginal.length; i++){
        //   oSsjlInfoOriginal[i].sampleTestProjectJson = [];
        //   for(let j = 0; j< oSsjlInfoList.length; j++){
        //     oSsjlInfoOriginal[i].sampleTestProjectJson.push({
        //       jysj: oSsjlInfoList[j].jysj,
        //       sfss: oSsjlInfoList[j]['sfss' + oSsjlInfoOriginal[i].smokeNo],
        //       syyl: oSsjlInfoList[j].syyl,
        //     })
        //   }
        // }
        // console.log(oSsjlInfoOriginal)
        // const newDataList = [...this.$refs.formTab.qdInfoList7d, ...this.$refs.formTab.qdInfoList28d, ...oSsjlInfoOriginal];
        
        this.$api.setExperimentDetail3({
          "experimentId": this.activeId,
          "ksdjSmokeList": oSsjList
        }).then(res => {
          if(res.succ){
            //this.$message.success("保存成功")
          }else{
            this.$message.error(res.msg || '保存失败')
          }
        })
        const newDataList = [...this.$refs.formTab.qdInfoList7d, ...this.$refs.formTab.qdInfoList28d];
        this.$api.setExperimentDetail2({
          smokeList: newDataList
        }).then(res => {
          if(res.succ){
            //this.$message.success("保存成功")
          }else{
            this.$message.error(res.msg || '保存失败')
          }
        })
      }
      
      
      //保存实现信息和结论
      let oTestInfoForm = JSON.parse(JSON.stringify(this.testInfoForm))
      if(oTestInfoForm.moldingTime && oTestInfoForm.moldingTime.length === 10){
        oTestInfoForm.moldingTime = oTestInfoForm.moldingTime + ' 00:00:01'
      }
      
      this.$api.setExperiment(oTestInfoForm).then(res =>{
        if(res.succ){
          this.$message.success("保存成功")
        }
      })
    },
  },
};
</script>

<style scoped lang="scss">
  ::v-deep .el-button{
    padding-left: 13px;
    padding-right: 13px;
  }
  ::v-deep .el-upload-list__item{
    transition: none !important; 
    .el-upload-list__item-status-label{
      display: none;
    }
  }
  .el-form-item{
    margin-bottom: 8px;
  }
  ::v-deep .el-form--inline{
    .el-form-item{
      margin-right: 24px;
      margin-bottom: 0;
      &:last-child{
        margin: 0;
      }
    }
  }
  .el-tabs{
    margin-top: 10px;
  }
  .search-box{
    padding: 16px;
    width: 100%;
    background-color: #fff;
  }
  
  .content{
    width: 100%;
    height: 100%;
    padding: 14px 0 16px;
    .con-lef,.con-right{
      width: 360px;
      background: #D2D8DB;
      border-radius: 16px;
      padding: 8px 8px 0;
      margin: 0 16px;
      height: calc(100vh - 180px);
      overflow-y: scroll;
      scrollbar-width: none; /* firefox */
      -ms-overflow-style: none; /* IE 10+ */
      position: relative;
    }
    .con-center{
      background: #D2D8DB;
      border-radius: 16px;
      padding: 8px 8px 0;
      overflow-y: scroll;
      height: calc(100vh - 180px);
      scrollbar-width: none; /* firefox */
      -ms-overflow-style: none; /* IE 10+ */
    }
  }
  ::-webkit-scrollbar {
    display: none; /* Chrome Safari */
  }
  
  
  .g-card{
    width: 100%;
    background: #FFFFFF;
    border-radius: 8px;
    padding: 8px;
    margin-bottom: 8px;
    cursor: pointer;
    &.active{
      background: #DDEFEA;
    }
    p{
      color: $color-txt;
      line-height: 20px;
      letter-spacing: 1px;
      padding-bottom: 4px;
      &:last-child{
        padding: 0;
      }
    }
    .gc-main{
      font-size: 14px;
      font-weight: 600;
      color: #1F2329;
      line-height: 20px;
      letter-spacing: 1px;
      span{
        line-height: 20px;
        height: 20px;
        background: #FFE9D1;
        border-radius: 10px;
        font-size: 12px;
        font-weight: 600;
        color: #FF7B2F;
        background: #FFE9D1;
        padding: 0 7px;
        &.succ{
          color: $color-success;
          background: #DDEFEA;
        }
      }
    }
  }
  
  
  
  .cc-box{
    width: 100%;
    background: #FFFFFF;
    border-radius: 8px;
    padding: 16px 16px 12px;
    margin-bottom: 8px;
    &:last-child{
      margin-bottom: 0;
    }
    h4{
      font-size: 16px;
      font-weight: 600;
      color: #1F2329;
      line-height: 22px;
      letter-spacing: 1px;
      padding-bottom: 24px;
    }
    .cc-info{
      margin: 0 16px 16px;
      padding-bottom: 12px;
      border-bottom: 1px solid #E8E8E8;
      &:last-child{
        margin-bottom: 0;
        border: none;
        padding: 0;
      }
      p{
        color: #6A727D;
        line-height: 20px;
        letter-spacing: 1px;
        padding-bottom: 4px;
        span{
          margin-right: 60px;
        }
      }
      .cci-main{
        font-weight: 600;
        line-height: 20px;
        letter-spacing: 1px;
        color: #1F2329;
      }
    }
  }
  
  .pb16{
    padding-bottom: 16px !important;
  }
  
  
  .center-footer{
    width: calc(100% + 16px);
    height: 72px;
    background: #FFFFFF;
    border-radius: 0px 0px 16px 16px;
    margin-left: -8px;
    display: flex;
    justify-content: flex-end;
    padding: 16px 24px;
    .el-button{
      width: 98px;
      margin-left: 32px;
    }
  }
  
  .img-box{
    width: 140px;
    margin-right: 8px;
    float: left;
    img{
      display: block;
      background: #E0E8EB;
      border-radius: 8px;
      width: 140px;
      height: 140px;
    }
    span{
      font-size: 14px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #1F2329;
      line-height: 20px;
      letter-spacing: 1px;
      display: block;
      padding: 8px 4px;
    }
  }
</style>