<template>
  <div>
    <panelComponent>
      <!-- <template #printBtn>
        <el-button type="primary" @click="printBtnClick">打印</el-button>
      </template> -->
    </panelComponent>
  </div>
</template>

<script>
import panelComponent from './components/panelComponent'
export default {
  components: {
    panelComponent
  },

  methods: {
    // printBtnClick() {
    //   let routeData = this.$router.resolve({
    //     path: "/printRawContent",
    //     query: {}
    //   });
    //   window.open(routeData.href, '_blank');
    // }
  }
};
</script>

<style scoped lang="scss">

</style>