<!--
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-06-02 22:53:58
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-04-24 23:08:26
 * @FilePath: /quality_center_web/src/pages/qualityControl/taskList.vue
 * @Description: 任务单列表
-->
<template>
    <div class="content-box">
        <el-row class="flex-box flex-column content">
            <p class="content-title">任务信息</p>
            <el-row style="margin-top: 24px;">
                <el-col :span="7">
                    <p style="margin-top: 8px;">
                        <span style="font-size: 14px;color: #6A727D;">工程名称：</span>
                        <span style="font-size: 14px;color: #1F2329;">{{ rwdextraInfo?.fgcmc || '--' }}</span>
                    </p>
                    <p style="margin-top: 8px;">
                        <span style="font-size: 14px;color: #6A727D;">合同编号：</span>
                        <span style="font-size: 14px;color: #1F2329;">{{ rwdextraInfo?.fhtbh || '--' }}</span>
                    </p>
                    <p style="margin-top: 8px;">
                        <span style="font-size: 14px;color: #6A727D;">计划生产时间：</span>
                        <span style="font-size: 14px;color: #1F2329;">{{ detailInfo?.plantime || '--' }}</span>
                    </p>
                    <p style="margin-top: 8px;">
                        <span style="font-size: 14px;color: #6A727D;">抗渗等级：</span>
                        <span style="font-size: 14px;color: #1F2329;">{{ ksdj || '--' }}</span>
                    </p>
                    <p style="margin-top: 8px;">
                        <span style="font-size: 14px;color: #6A727D;">联系人：</span>
                        <span style="font-size: 14px;color: #1F2329;">{{ detailInfo?.projectLinkMan || '--' }}</span>
                    </p>
                    <p style="margin-top: 8px;">
                        <span style="font-size: 14px;color: #6A727D;">交货地点：</span>
                        <span style="font-size: 14px;color: #1F2329;">{{ rwdextraInfo?.fgcdz || "--" }}</span>
                    </p>
                </el-col>
                <el-col :span="7">
                    <p style="margin-top: 8px;">
                        <span style="font-size: 14px;color: #6A727D;">施工单位：</span>
                        <span style="font-size: 14px;color: #1F2329;">{{ detailInfo?.buildNameNew || '--' }}</span>
                    </p>
                    <p style="margin-top: 8px;">
                        <span style="font-size: 14px;color: #6A727D;">坍落度：</span>
                        <span style="font-size: 14px;color: #1F2329;">{{ rwdextraInfo?.ftld || '--' }}</span>
                    </p>
                    <p style="margin-top: 8px;">
                        <span style="font-size: 14px;color: #6A727D;">计划方量：</span>
                        <span style="font-size: 14px;color: #1F2329;">{{ rwdextraInfo?.fjhsl || '--' }}</span>
                    </p>
                    <p style="margin-top: 8px;">
                        <span style="font-size: 14px;color: #6A727D;">施工方式：</span>
                        <span style="font-size: 14px;color: #1F2329;">{{ rwdextraInfo?.fjzfs || '--' }}</span>
                    </p>
                    <p style="margin-top: 8px;">
                        <span style="font-size: 14px;color: #6A727D;">联系电话：</span>
                        <span style="font-size: 14px;color: #1F2329;">{{ detailInfo?.projectLinkTel || '--' }}</span>
                    </p>
                    <p style="margin-top: 8px;">
                        <span style="font-size: 14px;color: #6A727D;">备注：</span>
                        <span style="font-size: 14px;color: #1F2329;">{{ remark || '--' }}</span>
                    </p>
                </el-col>
                <el-col :span="7">
                    <p style="margin-top: 8px;">
                        <span style="font-size: 14px;color: #6A727D;">任务编号：</span>
                        <span style="font-size: 14px;color: #1F2329;">{{ rwdextraInfo?.frwno || '--' }}</span>
                    </p>
                    <p style="margin-top: 8px;">
                        <span style="font-size: 14px;color: #6A727D;">砼品种：</span>
                        <span style="font-size: 14px;color: #1F2329;">{{ rwdextraInfo?.ftpz || '--' }}</span>
                    </p>
                    <p style="margin-top: 8px;">
                        <span style="font-size: 14px;color: #6A727D;">实际生产方量：</span>
                        <span style="font-size: 14px;color: #1F2329;">{{ detailInfo?.fhquantity || '--' }}</span>
                    </p>
                    <p style="margin-top: 8px;">
                        <span style="font-size: 14px;color: #6A727D;">运距：</span>
                        <span style="font-size: 14px;color: #1F2329;">{{ rwdextraInfo?.fgls || '--' }}km</span>
                    </p>
                    <p style="margin-top: 8px;">
                        <span style="font-size: 14px;color: #6A727D;">浇筑部位：</span>
                        <span style="font-size: 14px;color: #1F2329;">{{ rwdextraInfo?.fjzbw || '--' }}</span>
                    </p>
                </el-col>
            </el-row>
        </el-row>
        <!-- <el-row class="flex-box flex-column content" style="margin-top: 16px;">
            <p class="content-title">施工配合比</p>
            <el-row style="margin-top: 24px;">
                <el-table
                    :data="mixTableData"  
                    :height="337"              
                    :key="1"
                >
                    <template v-for="(item, index) in mixTableColumn">
                        <el-table-column
                            :prop="item.prop" 
                            :label="item.label" 
                            :fixed="item.fixed" 
                            :width="item.width || ''"
                            :formatter="item.formatter"
                            align="center" 
                        />
                    </template>
                    
                    
                    <el-table-column width="120" label="操作" align="center" key="handle" :resizable="false">
                        <template slot-scope="scope">
                            <el-button type="text" size="small">详情</el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <div style="height: 10px;" />
                <Pagination
                    :total="mixTotal" 
                    :pageNum="mixPageObj.pageNum" 
                    :pageSize="mixPageObj.pageSize" 
                    @getData="mixHandleFilter" 
                />
            </el-row>
        </el-row> -->
        <el-row class="flex-box flex-column content" style="margin-top: 16px;">
            <p class="content-title">调整记录</p>
            <el-row style="margin-top: 24px;">
                <el-table
                    :data="adjustTableData"  
                    :height="337"              
                    :key="1"
                    style="overflow:auto;"
                >
                    <template v-for="(item, index) in adjustTableColumn">
                        <el-table-column
                            v-if="item.prop == 'xgnrCust'"
                            :prop="item.prop" 
                            :label="item.label" 
                            :fixed="item.fixed" 
                            :width="item.width || ''"
                            :formatter="item.formatter"
                            align="center" 
                        >
                            <template slot-scope="scope">
                                <span style="white-space: pre;">{{ scope.row.xgnrCust }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column
                            v-else
                            :prop="item.prop" 
                            :label="item.label" 
                            :fixed="item.fixed" 
                            :width="item.width || ''"
                            :formatter="item.formatter"
                            align="center" 
                        />
                    </template>
                </el-table>
                <div style="height: 10px;" />
                <!-- <Pagination
                    :total="adjustTotal" 
                    :pageNum="adjustPageObj.pageNum" 
                    :pageSize="adjustPageObj.pageSize" 
                    @getData="adjustHandleFilter" 
                /> -->
            </el-row>
        </el-row>
        <el-row class="flex-box flex-row content" style="margin-top: 16px;">
            <el-col :span="12">
                <p class="content-title">修改记录</p>
                <el-row style="margin-top: 24px;">
                    <el-table
                        :data="erpRwdUpdateLogs"  
                        :height="337"              
                        :key="1"
                        style="overflow:auto;"
                    >
                        <el-table-column
                            prop="createrName" 
                            width="100"
                            label="修改人" 
                            align="center" 
                        >
                        </el-table-column>
                        <el-table-column
                            prop="createTime" 
                            label="修改时间" 
                            align="center" 
                        >
                        </el-table-column>
                        <!-- <el-table-column
                            prop="frwdh" 
                            label="修改内容" 
                            align="center" 
                        >
                            <template slot-scope="scope">
                                
                            </template>
                        </el-table-column> -->
                        <el-table-column width="200" label="修改内容" align="center" key="handle" :resizable="false">
                            <template slot-scope="scope">
                                <!-- <el-button type="text" size="small" @click="previewEditInfo(scope.row)">预览</el-button> -->
                                <el-button type="text" size="small" @click="printEditInfo(scope.row)">打印</el-button>
                                <!-- <el-button style="color: #ff0000;" type="text" size="small" @click="deletedEditInfo(scope.row)">删除</el-button> -->
                            </template>
                        </el-table-column>
                    </el-table>
                    <div style="height: 10px;" />
                </el-row>
            </el-col>
            <div style="width: 20px;"></div>
            <el-col :span="12">
                <p class="content-title">打印记录</p>
                <el-row style="margin-top: 24px;">
                    <el-table
                        :data="printTableData"  
                        :height="337"              
                        :key="1"
                        style="overflow:auto;"
                    >
                        <template v-for="(item, index) in printTableColumn">
                            <el-table-column
                                v-if="item.prop == 'printHtml'"
                                :prop="item.prop" 
                                :label="item.label" 
                                :fixed="item.fixed" 
                                :width="item.width || ''"
                                :formatter="item.formatter"
                                align="center" 
                            >
                                <template slot-scope="scope">
                                    <span>{{ printHtmlStr(scope.row) }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column
                                v-else
                                :prop="item.prop" 
                                :label="item.label" 
                                :fixed="item.fixed" 
                                :width="item.width || ''"
                                :formatter="item.formatter"
                                align="center" 
                            />
                        </template>
                        <el-table-column width="100" label="操作" align="center" key="handle" :resizable="false">
                            <template slot-scope="scope">
                                <el-button type="text" size="small" @click="printAgain(scope.row)">再次打印</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                    <div style="height: 10px;" />
                </el-row>
            </el-col>
        </el-row>
        <el-row class="flex-box flex-column content" style="margin-top: 16px;">
            <p class="content-title">生产记录</p>
            <el-row style="margin-top: 24px;">
                <el-table
                    :data="prodTableData"  
                    :height="337"              
                    :key="1"
                    style="overflow:auto;"
                >
                    <template v-for="(item, index) in prodTableColumn">
                        <el-table-column
                            :prop="item.prop" 
                            :label="item.label" 
                            :fixed="item.fixed" 
                            :width="item.width || ''"
                            :formatter="item.formatter"
                            align="center" 
                        />
                    </template>

                    <!-- <el-table-column width="120" label="操作" align="center" key="handle" :resizable="false">
                        <template slot-scope="scope">
                            <el-button type="text" size="small">详情</el-button>
                        </template>
                    </el-table-column> -->
                </el-table>
                <div style="height: 10px;" />
                <Pagination
                    :total="prodTotal" 
                    :pageNum="prodPageObj.pageNum" 
                    :pageSize="prodPageObj.pageSize" 
                    @getData="initProdData" 
                />
            </el-row>
        </el-row>

        <el-dialog
            title="修改内容"
            :visible.sync="editVisible"
            width="40%"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
        >
            <div style="height: 300px; box-sizing: border-box; padding-left: 30px; padding-right: 30px;">
                <el-col :span="11" class="flex-col" style="align-items: flex-start;">
                    <div style="font-size: 20px; font-weight: 600;">修改前</div>
                    <div style="margin-top: 20px; font-size: 16px;">工程名称：{{ editInfo?.rwdextraExtendBeforeJson?.projectName || '--' }}</div>
                    <div style="margin-top: 20px; font-size: 16px;">施工单位：{{ editInfo?.rwdextraExtendBeforeJson?.buildName || '--' }}</div>
                    <div style="margin-top: 20px; font-size: 16px;">合同名称：{{ editInfo?.rwdextraExtendBeforeJson?.contractName || '--' }}</div>
                </el-col>
                <el-col :span="11" class="flex-col" style="align-items: flex-start;">
                    <div style="font-size: 20px; font-weight: 600;">修改后</div>
                    <div style="margin-top: 20px; font-size: 16px;">工程名称：{{ editInfo?.rwdextraExtendAfterJson?.projectName || '--' }}</div>
                    <div style="margin-top: 20px; font-size: 16px;">施工单位：{{ editInfo?.rwdextraExtendAfterJson?.buildName || '--' }}</div>
                    <div style="margin-top: 20px; font-size: 16px;">合同名称：{{ editInfo?.rwdextraExtendAfterJson?.contractName || '--' }}</div>
                </el-col>
            </div>
        </el-dialog>

        <PrintDrawer ref="printDrawer" :show="printDrawer" @close="printDrawerClose"  @sure="getPrintType" @change="printTypeChange" />
    </div>
</template>  

<script>
import Pagination from "@/components/Pagination/index.vue";
import PrintDrawer from "./components/printDrawer.vue";
export default {
    components: {
        Pagination,
        PrintDrawer
    },
    data() {
        return {
            mixPageObj: {
                pageNum: 1, // 页数
                pageSize: 10, // 条数
            },
            mixTotal: 0,
            mixTableData: [],
            mixTableColumn: [
                {
                    label: '配合比编号',
                    prop: '配合比编号',
                },
                {
                    label: '基准配合比编号',
                    prop: '基准配合比编号',
                },
                {
                    label: '砼品种',
                    prop: '砼品种',
                },
                {
                    label: '浇筑部位',
                    prop: '浇筑部位',
                },
                {
                    label: '坍落度',
                    prop: '坍落度',
                },
                {
                    label: '泵送方式',
                    prop: '泵送方式',
                },
                {
                    label: '水胶比',
                    prop: '水胶比',
                },
                {
                    label: '用水量(kg)',
                    prop: '用水量(kg)',
                },
                {
                    label: '砂率(%)',
                    prop: '砂率(%)',
                },
            ],
            
            adjustPageObj: {
                pageNum: 1, // 页数
                pageSize: 10, // 条数
            },
            // adjustTotal: 0,
            adjustTableData: [],
            // adjustTableColumn: [
            //     {
            //         label: '调整编号',
            //         prop: '调整编号',
            //         width: '100'
            //     },
            //     {
            //         label: '配合比编号',
            //         prop: '配合比编号',
            //         width: '100'
            //     },
            //     {
            //         label: '通知单编号',
            //         prop: '通知单编号',
            //         width: '100'
            //     },
            //     {
            //         label: '通知单时间',
            //         prop: '通知单时间',
            //         width: '100'
            //     },
            //     {
            //         label: '拌台',
            //         prop: '拌台',
            //         width: '100'
            //     },
            //     {
            //         label: '用水量(kg)',
            //         prop: '用水量(kg)',
            //         width: '100'
            //     },
            //     {
            //         label: '水泥(kg)',
            //         prop: '水泥(kg)',
            //         width: '100'
            //     },
            //     {
            //         label: '细骨料1(kg)',
            //         prop: '细骨料1(kg)',
            //         width: '100'
            //     },
            //     {
            //         label: '细骨料2(kg)',
            //         prop: '细骨料2(kg)',
            //         width: '100'
            //     },
            //     {
            //         label: '粗骨料1(kg)',
            //         prop: '粗骨料1(kg)',
            //         width: '100'
            //     },
            //     {
            //         label: '粗骨料2(kg)',
            //         prop: '粗骨料2(kg)',
            //         width: '100'
            //     },
            //     {
            //         label: '掺合料1(kg)',
            //         prop: '掺合料1(kg)',
            //         width: '100'
            //     },
            //     {
            //         label: '掺合料2(kg)',
            //         prop: '掺合料2(kg)',
            //         width: '100'
            //     },
            //     {
            //         label: '外加计1(kg)',
            //         prop: '外加计1(kg)',
            //         width: '100'
            //     },
            //     {
            //         label: '外加计2(kg)',
            //         prop: '外加计2(kg)',
            //         width: '100'
            //     },
            //     {
            //         label: '调整人',
            //         prop: '调整人',
            //         width: '100'
            //     },
            //     {
            //         label: '调整时间',
            //         prop: '调整时间',
            //         width: '100'
            //     },
            //     {
            //         label: '操作工',
            //         prop: '操作工',
            //         width: '100'
            //     },
            //     {
            //         label: '操作时间',
            //         prop: '操作时间',
            //         width: '100'
            //     },
            //     {
            //         label: '批准人',
            //         prop: '批准人',
            //         width: '100'
            //     },
            //     {
            //         label: '审核人',
            //         prop: '审核人',
            //         width: '100'
            //     },
            //     {
            //         label: '编制人',
            //         prop: '编制人',
            //         width: '100'
            //     },
            //     {
            //         label: '审核时间',
            //         prop: '审核时间',
            //         width: '100'
            //     },
            //     {
            //         label: '调整原因',
            //         prop: '调整原因',
            //         width: '140'
            //     },
            // ],
            adjustTableColumn: [
                {
                    label: '日期',
                    prop: 'createtime',
                    width: '160'
                },
                {
                    label: '拌台',
                    prop: 'mixtable',
                    width: '160'
                },
                {
                    label: '配比类型',
                    prop: 'phbtype',
                    width: '160',
                    formatter: function (row) {
                        if (row.phbtype == 0) {
                            return "混凝土";
                        }else if (row.phbtype == 1) {
                            return "砂浆";
                        }
                        return "--"
                    }
                },
                {
                    label: '修改内容',
                    prop: 'xgnrCust', // 自定义的修改内容
                },
            ],
            
            prodPageObj: {
                pageNum: 1, // 页数
                pageSize: 10, // 条数
            },
            prodTotal: 0,
            prodTableData: [],
            prodTableColumn: [
                {
                    label: '生产记录ID',
                    prop: 'jlbids',
                },
                {
                    label: '本车方量(方)',
                    prop: 'fhquantity',
                },
                {
                    label: '拌台',
                    prop: 'mixtable',
                },
                {
                    label: '运输车号',
                    prop: 'carnumber',
                },
                {
                    label: '驾驶员姓名',
                    prop: 'drivername',
                },
                {
                    label: '运输车牌号',
                    prop: 'carlicenseno',
                },
                {
                    label: '完成方量(方)',
                    prop: 'finishquantity',
                },
                {
                    label: '发车时间',
                    prop: 'starttime',
                },
                {
                    label: '到达时间',
                    prop: 'arrivetime',
                },
                {
                    label: '累计车数',
                    prop: 'finishcarnumber',
                },
                // {
                //     label: '签收人',
                //     prop: '签收人',
                // },
            ],

            printTableColumn: [
                {
                    label: '操作人',
                    prop: 'printUserName',
                },
                {
                    label: '打印时间',
                    prop: 'printTime',
                },
                {
                    label: '打印范围',
                    prop: 'printHtml',
                },
            ],
            printTableData: [],

            detailInfo: {},
            rwdextraInfo: {},

            printHtmlList: [
                {
                    id: "b1",
                    label: '水泥试验报告'
                },
                {
                    id: "b2",
                    label: '矿渣粉试验报告'
                },
                {
                    id: "b3",
                    label: '粉煤灰试验报告'
                },
                {
                    id: "b4",
                    label: '细骨料试验报告'
                },
                {
                    id: "b5",
                    label: '粗骨料试验报告'
                },
                {
                    id: "b6",
                    label: '外加剂检测报告'
                },
                {
                    id: "a3",
                    label: '混凝土抗压报告'
                },
                {
                    id: "a4",
                    label: '混凝土抗渗报告'
                },
                // {
                //     id: "a5",
                //     label: '混凝土抗折报告'
                // },
                // {
                //     id: "a7",
                //     label: '混凝土综合性能报告'
                // },
                {
                    id: "a11",
                    label: '抗氯离子报告'
                },
                {
                    id: "a12",
                    label: '凝结时间报告'
                },
                {
                    id: "a1",
                    label: '预拌混凝土出厂质量证明书'
                },
                {
                    id: "a1N",
                    label: '预拌混凝土出厂质量证明书(新)'
                },
                {
                    id: "a2",
                    label: '开盘鉴定表'
                },
                {
                    id: "a8",
                    label: '配合比设计报告'
                },
                {
                    id: "a9",
                    label: '配合比调整通知'
                },
            ],

            erpRwdUpdateLogs: [],
            editInfo: {},
            editVisible: false,
            printDrawer: false,
            updateLogId: '',
            frwdh: '',
        }
    },

    computed: {
      // ksdj 字段
      ksdj() {
        let str = this.rwdextraInfo.ftbj || "";
        let list = str.split('|');
        if (list.length > 3) {
            return list[3];
        }
        return "";
      },  

      // 备注
      remark() {
        let str = this.rwdextraInfo.ftbj || "";
        let list = str.split('|');
        if (list.length > 0) {
            return list[0];
        }
        return "";
      },
    },

    activated() {
        this.getTglTrwdById();
    },

    methods: {
        // 查询任务详情
        getTglTrwdById() {
            this.$api.getErpRwdextraByFrwdh(`frwdh=${this.$route.query.frwdh}`, this).then(res => {
                if(res.succ){
                    this.detailInfo = res.data;
                    this.rwdextraInfo = res.data.rwdextraInfo;
                    // 生产记录
                    this.initProdData();
                    this.initAdjustData();

                    // 修改记录
                    this.queryErpRwdUpdateLogResp();
                    // 打印记录
                    this.queryPrintTableData();
                }else{
                    this.$message.error(res.msg || '查询失败')
                }
            });
        },
        
        mixHandleFilter() {
            this.initMixData();
        },
        // 获取施工配合比记录
        initMixData() {
            // this.$api.queryMixProportionPage({
            //     params: {},
            //     pageSize: this.mixPageObj.pageSize,
            //     pageNum: this.mixPageObj.pageNum,
            // }, this).then(res => {
            //     if(res.succ){
            //         this.mixTableData = res.data.list;
            //         this.mixTotal = res.data.total;
            //     }else{
            //         this.$message.error(res.msg || '查询失败')
            //     }
            // });
        },

        adjustHandleFilter() {
            this.initAdjustData();
        },
        // 获取调整记录
        initAdjustData() {
            this.$api.queryMixProportionHistory(`frwd=${this.detailInfo.frwdh}`, this).then(res => {
                if(res.succ){
                    this.adjustTableData = res.data.list;
                    this.adjustTableData.forEach(item => {
                        let afterPhbList = item.afterPhbList;
                        let beforePhbList = item.beforePhbList;
                        let xgnrCust = "";
                        if (afterPhbList.length > 0) {
                            afterPhbList.forEach(aft => {
                                let befNum = 0;
                                for (let bef of beforePhbList) {
                                    if (aft.name == bef.name) {
                                        befNum = bef.yl;
                                        break;
                                    }
                                }
                                xgnrCust = xgnrCust + `[${aft.name}${aft.pzgg ? '(' + aft.pzgg + ')' : ''}]:${befNum}KG->${aft.yl || '--'}KG     `
                            });
                        }else{
                            beforePhbList.forEach(bef => {
                                let aftNum = 0;
                                xgnrCust = xgnrCust + `[${bef.name}${bef.pzgg ? '(' + bef.pzgg + ')' : ''}]:${bef.yl || '--'}KG->${aftNum}KG     `
                            });
                        }
                        
                        item.xgnrCust = xgnrCust;
                    });
                    // this.adjustTotal = res.data.total;
                }else{
                    this.$message.error(res.msg || '查询失败')
                }
            });
        },

        // 获取生产记录
        initProdData(opageNum, opageSize) {

            if (opageNum) this.prodPageObj.pageNum = opageNum;
            if (opageSize) this.prodPageObj.pageSize = opageSize;
            
            this.$api.getErpItemorderPageList({
                params: {
                    frwdh: this.detailInfo.frwdh
                },
                pageSize: this.prodPageObj.pageSize,
                pageNum: this.prodPageObj.pageNum,
            }, this).then(res => {
                if(res.succ){
                    this.prodTableData = res.data.list;
                    this.prodTotal = res.data.total;
                }else{
                    this.$message.error(res.msg || '查询失败')
                }
            });
        },

        // 查询修改记录
        queryErpRwdUpdateLogResp() {
            this.$api.queryErpRwdUpdateLog({
                frwdh: this.$route.query.frwdh
            }).then(res => {
                if (res.succ) {
                    this.erpRwdUpdateLogs = res.data.list;
                } else {
                    this.$message.error(res.msg || '查询失败')
                }
            })
        },

        queryPrintTableData() {
            this.$api.queryErpRwdPrintRecord({
                frwdh: this.$route.query.frwdh
            }).then(res => {
                if (res.succ) {
                    this.printTableData = res.data.list;
                } else {
                    this.$message.error(res.msg || '查询失败')
                }
            })
        },

        printHtmlStr(row) {
            let text = [];
            if (row.printHtml != null && row.printHtml != "") {
                let list = row.printHtml.split(",");
                for (const l of this.printHtmlList) {
                    for (const i of list) {
                        if (l.id == i) {
                            text.push(l.label);
                        }
                    }
                }                
            }
            return text.join("、");
        },

        printAgain(row) { 
            this.updateLogId = row.id;
            this.frwdh = row.frwdh;
            let routeData = this.$router.resolve({
                path: "/printTaskContent",
                query: {
                    taskIds: [this.updateLogId],
                    printType: row.printHtml,
                    frwdh: this.frwdh,
                    formType: 'printAgain'
                }
            });
            window.open(routeData.href, '_blank');
            
        },

        previewEditInfo(row) {
            this.editInfo = row;
            this.editVisible = true;
        },

        printEditInfo(row) {
            this.printDrawer = true;
            this.updateLogId = row.id;
            this.frwdh = row.frwdh;
        },
        deletedEditInfo(row) {
          // 模态框提醒
          this.$confirm('是否删除该条修改记录?', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
          }).then(() => {
            this.$api.deleteErpRwdUpdateLog({
                id: row.id
            }).then(res => {
                if (res.succ) {
                  this.$message.success("操作成功");
                  this.queryErpRwdUpdateLogResp();
                } else {
                  this.$message.error(res.msg || '操作失败')
                }
            });
          });
        },
        printDrawerClose() {
            this.printDrawer = false;
        },
        getPrintType(val) {
            if (val.length == 0) {
            this.$message({
                showClose: true,
                message: "请选择打印数据表",
                type: "warning",
            });
            return;
            }
            this.printBtnClick(val.join(","))
        },
        printTypeChange() {

        },
        printBtnClick(types) {
            let routeData = this.$router.resolve({
                path: "/printTaskContent",
                query: {
                    taskIds: [this.updateLogId],
                    printType: types,
                    formType: 'logs',
                    frwdh: this.frwdh
                }
            });
            window.open(routeData.href, '_blank');
        },
    },
}
</script>

<style lang="scss" scoped>
 ::v-deep .el-button{
    padding-left: 13px;
    padding-right: 13px;
  }
  .el-form-item{
    margin-bottom: 8px;
  }
  ::v-deep .el-form--inline{
    .el-form-item{
      margin-right: 24px;
      margin-bottom: 0;
      &:last-child{
        margin: 0;
      }
    }
  }
  ::v-deep .el-table{
    .expanded,.expanded:hover{
      background-color: #FFFBD9;
      
    }
    .expanded + tr{
      background-color: #FFFBD9;
      td{
        background-color: #FFFBD9;
      }
    }
    
    .table-child-box{
      margin: 16px;
      padding: 16px;
      background: #FFFFFF;
    }
  }
  
  .content-box{
    padding: 16px;
    overflow: auto;
  }
  .content{
    width: 100%;
    padding: 16px;
    background: #FFFFFF;
    border-radius: 16px;

    .content-title {
        font-weight: 600;
        font-size: 16px;
        color: #1F2329;
    }
  }
  
  .search-box{
    padding-bottom: 16px;
    line-height: 40px;
  }
</style>