<template>
  <div class="content-box">
    <div class="content">
      <div class="g-card">
        <p class="gc-main">小票编号：SNWT000000001 <span class="nom">已签收</span></p>
        <el-row>
          <el-col :span="7">
            <p>工程监督号：<span>阿斯顿发34234</span></p>
            <p>砼品种：<span>阿斯顿发34234</span></p>
            <p>车辆信息：<span>阿斯顿发34234</span></p>
            <p>质检员：<span>阿斯顿发34234</span></p>
          </el-col>
          <el-col :span="7">
            <p>工程名称：<span>阿斯顿发34234</span></p>
            <p>浇筑方式：<span>阿斯顿发34234</span></p>
            <p>司机信息：<span>阿斯顿发34234</span></p>
          </el-col>
          <el-col :span="7">
            <p>浇筑部位：<span>阿斯顿发34234</span></p>
            <p>是否检测：<span>阿斯顿发34234</span></p>
            <p>生产员：<span>阿斯顿发34234</span></p>
          </el-col>
        </el-row>
      </div>
      
      
      <p class="title">工单列表</p>
      <div class="flex-box" style="gap: 16;">
        <div class="ed-info">
          <p>CGRWD0000001 丨 <span>{{detailData.supervNo}}</span></p>
          <p><span>23{{detailData.projectAbbreviation}}</span></p>
          <div class="edi-box cbo">
            <div class="edi-item fl">
              <span>{{detailData.experimentCount}}</span>
              <span>累计抽检 (个)</span>
            </div>
            <div class="edi-item fl">
              <span>{{detailData.experimentExpCount}}</span>
              <span>抽检异常 (个)</span>
            </div>
            <div class="edi-item fl">
              <span>{{detailData.taskListNum}}</span>
              <span>累计订单 (个)</span>
            </div>
            <div class="edi-item fl">
              <span>{{detailData.totalVolume}}</span>
              <span>累计方量 (m³)</span>
            </div>
          </div>
        </div>
        <div class="ed-info">
          <p>CGRWD0000001 丨 <span>{{detailData.supervNo}}</span></p>
          <p><span>23{{detailData.projectAbbreviation}}</span></p>
          <div class="edi-box cbo">
            <div class="edi-item fl">
              <span>{{detailData.experimentCount}}</span>
              <span>累计抽检 (个)</span>
            </div>
            <div class="edi-item fl">
              <span>{{detailData.experimentExpCount}}</span>
              <span>抽检异常 (个)</span>
            </div>
            <div class="edi-item fl">
              <span>{{detailData.taskListNum}}</span>
              <span>累计订单 (个)</span>
            </div>
            <div class="edi-item fl">
              <span>{{detailData.totalVolume}}</span>
              <span>累计方量 (m³)</span>
            </div>
          </div>
        </div>
      </div>
      
      <el-row :gutter="90">
        <el-col :span="12">
          <p class="card-title">混凝土配比<span>容量：2400</span>
            <span>水胶比：0.4</span>
            <span>γ1取值：0.4</span>
            <span>γ2取值：0.4</span>
          </p>
          
          <div>
            <el-table
              border
              class="lengthways-table"
              :data="tableData"
              style="width: 100%">
              <af-table-column
                v-for="item in tableColumn" 
                :key="Math.random()" :prop="item.prop" 
                :fixed="item.fixed" 
                :width="item.width || ''"
                align="center" 
              />
            </el-table>
          </div>
        </el-col>
        <el-col :span="12">
          <p class="card-title">生产消耗偏差</p>
          <el-table
            :data="tableData"
            style="width: 100%">
            <af-table-column
              v-for="item in tableColumn" 
              :key="Math.random()" :prop="item.prop" 
              :label="item.label" 
              :fixed="item.fixed" 
              :width="item.width || ''"
              align="center" 
            />
          </el-table>
        </el-col>
      </el-row>
    </div>
    
  </div>
</template>

<script>
import { rdColumn } from "./config.js"
import Pagination from "@/components/Pagination/index.vue";
export default {
  components: {
    Pagination
  },
  data() {
    return {
      tableData: [{
        title: "水泥",
        ventureName: "asdf",
        shopName: "asf",
        tradeNo2: "asdf",
        goodsNo: "adf",
        goodsName: "adf",
      },{
        title: "掺合料",
        ventureName: "asdf",
        shopName: "asf",
        tradeNo2: "asdf",
        goodsNo: "adf",
        goodsName: "adf",
      }],
      tableColumn: rdColumn,
      detailData: {},
    };
  },
  
  created: function() {

  },
  methods: {
    
  },
};
</script>

<style scoped lang="scss">
  .content-box{
    padding: 16px;
    overflow-y: auto;
  }
  .content{
    width: 100%;
    padding: 24px 16px;
    background: #FFFFFF;
    border-radius: 16px;
    margin-bottom: 16px;
    .title{
      font-size: 16px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      color: #1F2329;
      line-height: 22px;
      letter-spacing: 1px;
      padding-bottom: 8px;
      border-bottom: 1px solid #E8E8E8;
      margin-bottom: 8px;
      padding-top: 45px;
    }
    
  }
  .h40{
    height: 40px;
    width: 100%;
  }
  .cc-info{
    margin: 0 0px 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #E8E8E8;
    &:last-child{
      margin-bottom: 0;
      border: none;
      padding: 0;
    }
  }
  .pb16{
    padding-bottom: 16px !important;
  }
  
  .g-card{
    width: 100%;
    padding: 0;
    margin-bottom: 0;
    & > div{
      margin-right: 62px;
    }
    p{
      color: $color-txt;
      line-height: 20px;
      letter-spacing: 1px;
      padding-bottom: 8px;
      span{
        color: #1F2329;
      }
      &:last-child{
        padding: 0;
      }
    }
    .gc-main{
      font-size: 16px;
      font-weight: 600;
      color: #1F2329;
      line-height: 22px;
      letter-spacing: 1px;
      padding-bottom: 24px;
      span{
        line-height: 20px;
        height: 20px;
        // background: #FFE9D1;
        border-radius: 10px;
        font-size: 12px;
        font-weight: 600;
        display: inline-block;
        vertical-align: middle;
        margin-top: -2px;
        color: #FF7B2F;
        margin-left: 16px;
        padding: 0 7px;
        &.nom{
          color: #0066FF;
        }
        &.succ{
          color: $color-success;
          background: #DDEFEA;
        }
        &.red{
          color: #FF2F2F;
          background: #FFE9E9;
        }
      }
    }
  }
  
  
  .card-title{
    font-size: 16px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    color: #1F2329;
    line-height: 22px;
    letter-spacing: 1px;
    padding-bottom: 16px;
    span{
      font-weight: 400;
      font-size: 14px;
      margin: 0 24px 0 17px;
    }
  }
  
  .video-box{
    width: 100%;
    margin-bottom: 24px;
    &:last-child{
      margin: 0;
    }
    .video-item{
      width: 220px;
      margin-right: 8px;
      float: left;
      .art-video{
        width: 100%;
      }
      p{
        height: 20px;
        font-size: 14px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #1F2329;
        line-height: 20px;
        letter-spacing: 1px;
        text-align: center;
        margin-top: 8px;
        margin-bottom: 16px;
      }
    }
  }
  
  
 ::v-deep .lengthways-table{
    .has-gutter{
      display: none;
    }
    .el-table__row{
      .el-table__cell{
        padding: 4px 0;
      }
      td:first-child{
        background: #E0E8EB;
      }
    }
  }
  
  
  .ed-info{
    flex: 1;
    p{
      height: 20px;
      font-weight: 600;
      font-size: 14px;
      color: #1F2021;
      line-height: 20px;
      margin-bottom: 8px;
      span{
        font-weight: 400;
        font-size: 14px;
      }
    }
    .edi-box{
      margin-top: 16px;
    }
    .edi-item{
      width: 140px;
      height: 72px;
      background: #F2F6FE;
      border-radius: 4px;
      text-align: center;
      padding: 8px;
      margin-right: 8px;
      &:last-child{
        margin-right: 0;
      }
      span{
        font-size: 20px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 600;
        color: #1F2329;
        line-height: 28px;
        display: block;
        padding: 0 0 4px;
        &:nth-child(2){
          font-size: 14px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #6A727D;
          line-height: 20px;
        }
      }
    }
  }
</style>