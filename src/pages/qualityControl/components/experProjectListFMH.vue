<template>
  <div>
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane v-if="quickData.length > 0" label="快检" name="quick"></el-tab-pane>
      <template v-if="batchData.length > 0">
        <el-tab-pane v-for="(item, index) in batchData" :key="item.testProjectCode" :label="item.testProjectName" :name="item.testProjectCode">
        </el-tab-pane>
      </template>
    </el-tabs>
    <div v-if="activeName === 'quick'" key="quick">
      <el-form ref="quickForm"  :model="quickForm" :disabled="loading2 || experimentStatus == 3">
        <el-row>
          <el-col :span="24" style="margin-bottom: 8px;">
            <el-form-item :label="item.testProjectName" v-for="item in quickData" :key="item.testProjectCode">
               <!-- v-model="item.objJson." -->
              <el-radio-group v-model="quickForm[item.testProjectCode].objJson.val" 
                v-if="item.testProjectName === '流动性' 
                || item.testProjectName === '保水性' 
                || item.testProjectName === '粘聚性'"
              >
                <el-radio label="fcc">非常差</el-radio>
                <el-radio label="yb">一般</el-radio>
                <el-radio label="hh">良好</el-radio>
              </el-radio-group>
              
              <template v-else-if="item.testProjectName === '坍落度'">
                <!-- <el-input
                  v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="quickForm[item.testProjectCode].objJson.tld1" 
                  clearable
                  placeholder="坍落度"
                  :style="{'width': 150 + 'px'}"
                >
                </el-input> -->
                <el-select
                  v-model="quickForm[item.testProjectCode].objJson.tld1" 
                  filterable 
                  :style="{'width': 150 + 'px'}"
                  placeholder="坍落度" >
                  <el-option
                    v-for="item in 13"
                    :key="item"
                    :label="70 + item * 10"
                    :value="70 + item * 10">
                  </el-option>
                </el-select>
                
                <span>&nbsp;&nbsp;±&nbsp;&nbsp;</span>
                <el-select
                  v-model="quickForm[item.testProjectCode].objJson.tld2" 
                  filterable 
                  :style="{'width': 80 + 'px'}"
                  placeholder="差值" >
                  <el-option
                    v-for="item in 2"
                    :key="item"
                    :label="10 + item * 10"
                    :value="10 + item * 10">
                  </el-option>
                </el-select>
    
                <span>&nbsp;&nbsp;&nbsp;&nbsp;方式：</span>
                <el-radio-group  v-model="quickForm[item.testProjectCode].objJson.tldfs">
                  <el-radio label="目测">目测</el-radio>
                  <el-radio label="实测">实测</el-radio>
                </el-radio-group>
              </template>
              <template v-else-if="item.testProjectName === '目测砂率'">
                <el-input
                  v-model="quickForm[item.testProjectCode].objJson.sl" 
                  clearable
                  placeholder="目测砂率"
                  style="width: 253px; margin-left: 14px;"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="图片：">
              <div class="cbo">
                <div class="img-box">
                  <el-upload
                    accept=".jpeg,.png,.jpg,.bmp,.gif"
                    :action="baseUrl + '/upload/file'"
                    list-type="picture-card"
                    :on-preview="handlePreview"
                    :file-list="quickForm.img"
                    :on-success="handlePicSuccess"
                    :on-remove="handlePicRemove">
                    <i class="el-icon-plus"></i>
                  </el-upload>
                  <!-- <span>抽样图片</span> -->
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <!-- 细度-->
    
    <div v-if="activeName === 'FLY_ASK_PARAM_FMH_XD'" key="FLY_ASK_PARAM_FMH_XD" class="custom-form">
      <el-form :disabled="loading2 || experimentStatus == 3" :inline="true">
        <div class="mt16 jcrq-box flex-box">
          <el-form-item label="检测日期：" class="flex-item">
            <el-date-picker
              type="date"
              v-model="batchForm[activeName].jcrq"
              placeholder="选择日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
          
          <el-form-item label="方孔筛规格：" class="flex-item">
            <el-input
              v-manual-update
              @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].fksgg"
              placeholder="请输入"
            >
              <template slot="append">um</template>
            </el-input>
          </el-form-item>
          <div class="flex-item"></div>
        </div>
        <div v-for="(item, index) in batchForm[activeName].xdInfo" :key="index">
          <p class="info-form-title mt16">{{index == 0 ? '第一次试验' : '第二次试验'}}</p>
          <div class="info-form-piece flex-box">
            <el-form-item label="试样质量：" class="flex-item flex-box">
              <el-input
                v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @input="val => clacXD(index)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="item.syzl" 
                placeholder="请输入"
              >
                <template slot="append">g</template>
              </el-input>
            </el-form-item>
            <el-form-item class="flex-item flex-box" label="筛余物重：">
              <el-input
                v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="item.sywz"
                @input="val => clacXD(index)"
                placeholder="请输入"
              >
                <template slot="append">g</template>
              </el-input>
            </el-form-item>
            <el-form-item label="矫正系数：" class="flex-item flex-box">
              <el-input
                v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @input="val => clacXD(index)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="item.jzxs"
                placeholder="请输入"
              >
                <template slot="append">K</template>
              </el-input>
            </el-form-item>
            <el-form-item label="细度：" class="flex-item flex-box">
              <el-input
                v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                disabled
                v-model="item.xd"
                placeholder="请输入"
              >
                <template slot="append">%</template>
              </el-input>
            </el-form-item>
            
          </div>
        </div>
        <div class="line"></div>
        <el-row>
          <el-col :span="10">
            <el-form-item class="flex-box" label="矫正后细度代表值：">
              <el-input
                v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                disabled
                v-model="batchForm[activeName].jzhxddbz" 
                :style="{'width': 200 + 'px'}"
              >
                <template slot="append">%</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="14">
            <el-form-item class="flex-box" label="单项结论：">
              <el-input
                :disabled="dxjlDisabled"
                v-model="batchForm[activeName].dxjl" 
                placeholder="请输入"
                :style="{'width': 180 + 'px'}"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row>
          <el-col :span="24">
            <el-form-item label="检测图片：">
              <div class="cbo">
                <div class="img-box" >
                  <el-upload
                    accept=".jpeg,.png,.jpg,.bmp,.gif"
                    :action="baseUrl + '/upload/file'"
                    list-type="picture-card"
                    :on-preview="handlePreview"
                    :file-list="batchForm[activeName].img"
                    :on-success="handlePicSuccess"
                    :on-remove="handlePicRemove">
                    <i class="el-icon-plus"></i>
                  </el-upload>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    
    <!-- 烧失量 -->
    <div v-if="activeName === 'FLY_ASK_PARAM_FMH_SSL'" key="FLY_ASK_PARAM_FMH_SSL" class="custom-form">
      <el-form label-width="120px" :disabled="loading2 || experimentStatus == 3" :inline="true">
        <div class="mt16 jcrq-box">
          <el-form-item label="检测日期：">
            <el-date-picker
              type="date"
              v-model="batchForm[activeName].jcrq"
              placeholder="选择日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
        </div>
        <div v-for="(item, index) in batchForm[activeName].sslInfo" :key="index">
          <p class="info-form-title mt16">{{index == 0 ? '第一次试验' : '第二次试验'}}</p>
          <div class="info-form-piece flex-box" style="padding-bottom: 0;">
            
            <el-form-item label="称量器皿质量：" class="flex-item flex-box">
              <el-input
                v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @input="val => setSSL(index)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="item.clqmzl" 
                placeholder="请输入"
              >
                <template slot="append">g</template>
              </el-input>
            </el-form-item>
            <el-form-item label="试料+称量器皿质量：" class="flex-item flex-box"  label-width="180px">
              <!-- @input="val => {item.slclqmzl = val * 1 > 10000 ? val.match(/10000|\d{1,4}/)[0] : val.match(/\d+\.?\d{0,4}/)[0]; setSSL(index)}" -->
              <el-input
                v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @input="val => setSSL(index)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="item.slclqmzl" 
                placeholder="请输入"
              >
                <template slot="append">g</template>
              </el-input>
            </el-form-item>
            
            
            <!-- 11 -->
            <el-form-item label="试料质量：" class="flex-item flex-box" label-width="130px">
              <el-input
                v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="item.slzl"
                disabled
              >
                <template slot="append">g</template>
              </el-input>
            </el-form-item>
          </div>
          <div class="info-form-piece flex-box" style="padding: 0 16px;">
            <el-form-item label="瓷坩埚质量：" class="flex-item flex-box">
              <el-input
                v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @input="val => setSSL(index)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="item.cggzl" 
                placeholder="请输入"
              >
                <template slot="append">g</template>
              </el-input>
            </el-form-item>
            <el-form-item label="灼烧后试料+瓷坩埚质量：" class="flex-item flex-box"  label-width="180px">
              <el-input
                v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @input="val => setSSL(index)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="item.zshcggzl" 
                placeholder="请输入"
              >
                <template slot="append">g</template>
              </el-input>
            </el-form-item>
            
            <!-- 11 -->
            <el-form-item class="flex-item flex-box" label="灼烧后试样质量："  label-width="130px">
              <el-input
                v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="item.zshsyzl"
                disabled
              >
                <template slot="append">g</template>
              </el-input>
            </el-form-item>
          </div>
          <div class="info-form-piece flex-box" style="padding: 0 16px 16px 16px;">
            <el-form-item label="试验结果：" class="flex-item flex-box">
              <el-input
                v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                disabled
                v-model="item.ssl"
              >
                <template slot="append">%</template>
              </el-input>
            </el-form-item>
          </div>
        </div>
        
        <div class="line"></div>
        <!-- <el-row>
          <el-col :span="12">
            <el-form-item label-width="280px" class="flex-box" label="矿渣灼烧测得的三氧化硫质量分数：">
              <el-input
                v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                v-model="batchForm[activeName].zssyhl" 
                disabled
                :style="{'width': 130 + 'px'}"
              >
                <template slot="append">%</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label-width="280px" class="flex-box" label="矿渣未经灼烧测得的三氧化硫质量分数：">
              <el-input
                v-model="batchForm[activeName].wzssyhl" 
                disabled
                placeholder="请输入"
                :style="{'width': 130 + 'px'}"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12" class="mt16">
            <el-form-item label-width="280px" class="flex-box mt8" label="矿渣灼烧过程中吸收空气中氧的质量分数：">
              <el-input
                v-model="batchForm[activeName].zskqy" 
                disabled
                placeholder="请输入"
                :style="{'width': 130 + 'px'}"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row> -->
        
        <el-row>
          <el-col :span="12">
            <el-form-item label-width="138px" class="flex-box" label="烧失量平均值：">
              <el-input
                v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                v-model="batchForm[activeName].sslpjz" 
                disabled
              >
                <template slot="append">%</template>
              </el-input>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="12">
            <el-form-item label-width="280px" class="flex-box" label="烧失量矫正值：">
              <el-input
                v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                v-model="batchForm[activeName].ssljzz" 
                disabled
                :style="{'width': 130 + 'px'}"
              >
                <template slot="append">%</template>
              </el-input>
            </el-form-item>
          </el-col> -->
          <el-col :span="12">
            <el-form-item class="flex-box" label="单项结论：">
              <el-input
                v-model="batchForm[activeName].dxjl" 
                :disabled="dxjlDisabled"
                placeholder="请输入"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row>
          <el-col :span="24">
            <el-form-item label="检测图片：" label-width="138px">
              <div class="cbo">
                <div class="img-box">
                  <el-upload
                    accept=".jpeg,.png,.jpg,.bmp,.gif"
                    :action="baseUrl + '/upload/file'"
                    list-type="picture-card"
                    :on-preview="handlePreview"
                    :file-list="batchForm[activeName].img"
                    :on-success="handlePicSuccess"
                    :on-remove="handlePicRemove">
                    <i class="el-icon-plus"></i>
                  </el-upload>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    
    <!-- 需水量比 -->
    <div v-if="activeName === 'FLY_ASK_PARAM_FMH_XSLB'" key="FLY_ASK_PARAM_FMH_XSLB"class="custom-form">
      <el-form :disabled="loading2 || experimentStatus == 3" :inline="true" label-width="180px">
        <div class="mt16 jcrq-box">
          <el-form-item label="检测日期：">
            <el-date-picker
              type="date"
              v-model="batchForm[activeName].jcrq"
              placeholder="选择日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
        </div>
        <div class="mt16 flex-box">
          <!-- :class="{'is-error': batchForm[activeName].jslddPJZ < 125 || batchForm[activeName].jslddPJZ > 135}" -->
          <el-form-item label="胶砂流动度1：" class="flex-item flex-box" >
            <el-input
              v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @input="val => setXSLB('jsldd')"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].jsldd1" 
              clearable
              placeholder="请输入"
            >
              <template slot="append">mm</template>
            </el-input>
          </el-form-item>
           <!-- :class="{'is-error': batchForm[activeName].dbjslddPJZ < 125 || batchForm[activeName].dbjslddPJZ > 135}" -->
          <el-form-item class="flex-item flex-box" label="对比胶砂流动度1：">
            <el-input
              v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].dbjsldd1"
              @input="val => setXSLB('dbjsldd')"
              clearable
              placeholder="请输入"
            >
              <template slot="append">mm</template>
            </el-input>
          </el-form-item>
          <div class="flex-item flex-box"></div>
        </div>
        <div class="flex-box">
           <!-- :class="{'is-error': batchForm[activeName].jslddPJZ < 125 || batchForm[activeName].jslddPJZ > 135}" -->
          <el-form-item label="胶砂流动度2：" class="flex-item flex-box">
            <el-input
              v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @input="val => setXSLB('jsldd')"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].jsldd2" 
              clearable
              placeholder="请输入"
            >
              <template slot="append">mm</template>
            </el-input>
          </el-form-item>
           <!-- :class="{'is-error': batchForm[activeName].dbjslddPJZ < 125 || batchForm[activeName].dbjslddPJZ > 135}" -->
          <el-form-item class="flex-item flex-box" label="对比胶砂流动度2：">
            <el-input
              v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].dbjsldd2"
              @input="val => setXSLB('dbjsldd')"
              clearable
              placeholder="请输入"
            >
              <template slot="append">mm</template>
            </el-input>
          </el-form-item>
          <div class="flex-item flex-box"></div>
        </div>
        
        <div class="flex-box">
           <!-- :class="{'is-error': batchForm[activeName].jsldd1pjz < 125 || batchForm[activeName].dbjslddPJZ > 135}" -->
          <el-form-item class="flex-item flex-box" label="胶砂流动度平均值：">
            <el-input
              type="number" v-decimal="0"
              @keydown.native="e=>e.returnValue=(['e','E','-','.','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].jsldd1pjz"
              disabled
            >
              <template slot="append">mm</template>
            </el-input>
          </el-form-item>
           <!-- :class="{'is-error': batchForm[activeName].jsldd2pjz < 125 || batchForm[activeName].dbjslddPJZ > 135}" -->
          <el-form-item class="flex-item flex-box" label="对比胶砂流动度平均值：">
            <el-input
              type="number" v-decimal="0"
              @keydown.native="e=>e.returnValue=(['e','E','-','.','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].jsldd2pjz"
              disabled
            >
              <template slot="append">mm</template>
            </el-input>
          </el-form-item>
          <div class="flex-item flex-box"></div>
        </div>
        <div class="flex-box">
          <el-form-item label="粉煤灰需水量：" class="flex-item flex-box">
            <el-input
              type="number" v-decimal="0"
              @input="val => setxslb()"
              @keydown.native="e=>e.returnValue=(['e','E','-','.','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].fmhxsl" 
              clearable
              placeholder="请输入"
            >
              <template slot="append">ml</template>
            </el-input>
          </el-form-item>
          <el-form-item class="flex-item flex-box" label="水泥需水量：">
            <el-input
              type="number" v-decimal="0"
              @keydown.native="e=>e.returnValue=(['e','E','-','.','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].snxsl"
              @input="val => setxslb()"
              clearable
              placeholder="请输入"
            >
              <template slot="append">ml</template>
            </el-input>
          </el-form-item>
          <div class="flex-item flex-box"></div>
        </div>
        
        <div class="line"></div>
        <el-row>
          <el-col :span="10">
            <el-form-item class="flex-box" label="需水量比：">
              <el-input
                type="number" v-decimal="0"
                v-model="batchForm[activeName].xslb" 
                disabled
                :style="{'width': 200 + 'px'}"
              >
                <template slot="append">%</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="14">
            <el-form-item class="flex-box" label="单项结论：">
              <el-input
                v-model="batchForm[activeName].dxjl" 
                :disabled="dxjlDisabled"
                placeholder="请输入"
                :style="{'width': 180 + 'px'}"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row>
          <el-col :span="24">
            <el-form-item label="检测图片：">
              <div class="cbo">
                <div class="img-box">
                  <el-upload
                    accept=".jpeg,.png,.jpg,.bmp,.gif"
                    :action="baseUrl + '/upload/file'"
                    list-type="picture-card"
                    :on-preview="handlePreview"
                    :file-list="batchForm[activeName].img"
                    :on-success="handlePicSuccess"
                    :on-remove="handlePicRemove">
                    <i class="el-icon-plus"></i>
                  </el-upload>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    
    
    <!-- 三氧化硫 -->
    <!-- <div v-if="activeName === 'FLY_ASK_PARAM_FMH_SYHL'" key="FLY_ASK_PARAM_FMH_SYHL"class="custom-form">
      <el-form :disabled="loading2 || experimentStatus == 3" :inline="true" label-width="130px">
        <div class="mt16 jcrq-box">
          <el-form-item label="检测日期：">
            <el-date-picker
              type="date"
              v-model="batchForm[activeName].jcrq"
              placeholder="选择日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
        </div>
        <div class="mt16 flex-box">
          <el-form-item label="试样质量：" class="flex-item flex-box" >
            <el-input
              v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @input="val => setSYLZLFS()"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].syzl" 
              placeholder="请输入"
            >
              <template slot="append">g</template>
            </el-input>
          </el-form-item>
          <el-form-item class="flex-item flex-box" label="空白试验硫质量：">
            <el-input
              v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].kbsylzl"
              @input="val => setSYLZLFS()"
              placeholder="请输入"
            >
              <template slot="append">mg</template>
            </el-input>
          </el-form-item>
          <el-form-item class="flex-item flex-box" label="换算系数：">
            <el-input
              v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].hsxs"
              disabled
              value="2.5"
              @input="val => setSYLZLFS()"
              placeholder="2.5"
            >
            </el-input>
          </el-form-item>
        </div>
        <div class="flex-box">
          <el-form-item label="试样硫质量：" class="flex-item flex-box" >
            <el-input
              v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @input="val => setSYLZLFS()"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].sylzl" 
              placeholder="请输入"
            >
              <template slot="append">mg</template>
            </el-input>
          </el-form-item>
          <el-form-item class="flex-item flex-box" label="">
          </el-form-item>
          <el-form-item class="flex-item flex-box" label="">
          </el-form-item>
        </div>
        <div class="line"></div>
        <el-row>
          <el-col :span="10">
            <el-form-item class="flex-box" label="试验硫质量分数：">
              <el-input
                v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                v-model="batchForm[activeName].sylzlfs" 
                disabled
                :style="{'width': 200 + 'px'}"
              >
                <template slot="append">%</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="14">
            <el-form-item class="flex-box" label="单项结论：">
              <el-input
                v-model="batchForm[activeName].dxjl" 
                disabled
                placeholder="请输入"
                :style="{'width': 180 + 'px'}"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row>
          <el-col :span="24">
            <el-form-item label="检测图片：">
              <div class="cbo">
                <div class="img-box">
                  <el-upload
                    accept=".jpeg,.png,.jpg,.bmp,.gif"
                    :action="baseUrl + '/upload/file'"
                    list-type="picture-card"
                    :on-preview="handlePreview"
                    :file-list="batchForm[activeName].img"
                    :on-success="handlePicSuccess"
                    :on-remove="handlePicRemove">
                    <i class="el-icon-plus"></i>
                  </el-upload>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div> -->


    <!-- 三氧化硫 -->
    <div v-if="activeName === 'FLY_ASK_PARAM_FMH_SYHL'" key="FLY_ASK_PARAM_FMH_SYHL"class="custom-form">
      <el-form :disabled="loading2 || experimentStatus == 3" :inline="true">
        <div class="flex-row mt16 jcrq-box">
          <el-form-item label="检测日期：">
            <el-date-picker
              type="date"
              v-model="batchForm[activeName].jcrq"
              placeholder="选择日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
        </div>
        <div class="line"></div>
        <div v-for="(item, index) in batchForm[activeName].syhlList" :key="index">
          <p class="info-form-title mt16">{{index == 0 ? '第一次试验' : '第二次试验'}}</p>
          <div class="info-form-piece flex-box" style="flex-direction: column;">
            <div class="flex-box" style="width: 100%;">
              <el-form-item label="称量器皿质量：" class="flex-item flex-box" label-width="120px">
                <el-input
                  v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  @input="val => setSYHL(item, index, 'sy')"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  v-model="item.clqmzl"
                  :key="`clqmzl-${index}`"
                  placeholder="请输入"
                >
                  <template slot="append">g</template>
                </el-input>
              </el-form-item>
              <el-form-item class="flex-item flex-box" label="试样+称量器皿质量：" label-width="180px">
                <el-input
                  v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  v-model="item.slqmzl"
                  :key="`slqmzl-${index}`"
                  @input="val => setSYHL(item, index, 'sy')"
                  placeholder="请输入"
                >
                  <template slot="append">g</template>
                </el-input>
              </el-form-item>
              <el-form-item class="flex-item flex-box" label="试样质量：" label-width="130px">
                <el-input
                  v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  v-model="item.syzl"
                  disabled
                  placeholder="请输入"
                >
                  <template slot="append">g</template>
                </el-input>
              </el-form-item>
            </div>
            <div class="flex-box" style="width: 100%;">
              <el-form-item label="瓷坩埚质量：" class="flex-item flex-box" label-width="120px">
                <el-input
                  v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  @input="val => setSYHL(item, index, 'cd')"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  v-model="item.zggzl"
                  placeholder="请输入"
                >
                  <template slot="append">g</template>
                </el-input>
              </el-form-item>
              <el-form-item class="flex-item flex-box" label="灼烧后沉淀+瓷坩埚质量：" label-width="180px">
                <el-input
                  v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  v-model="item.zshcdcggzl"
                  @input="val => setSYHL(item, index, 'cd')"
                  placeholder="请输入"
                >
                  <template slot="append">g</template>
                </el-input>
              </el-form-item>
              <el-form-item class="flex-item flex-box" label="灼烧后沉淀质量：" label-width="130px">
                <el-input
                  v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  v-model="item.zshcdzl"
                  @input="val => setSYHL(item, index, 'cd')"
                  disabled
                  placeholder="请输入"
                >
                  <template slot="append">g</template>
                </el-input>
              </el-form-item>
            </div>
            <div class="flex-box" style="width: 100%;">
              <el-form-item label="换算系数：" class="flex-item flex-box" label-width="120px">
                <el-input
                  v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  @input="val => setSYHL(item, index, 'cd')"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  v-model="item.hsxs" 
                  :value="0.343"
                  disabled
                  placeholder="请输入"
                >
                  <template slot="append"></template>
                </el-input>
              </el-form-item>
              <el-form-item class="flex-item flex-box" label="质量分数：" label-width="180px">
                <el-input
                  v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  v-model="item.zlfs"
                  disabled
                  @input="val => setSYHL(item, index, 'cd')"
                  placeholder="请输入"
                >
                  <template slot="append"></template>
                </el-input>
              </el-form-item>
              <div class="flex-item flex-box"></div>
            </div>
          </div>
        </div>
        <div class="line"></div>
        <el-row>
          <el-col :span="8">
            <el-form-item class="flex-box" label="平均值：">
              <el-input
                v-model="batchForm[activeName].pjz" 
                disabled
                placeholder="请输入"
                :style="{'width': 180 + 'px'}"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="14">
            <el-form-item class="flex-box" label="单项结论：">
              <el-input
                v-model="batchForm[activeName].dxjl" 
                :disabled="dxjlDisabled"
                placeholder="请输入"
                :style="{'width': 180 + 'px'}"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row>
          <el-col :span="24">
            <el-form-item label="检测图片：">
              <div class="cbo">
                <div class="img-box">
                  <el-upload
                    accept=".jpeg,.png,.jpg,.bmp,.gif"
                    :action="baseUrl + '/upload/file'"
                    list-type="picture-card"
                    :on-preview="handlePreview"
                    :file-list="batchForm[activeName].img"
                    :on-success="handlePicSuccess"
                    :on-remove="handlePicRemove">
                    <i class="el-icon-plus"></i>
                  </el-upload>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    
    
    <!-- 游离氧化钙公式 -->
    <div v-if="activeName === 'FLY_ASK_PARAM_FMH_YLYHG'" key="FLY_ASK_PARAM_FMH_YLYHG"class="custom-form">
      <el-form :disabled="loading2 || experimentStatus == 3" :inline="true"  label-width="100px" label-position="right">
        <div class="mt16 jcrq-box">
          <el-form-item label="检测日期：">
            <el-date-picker
              type="date"
              v-model="batchForm[activeName].jcrq"
              placeholder="选择日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
        </div>
        
        <div v-for="(item, index) in batchForm[activeName].ylyhgList" :key="index">
          <p class="info-form-title mt16">{{index == 0 ? '第一次试验' : '第二次试验'}}</p>
          <div class="info-form-piece flex-box">
            <el-form-item label="称量器皿质量：" class="flex-item flex-box" label-width="170px">
              <el-input
                v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @input="val => setYLYHG(index)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="item.clqmzl" 
                placeholder="请输入"
              >
                <template slot="append">g</template>
              </el-input>
            </el-form-item>
            <el-form-item label="试料+称量器皿质量：" class="flex-item flex-box"  label-width="150px">
              <el-input
                v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @input="val => setYLYHG(index)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="item.slclqmzl" 
                placeholder="请输入"
              >
                <template slot="append">g</template>
              </el-input>
            </el-form-item>
            <el-form-item label="试样质量：" class="flex-item flex-box" >
              <el-input
                v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="item.syzl" 
                disabled
              >
                <template slot="append">g</template>
              </el-input>
            </el-form-item>
          </div>
          <div class="info-form-piece flex-box">
            <el-form-item class="flex-item flex-box" label="苯甲酸无水乙醇滴定度：" label-width="170px">
              <el-input
                v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="item.bjswsycddd"
                @input="val => setYLYHG(index)"
                placeholder="请输入"
              >
                <template slot="append">mg/ml</template>
              </el-input>
            </el-form-item>
            <el-form-item class="flex-item flex-box" label="标准滴定溶液体积：" label-width="150px">
              <el-input
                v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="item.bzddrytj"
                @input="val => setYLYHG(index)"
                placeholder="请输入"
              >
                <template slot="append">ml</template>
              </el-input>
            </el-form-item>
            <el-form-item class="flex-item flex-box" label="结果：">
              <el-input
                v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="item.syjg"
                disabled
                placeholder="请输入"
              >
                <template slot="append">%</template>
              </el-input>
            </el-form-item>
          </div>
        </div>
        
        <div class="mt16 flex-box">
          
        </div>
        <div class="line"></div>
        <el-row>
          <el-col :span="10">
            <el-form-item class="flex-box" label="平均值：">
              <el-input
                v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                v-model="batchForm[activeName].pjz" 
                disabled
                :style="{'width': 200 + 'px'}"
              >
                <template slot="append">%</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="14">
            <el-form-item class="flex-box" label="单项结论：">
              <el-input
                v-model="batchForm[activeName].dxjl" 
                :disabled="dxjlDisabled"
                placeholder="请输入"
                :style="{'width': 180 + 'px'}"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row>
          <el-col :span="24">
            <el-form-item label="检测图片：">
              <div class="cbo">
                <div class="img-box">
                  <el-upload
                    accept=".jpeg,.png,.jpg,.bmp,.gif"
                    :action="baseUrl + '/upload/file'"
                    list-type="picture-card"
                    :on-preview="handlePreview"
                    :file-list="batchForm[activeName].img"
                    :on-success="handlePicSuccess"
                    :on-remove="handlePicRemove">
                    <i class="el-icon-plus"></i>
                  </el-upload>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    
    <!-- 安定性 -->
    <div v-if="activeName === 'FLY_ASK_PARAM_FMH_ADX'" key="FLY_ASK_PARAM_FMH_ADX">
      <el-form :disabled="loading2 || experimentStatus == 3" label-width="120px">
        <div class="mt16 jcrq-box flex-box">
          <el-form-item label="检测日期：" class="flex-item">
            <el-date-picker
              type="date"
              v-model="batchForm[activeName].jcrq"
              placeholder="选择日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
          
          <el-form-item label="粉煤灰掺量：" class="flex-item">
            <el-input
              v-manual-update
              @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].fmhcl"
              placeholder="请输入"
            >
              <!-- <template slot="append">um</template> -->
            </el-input>
          </el-form-item>
          <div class="flex-item"></div>
        </div>
        <p class="info-form-title">雷氏法</p>
        <div class="info-form-piece">
          <el-row>
            <el-col :span="8">
              <el-form-item label="煎煮A：">
                <el-input
                  v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  v-model="batchForm[activeName].lsf.jza"
                  @input="val => setADXlsf()"
                  placeholder="请输入"
                  :style="{'width': 200 + 'px'}"
                >
                  <template slot="append">mm</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="煎煮C：">
                <el-input
                  v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  v-model="batchForm[activeName].lsf.jzc"
                  @input="val => setADXlsf()"
                  placeholder="请输入"
                  :style="{'width': 200 + 'px'}"
                >
                  <template slot="append">mm</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="增加距离C-A：">
                <el-input
                  v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="batchForm[activeName].lsf.zjjl" 
                  disabled
                  :style="{'width': 200 + 'px'}"
                >
                  <template slot="append">mm</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="煎煮A：">
                <el-input
                  v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  v-model="batchForm[activeName].lsf.jza1"
                  @input="val => setADXlsf('1')"
                  placeholder="请输入"
                  :style="{'width': 200 + 'px'}"
                >
                  <template slot="append">mm</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="煎煮C：">
                <el-input
                  v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  v-model="batchForm[activeName].lsf.jzc1"
                  @input="val => setADXlsf('1')"
                  placeholder="请输入"
                  :style="{'width': 200 + 'px'}"
                >
                  <template slot="append">mm</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="增加距离C-A：">
                <el-input
                  v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="batchForm[activeName].lsf.zjjl1" 
                  disabled
                  :style="{'width': 200 + 'px'}"
                >
                  <template slot="append">mm</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="C-A平均值：">
                <el-input
                  v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="batchForm[activeName].lsf.pjz" 
                  disabled
                  :style="{'width': 200 + 'px'}"
                >
                  <template slot="append">mm</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="16">
              <el-form-item label="结论：">
                <el-input
                  v-model="batchForm[activeName].lsf.jl" 
                  :disabled="dxjlDisabled"
                  placeholder="请输入"
                  :style="{'width': 180 + 'px'}"
                >
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <p class="info-form-title mt16">试饼法</p>
        <div class="info-form-piece">
          <el-row>
            <el-col :span="8">
              <el-form-item label="有无纹裂：">
                <el-select @input="setADXsbf" v-model="batchForm[activeName].sbf.ywlw" placeholder="请选择">
                  <el-option label="是" value="是">
                  </el-option>
                  <el-option label="否" value="否">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="是否弯曲：">
                <el-select @input="setADXsbf" v-model="batchForm[activeName].sbf.sfwq" placeholder="请选择">
                  <el-option label="是" value="是">
                  </el-option>
                  <el-option label="否" value="否">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="结论：">
                <el-input
                  v-model="batchForm[activeName].sbf.jl" 
                  :disabled="dxjlDisabled"
                  placeholder="请输入"
                  :style="{'width': 200 + 'px'}"
                >
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <el-row>
          <el-col :span="24">
            <el-form-item label="检测图片：">
              <div class="cbo">
                <div class="img-box">
                  <el-upload
                    accept=".jpeg,.png,.jpg,.bmp,.gif"
                    :action="baseUrl + '/upload/file'"
                    list-type="picture-card"
                    :on-preview="handlePreview"
                    :file-list="batchForm[activeName].img"
                    :on-success="handlePicSuccess"
                    :on-remove="handlePicRemove">
                    <i class="el-icon-plus"></i>
                  </el-upload>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    
    <!-- 含水量 -->
    <div v-if="activeName === 'FLY_ASK_PARAM_FMH_HSL'" key="FLY_ASK_PARAM_FMH_HSL"class="custom-form">
      <el-form :disabled="loading2 || experimentStatus == 3" :inline="true" label-width="200px">
        <div class="mt16 jcrq-box">
          <el-form-item label="检测日期：">
            <el-date-picker
              type="date"
              v-model="batchForm[activeName].jcrq"
              placeholder="选择日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
        </div>
        <div class="flex-box mt16">
          <el-form-item label="称量器皿质量：" class="flex-item flex-box">
            <el-input
              v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @input="setHSL"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].clqmzl" 
              style="width: 200px;"
              placeholder="请输入"
            >
              <template slot="append">g</template>
            </el-input>
          </el-form-item>
        </div>
        <div class="flex-box mt16">
          <el-form-item class="flex-item flex-box" label="试料+称量器皿质量：">
            <el-input
              v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @input="setHSL"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].slclqmzl"
              placeholder="请输入"
              style="width: 200px;"
            >
              <template slot="append">g</template>
            </el-input>
          </el-form-item>
          
          <el-form-item label="烘前试样质量：" class="flex-item flex-box">
            <el-input
              v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].cyzl" 
              disabled
              style="width: 200px;"
            >
              <template slot="append">g</template>
            </el-input>
          </el-form-item>
        </div>
        
        <div class="flex-box">
          <el-form-item class="flex-item flex-box" label="烘干后试料+称量器皿质量：">
            <el-input
              v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @input="setHSL"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].hghclqmzl"
              placeholder="请输入"
              style="width: 200px;"
            >
              <template slot="append">g</template>
            </el-input>
          </el-form-item>
          <el-form-item class="flex-item flex-box" label="烘后试样质量：">
            <el-input
              v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              disabled
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].hghsyzl"
              style="width: 200px;"
            >
              <template slot="append">g</template>
            </el-input>
          </el-form-item>
          
        </div>
        <el-row>
          <el-col :span="12">
            <el-form-item class="flex-item flex-box" label="含水率：">
              <el-input
                v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="batchForm[activeName].hsl"
                disabled
                placeholder="请输入"
                style="width: 200px;"
              >
                <template slot="append">%</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item class="flex-box" label="单项结论：">
              <el-input
                v-model="batchForm[activeName].dxjl" 
                :disabled="dxjlDisabled"
                placeholder="请输入"
                style="width: 200px;"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row>
          <el-col :span="24">
            <el-form-item label="检测图片：">
              <div class="cbo">
                <div class="img-box">
                  <el-upload
                    accept=".jpeg,.png,.jpg,.bmp,.gif"
                    :action="baseUrl + '/upload/file'"
                    list-type="picture-card"
                    :on-preview="handlePreview"
                    :file-list="batchForm[activeName].img"
                    :on-success="handlePicSuccess"
                    :on-remove="handlePicRemove">
                    <i class="el-icon-plus"></i>
                  </el-upload>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
  
    <!-- 活性指数 -->
    <div v-if="activeName === 'FLY_ASK_PARAM_FMH_HXZS'" key="FLY_ASK_PARAM_FMH_HXZS"class="custom-form">
      
      <div class="mt16">
        <span @click="dayNum = 0" class="tag-btn" :class="{'tag-btn-primary' : dayNum === 0}">活性指数-7d</span>
        <span @click="dayNum = 1" class="tag-btn" :class="{'tag-btn-primary' : dayNum === 1}">活性指数-28d</span>
      </div>
      
      <el-form :disabled="loading2 || experimentStatus == 3" :inline="true" class="mt16">
        <div class="mt16 jcrq-box">
          <el-form-item label="检测日期：">
            <el-date-picker
              type="date"
              v-model="batchForm[activeName].hxzsInfoList[dayNum].jcrq"
              placeholder="选择日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
        </div>
        <div class="table-box-kzf mt16">
          <div class="tb-title">试验胶砂对比强度(kN)</div>
          <div class="tb-content">
            <div class="flex-box tb-item">
              <el-form-item class="flex-item">
                <el-input
                  v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="batchForm[activeName].hxzsInfoList[dayNum].syjskdbqd.qd1"
                  @input="val => setHXZSPJZ('sy')"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  placeholder="请输入"
                  style="width: 80px;"
                >
                </el-input>
              </el-form-item>
              <el-form-item class="flex-item">
                <el-input
                  v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="batchForm[activeName].hxzsInfoList[dayNum].syjskdbqd.qd2"
                  @input="val => setHXZSPJZ('sy')"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  placeholder="请输入"
                  style="width: 80px;"
                >
                </el-input>
              </el-form-item>
              <el-form-item class="flex-item">
                <el-input
                  v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="batchForm[activeName].hxzsInfoList[dayNum].syjskdbqd.qd3"
                  @input="val => setHXZSPJZ('sy')"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  placeholder="请输入"
                  style="width: 80px;"
                >
                </el-input>
              </el-form-item>
              <el-form-item class="flex-item">
                <el-input
                  v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="batchForm[activeName].hxzsInfoList[dayNum].syjskdbqd.qd4"
                  @input="val => setHXZSPJZ('sy')"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  placeholder="请输入"
                  style="width: 80px;"
                >
                </el-input>
              </el-form-item>
              <el-form-item class="flex-item">
                <el-input
                  v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="batchForm[activeName].hxzsInfoList[dayNum].syjskdbqd.qd5"
                  @input="val => setHXZSPJZ('sy')"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  placeholder="请输入"
                  style="width: 80px;"
                >
                </el-input>
              </el-form-item>
              <el-form-item class="flex-item">
                <el-input
                  v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="batchForm[activeName].hxzsInfoList[dayNum].syjskdbqd.qd6"
                  @input="val => setHXZSPJZ('sy')"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  placeholder="请输入"
                  style="width: 80px;"
                >
                </el-input>
              </el-form-item>
            </div>
          </div>
          <div class="tb-bottom">
            <el-form-item label="平均值：" class="flex-item">
              <el-input
                v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                v-model="batchForm[activeName].hxzsInfoList[dayNum].syjsbfb"
                disabled
              >
                <template slot="append">MPa</template>
              </el-input>
            </el-form-item>
          </div>
        </div>
        
        <p class="info-form-title mt16">对比程度（MPa）</p>
        <div class="table-box-kzf mt16">
          <div class="tb-title">对比胶砂对比强度(kN)</div>
          <div class="tb-content">
            <div class="flex-box tb-item">
              <el-form-item class="flex-item">
                <el-input
                  v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="batchForm[activeName].hxzsInfoList[dayNum].dbjsdbqd.qd1"
                  @input="val => setHXZSPJZ('db')"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  placeholder="请输入"
                  style="width: 80px;"
                >
                </el-input>
              </el-form-item>
              <el-form-item class="flex-item">
                <el-input
                  v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="batchForm[activeName].hxzsInfoList[dayNum].dbjsdbqd.qd2"
                  @input="val => setHXZSPJZ('db')"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  placeholder="请输入"
                  style="width: 80px;"
                >
                </el-input>
              </el-form-item>
              <el-form-item class="flex-item">
                <el-input
                  v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="batchForm[activeName].hxzsInfoList[dayNum].dbjsdbqd.qd3"
                  @input="val => setHXZSPJZ('db')"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  placeholder="请输入"
                  style="width: 80px;"
                >
                </el-input>
              </el-form-item>
              <el-form-item class="flex-item">
                <el-input
                  v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="batchForm[activeName].hxzsInfoList[dayNum].dbjsdbqd.qd4"
                  @input="val => setHXZSPJZ('db')"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  placeholder="请输入"
                  style="width: 80px;"
                >
                </el-input>
              </el-form-item>
              <el-form-item class="flex-item">
                <el-input
                  v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="batchForm[activeName].hxzsInfoList[dayNum].dbjsdbqd.qd5"
                  @input="val => setHXZSPJZ('db')"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  placeholder="请输入"
                  style="width: 80px;"
                >
                </el-input>
              </el-form-item>
              <el-form-item class="flex-item">
                <el-input
                  v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="batchForm[activeName].hxzsInfoList[dayNum].dbjsdbqd.qd6"
                  @input="val => setHXZSPJZ('db')"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  placeholder="请输入"
                  style="width: 80px;"
                >
                </el-input>
              </el-form-item>
            </div>
          </div>
          <div class="tb-bottom">
            <el-form-item label="平均值：" class="flex-item">
              <el-input
                v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                v-model="batchForm[activeName].hxzsInfoList[dayNum].dbjsbfb"
                disabled
              >
                <template slot="append">MPa</template>
              </el-input>
            </el-form-item>
          </div>
        </div>
      </el-form>
      
      <div class="line"></div>
      <p class="info-form-title mt16">结论</p>
      <el-form :disabled="loading2 || experimentStatus == 3" :inline="true" class="mt16">
        <el-row class="mt16" v-if="dayNum === 0">
          <el-col :span="10">
            <el-form-item class="flex-box" label="7d活性指数：">
              <el-input
                v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                v-model="batchForm[activeName].hxzsInfoList[dayNum].hxzs" 
                disabled
                :style="{'width': 200 + 'px'}"
              >
                <template slot="append">%</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="14">
            <el-form-item class="flex-box" label="7d单项结论：">
              <el-input
                v-model="batchForm[activeName].hxzsInfoList[dayNum].dxjl" 
                :disabled="dxjlDisabled"
                :style="{'width': 180 + 'px'}"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row class="mt16" v-else>
          <el-col :span="10">
            <el-form-item class="flex-box" label="28d活性指数：">
              <el-input
                v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                v-model="batchForm[activeName].hxzsInfoList[dayNum].hxzs" 
                disabled
                :style="{'width': 200 + 'px'}"
              >
                <template slot="append">%</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="14">
            <el-form-item class="flex-box" label="28d单项结论：">
              <el-input
                v-model="batchForm[activeName].hxzsInfoList[dayNum].dxjl" 
                :disabled="dxjlDisabled"
                :style="{'width': 180 + 'px'}"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="检测图片：">
              <div class="cbo">
                <div class="img-box">
                  <el-upload
                    accept=".jpeg,.png,.jpg,.bmp,.gif"
                    :action="baseUrl + '/upload/file'"
                    list-type="picture-card"
                    :on-preview="handlePreview"
                    :file-list="batchForm[activeName].img"
                    :on-success="handlePicSuccess"
                    :on-remove="handlePicRemove">
                    <i class="el-icon-plus"></i>
                  </el-upload>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    
    <image-viewer
      v-if="imgViewerVisible"
      :urlList="[previewUrl]"
      :on-close="onClose"
    >
    </image-viewer>
  </div>
</template>

<script>
import util from "../../../common/js/util.js";
import getOpt from "@/common/js/getListData.js"
import {conclusion_FMH_XD, conclusion_FMH_SSL, conclusion_FMH_XSLB} from "./conclusion.js"
import {add,  sub,  mul,  div, roundToDecimalPlace, calcEquation} from "@/utils/calculate.js"
import moment from "@/utils/moment.js"
import ImageViewer from "element-ui/packages/image/src/image-viewer";
import { number } from 'echarts';
export default {
  name:'userMgt',
  components: {
    ImageViewer
  },
  props: {
    activeId: {
      type: Number | String,
    },
    experimentStatus: {
      type: Number | String,
    },
    sampleLevel: {
      type: String,
      default: '1'
    },
    entrustTime: {
      type: String,
      default: new Date().toISOString().split('T')[0],
    },
    
  },
  watch: {
    batchForm: {
      handler(newVal, oldVal){
        console.log(">>>>FMH>>>>>", newVal);

        // if(!newVal.FLY_ASK_PARAM_FMH_XD.xdInfo[0].jzxs){
        //   this.FLY_ASK_PARAM_FMH_XD.xdInfo[0].jzxs = 1.10;
        // }
        // if(!newVal.FLY_ASK_PARAM_FMH_XD.xdInfo[1].jzxs){
        //   this.FLY_ASK_PARAM_FMH_XD.xdInfo[1].jzxs = 1.10;
        // }
      },
      deep: true,
      //immediate: true
    },
    
    entrustTime: {
      handler(newValue, oldValue){
        if(newValue){
          this.setDefaultTime(this.batchForm)
        }
      },
      immediate: true
    },
    
  },
  computed: {
    xd(){
      //let a = this.batchForm.FLY_ASK_PARAM_FMH_XD.xdInfo[0] + 
    }
  },
  data() {
    return {
      defaultDate: moment.today(),
      infoForm: '',
      filePrefix: this.$store.state.loginStore.userInfo.filePrefix,
      baseUrl: process.env.NODE_ENV === 'development' ? "/api" : "/mes-platform",
      activeName: 'quick',
      activeInfoName: 'xdInfo',
      quickForm: {},//快检数据
      quickData: [],//快检列表
      batchForm: {
        CONCRETE_PARAM_XGL_HNL: {
          hnlInfo: []
        },
        CONCRETE_PARAM_XGL_NKHL: {
          hnkInfo: []
        }
      },//批检数据
      batchData: [],//批检列表
      
      loading2: false,
      
      previewUrl: "",
      imgViewerVisible: false,
      
      
      dayNum: 0,
      sampleLevel2: 'PO42.5',//流动度等级
      HXZSLevel: 'S105',
      
      
      accordingObj: {},
      dxjlDisabled: false,
    };
  },
  
  created() {
  },
  methods: {
     setDefaultTime(val){

      // 粉煤灰：安定性=委托时间+1天
      // 安定性
      if(val.FLY_ASK_PARAM_FMH_ADX && this.isEmptyValue(val.FLY_ASK_PARAM_FMH_ADX?.jcrq)){
        val.FLY_ASK_PARAM_FMH_ADX['jcrq'] = util.calculateFutureDate(this.entrustTime, 1)
      }
      // 细度
      if(val.FLY_ASK_PARAM_FMH_XD && this.isEmptyValue(val.FLY_ASK_PARAM_FMH_XD?.jcrq)){
        val.FLY_ASK_PARAM_FMH_XD['jcrq'] = util.calculateFutureDate(this.entrustTime, 0)
      }
      
      // 需水量比
      if(val.FLY_ASK_PARAM_FMH_XSLB && this.isEmptyValue(val.FLY_ASK_PARAM_FMH_XSLB?.jcrq)){
        val.FLY_ASK_PARAM_FMH_XSLB['jcrq'] = util.calculateFutureDate(this.entrustTime, 0)
      }
      
      // 烧失量
      if(val.FLY_ASK_PARAM_FMH_SSL && this.isEmptyValue(val.FLY_ASK_PARAM_FMH_SSL?.jcrq)){
        val.FLY_ASK_PARAM_FMH_SSL['jcrq'] = util.calculateFutureDate(this.entrustTime, 0)
      }
      // 三氧化硫
      if(val.FLY_ASK_PARAM_FMH_SYHL && this.isEmptyValue(val.FLY_ASK_PARAM_FMH_SYHL?.jcrq)){
        val.FLY_ASK_PARAM_FMH_SYHL['jcrq'] = util.calculateFutureDate(this.entrustTime, 0)
      }

      // 游离氧化钙
      if(val.FLY_ASK_PARAM_FMH_YLYHG && this.isEmptyValue(val.FLY_ASK_PARAM_FMH_YLYHG?.jcrq)){
        val.FLY_ASK_PARAM_FMH_YLYHG['jcrq'] = util.calculateFutureDate(this.entrustTime, 0)
      }
      
      // 筛分析
      if(val.FLY_ASK_PARAM_FMH_HXZS && this.isEmptyValue(val.FLY_ASK_PARAM_FMH_HXZS?.jcrq)){
        val.FLY_ASK_PARAM_FMH_HXZS['jcrq'] = util.calculateFutureDate(this.entrustTime, 0)
      }
      // 含水率
      if(val.FLY_ASK_PARAM_FMH_HSL && this.isEmptyValue(val.FLY_ASK_PARAM_FMH_HSL?.jcrq)){
        val.FLY_ASK_PARAM_FMH_HSL['jcrq'] = util.calculateFutureDate(this.entrustTime, 0)
      }

    

        },

    isEmptyValue(value) {
            // 处理用户输入的特殊字符串
            if (value === 'null') return true;
            if (value === 'undefined') return true;
            if (value === '') return true;
            
            // 处理实际的JavaScript值
            if (value === null) return true;
            if (value === undefined) return true;
            if (value === '') return true;
            
            return false;
        },
    //公用
    getPJZUtil(data,key,n){//数组，key，保留小数
      let len = 0,num = 0;
      data.forEach(item =>{
        if(item[key] !== ''){
          len++;
          num += item[key] * 1
        }
      })
      //2次结果之和除以2，保留2位小数，四舍五入
      let arr = [
        {
          v: num,
        },{
          k: '/',
          v: len === 0 ? '' : len
        }
      ]
      return calcEquation(arr, n);
    },
    //判断结论
    conclusion(num, key){
      if(num === ''){
        return ''
      }
      const aName = this.activeName;
      if(!key){
        key = aName.match(/[^_]+$/)[0];
        key = key.toLowerCase();
      }
      if(!this.accordingObj[aName][key]){
        return ''
      }
      const min = this.accordingObj[aName][key].min;
      const max = this.accordingObj[aName][key].max;
      
      console.log(num, aName, key, min, max);
      if(min !== null && num < min){
        // this.$parent.testInfoForm.isQualified = 2;
        return '不合格'
      }
      if(max !== null && num > max){
        // this.$parent.testInfoForm.isQualified = 2;
        return '不合格'
      }
      return '合格';
      // if(min !== null && max!== null){
      //   if(num >= min && num <= max){
      //     return '合格'
      //   }else{
      //     return '不合格'
      //   }
      // }else if(min !== null){
      //   if(num >= min){
      //     return '合格'
      //   }else{
      //     return '不合格'
      //   }
      // }else if(max !== null){
      //   if(num <= max){
      //     return '合格'
      //   }else{
      //     return '不合格'
      //   }
      // }
    },
    
    
    //细度
    clacXD(index){
      let data = this.batchForm[this.activeName][this.activeInfoName][index];
      if(data.syzl === '0' || data.syzl === 0 || data.sywz === '0' || data.sywz === 0){
        this.$message.error("试样质量和筛余物重，均需要大于0");
        this.batchForm[this.activeName][this.activeInfoName][index].xd = '';
      }else{
        let arr = [
          {
            v: data.sywz
          },{
            k: '/',
            v: data.syzl,
          },{
            k: '*',
            v: data.jzxs,
          },{
            k: '*',
            v: 100,
          }
        ]
        let res = calcEquation(arr, 1)
        this.batchForm[this.activeName][this.activeInfoName][index].xd = res;
      }
      this.setPJZ();
    },
    //设置平均值
    setPJZ(){
      const data = this.batchForm[this.activeName][this.activeInfoName];
      let res = this.getPJZUtil(data, 'xd', 1);
      this.batchForm[this.activeName].jzhxddbz = res;
      this.batchForm[this.activeName].dxjl = this.conclusion(res);
    },
    
    //烧失量
    setSSL(index){
      const data = this.batchForm[this.activeName][this.activeInfoName][index];
      console.log(data, data.slclqmzl)
      let a = sub(data.slclqmzl,data.clqmzl);
      let b = sub(data.zshcggzl,data.cggzl);
      this.batchForm[this.activeName][this.activeInfoName][index].slzl = a;
      this.batchForm[this.activeName][this.activeInfoName][index].zshsyzl = b;
      
      //2次结果之和除以2，保留2位小数，四舍五入
      let arr = [
        {
          v: a
        },{
          k: '-',
          v: b,
        },{
          k: '/',
          v: a,
        },{
          k: '*',
          v: 100,
        }
      ]
      let res = calcEquation(arr, 2)
      this.batchForm[this.activeName][this.activeInfoName][index].ssl = res;
      this.setSSLPJZ();
    },
    setSSLPJZ(){
      const oarr = this.batchForm[this.activeName][this.activeInfoName];
      let res = this.getPJZUtil(oarr, 'ssl', 1);
      this.batchForm[this.activeName].sslpjz = res;
      this.batchForm[this.activeName].dxjl = this.conclusion(res);
      
      // let len = 0,num = 0;
      // oarr.forEach(item =>{
      //   if(item.ssl !== ''){
      //     len++;
      //     num += item.ssl * 1
      //   }
      // })
      // //2次结果之和除以2，保留2位小数，四舍五入
      // let arr = [
      //   {
      //     v: num,
      //   },{
      //     k: '/',
      //     v: len === 0 ? '' : len
      //   }
      // ]
      // let res = calcEquation(arr, 1)
      // this.batchForm[this.activeName].sslpjz = res;
      // this.batchForm[this.activeName].dxjl = this.conclusion(res);;
    },
    //提叫需注意如流动度1,2的平均值不在125~135mm之间，则对应2个流动度文本框高亮显示，完成时提示：需水量比
    //需水量比
    setXSLB(type){
      const data = this.batchForm[this.activeName]
      if(type === 'jsldd'){
        let arr = [
          {
            v: data.jsldd1,
            k: '+'
          },{
            v: data.jsldd2,
            k: '+'
          },{
            v: 2,
            k: '/'
          }
        ]
        let pjz = calcEquation(arr, 0);
        this.batchForm[this.activeName].jsldd1pjz = pjz;
      }else if(type === 'dbjsldd'){
        let arr = [
          {
            v: data.dbjsldd1,
            k: '+'
          },{
            v: data.dbjsldd2,
            k: '+'
          },{
            v: 2,
            k: '/'
          }
        ]
        let pjz = calcEquation(arr, 0);
        this.batchForm[this.activeName].jsldd2pjz = pjz;
      }
      this.$forceUpdate();
    },
    setxslb(){
      const data = this.batchForm[this.activeName];
      //粉煤灰需水量/水泥需水量 % 100，保留2位小数，四舍五入，不可编辑
      let arr = [
        {
          v: data.fmhxsl,
        },{
          k: '/',
          v: data.snxsl,
        },{
          k: '*',
          v: 100,
        }
      ]
      let res = calcEquation(arr, 0)
      this.batchForm[this.activeName].xslb = res;
      this.conclusion('dxjl', res, conclusion_FMH_XSLB);
    },
    // 三氧化硫
    // setSYLZLFS(){
    //   const data = this.batchForm[this.activeName];
    //   //粉煤灰需水量/水泥需水量 % 100，保留2位小数，四舍五入，不可编辑
    //   let arr = [
    //     {
    //       v: data.sylzl,
    //     },{
    //       k: '-',
    //       v: data.kbsylzl,
    //     },{
    //       k: '*',
    //       v: 2.5,//data.hsxs
    //     },{
    //       k: '/',
    //       v: data.syzl * 1000,
    //     },{
    //       k: '*',
    //       v: 100,
    //     }
    //   ]
    //   let res = calcEquation(arr, 2)
    //   this.batchForm[this.activeName].sylzlfs = res;
    //   this.batchForm[this.activeName].dxjl = res <= 3 ? '合格' : '不合格';
    //   this.$forceUpdate();
    // },
    setSYHL(item, index, type) {
      console.log(item, index, type)
      const data = this.batchForm[this.activeName].syhlList[index];
      let arrsyzl = [
        {
          v: data.slqmzl,
        },{
          k: '-',
          v: data.clqmzl,
        }
      ]
      let syzl = calcEquation(arrsyzl, 4);
      
      this.batchForm[this.activeName].syhlList[index].syzl = syzl
      // 灼烧沉淀质量 = 灼烧后沉淀+瓷坩埚质量 - 瓷坩埚质量
      let zshcdzl = calcEquation([
        {
          v: data.zshcdcggzl,
        },{
          k: '-',
          v: data.zggzl,
        },
      ], 4);
      this.batchForm[this.activeName].syhlList[index].zshcdzl = zshcdzl
      let hsxs = data.hsxs || '0.343';
      let arr = [
        {
          v: zshcdzl,
        },{
          k: '*',
          v: hsxs,
        },{
          k: '*',
          v: 100,
        },{
          k: '/',
          v: syzl,
        }
      ]
      this.batchForm[this.activeName].syhlList[index].zlfs = calcEquation(arr, 2);
      this.setSYHLPjz()
    },
    setSYHLPjz(){
      const syhlList = this.batchForm[this.activeName].syhlList;
      
      let res = this.getPJZUtil(syhlList, 'zlfs', 2);
      this.batchForm[this.activeName].pjz = res;
      this.batchForm[this.activeName].dxjl = this.conclusion(res);
    },
    isEmpty(val) {
      if (typeof val === "boolean") {
        return false;
      }
      if (typeof val === "number") {
        return false;
      }
      if (val instanceof Array) {
        if (val.length === 0) return true;
      } else if (val instanceof Object) {
        if (JSON.stringify(val) === "{}") return true;
      } else {
        if (
          val === "null" ||
          val == null ||
          val === "undefined" ||
          val === undefined ||
          val === ""
        )
          return true;
        return false;
      }
      return false;
    },

    // 游离氧化钙公式
    setYLYHG(index){
      const data = this.batchForm[this.activeName].ylyhgList[index];
      let syzl = sub(data.slclqmzl, data.clqmzl)
      this.batchForm[this.activeName].ylyhgList[index].syzl = syzl;
      //粉煤灰需水量/水泥需水量 % 100，保留2位小数，四舍五入，不可编辑
      let arr = [
        {
          v: data.bzddrytj,
        },{
          k: '*',
          v: data.bjswsycddd,
        },{
          k: '/',
          v: mul(syzl, 1000),
        },{
          k: '*',
          v: 100,
        }
      ]
      let res = calcEquation(arr, 2)
      this.batchForm[this.activeName].ylyhgList[index].syjg = res;
      this.setYLYHGPJZ();
    },
    setYLYHGPJZ(){
      const data = this.batchForm[this.activeName].ylyhgList;
      
      let res = this.getPJZUtil(data, 'syjg', 1);
      this.batchForm[this.activeName].pjz = res;
      this.batchForm[this.activeName].dxjl = this.conclusion(res);
    },
    
    // 含水量
    setHSL(){
      const data = this.batchForm[this.activeName];
      let hq = sub(data.slclqmzl, data.clqmzl)
      let hh = sub(data.hghclqmzl, data.clqmzl)
      
      //粉煤灰需水量/水泥需水量 % 100，保留2位小数，四舍五入，不可编辑
      let arr = [
        {
          v: hq,
        },{
          k: '-',
          v: hh,
        },{
          k: '/',
          v: hq,
        },{
          k: '*',
          v: 100,
        }
      ]
      let res = calcEquation(arr, 1)
      this.batchForm[this.activeName].hsl = res;
      this.batchForm[this.activeName].cyzl = hq;
      this.batchForm[this.activeName].hghsyzl = hh;
      
      this.batchForm[this.activeName].dxjl = this.conclusion(res);
    },
    // <!-- 安定性 -->
    setADXlsf(type){
      const data = this.batchForm[this.activeName].lsf;
      if(type == '1'){
        let res = sub(data.jzc1, data.jza1)
        this.batchForm[this.activeName].lsf.zjjl1 = res;
      }else{
        let res = sub(data.jzc, data.jza)
        this.batchForm[this.activeName].lsf.zjjl = res;
      }
      let odata = [{
        zj: data.zjjl
      },{
        zj: data.zjjl1
      }]
      
      let res = this.getPJZUtil(odata, 'zj', 1);
      this.batchForm[this.activeName].lsf.pjz = res;
      this.batchForm[this.activeName].lsf.jl = this.conclusion(res);
      
    },
    setADXsbf(){
      // 结论：均为否，视为合格，否则，不合格
      // 雷氏法域试饼法二选一，只需要做一个试验，其中一个试验合格，视为安定性试验合格，如2个都做，以雷氏法为准。
      const data = this.batchForm[this.activeName].sbf;
      if(data.ywlw === '否' && data.sfwq == "否"){
        this.batchForm[this.activeName].sbf.jl = '合格'
      }else if(data.ywlw && data.sfwq){
        this.batchForm[this.activeName].sbf.jl = '不合格'
      }else{
        this.batchForm[this.activeName].sbf.jl = ''
      }
    },
    
    
    //活性指数
    setHXZSPJZ(type){
      let data;
      if(type == 'sy'){
        data = this.batchForm[this.activeName].hxzsInfoList[this.dayNum][type + 'jskdbqd']
      }else{
        data = this.batchForm[this.activeName].hxzsInfoList[this.dayNum][type + 'jsdbqd']
      }
      console.log(data)
      let len = 0;
      let num = 0;
      for(let item in data){
        if(data[item] && data[item]>=0){
          len++;
          num += data[item] * 1
        }
      }
      
      let arr = [
        {
          v: num,
        },{
          k: '/',
          v: len,
        }
      ]
      let res = calcEquation(arr, 1)
      this.batchForm[this.activeName].hxzsInfoList[this.dayNum][type + 'jsbfb'] = res;
      
      let arr2 = [
        {
          v: this.batchForm[this.activeName].hxzsInfoList[this.dayNum].syjsbfb,
        },{
          k: '/',
          v: this.batchForm[this.activeName].hxzsInfoList[this.dayNum].dbjsbfb,
        },{
          k: '*',
          v: 100,
        }
      ]
      let res2 = calcEquation(arr2, 1)
      this.batchForm[this.activeName].hxzsInfoList[this.dayNum].hxzs = res2;
    },
    
    
    //活性指数
    setKYQD(index){
      const data = this.batchForm[this.activeName]['hxzs' + this.dayNum + 'Info'].syjskysy[index];
      let arr = [
        {
          v: data.hz,
        },{
          k: '/',
          v: data.symj,
        }
      ]
      let res = calcEquation(arr, 1)
      this.batchForm[this.activeName]['hxzs' + this.dayNum + 'Info'].syjskysy[index].kyqd = res;
      this.setKZQDpjz();
    },
    setKZQDpjz(){
      const data = this.batchForm[this.activeName]['hxzs' + this.dayNum + 'Info'];
      let num = data.syjskysy.reduce((pre, cur) => {
        return add(pre, cur.kyqd)
      }, 0);
      let arr = [
        {
          v: num,
        },{
          k: '/',
          v: data.syjskysy.length,
        }
      ]
      let res = calcEquation(arr, 1)
      this.batchForm[this.activeName]['hxzs' + this.dayNum + 'Info'].syjspjz = res;
      
      if(res >= standard[this.sampleLevel2]['kzqdsy' + this.dayNum]){
        this.batchForm[this.activeName]['hxzs' + this.dayNum + 'Info'].syjsdxjl = '合格'
      }else{
        this.batchForm[this.activeName]['hxzs' + this.dayNum + 'Info'].syjsdxjl = '不合格'
      }
      this.setHXZSJL(this.dayNum);
    },
    setHXZSDBZ(type){
      const data = this.batchForm[this.activeName]['hxzs' + type + 'Info'].dbjsdbqd;
      let num = 0;
      for (let i = 1; i<=6; i++) {
        num = num + data['qd'+ i] * 1
      }
      let arr = [
        {
          v: num,
        },{
          k: '/',
          v: 6,
        }
      ]
      let res = calcEquation(arr, 1)
      this.batchForm[this.activeName]['hxzs' + type + 'Info'].dbjsdbqd.dbz = res;
      
      this.setHXZSJL(type);
    },
    setHXZSJL(type){
      // 对应天数活性指数：
      // 活性指数=试验胶砂对应天数强度/对比胶砂对应天数强度*100
      const data = this.batchForm[this.activeName]['hxzs' + type + 'Info'];
      let arr = [
        {
          v: data.syjspjz,
        },{
          k: '/',
          v: data.dbjsdbqd.dbz,
        },{
          k: '*',
          v: 100,
        }
      ]
      let res = calcEquation(arr, 1)
      console.log(data,type)
      this.batchForm[this.activeName]['hxzs' + type] = res;
      
      if(type === '7d'){
        this.batchForm[this.activeName]['dxjl' + type] = this.conclusion(res,conclusion_KZF_HXZS_7d);;
      }else{
        this.batchForm[this.activeName]['dxjl' + type] = this.conclusion(res,conclusion_KZF_HXZS_28d);;
      }
      
    },
    
    
    //图片
    handlePicSuccess(response, file, fileList) {
      console.log(fileList)
      let oUploadImg = fileList.map(item =>{
        if(item.response){
          return {
            url: item.response.data.filePath,
            uid: item.uid
          }
        }else{
          return item;
          // if(item.url.startsWith(this.filePrefix)){
          //   return item.url;
          // }else{
          //   return this.filePrefix + item.url;
          // }
        }
      })
      
      if(this.activeName === 'quick'){
        this.quickForm.img = oUploadImg;
      }else{
        this.batchForm[this.activeName].img = oUploadImg;
      }
    },
    handlePicRemove(file, fileList) {
      let oUploadImg = fileList.map(item =>{
        if(item.response){
          return {
            url: item.response.data.filePath,
            uid: item.uid
          }
        }else{
          return item
        }
      })
      if(this.activeName === 'quick'){
        this.quickForm.img = oUploadImg;
      }else{
        this.batchForm[this.activeName].img = oUploadImg;
      }
    },
    
    
    //获取所有快检或批检试验项目名称
    async setExperimentProject(id){
      //获取抗压 抗渗 和其它
      const resDetail = await this.$api.getExperimentDetail({
        experimentId: this.activeId,
        // "testProjectCode":"FINE_AGGREGATE",
        // "checkType": 2
      }, this)
      if(resDetail.succ){
        let quickData = [];
        let batchData = [];//批检
        let quickForm = {};
        quickForm.img = [];
        let batchForm = {};
        resDetail.data.list.forEach(item => {
          let oImgArr = [];
          if(item.objImg && item.objImg != 'null'){
            oImgArr = item.objImg.split(',').map(item => {
              if(item.startsWith(this.filePrefix)){
                return {
                  url: item
                }
              }else{
                return {
                  url: this.filePrefix + item
                }
              }
            }) 
          }else{
            oImgArr = [];
          }
          
          if(item.testProjectName === '流动性' || item.testProjectName === '保水性' 
            || item.testProjectName === '粘聚性'|| item.testProjectName === '坍落度' 
            || item.testProjectName === '目测砂率' 
          ){
            if (item.testProjectName === '坍落度') {
              if (!item.objJson.tldfs) {
                item.objJson.tldfs = "目测";
              }
            }
            if((item.testProjectName === '流动性' || item.testProjectName === '保水性' 
            || item.testProjectName === '粘聚性')){
              // 接口没有val 字段，根据判断转化一个加进去自己用
              item.objJson.val = 'hh'
              if (item.objJson.hh == 1) {
                item.objJson.val = 'hh'
              }else if (item.objJson.yb == 1) {
                item.objJson.val = 'yb'
              }else if (item.objJson.fcc == 1) {
                item.objJson.val = 'fcc'
              }
            }
            quickData.push(item)
            quickForm[item.testProjectCode] = item;
            quickForm.img = quickForm.img.concat(oImgArr);
          }else{
            if(!item.objJson?.jcrq){
              item.objJson.jcrq = this.defaultDate;
            }
            item.objJson.img = oImgArr;
            batchForm[item.testProjectCode] = JSON.parse(JSON.stringify(item.objJson));
            item.objJson = undefined;
            batchData.push(item);
          }
        })
        this.quickData = quickData;
        this.quickForm = quickForm;
        this.batchData = batchData;
        this.batchForm = this.setDefaultVal(batchForm);
        
        console.log(this.batchForm,this.batchData)
        if(this.quickData.length > 0){
          this.activeName = 'quick'
        }else{
          this.activeName = this.batchData[0].testProjectCode;
          this.getAccordingToHand()
        }
        
      }  
    },
    getAccordingToHand(){
      this.$api.getAccordingTo({
        testProjectCode: this.activeName,
        materialAbbreviation : this.$parent.activeData.materialAbbreviation,
        materialsName	 : this.$parent.activeData.materialsName,
        materialsSpec : this.$parent.activeData.materialsSpecs
      }, this).then(res =>{
        if(res.data.list.length > 0){
          let o = res.data.list[0].objJson;
          this.accordingObj[this.activeName] = JSON.parse(o);
        }else{
          this.accordingObj[this.activeName] = {};
        }
      })
    },
    //设置默认值
    setDefaultVal(val){
      this.setDefaultTime(val);
      console.log(val)
      if(val.FLY_ASK_PARAM_FMH_HXZS?.hxzs7dInfo){
        val.FLY_ASK_PARAM_FMH_HXZS?.hxzs7dInfo?.syjskysy.forEach(item =>{
          if(item.symj == ''){
            item.symj = '1600'
          }
        })
      }
      if(val.FLY_ASK_PARAM_FMH_HXZS?.hxzs28dInfo){
        val.FLY_ASK_PARAM_FMH_HXZS?.hxzs28dInfo?.syjskysy.forEach(item =>{
          if(item.symj == ''){
            item.symj = '1600'
          }
        })
      }
      
      if(val.FLY_ASK_PARAM_FMH_SYHL?.syhlList){
        val.FLY_ASK_PARAM_FMH_SYHL.syhlList.forEach(item =>{
          if(item.hsxs == ''){
            item.hsxs = '0.343'
          }
        })
      }
      
      if(val.FLY_ASK_PARAM_FMH_SYHL?.syzl == ''){
        val.FLY_ASK_PARAM_FMH_SYHL.syzl = '0.05'
      }
      if(val.FLY_ASK_PARAM_FMH_YLYHG?.syzl == ''){
        val.FLY_ASK_PARAM_FMH_YLYHG.syzl = '0.05'
      }
      
      if(val.FLY_ASK_PARAM_FMH_XD?.xdInfo[0].jzxs == ''){
        val.FLY_ASK_PARAM_FMH_XD.xdInfo[0].jzxs = '1.10'
      }
      if(val.FLY_ASK_PARAM_FMH_XD?.xdInfo[1].jzxs == ''){
        val.FLY_ASK_PARAM_FMH_XD.xdInfo[1].jzxs = '1.10'
      }
      return val;
    },
    handleClick(tab, event){
      this.getAccordingToHand()
      switch (this.activeName){
        case 'FLY_ASK_PARAM_FMH_XD':
          this.activeInfoName = 'xdInfo'
          break;
        case 'FLY_ASK_PARAM_FMH_SSL':
          this.activeInfoName = 'sslInfo'
          break;
        case 'FLY_ASK_PARAM_FMH_XSLB':
          this.activeInfoName = 'xslbInfo'
          break;
        case 'FLY_ASK_PARAM_FMH_XD':
          this.activeInfoName = 'xdInfo'
          break;
        default:
          break;
      }
    },
    clearData(){
      this.activeName= 'quick';
      this.quickForm= {};
      this.quickData= [];
      this.batchForm= {};
      this.batchData= [];
    },
    handlePreview(file) {
      this.previewUrl = file.url
      this.imgViewerVisible = true;
    },
    onClose() {
      this.imgViewerVisible = false;
    },
  },
};
</script>
<style scoped lang="scss">
  .line{
    height: 1px;
    width: 100%;
    background: #E8E8E8;
    margin: 24px 0;
  }
  .info-form-title{
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 14px;
    color: #1F2329;
    line-height: 20px;
    text-align: left;
    font-style: normal;
    padding: 10px 0 4px;
    margin-bottom: 4px;
  }
  .info-form-piece{
    background: #F8F8F8;
    border-radius: 8px;
    padding: 16px 16px 8px 16px;
    // &:last-child{
    //   padding-bottom: 16px;
    // }
  }
  ::v-deep .custom-form .el-form .el-form-item{
    margin-bottom: 8px;
    margin-right: 10px;
    .el-form-item__content{
      flex: 1;
    }
  }
  ::v-deep .el-input-group__append, 
  ::v-deep .el-input-group__prepend{
    padding: 0 5px;
    text-align: center;
    width: 40px;
  }
  // ::v-deep .el-form-item__label {
  //   text-align: left;
  // }
  ::v-deep .pr0{
    .el-input__inner{
      padding-right: 0;
    }
  }
  ::v-deep .el-input-number.is-controls-right .el-input__inner{
    text-align: left;
  }
  ::v-deep .textspan .el-input__inner{
    background: transparent;
    border: none;
    padding: 0;
    color: #000;
    margin-left: -10px;
    margin-top: -2px;
  }
  
  
  ::v-deep .el-upload-list__item{
    transition: none !important; 
  }
  
  
  .el-row{
    margin-bottom: 24px;
    &:last-child{
      margin-bottom: 0;
    }
  }
  .mb16{
    margin-bottom: 16px;
  }
  .mt24{
    margin-top: 24px;
  }
  .el-tabs{
    height: 40px;
  }
  
  
  
  
  .table-box-kzf{
    width: 100%;
    background: #FFFFFF;
    border-radius: 4px;
    border: 1px solid #DDDFE6;
    
    .tb-title{
      height: 52px;
      background: #F8F8F8;
      border-radius: 4px 4px 0px 0px;
      border-bottom: 1px solid #DDDFE6;
      line-height: 52px;
      padding: 0 16px;
      font-weight: 600;
      font-size: 14px;
      color: #1F2329;
    }
    .tb-bottom{
      border-top: 1px solid #DDDFE6;
      padding: 16px;
    }
    .tb-left{
      line-height: 40px;
      border-right: 1px solid #DDDFE6;
      width: 72px;
      text-align: center;
    }
    .tb-left2{
      width: 118px;
      padding: 24px 0;
    }
    .tb-content{
      .tb-left,.el-form-item{
        padding-bottom: 8px;
      }
      .el-form-item{
        padding-left: 16px;
      }
      .tb-item:first-child{
        .tb-left,.el-form-item{
          padding-top: 24px;
        }
      }
      .tb-item:last-child{
        .tb-left,.el-form-item{
          padding-bottom: 24px;
        }
      }
    }
    .tb-center{
      padding: 24px 0;
      margin: 0 8px;
      &>div:first-child{
        margin-bottom: 8px;
      }
    }
    .tb-right{
      height: 88px;
      margin-top: 24px;
      border-left: 1px solid #DDDFE6;
      line-height: 40px;
      padding: 0 16px;
      &>p:first-child{
        margin-bottom: 8px;
      }
    }
  }
  
  
  .tag-btn{
    display: inline-block;
    cursor: pointer;
    height: 32px;
    border: 1px solid #F8F8F8;
    line-height: 30px;
    padding: 0 8px;
    background: #F8F8F8;
    border-radius: 4px;
    margin-right: 12px;
    font-weight: 500;
    box-sizing: border-box;
    &.tag-btn-succ{
      background: #BBFADA;
      color: #13D466;
      border: 1px solid #13D466;
    }
    &.tag-btn-warn{
      background: rgba(255,206,80,0.26);
      border: 1px solid #FCCB83;
      color: #FFB803;
    }
    &.tag-btn-error{
      background: rgba(255, 95, 88, 0.26);
      border: 1px solid #FF5F58;
      color: #FF5F58;
    }
    &.tag-btn-primary{
      background: rgba(31, 87, 179, .1);
      color: #1F57B3;
      border: 1px dashed rgba(31, 87, 179, .1);
    }
    &.tag-btn-disabled{
      color: rgba(153,153,153,0.85);
    }
    &.tag-btn-dotted{
      padding: 0 13px;
      border-style: dashed;
    }
    &.tag-btn-mini{
      font-size: 16px;
      height: 28px;
      line-height: 26px;
    }
    &:last-child{
      margin: 0;
    }
  }
</style>