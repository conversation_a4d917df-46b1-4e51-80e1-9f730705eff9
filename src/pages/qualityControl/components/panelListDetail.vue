<template>
  <div class="content-box">
    <div class="content flex-box flex-column ">
      <div class="g-card">
        <p class="gc-main pb16">{{taskData.rwdextraInfo?.frwno}} (配比编号：{{taskData.rwdextraInfo?.fphbNo}})<span class="red">{{ taskData.rwdextraInfo?.fzt}}</span></p>
        <!-- <p class="gc-main pb16">{{taskData.ftbj}}</p> -->
        <!-- <p>浇筑部位：{{taskData.rwdextraInfo.fjzbw}}</p> -->
        <div class="fl">
          <p>工程名称：{{taskData.rwdextraInfo?.fgcmc}}</p>
          <p>浇筑部位：{{taskData.rwdextraInfo?.fjzbw}}</p>
          <p>施工单位：{{taskData.rwdextraInfo?.fhtdw}}</p>
        </div>
        <div class="fl">
          <p>计划方量：{{taskData.rwdextraInfo?.fjhsl}}</p>
          <p>工程级别：{{taskData.rwdextraInfo?.fgcjb}}</p>
        </div>
        <div class="fl">
          <p>计划时间：{{taskData.rwdextraInfo?.fjhrq}}</p>
          <p>坍落度：{{taskData.rwdextraInfo?.ftld}}</p>
        </div>
        <div class="fl">
          <p>发货方量：{{taskData.fhquantity}}</p>
          <p>泵送方式：{{taskData.rwdextraInfo?.fjzfs}}</p>
        </div>
        <div class="fl">
          <p>累计车数：{{taskData.rwdextraInfo?.fljcs}}</p>
          <p>生产拌台：{{taskData.rwdextraInfo?.fscbt}}</p>
        </div>
        <div class="cb"></div>
      </div>

      <div class="h40"></div>
      <p class="title">小票列表</p>
      <div class="region-con">
        <el-row :gutter="32" style="height: 100%;">
          <el-table
            :data="receiptData"
            empty-text="暂无数据"
            height="100%"
            width="100%">
            <template v-for="item in receiptColumn">
              <af-table-column
                :key="item.prop"
                :prop="item.prop" 
                :label="item.label" 
                :formatter="item.formatter"
                :fixed="item.fixed" 
                :width="item.width || ''"
                align="center" 
              >
                
              </af-table-column>
            </template>
            <af-table-column width="150" label="操作" fixed="right" align="center" key="handle" :resizable="false">
              <template slot-scope="scope">
                <el-button type="text" size="small" style="color: #ff0000;" @click="deleteReceipt(scope.row)">删除</el-button>
              </template>
            </af-table-column>
          </el-table>
          <!-- <el-col :span="24">
            <div class="receipt-list">
              <div class="receipt-item" v-for="(item,index) in receiptData">
                <p>{{item.itemorderno}}</p>
                <p>生产<span>{{item.totalscquantity}}</span>发货<span>{{item.fhquantity}}</span></p>
                <p><img src="@/assets/images/icon_car_blue.png" alt="" />
                  <span>{{item.carnumber}}，{{item.carLicenseNo}}</span>{{item.drivername}}</p>
              </div>
            </div>
          </el-col> -->

          <!-- <el-col :span="16">
            <div>
              <p class="title-public">生产视频：</p>
              <div class="video-box clearfloat">
                <div class="video-item">
                  <video class="art-video"
                    controls
                    preload="auto" crossorigin="anonymous" autoplay="" 
                    src="https://upload.wikimedia.org/wikipedia/commons/8/87/Schlossbergbahn.webm">
                  </video>
                  <p>dsaf</p>
                </div>
                <div class="cb"></div>
              </div>
              <p class="title-public">装车视频：</p>
              <div class="video-box clearfloat">
                <div class="video-item">
                  <video class="art-video"
                    controls
                    preload="auto" crossorigin="anonymous" autoplay="" 
                    src="https://upload.wikimedia.org/wikipedia/commons/8/87/Schlossbergbahn.webm">
                  </video>
                  <p>dsaf</p>
                </div>
                <div class="cb"></div>
              </div>
            </div>
          </el-col> -->
        </el-row>
      </div>

      <!-- 工单列表 -->
      <div class="h40"></div>
      <p class="title">工单列表</p>
      <div class="region-con">
        <el-row :gutter="32" style="height: 100%;">
          <el-table
            :data="workTableDatas"
            empty-text="暂无数据"
            height="100%"
            width="100%"
            style="width: 100%; overflow:auto;">
            <template v-for="item in engDetailColumn">
              <af-table-column
                v-if="item.prop == 'orderStatusText'"
                :key="item.prop"
                :prop="item.prop" 
                :label="item.label" 
                :formatter="item.formatter"
                :fixed="item.fixed" 
                :width="item.width || ''"
                align="center" 
              >
                <template slot-scope="scope">
                  <!-- 工单状态  0-待处理  1-接受  2-完成  3-拒绝 -->
                  <el-row class="cell-state">
                    <label v-if="scope.row.orderStatusText == '待处理'" class="rda-task-state dqr">{{ scope.row.orderStatusText }}</label>
                    <label v-if="scope.row.orderStatusText == '接受'" class="rda-task-state ddd">已接受</label>
                    <label v-if="scope.row.orderStatusText == '完成'" class="rda-task-state yqx">已完成</label>
                    <label v-if="scope.row.orderStatusText == '拒绝'" class="rda-task-state yqx">已拒绝</label>
                  </el-row>
                </template>
              </af-table-column>

              <af-table-column
                v-else
                :key="item.prop"
                :prop="item.prop" 
                :label="item.label" 
                :formatter="item.formatter"
                :fixed="item.fixed" 
                :width="item.width || ''"
                show-overflow-tooltip
                align="center" 
              />
            </template>

            <af-table-column width="150" label="操作" align="center" key="handle" :resizable="false">
              <template slot-scope="scope">
                <el-button type="text" size="small" @click="gotoWorkDetail(scope.row)">查看详情</el-button>
              </template>
            </af-table-column>
          </el-table>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script>
import { engDetailColumn } from '../../engineeringService/config.js'
export default {
  components: {},
  data() {
    return {
      taskData: {},
      loading: false,
      receiptData: [],
      engDetailColumn: engDetailColumn,
      receiptColumn: [
        {
          label: '小票编号',
          prop: 'itemorderno',
        },
        {
          label: '车号',
          prop: 'carnumber',
        },
        {
          label: '司机名称',
          prop: 'drivername',
        },
        {
          label: '车牌号',
          prop: 'carLicenseNo',
        },
        {
          label: '施工部位',
          prop: 'jzbw',
        },
        {
          label: '拌台',
          prop: 'mixtable',
        },
        {
          label: '生产方量',
          prop: 'scquantity',
        },
        {
          label: '发货方量',
          prop: 'fhquantity',
        },
        {
          label: '代表数量',
          prop: 'behalfNumber',
        },
        {
          label: '生产时间',
          prop: 'starttime',
        },
        {
          label: '发货员',
          prop: 'fhy',
        },
      ],
      workTableDatas: [],
      experimentType: '',
    };
  },
  
  created: function() {
    this.experimentType = this.$route.query.experimentType || '';

    this.getTaskInfo(this.$route.query.experimentId);
  },
  methods: {
    initData(row){
      
    },
    //获取任务信息
    getTaskInfo(id){
      this.$api.getTaskInfo(`experimentId=${id}`, this).then(res => {
        this.loading = false;
        if(res.succ){
          this.taskData = res.data.filter(item => item.frwdh == this.$route.query.frwdh)[0]
          this.showWorkOrders();
          this.getReceiptInfo(this.$route.query.experimentId);
        }else{
          this.$message.error(res.msg || '查询失败')
        }
      })
    },
    //获取小票信息
    getReceiptInfo(id){
      let parma = `experimentId=${id}`;
      if (this.experimentType == '7') {
        parma = parma + `&frwdh=${this.$route.query.frwdh || this.taskData.frwdh}`;
      }
      this.$api.getReceiptInfo(parma, this).then(res => {
        this.loading = false;
        if(res.succ){
          this.receiptData = res.data;
        }else{
          this.$message.error(res.msg || '查询失败')
        }
      })
    },

    // 根据任务单展示异常工单
    showWorkOrders() {
      this.$api['getWorkOrderAllList']({trwdId: this.taskData.frwdh}, this).then(res => {
        if(res.succ){
          this.workTableDatas = res.data.list;
        }else{
          this.$message.error(res.msg || '查询失败')
        }
      })
    },
    // 前往工单详情
    gotoWorkDetail(event) {
      this.$router.push({
        path: '/engineeringService/workOrderDetial',
        query: {
          id: event.id
        }
      })
    },

    deleteReceipt(row){
      this.$confirm("确定要删除吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        //删除
        this.$api.deleteByExperimentIdAndItemorderId({
          experimentId: this.$route.query.experimentId,
          itemorderId: row.itid
        }, this).then((res) => {
          if (res.succ) {
            this.$message({
              showClose: true,
              message: "删除成功",
              type: "success",
            });
            this.getReceiptInfo(this.$route.query.experimentId);
          }
        });
      });
    },
  },
};
</script>

<style scoped lang="scss">
  
  .content-box{
    padding: 16px;
    overflow: scroll;
  }
  .content{
    width: 100%;
    height: 100%;
    padding: 16px;
    background: #FFFFFF;
    border-radius: 16px;
    .title{
      font-size: 16px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      color: #1F2329;
      line-height: 22px;
      letter-spacing: 1px;
      padding-bottom: 8px;
      border-bottom: 1px solid #E8E8E8;
      margin-bottom: 8px;
    }
    .region-con{
      height: 300px;
      .el-row,.el-col{
        height: 100%;
      }
      
      .rc-item{
        padding-top: 8px;
        height: 28px;
        span{
          color: $color-txt;
          line-height: 20px;
          letter-spacing: 1px;
          padding-right: 16px;
        }
        .el-button{
          height: 20px;
          padding: 0;
        }
      }
    }
  }
  .h40{
    height: 40px;
    width: 100%;
  }
  .cc-info{
    margin: 0 0px 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #E8E8E8;
    &:last-child{
      margin-bottom: 0;
      border: none;
      padding: 0;
    }
  }
  .pb16{
    padding-bottom: 16px !important;
  }
  
  .g-card{
    width: 100%;
    padding: 16px 0 0;
    margin-bottom: 0;
    & > div{
      margin-right: 62px;
    }
    p{
      color: $color-txt;
      line-height: 20px;
      letter-spacing: 1px;
      padding-bottom: 8px;
      &:last-child{
        padding: 0;
      }
    }
    .gc-main{
      font-size: 16px;
      font-weight: 600;
      color: #1F2329;
      line-height: 22px;
      letter-spacing: 1px;
      span{
        line-height: 20px;
        height: 20px;
        background: #FFE9D1;
        border-radius: 10px;
        font-size: 12px;
        font-weight: 600;
        display: inline-block;
        vertical-align: middle;
        margin-top: -2px;
        color: #FF7B2F;
        margin-left: 16px;
        padding: 0 7px;
        &.succ{
          color: $color-success;
          background: #DDEFEA;
        }
        &.red{
          color: #FF2F2F;
          background: #FFE9E9;
        }
      }
    }
  }
  
  .receipt-list{
    width: 100%;
    height: 100%;
    background: #EFF1F2;
    border-radius: 8px;
    padding: 16px;
    overflow-y: auto;
    display: flex;
    flex-wrap: wrap;
    gap: 16px 8px;
    align-content: flex-start;
    .receipt-item{
      // width: calc(50% - 4px);
      width: calc(30% - 4px);
      height: 106px;
      background: #FFFFFF;
      border-radius: 8px;
      padding: 8px;
      p{
        &:nth-child(1){
          height: 22px;
          font-size: 16px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 600;
          color: #1F2329;
          line-height: 22px;
          letter-spacing: 1px;
        }
        &:nth-child(2){
          padding: 16px 0 7px;
          color: #1F2329;
          line-height: 25px;
          height: 48px;
          letter-spacing: 1px;
          span{
            font-size: 16px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 600;
            padding: 0 16px 0 4px;
          }
        }
        &:nth-child(3){
          height: 20px;
          font-size: 14px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #6A727D;
          line-height: 20px;
          letter-spacing: 1px;
          span{
            padding: 0 16px 0 4px;
          }
          img{
            width: 18px;
            height: 18px;
            display: inline-block;
            vertical-align: middle;
            margin-top: -2px;
          }
        }
      }
    }
  }
  
  
  .video-box{
    width: 100%;
    overflow-x: auto;
    padding-top: 8px;
    border-bottom: 1px solid #E8E8E8;
    margin-bottom: 24px;
    height: 185px;
    &:last-child{
      border: none;
    }
    .video-item{
      width: 220px;
      height: 140px;
      margin-right: 8px;
      float: left;
      .art-video{
        width: 100%;
      }
      p{
        height: 20px;
        font-size: 14px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #1F2329;
        line-height: 20px;
        letter-spacing: 1px;
        text-align: center;
        margin-top: 8px;
        margin-bottom: 24px;
      }
    }
  }

  .cell-state {
    .rda-task-state {
        display: inline-block;
        width: 43px;
        height: 18px;
        line-height: 18px;
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #FFFFFF;
        text-align: center;
        background: #1F7AFF;
        border-radius: 2px;
    }
    .dqr {
        background: #DC3290;
    }
    .ddd {
        background: #3369FF;
    }
    .dwc {
        background: #1FAE66;
    }
    .yqx {
        background: #D6D6D6;
    }
    .yjj {
        background: #ADAA00;
    }
    .ywc {
        background: #515157;
    }
  }
</style>