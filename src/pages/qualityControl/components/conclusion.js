//  碎石
// 一级（I级）：针片状颗粒含量0-10%视为单项结论合格，否则单项结论为不合格
// 二级（II级）：针片状颗粒含量0-20%视为单项结论合格，否则单项结论为不合格
// 三级（III级）：针片状颗粒含量0-30%视为单项结论合格，否则单项结论为不合格
// 卵石
// 一级（I级）：针片状颗粒含量0-12%视为单项结论合格，否则单项结论为不合格
// 二级（II级）：针片状颗粒含量0-14%视为单项结论合格，否则单项结论为不合格
// 三级（III级）：针片状颗粒含量0-16%视为单项结论合格，否则单项结论为不合格
export const conclusion_CGL_YSZZB_ss = {
  '1': {
    min: 0,
    max: 10,
    txt:''
  },
  '2': {
    min: 0,
    max: 20,
    txt:''
  },
  '3': {
    min: 0,
    max: 30,
    txt:''
  }
}
export const conclusion_CGL_YSZZB_ls = {
  '1': {
    min: 0,
    max: 12,
    txt:''
  },
  '2': {
    min: 0,
    max: 14,
    txt:''
  },
  '3': {
    min: 0,
    max: 16,
    txt:''
  }
}
// 一级（I级）：针片状颗粒含量5%视为单项结论合格，否则单项结论为不合格
// 二级（II级）：针片状颗粒含量10%视为单项结论合格，否则单项结论为不合格
// 三级（III级）：针片状颗粒含量15%视为单项结论合格，否则单项结论为不合格
export const conclusion_CGL_ZPZKLHL = {
  '1': {
    min: 0,
    max: 5,
    txt:'I级：针片状颗粒含量0-5%视为单项结论合格'
  },
  '2': {
    min: 0,
    max: 10,
    txt:'II级：针片状颗粒含量0-10%视为单项结论合格'
  },
  '3': {
    min: 0,
    max: 15,
    txt:'III级：针片状颗粒含量0-15%视为单项结论合格'
  }
}



// 参考细骨料含泥量试验
// 一级（I级）：含泥量0-3%视为单项结论合格，否则单项结论为不合格
// 二级（II级）：含泥量0-5%视为单项结论合格，否则单项结论为不合格
// 三级（III级）：含泥量0-8%视为单项结论合格，否则单项结论为不合格
export const conclusion_CGL_HNL = {
  '1': {
    min: 0,
    max: 0.5,
    txt:'I级：含泥量0-3%视为单项结论合格'
  },
  '2': {
    min: 0,
    max: 5,
    txt:'II级：含泥量0-5%视为单项结论合格'
  },
  '3': {
    min: 0,
    max: 8,
    txt:'III级：含泥量0-8%视为单项结论合格'
  }
}
// 参考细骨料泥块含量试验
// 一级（I级）：泥块含量0-0%视为单项结论合格，否则单项结论为不合格
// 二级（II级）：泥块含量0-2%视为单项结论合格，否则单项结论为不合格
// 三级（III级）：泥块含量0-0.5%视为单项结论合格，否则单项结论为不合格
export const conclusion_CGL_NKHL = {
    '1': {
      min: 0,
      max: 1,
      txt:'I级：泥块含量0-0%视为单项结论合格'
    },
    '2': {
      min: 0,
      max: 2,
      txt:'II级：泥块含量0-2%视为单项结论合格'
    },
    '3': {
      min: 0,
      max: 0.5,
      txt:'III级：泥块含量0-0.5%视为单项结论合格'
    }
}




// 单项结论：根据样品等级来判定（细度模数后端校验），不可编辑
// 一级（I级，粗砂）： 含泥量0-1%视为单项结论合格，否则单项结论为不合格
// 二级（II级，中砂）：含泥量0-3%视为单项结论合格，否则单项结论为不合格
// 三级（III级，细砂）：含泥量0-5%视为单项结论合格，否则单项结论为不合格
export const conclusion_XGL_HNL = {
  '1': {
    min: 0,
    max: 0,
    txt:'I级：含泥量0-0%视为单项结论合格'
  },
  '2': {
    min: 0,
    max: 1,
    txt:'II级：含泥量0-1%视为单项结论合格'
  },
  '3': {
    min: 0,
    max: 2,
    txt:'III级：含泥量0-2%视为单项结论合格'
  }
}
// 细骨料泥块含量试验
// 一级（I级，粗砂）：含泥量0-0%视为单项结论合格，否则单项结论为不合格
// 二级（II级，中砂）：含泥量0-1%视为单项结论合格，否则单项结论为不合格
// 三级（III级，细砂）：含泥量0-2%视为单项结论合格，否则单项结论为不合格
export const conclusion_XGL_NKHL = {
    '1': {
      min: 0,
      max: 0,
      txt:'I级：泥块含量0-0%视为单项结论合格'
    },
    '2': {
      min: 0,
      max: 1,
      txt:'II级：泥块含量0-1%视为单项结论合格'
    },
    '3': {
      min: 0,
      max: 2,
      txt:'III级：泥块含量0-2%视为单项结论合格'
    }
}


// 一级（I级，粗砂）：MB值0-0.5%视为单项结论合格，否则单项结论为不合格
// 二级（II级，中砂）：MB值0-1%视为单项结论合格，否则单项结论为不合格
// 三级（III级，细砂）：MB值0-1.4%视为单项结论合格，否则单项结论为不合格
export const conclusion_XGL_MBZ = {
    '1': {
      min: 0,
      max: 0.5,
      txt:'I级：MB值0-0.5%视为单项结论合格'
    },
    '2': {
      min: 0,
      max: 1,
      txt:'II级：MB值0-1%视为单项结论合格'
    },
    '3': {
      min: 0,
      max: 1.4,
      txt:'III级：MB值0-1.4%视为单项结论合格'
    }
}



// 一级（I级）：氯离子含量0-0.01%视为单项结论合格，否则单项结论为不合格
// 二级（II级）：氯离子含量0-0.02%视为单项结论合格，否则单项结论为不合格
// 三级（III级）：氯离子含量0-0.06%视为单项结论合格，否则单项结论为不合格
export const conclusion_XGL_LLZHL = {
  '1': {
    min: 0,
    max: 0.01,
    txt:'I级：氯离子含量0-0.01%视为单项结论合格'
  },
  '2': {
    min: 0,
    max: 0.02,
    txt:'II级：氯离子含量0-0.02%视为单项结论合格'
  },
  '3': {
    min: 0,
    max: 0.06,
    txt:'III级：氯离子含量0-0.06%视为单项结论合格'
  }
}

// 一级（I级，粗砂）：贝壳含量0-1%视为单项结论合格，否则单项结论为不合格
// 二级（II级，中砂）：贝壳含量0-2%视为单项结论合格，否则单项结论为不合格
// 三级（III级，细砂）：贝壳含量0-2%视为单项结论合格，否则单项结论为不合格
export const conclusion_XGL_BKHL = {
  '1': {
    min: 0,
    max: 1,
    txt:'I级：贝壳含量0-1%视为单项结论合格'
  },
  '2': {
    min: 0,
    max: 2,
    txt:'II级：云母含量0-2%视为单项结论合格'
  },
  '3': {
    min: 0,
    max: 2,
    txt:'III级：贝壳含量0-2%视为单项结论合格'
  }
}

// 一级（I级，粗砂）：云母含量0-1%视为单项结论合格，否则单项结论为不合格
      // 二级（II级，中砂）：云母含量0-2%视为单项结论合格，否则单项结论为不合格
      // 三级（III级，细砂）：云母含量0-2%视为单项结论合格，否则单项结论为不合格
export const conclusion_XGL_YMHL = {
  '1': {
    min: 0,
    max: 1,
    txt:'I级：云母含量0-1%视为单项结论合格'
  },
  '2': {
    min: 0,
    max: 2,
    txt:'II级：云母含量0-2%视为单项结论合格'
  },
  '3': {
    min: 0,
    max: 2,
    txt:'III级：云母含量0-2%视为单项结论合格'
  }
}

// 一级（I级，粗砂）：压碎指标0-20%视为单项结论合格，否则单项结论为不合格
// 二级（II级，中砂）：压碎指标0-25%视为单项结论合格，否则单项结论为不合格
// 三级（III级，细砂）：压碎指标0-30%视为单项结论合格，否则单项结论为不合格
export const conclusion_XGL_YSZZB = {
  '1': {
    min: 0,
    max: 20,
    txt:'I级：压碎指标0-20%视为单项结论合格，否则单项结论为不合格'
  },
  '2': {
    min: 0,
    max: 25,
    txt:'II级：压碎指标0-25%视为单项结论合格，否则单项结论为不合格'
  },
  '3': {
    min: 0,
    max: 30,
    txt:'III级：压碎指标0-30%视为单项结论合格，否则单项结论为不合格'
  }
}


// I级：细度平均值0-12，视为合格，否则视为不合格
// II级：细度平均值0-20，视为合格，否则视为不合格
// III级：细度平均值0-45，视为合格，否则视为不合格
export const conclusion_FMH_XD = {
  '1': {
    min: 0,
    max: 12,
    txt:'I级：细度平均值0-12，视为合格，否则视为不合格'
  },
  '2': {
    min: 0,
    max: 20,
    txt:'II级：细度平均值0-20，视为合格，否则视为不合格'
  },
  '3': {
    min: 0,
    max: 45,
    txt:'III级：细度平均值0-45，视为合格，否则视为不合格'
  }
}
// 结论：
// I级：烧失量平均值0-5，视为合格，否则视为不合格
// II级：烧失量平均值0-8，视为合格，否则视为不合格
// III级：烧失量平均值0-15，视为合格，否则视为不合格
export const conclusion_FMH_SSL = {
  '1': {
    min: 0,
    max: 5,
    txt:''
  },
  '2': {
    min: 0,
    max: 8,
    txt:''
  },
  '3': {
    min: 0,
    max: 15,
    txt:''
  }
}

// 单项结论：
// I级：需水量比小于95，视为合格，否则视为不合格
// II级：需水量比小于105，视为合格，否则视为不合格
// III级：需水量比小于115，视为合格，否则视为不合格
// 粉煤灰三氧化硫公式详解
export const conclusion_FMH_XSLB = {
  '1': {
    min: 0,
    max: 95,
    txt:''
  },
  '2': {
    min: 0,
    max: 105,
    txt:''
  },
  '3': {
    min: 0,
    max: 115,
    txt:''
  }
}


// 判定：
// 7天
// S105：大于95%视为合格，否则视为不合格
// S95：大于70%视为合格，否则视为不合格
// S75：大于55%视为合格，否则视为不合格
// 28天
// S105：大于105%视为合格，否则视为不合格
// S95：大于95%视为合格，否则视为不合格
// S75：大于75%视为合格，否则视为不合格
export const conclusion_KZF_HXZS_7d = {
  '1': {
    min: 95,
    max: 10000000,
    txt:''
  },
  'S105': {
    min: 95,
    max: 10000000,
    txt:''
  },
  'S95': {
    min: 70,
    max: 10000000,
    txt:''
  },
  'S75': {
    min: 55,
    max: 10000000,
    txt:''
  }
}
export const conclusion_KZF_HXZS_28d = {
  '1': {
    min: 105,
    max: 10000000,
    txt:''
  },
  'S105': {
    min: 105,
    max: 10000000,
    txt:''
  },
  'S95': {
    min: 95,
    max: 10000000,
    txt:''
  },
  'S75': {
    min: 75,
    max: 10000000,
    txt:''
  }
}