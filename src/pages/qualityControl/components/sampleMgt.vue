<template>
  <div class="cc-box">
    <h4>样品管理</h4>
    <div class="cc-info">
      <div class="search-box flex-box">
        <el-form ref="searchForm" :inline="true" :model="searchForm"
          :disabled="loading3">
          <el-form-item label="">
          	<el-input v-model="searchForm.sampleQuantity" clearable
              :disabled="!isEditQY"
              type="number"
              placeholder="抽样数量" 
              style="width: 150px" 
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
            >
              <template slot="append">{{experimentType == 7 ?'方':'克'}}</template>
            </el-input>
          </el-form-item>
          <el-form-item label="">
          	<el-input v-model="searchForm.sampleStatus" clearable
              :disabled="!isEditQY"
              placeholder="样品状态" 
              style="width: 90px" 
            />
          </el-form-item>
          <el-form-item label="" v-if="experimentType == 7">
          	<el-input v-model="searchForm.itemorderNo" clearable
              placeholder="小票编号"
              :disabled="!isEditQY"
              readonly="readonly"
              style="width: 150px" 
            />
            <el-button type="text" :disabled="!isEditQY" @click="selectInvoice">选择小票</el-button>
          </el-form-item>
          <el-form-item label="" v-else>
          	<el-input v-model="searchForm.waybillCode"
              placeholder="运单编号"
              :disabled="!isEditQY"
              readonly="readonly"
              style="width: 150px" 
            />
            <el-button type="text" :disabled="!isEditQY" @click="selectInvoice">选择运单</el-button>
          </el-form-item>
          
          <el-form-item label="">
            <span>车辆：{{searchForm.carnumber}}/{{searchForm.carLicenseNo}}，
            司机：{{searchForm.drivername}}/{{searchForm.tel}}，
              <span v-if="experimentType == 7">拌台：{{ searchForm.mixtable }}</span>
            </span>
          	<!-- <el-input v-model="searchForm.carNo" 
              clearable
              :disabled="!isEditQY"
              placeholder="车量号码" 
              style="width: 150px" 
            /> -->
          </el-form-item>
          <el-form-item v-if="experimentStatus != 3">
            <el-button v-if="!isEditQY" type="primary" @click="isEditQY = true">编辑</el-button>
            <el-button v-else type="primary" @click="handlesave()">保存抽样</el-button>
          </el-form-item>
          
          <div class="img-box" style="margin-top: 10px;">
            <el-upload
              :disabled="!isEditQY"
              :action="baseUrl + '/upload/file'"
              list-type="picture-card"
              :on-preview="handlePreview"
              :file-list="searchForm.img"
              :on-success="ksdjHandlePicSuccess"
              :on-remove="ksdjHandlePicRemove">
              <i class="el-icon-plus"></i>
            </el-upload>
          </div>
        </el-form>
        
        <!-- <el-button v-if="searchForm.id" type="primary" @click="handleSetTarget()">新增取样</el-button> -->
      </div>
      
      <!-- <el-form ref="searchForm" :inline="true" :model="searchForm" :disabled="loading3">
        <el-form-item class="fr">
          <el-button type="primary" :disabled="loading3" @click="handleFilter">保存抽样信息</el-button>
        </el-form-item>
      </el-form> -->
      
      <el-table :data="tableData" 
        v-loading="loading3" 
        class="mt16"
        border :max-height="500"
        ref="tableDataDom" 
        style="width: 100%;"
      >
      	
      	<el-table-column 
          v-for="item in tableColumn" 
          :key="item.prop" :prop="item.prop" 
          :label="item.label" 
          :fixed="item.fixed" 
          :width="item.width || ''"
          :formatter="item.formatter"
          align="center" 
          :resizable="false" 
        />
      	<el-table-column v-if="experimentStatus != 3" width="140" label="操作" align="center" key="handle" :resizable="false">
      		<template slot-scope="scope">
      			<el-button type="text" size="small" @click="handleSetTarget(scope.row)">编辑</el-button>
      			<el-button type="text" size="small" style="color: #ff0000;" @click="handleDel(scope.row)" col>删除</el-button>
      		</template>
      	</el-table-column>
      </el-table>
      <div class="mt16 mb4">
        <Pagination
          :total="total" 
          :pageNum="pageObj.pageNum" 
          :pageSize="pageObj.pageSize" 
          @getData="initData" 
        />
      </div>
    </div>
    
    <DialogForm
      :formContent="formContent"
      ref="dialogForm"
      @saveTarget="saveTargetD"
    >
    </DialogForm>
    
    <TableDialog @setData="setReceiptData" ref="tableDialog" :tableColumn="dialogTableColumn"></TableDialog>

    <image-viewer
      v-if="imgViewerVisible"
      :urlList="[previewUrl]"
      :on-close="onClose"></image-viewer>
  </div>
</template>

<script>

import { rangePrecondTableProp as tableColumn, receiptTableColumn, taskTableColumn } from "../config.js"
import Pagination from "@/components/Pagination/index.vue";
import TableDialog from "@/components/tableDialog.vue";
import DialogForm from "@/components/dialogForm.vue";
import getOpt from "@/common/js/getListData.js"
import ImageViewer from "element-ui/packages/image/src/image-viewer";

export default {
  name:'userMgt',
  components: {
    Pagination,
    TableDialog,
    DialogForm,
    ImageViewer
  },
  props: {
    id: {
      type: String | Number,
    },
    experimentType: {
      type: String | Number,
      default: 7
    },
    experimentStatus: {
      type: String | Number,
    },
    checkType: {
      // checkType == '1' ? '快检' : '批检'
      type: String | Number,
    },
  },
  watch: {
    id(newValue, oldValue) {
      if(newValue){
        //this.initData()
      }
    },
  },
  data() {
    return {
      baseUrl: process.env.NODE_ENV === 'development' ? "/api" : "/mes-platform",
      filePrefix: this.$store.state.loginStore.userInfo.filePrefix,
      addApi: 'setSampleExclusive',//'addSample',
      updateApi: 'setSampleExclusive',//'setSample',
      getListApi: 'getSampleExclusive',//'getSampleExclusive',//
      //QYdata: {},//取样数据
      isEditQY: true,
      loading3: false,
      searchForm: {
        sampleQuantity: 0.05,
        sampleStatus: '正常',
        itemorderNo: "",
        itemorderId: "",
        carnumber: "",
        carLicenseNo: "",
        drivername: "",
        tel: "",
        mixtable: ""
      },
      receiptTableColumn: receiptTableColumn,
      
      
      delApi: 'delSampleMore',
      tableColumn: tableColumn,
      pageObj: {
        pageNum: 1, // 页数
        pageSize: 5, // 条数
      },
      total: 1,
      tableData: [],
      
      activeRow: {},//编辑对象
      formContent: [],
      userOpt: [],
      sampleUseOpt: [],
      // rules: {},

      previewUrl: "",
      imgViewerVisible: false,
      
      dialogTableColumn: receiptTableColumn
    };
  },
  
  created() {
    //this.initData();
    this.setOptMethod();
    
  },
  methods: {
    handlePreview(file) {
      this.previewUrl = file.url
      this.imgViewerVisible = true;
    },
    onClose() {
      this.imgViewerVisible = false;
    },
    //图片C
    ksdjHandlePicSuccess(response, file, fileList) {
      this.searchForm.img = fileList.map(item =>{
        if(item.response){
          return item.response.data.filePath;
        }else{
          return item.url
        }
      })
    },
    ksdjHandlePicRemove(file, fileList) {
      this.searchForm.img = fileList.map(item =>{
        if(item.response){
          return item.response.data.filePath;
        }else{
          return item.url
        }
      })
    },
    
    
    async setOptMethod(){
      this.userOpt = await getOpt.getUserAll(this)
      let odata2 = JSON.parse(JSON.stringify(this.userOpt))

      let parm = {
        "testType": "CONCRETE", //这个对应的是混凝土 也是之前通过字典表查询返回的
      }
      if (this.checkType == 1) {
        parm["isQuick"] = 1
      }else{
        parm["isBatch"] = 1
      }
      this.sampleUseOpt = await getOpt.getTestProjectInfo(parm, this)
      
      odata2 = odata2.map(item => {
        item.value = item.label
        return item
      })
     
      this.formContent = [
        {
          type: 'input',
          label: '样品别称',
          prop: 'smokeNoAlias',
        },{
          type: 'select',
          label: '样品用途',
          prop: 'sampleUse',
          options: this.sampleUseOpt
        },{
          type: 'select',
          label: '抽样人',
          prop: 'samplePersonId',
          options: this.userOpt
        },{
          type: 'datetime',
          label: '抽样时间	',
          prop: 'sampleTime',
          format: 'yyyy-MM-dd',
          valueFormat: "yyyy-MM-dd HH:mm:ss"
        },{
          type: 'select',
          label: '处置人',
          prop: 'disposerPersonId',
          options: this.userOpt
        },{
          type: 'datetime',
          label: '处置时间	',
          prop: 'updateTime',
          format: 'yyyy-MM-dd',
          valueFormat: "yyyy-MM-dd HH:mm:ss"
        },{
          type: 'input',
          label: '存放位置',
          prop: 'sampleAddress',
        },
        {
          type: 'number',
          label: '抽样数量',
          prop: 'sampleQuantity',
        },
        
        
      ]
    
      
    },
    //获取抽样
    initDataCY(opageNum, opageSize){
      this.tableData = [];
      this.loading3 = true;
      if (opageNum) this.pageObj.pageNum = opageNum;
      if (opageSize) this.pageObj.pageSize = opageSize;
      const params ={
        ...this.pageObj,
        params: {
          experimentId: this.id,
        }
      }
      //params
      //获取列表
      this.$api.getSampleMoreList(params, this).then(res => {
        this.loading3 = false;
        if(res.succ){
          this.tableData = res.data.list.map(item =>{
            item.filePrefix = this.filePrefix;
            return item
          });
          this.total = res.data.total;
          this.$emit('setSampleTotal', res.data.total)
        }else{
          this.$message.error(res.msg || '查询失败')
        }
      })
    },
    //抽样
    saveTargetD(formData){
      formData.experimentId = this.id
      formData.samplePersonName = getOpt.getNameFromId(formData.samplePersonId, this.userOpt)
      formData.disposerPersonName = getOpt.getNameFromId(formData.disposerPersonId, this.userOpt)
      if (formData.img) {
        let objImg = ""
        objImg = formData.img.map(item => {
          if(item.url){
            return item.url.replace(this.filePrefix, '');
          }else{
            return item.replace(this.filePrefix, '');
          }
        })
        objImg = objImg.join(',');
        
        formData.sampleImg = objImg;
      }
      delete formData.img;
      
      if(formData.id){//修改this.activeRow
        this.$api.setSampleMore(formData, this).then((res) => {
          if (res.succ) {
            this.$message({
              showClose: true,
              message: "修改成功",
              type: "success",
            });
            this.isEditQY = false;
            this.initDataCY();
            this.$refs.dialogForm.handleClose();
          }
        });
      }else{
        this.$api.addSampleMore(formData, this).then((res) => {
          if (res.succ) {
            this.$message({
              showClose: true,
              message: "添加成功",
              type: "success",
            });
            this.isEditQY = false;
            this.initDataCY();
            this.$refs.dialogForm.handleClose();
          }
        });
      }
    },
    
    
    //删除
    handleDel(row){
      this.$confirm("删除后不能恢复，确定要删除吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        //删除
        this.$api[this.delApi]({
          id: row.id
        }, this).then((res) => {
          if (res.succ) {
            this.$message({
              showClose: true,
              message: "删除成功",
              type: "success",
            });
            if(this.tableData.length == 1 && this.pageObj.pageNum > 1){
              this.pageObj.pageNum = this.pageObj.pageNum -1
            }
            this.initDataCY();
          }
        });
      }).catch(() => {
        this.$message({
          type: "info",
          message: "已取消删除",
        });
      });
    },
    handleSetTarget(row){
      
      if(row.sampleImg){
        row.img = row.sampleImg.split(',').map(item => {
          return {
            url: row.filePrefix + item
          }
        }) 
      }else{
        row.img = [];
      }
      
      //this.activeRow = row;
      this.$refs.dialogForm.initData(row);
    },
    
    
    
    
    
    //选择小票车辆
    selectInvoice(init) {
      if(this.experimentType == 7){
        this.dialogTableColumn = receiptTableColumn;
        this.$refs.tableDialog.initData('getReceiptInfo', `experimentId=${this.id}`, init)
      }else{
        this.dialogTableColumn = taskTableColumn;
        this.$refs.tableDialog.initData('getTaskInfoMaterial', `experimentId=${this.id}`, init)
      }
    },
    //获取取样
    initData(opageNum, opageSize){
      this.searchForm = {
        sampleQuantity: 0.05,
        sampleStatus: '正常',
        itemorderNo: "",
        itemorderId: "",
        carnumber: "",
        carLicenseNo: "",
        drivername: "",
        tel: "",
        mixtable: ""
      },
      this.selectInvoice('init');
      this.loading3 = true;
      if (opageNum) this.pageObj.pageNum = opageNum;
      if (opageSize) this.pageObj.pageSize = opageSize;
        
      const params ={
        ...this.pageObj,
        params: {
          experimentId: this.id
        }
      }
      
      //params
      //获取列表
      this.$api[this.getListApi](`experimentId=${this.id}`, this).then(res => {
        this.loading3 = false;
        if(res.succ){
          if(res && res.data && res.data.id){
            const searchForm = {
              sampleQuantity: 0.05,
              sampleStatus: '正常',
              itemorderNo: "",
              itemorderId: "",
              carnumber: "",
              carLicenseNo: "",
              drivername: "",
              tel: "",
              mixtable: "",
            }
            
            if(res.data.sampleImg){
              res.data.img = res.data.sampleImg.split(',').map(item => {
                if(item.startsWith(this.filePrefix)){
                  return {
                    url: item
                  }
                }else{
                  return {
                    url: this.filePrefix + item
                  }
                }
              }) 
            }else{
              res.data.img = [];
            }
            
            this.searchForm = {...searchForm, ...res.data };
            this.searchForm.carnumber = res.data.carNo
            this.searchForm.carLicenseNo = res.data.carCard
            this.searchForm.drivername = res.data.driverName
            this.searchForm.tel = res.data.driverTel
            this.searchForm.mixtable = res.data.mixtable;

            this.isEditQY = false;
            this.initDataCY();
          }else{
            this.searchForm = {
              sampleQuantity: 0.05,
              sampleStatus: '正常',
              itemorderNo: "",
              itemorderId: "",
              carnumber: "",
              carLicenseNo: "",
              drivername: "",
              tel: "",
              mixtable: ""
            }
            this.isEditQY = true;
            this.tableData = [];
            this.total = 0;
            this.$emit('setSampleTotal',0)
          }
          //this.QYdata = res.data
          // this.tableData = res.data.list;
          // this.total = res.data.total;
        }else{
          this.$message.error(res.msg || '查询失败')
        }
      })
    },
    //选择小票车辆
    setReceiptData(val){
      console.log(val)
      // this.searchForm.itemorderNo = val.itemorderno
      // this.searchForm.itemorderId = val.id
      // this.searchForm.carnumber = val.carnumber
      // this.searchForm.carLicenseNo = val.carLicenseNo
      // this.searchForm.drivername = val.drivername
      // this.searchForm.tel = val.tel

      
      
      if(this.experimentType == 7){
        this.$set(this.searchForm, 'itemorderNo', val.itemorderno)
        this.$set(this.searchForm, 'itemorderId', val.id)
        this.$set(this.searchForm, 'carnumber', val.carnumber)
        this.$set(this.searchForm, 'carLicenseNo', val.carLicenseNo)
        this.$set(this.searchForm, 'drivername', val.drivername)
        this.$set(this.searchForm, 'tel', val.tel)
        this.$set(this.searchForm, 'mixtable', val.mixtable);
      }else{
        this.$set(this.searchForm, 'waybillCode', val.waybillCode)
        this.$set(this.searchForm, 'waybillId', val.waybillId)
        this.$set(this.searchForm, 'carnumber', val.vehicleNum)
        this.$set(this.searchForm, 'carLicenseNo', val.vehiclePlate)
        this.$set(this.searchForm, 'drivername', val.driverName)
        this.$set(this.searchForm, 'tel', val.driverTel)
      }
      
      // this.searchForm.weight = 111
    },
    
    getName(oVal, list){
      for(let i =0;i<list.length; i++){
        if(oVal == list[i].value){
          return list[i].label;
        }
      }
    },
    //取样
    handlesave(){
      let odata = JSON.parse(JSON.stringify(this.searchForm))
      this.saveTarget(odata);
    },
    //取样
    saveTarget(formData){
      formData.experimentId = this.id
      formData.carNo = formData.carnumber;
      formData.carCard = formData.carLicenseNo;
      formData.driverName = formData.drivername;
      formData.driverTel = formData.tel;
      delete formData.carnumber;
      if (formData.img) {
        let objImg = ""
        objImg = formData.img.map(item => {
          if(item.url){
            return item.url.replace(this.filePrefix, '');
          }else{
            return item.replace(this.filePrefix, '');
          }
        })
        objImg = objImg.join(',');
        
        formData.sampleImg = objImg;
      }
      if(formData.sampleStatus == ''){
        formData.sampleStatus = '正常'
      }
      if(formData.id){//修改this.activeRow
        this.$api[this.updateApi](formData, this).then((res) => {
          if (res.succ) {
            this.$message({
              showClose: true,
              message: "修改成功",
              type: "success",
            });
            this.isEditQY = false;
            this.initDataCY();
            
            //this.$parent.initData(this.id);
          }
        });
      }else{
        this.$api[this.addApi](formData, this).then((res) => {
          if (res.succ) {
            this.$message({
              showClose: true,
              message: "添加成功",
              type: "success",
            });
            this.isEditQY = false;
            this.initDataCY();
            
            this.$parent.showDetail({
              id: this.id
            });
          }
        });
      }
      
    },
  },
};
</script>

<style scoped lang="scss">
  .multi-form-item-box{
    padding: 0 0 4px 0;
    // display: flex;
    // justify-content: space-between;
    .el-select,.el-input{
      margin-right: 20px;
    }
  }
  ::v-deep .el-button{
    padding-left: 13px;
    padding-right: 13px;
  }
  .el-form-item{
    margin-bottom: 8px;
  }
  ::v-deep .el-form--inline{
    .el-form-item{
      margin-right: 20px;
      margin-bottom: 0;
      &:last-child{
        margin: 0;
      }
    }
  }
  ::v-deep .el-table{
    .expanded,.expanded:hover{
      background-color: #FFFBD9;
      
    }
    .expanded + tr{
      background-color: #FFFBD9;
      td{
        background-color: #FFFBD9;
      }
    }
    
    .table-child-box{
      margin: 16px;
      padding: 16px;
      background: #FFFFFF;
    }
  }
  
  .content-box{
    padding: 16px;
  }
  .content{
    width: 100%;
    height: 100%;
    padding: 16px;
    background: #FFFFFF;
    border-radius: 16px;
  }
  
  .search-box{
    padding-bottom: 16px;
    line-height: 40px;
  }
  ::v-deep .img-box{
    .is-disabled{
      & + div{
        display: none;
      }
    }
  }
  
  .cc-box{
    width: 100%;
    background: #FFFFFF;
    border-radius: 8px;
    padding: 16px 16px 12px;
    margin-bottom: 8px;
    &:last-child{
      margin-bottom: 0;
    }
    h4{
      font-size: 16px;
      font-weight: 600;
      color: #1F2329;
      line-height: 22px;
      letter-spacing: 1px;
      padding-bottom: 24px;
    }
    .cc-info{
      margin: 0 16px 16px;
      padding-bottom: 12px;
      border-bottom: 1px solid #E8E8E8;
      &:last-child{
        margin-bottom: 0;
        border: none;
        padding: 0;
      }
      p{
        color: #6A727D;
        line-height: 20px;
        letter-spacing: 1px;
        padding-bottom: 4px;
        span{
          margin-right: 60px;
        }
      }
      .cci-main{
        font-weight: 600;
        line-height: 20px;
        letter-spacing: 1px;
        color: #1F2329;
      }
    }
  }
  
  .pb16{
    padding-bottom: 16px !important;
  }
  
  ::v-deep .el-upload-list__item{
    transition: none !important; 
  }
</style>