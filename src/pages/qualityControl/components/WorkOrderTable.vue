<template>
    <el-dialog
      v-loading="loading"
      title=""
      :visible.sync="detailVisible"
      class="claim-dialog-box"
      :before-close="handleClose"
    >
      <el-table
        :data="tableData"
        empty-text="暂无数据"
        style="width: 100%; min-height: 300px;">
        <af-table-column
          v-for="item in tableColumn" 
          :key="item.prop" :prop="item.prop" 
          :label="item.label" 
          :formatter="item.formatter"
          :fixed="item.fixed" 
          :width="item.width || ''"
          align="center" 
        />
        <af-table-column width="150" label="操作" align="center" key="handle" :resizable="false">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="handleSetTarget(scope.row)">{{ optBtn }}</el-button>
          </template>
        </af-table-column>
      </el-table>
    </el-dialog>
  </template>
  
  <script>
  export default {
    name: "",
    props: {
      tableColumn: {
        type: Array,
        required: true,
      },
      optBtn: {
        type: String,
        default: '查看详情'
      }
    },
    data() {
      return {
        loading: false,
        tableData: [],
        detailVisible: false,
        getDataUrl: '',
        params:{},
      };
    },
    watch: {},
    computed: {},
    created() {},
    methods: {
      initData(getDataUrl,params, isShowDio) {
        //初始化
        if(isShowDio != 'init'){
          this.detailVisible = true;
        }
        this.getDataUrl = getDataUrl;
        this.params = params;
        this.onLoad();
      },
      onLoad(){
        this.loading = true;
        
        const params ={
          ...this.pageObj,
        }
        //获取列表
        this.$api[this.getDataUrl](this.params, this).then(res => {
          this.loading = false;
          if(res.succ){
            this.tableData = res.data.list;
          }else{
            this.$message.error(res.msg || '查询失败')
          }
        }).catch(err => {
          this.loading = false;
        })
      },
      handleClose() {
        //关闭弹簧
        this.detailVisible = false;
      },
      handleSetTarget(row){
        this.$emit('selectedData', row);
        this.handleClose()
      },
      confirmClaim(){
        
      }
    },
  };
  </script>
  
  <style lang="scss" scoped>
  
  .handle-btn {
    display: inline-block;
    margin-top: 2px;
  }
  
  ::v-deep .el-dialog {
    width: 80%;
  }
  </style>
  