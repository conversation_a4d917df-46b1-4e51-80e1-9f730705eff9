<template>
  <div>
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane v-if="quickData.length > 0" label="快检" name="quick"></el-tab-pane>
      <template v-if="batchData.length > 0">
        <el-tab-pane v-for="(item, index) in batchData" :key="item.testProjectCode" :label="item.testProjectName" :name="item.testProjectCode">
        </el-tab-pane>
      </template>
    </el-tabs>
    <div v-show="activeName === 'quick'">
      <el-form ref="quickForm"  :model="quickForm" :disabled="loading2 || experimentStatus == 3" label-width="120px">
        <el-row>
          <el-col :span="24" style="margin-bottom: 8px;">
            <el-form-item :label="item.testProjectName" v-for="item in quickData" :key="item.testProjectCode">
              <template v-if="item.testProjectName === '目测含水率'"
              >
                <el-input
                  v-manual-update
                  @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  v-model="quickForm[item.testProjectCode].objJson.muhsl" 
                  clearable
                  :placeholder="item.testProjectName"
                  style="width: 253px; margin-left: 14px;"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
              <template v-if="item.testProjectName === '目测含泥量'"
              >
                <el-input
                  v-manual-update
                  @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  v-model="quickForm[item.testProjectCode].objJson.muhnl" 
                  clearable
                  :placeholder="item.testProjectName"
                  style="width: 253px; margin-left: 14px;"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
              <template v-if="item.testProjectName === '目测泥块含量'"
              >
                <el-input
                  v-manual-update
                  @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  v-model="quickForm[item.testProjectCode].objJson.muhnkl" 
                  clearable
                  :placeholder="item.testProjectName"
                  style="width: 253px; margin-left: 14px;"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="图片：">
              <div class="cbo">
                <div class="img-box">
                  <el-upload
                    accept=".jpeg,.png,.jpg,.bmp,.gif"
                    :action="baseUrl + '/upload/file'"
                    list-type="picture-card"
                    :on-preview="handlePreview"
                    :file-list="quickForm.img"
                    :on-success="handlePicSuccess"
                    :on-remove="handlePicRemove">
                    <i class="el-icon-plus"></i>
                  </el-upload>
                  <!-- <span>抽样图片</span> -->
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <!-- 含泥量 泥块含量-->
    <div v-if="activeName === 'COARSE_AGGREGATE_PARAM_CGL_HNL' || activeName === 'COARSE_AGGREGATE_PARAM_CGL_HNKL'">
      <el-form :disabled="loading2 || experimentStatus == 3">
        <div class="mt16 jcrq-box">
          <el-form-item label="检测日期：">
            <el-date-picker
              type="date"
              v-model="batchForm[activeName].jcrq"
              placeholder="选择日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
        </div>
        <div v-for="(item, index) in batchForm[activeName][activeInfoName]" :key="index">
          <p class="info-form-title mt16">{{index == 0 ? '第一次试验' : '第二次试验'}}</p>
          <div class="info-form-piece">
            <el-row>
              <el-col :span="8">
                <el-form-item label="烘前试样质量：">
                  <el-input
                    v-manual-update
                    @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                    @input="val => setHnl(index)"
                    @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                    v-model="item.hqsyzl" 
                    clearable
                    placeholder="请输入"
                    :style="{'width': 200 + 'px'}"
                  >
                    <template slot="append">g</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="烘后试样质量：">
                  <el-input
                    v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                    @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                    v-model="item.hhsyzl"
                    @input="val => setHnl(index)"
                    clearable
                    placeholder="请输入"
                    :style="{'width': 200 + 'px'}"
                  >
                    <template slot="append">g</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="含泥量：">
                  <el-input
                    v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                    disabled
                    v-model="item.hnl"
                    clearable
                    placeholder="请输入"
                    :style="{'width': 200 + 'px'}"
                  >
                    <template slot="append">%</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>
        <div class="line"></div>
        <el-row>
          <el-col :span="8">
            <el-form-item label="平均值：">
              <el-input
                v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                v-model="batchForm[activeName].hnlpjz" 
                disabled
                :style="{'width': 200 + 'px'}"
              >
                <template slot="append">%</template>
              </el-input>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="16">
            <el-form-item label="单项结论：">
              <el-input
                v-model="batchForm[activeName].dxjl" 
                :disabled="dxjlDisabled"
                :style="{'width': 180 + 'px'}"
              >
              </el-input>
            </el-form-item>
          </el-col> -->
        </el-row>
        
        <el-row>
          <el-col :span="24">
            <el-form-item label="检测图片：">
              <div class="cbo">
                <div class="img-box">
                  <el-upload
                    accept=".jpeg,.png,.jpg,.bmp,.gif"
                    :action="baseUrl + '/upload/file'"
                    list-type="picture-card"
                    :on-preview="handlePreview"
                    :file-list="batchForm[activeName].img"
                    :on-success="handlePicSuccess"
                    :on-remove="handlePicRemove">
                    <i class="el-icon-plus"></i>
                  </el-upload>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    
     <!-- 针、片状颗粒含量 -->
    <div v-if="activeName === 'COARSE_AGGREGATE_PARAM_CGL_ZPZKLHL'">
      <el-form :disabled="loading2 || experimentStatus == 3" label-width="180px">
        <div class="mt16 jcrq-box">
          <el-form-item label="检测日期：">
            <el-date-picker
              type="date"
              v-model="batchForm[activeName].jcrq"
              placeholder="选择日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              :style="{'width': 180 + 'px'}"
            >
            </el-date-picker>
          </el-form-item>
        </div>
        <el-row>
          <el-col :span="12">
            <el-form-item label="试样质量：">
              <el-input
                v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="batchForm[activeName].syzl" 
                @input="val => zpzklhlChange()"
                clearable
                placeholder="请输入"
                :style="{'width': 180 + 'px'}"
              >
                <template slot="append">g</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="含针、片状颗粒总质量：">
              <el-input
                v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @input="val => zpzklhlChange()"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="batchForm[activeName].zpzklzzl"
                clearable
                placeholder="请输入"
                :style="{'width': 180 + 'px'}"
              >
                <template slot="append">g</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="针、片状颗粒含量：">
              <el-input
                v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                v-model="batchForm[activeName].zpzkklhl"
                disabled
                placeholder="请输入"
                :style="{'width': 180 + 'px'}"
              >
                <template slot="append">%</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="单项结论：">
              <el-input
                v-model="batchForm[activeName].dxjl"
                :disabled="dxjlDisabled"
                placeholder="请输入"
                :style="{'width': 180 + 'px'}"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="检测图片：">
              <div class="cbo">
                <div class="img-box">
                  <el-upload
                    accept=".jpeg,.png,.jpg,.bmp,.gif"
                    :action="baseUrl + '/upload/file'"
                    list-type="picture-card"
                    :on-preview="handlePreview"
                    :file-list="batchForm[activeName].img"
                    :on-success="handlePicSuccess"
                    :on-remove="handlePicRemove">
                    <i class="el-icon-plus"></i>
                  </el-upload>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <!-- 筛分析 -->
     <!-- v-if="activeName === 'COARSE_AGGREGATE_PARAM_CGL_SFX'" -->
    <div v-if="activeName === 'COARSE_AGGREGATE_PARAM_CGL_SFX'" class="custom-form">
      <el-form :disabled="loading2 || experimentStatus == 3" :inline="true" class="mt16">
        <div class="mt16 jcrq-box">
          <el-form-item label="检测日期：">
            <el-date-picker
              type="date"
              v-model="batchForm[activeName].jcrq"
              placeholder="选择日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
        </div>
        <div class="mt16 flex-box">
          <el-form-item label="最大粒径：" class="flex-item flex-box" >
            <el-input
              v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              v-model="batchForm[activeName].zdlj" 
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              placeholder="请输入"
            >
              <template slot="append">mm</template>
            </el-input>
          </el-form-item>
          <el-form-item label="试样重量：" class="flex-item flex-box" >
            <el-input
              v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @input="setFJSYAll"
              v-model="batchForm[activeName].syzl" 
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              placeholder="请输入"
            >
              <template slot="append">g</template>
            </el-input>
          </el-form-item>
        </div>
        
        <div class="table-box-kzf mt16">
          <div class="tb-title flex-box">
            <div class="tb-title-lef">筛孔公称直径</div>
            <div class="tb-title-con flex-item">
              <span>筛余量（g）</span>
            </div>
            <div class="tb-title-con flex-item"><span>分计筛余（%）</span></div>
            <div class="tb-title-con flex-item"><span>累计筛余（%）</span></div>
          </div>
          <div class="tb-content">
            <div class="flex-box tb-item" style="margin-bottom: 20px;"v-for="(item, index) in batchForm[activeName].sfxInfo" :key="index">
              <div class="tb-left">{{item.skgjzj}}</div>
              <div class="flex-item">
                <el-form-item label="筛余量" class="flex-item">
                  <el-input
                    v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                    v-model="item.syl"
                    @input="val => setFJSYAll()"
                    @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                    placeholder="请输入"
                  >
                  </el-input>
                </el-form-item>
              </div>
              <div class="flex-item">
                <el-form-item label="分计筛余：" class="flex-item">
                  <el-input
                    v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                    v-model="item.fjsy"
                    @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                    disabled
                  >
                  </el-input>
                </el-form-item>
              </div>
              <div class="flex-item">
                <el-form-item label="累计筛余：" class="flex-item">
                  <el-input
                    v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                    v-model="item.ljsy"
                    disabled
                    @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                    placeholder="请输入"
                  >
                  </el-input>
                </el-form-item>
              </div>
            </div>
          </div>
        </div>
        
        <div class="mt16">
          <el-form-item label="单项结论：" class="flex-item flex-box" >
            <el-input
              v-model="batchForm[activeName].dxjl" 
              :disabled="dxjlDisabled"
            >
            </el-input>
          </el-form-item>
        </div>
        <!-- <div class="table-box-kzf mt16">
          <div class="tb-title">
            <span @click="setKljChart">颗粒级配分析图</span>
            <span>1,23</span>
          </div>
          <div class="tb-content">
            <div id="kljptfx" style="height: 400px;">
              
            </div>
          </div>
        </div> -->
        
        <el-row class="mt16">
          <el-col :span="24">
            <el-form-item label="检测图片：">
              <div class="cbo">
                <div class="img-box">
                  <el-upload
                    accept=".jpeg,.png,.jpg,.bmp,.gif"
                    :action="baseUrl + '/upload/file'"
                    list-type="picture-card"
                    :on-preview="handlePreview"
                    :file-list="batchForm[activeName].img"
                    :on-success="handlePicSuccess"
                    :on-remove="handlePicRemove">
                    <i class="el-icon-plus"></i>
                  </el-upload>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    
    <!-- 表观密度 COARSE_AGGREGATE_PARAM_CGL_BGMD-->
    <div v-if="activeName === 'COARSE_AGGREGATE_PARAM_CGL_BGMD'" class="custom-form">
      <el-form :disabled="loading2 || experimentStatus == 3" :inline="true" label-width="200px">
        
        <div class="mt16 flex-box">
          <!-- <el-form-item label="取样数量：" class="flex-item flex-box" label-width="110px">
            <el-input
              v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].qyzl" 
              placeholder="请输入(默认660克)"
              :style="{'width': 100 + 'px'}"
            >
              <template slot="append">g</template>
            </el-input>
          </el-form-item> -->
          <el-form-item label="检测日期：" class="flex-item flex-box" label-width="100px">
            <el-date-picker
              type="date"
              v-model="batchForm[activeName].jcrq"
              placeholder="选择日期"
              format="yyyy-MM-dd"
              style="width: 135px;"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
          
          <el-form-item class="flex-item flex-box" label="水温：" label-width="80px">
            <!-- <el-input
              v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].md"
              disabled
            >
              <template slot="append">℃</template>
            </el-input> -->
            <el-input-number v-model="batchForm[activeName].sw"  
              :max="25" :min="15"
              @input="setSZXS"
              style="width: 135px;"
              :step="1" step-strictly>
            </el-input-number>
    
          </el-form-item>
          <el-form-item class="flex-item flex-box" label="温度修正系数：" label-width="110px">
            <el-input
              v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].xzxs"
              style="width: 100px;"
              disabled
            >
            </el-input>
          </el-form-item>
          <el-form-item label="水表观密度：" class="flex-item flex-box"  label-width="110px">
            <el-input
              v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].sbgmd" 
              style="width: 135px;"
              disabled
              placeholder="请输入"
            >
              <template slot="append">kg/m³</template>
            </el-input>
          </el-form-item>
        </div>
        <div v-for="(item, index) in batchForm[activeName].bgmdInfo" :key="index">
          <p class="info-form-title mt16">{{index == 0 ? '第一次试验' : '第二次试验'}}</p>
          <div class="info-form-piece">
            <div class="flex-box">
              <el-form-item label="烘干试样质量：" class="flex-item flex-box">
                <el-input
                  v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  @input="val => setBGMD(index)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  v-model="item.hgsyzl" 
                  placeholder="请输入(默认300克)"
                >
                  <template slot="append">g</template>
                </el-input>
              </el-form-item>
              <el-form-item class="flex-item flex-box" label="总质量(水+吊篮)：">
                <el-input
                  v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  @input="val => setBGMD(index)"
                  v-model="item.zzlsdl"
                  placeholder="请输入"
                >
                  <template slot="append">g</template>
                </el-input>
              </el-form-item>
            </div>
            <div class="flex-box">
              <el-form-item class="flex-item flex-box" label="总质量(水+试样+吊篮)：">
                <el-input
                  v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  @input="val => setBGMD(index)"
                  v-model="item.zzlssydl"
                  placeholder="请输入"
                >
                  <template slot="append">g</template>
                </el-input>
              </el-form-item>
              <el-form-item label="结果：" class="flex-item flex-box">
                <el-input
                  v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  disabled
                  v-model="item.jg"
                >
                  <template slot="append">kg/m³</template>
                </el-input>
              </el-form-item>
            </div>
          </div>
        </div>
        <div class="line"></div>
        <el-row>
          <el-col :span="10">
            <el-form-item class="flex-box" label="平均值：">
              <el-input
                v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                disabled
                v-model="batchForm[activeName].pjz" 
                :style="{'width': 200 + 'px'}"
              >
                <template slot="append">kg/m³</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="14">
            <el-form-item class="flex-box" label="单项结论：">
              <el-input
                :disabled="dxjlDisabled"
                v-model="batchForm[activeName].dxjl" 
                placeholder="请输入"
                :style="{'width': 180 + 'px'}"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row class="mt16">
          <el-col :span="24">
            <el-form-item label="检测图片：">
              <div class="cbo">
                <div class="img-box">
                  <el-upload
                    accept=".jpeg,.png,.jpg,.bmp,.gif"
                    :action="baseUrl + '/upload/file'"
                    list-type="picture-card"
                    :on-preview="handlePreview"
                    :file-list="batchForm[activeName].img"
                    :on-success="handlePicSuccess"
                    :on-remove="handlePicRemove">
                    <i class="el-icon-plus"></i>
                  </el-upload>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    
    <!-- 堆积密度 COARSE_AGGREGATE_PARAM_CGL_DJMD -->
    <div v-if="activeName === 'COARSE_AGGREGATE_PARAM_CGL_DJMD'" class="custom-form">
      <el-form :disabled="loading2 || experimentStatus == 3" :inline="true" label-width="100px">
        
        <div class="mt16 flex-box">
          <!-- <el-form-item label="取样数量：" class="flex-item flex-box" >
            <el-input
              v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 100000 ? e.target.value.match(/100000|\d{1,5}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].qysl" 
              placeholder="请输入(默认3)"
            >
              <template slot="append">L</template>
            </el-input>
          </el-form-item> -->
          <el-form-item label="检测日期：" class="flex-item flex-box">
            <el-date-picker
              type="date"
              v-model="batchForm[activeName].jcrq"
              placeholder="选择日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              style="width: 135px;"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item class="flex-item flex-box" label="容量筒质量：">
            <el-input
              v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 100000 ? e.target.value.match(/100000|\d{1,5}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @input="val => setDJMDAll()"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].rltzl"
              placeholder="请输入"
              style="width: 135px;"
            >
              <template slot="append">g</template>
            </el-input>
          </el-form-item>
          <el-form-item label="容量筒体积：" class="flex-item flex-box" >
            <el-input
              v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 100000 ? e.target.value.match(/100000|\d{1,5}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @input="val => setDJMDAll()"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].rlttj" 
              placeholder="请输入"
              style="width: 135px;"
            >
              <template slot="append">m³</template>
            </el-input>
          </el-form-item>
          <el-form-item label="空隙率：" class="flex-item flex-box" >
            <el-input
              v-manual-update
              @keyup.native="e=> e.target.value = e.target.value * 1 > 100000 ? e.target.value.match(/100000|\d{1,5}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].kxl" 
              placeholder="请输入"
              style="width: 135px;"
            >
              <template slot="append">%</template>
            </el-input>
          </el-form-item>
        </div>
        <div v-for="(item, index) in batchForm[activeName].djmdInfo" :key="index">
          <p class="info-form-title mt16">{{index == 0 ? '第一次试验' : '第二次试验'}}</p>
          <div class="info-form-piece flex-box">
            <el-form-item label="总质量：" class="flex-item flex-box">
              <el-input
                v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 100000 ? e.target.value.match(/100000|\d{1,5}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @input="val => setDJMD(index)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="item.zzl" 
                placeholder="请输入"
              >
                <template slot="append">g</template>
              </el-input>
            </el-form-item>
            <el-form-item label="试样质量：" class="flex-item flex-box">
              <el-input
                v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 100000 ? e.target.value.match(/100000|\d{1,5}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                disabled
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="item.syzl" 
                placeholder="请输入"
              >
                <template slot="append">g</template>
              </el-input>
            </el-form-item>
            <el-form-item label="结果：" class="flex-item flex-box">
              <el-input
                v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 100000 ? e.target.value.match(/100000|\d{1,5}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                disabled
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="item.jg" 
              >
                <template slot="append">g/m³</template>
              </el-input>
            </el-form-item>
          </div>
        </div>
        <div class="line"></div>
        <el-row>
          <el-col :span="10">
            <el-form-item class="flex-box" label="平均值：">
              <el-input
                v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 100000 ? e.target.value.match(/100000|\d{1,5}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                disabled
                v-model="batchForm[activeName].pjz" 
                :style="{'width': 200 + 'px'}"
              >
                <template slot="append">kg/m³</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="14">
            <el-form-item class="flex-box" label="单项结论：">
              <el-input
                :disabled="dxjlDisabled"
                v-model="batchForm[activeName].dxjl" 
                placeholder="请输入"
                :style="{'width': 180 + 'px'}"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row class="mt16">
          <el-col :span="24">
            <el-form-item label="检测图片：">
              <div class="cbo">
                <div class="img-box">
                  <el-upload
                    accept=".jpeg,.png,.jpg,.bmp,.gif"
                    :action="baseUrl + '/upload/file'"
                    list-type="picture-card"
                    :on-preview="handlePreview"
                    :file-list="batchForm[activeName].img"
                    :on-success="handlePicSuccess"
                    :on-remove="handlePicRemove">
                    <i class="el-icon-plus"></i>
                  </el-upload>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    
    <!-- 紧密密度 -->
    <div v-if="activeName === 'COARSE_AGGREGATE_PARAM_CGL_JMMD'" class="custom-form">
      <el-form :disabled="loading2 || experimentStatus == 3" :inline="true" label-width="100px">
        
        <div class="mt16 flex-box">
          <el-form-item label="检测日期：" class="flex-item flex-box">
            <el-date-picker
              type="date"
              v-model="batchForm[activeName].jcrq"
              placeholder="选择日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              style="width: 135px;"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item class="flex-item flex-box" label="容量筒质量：">
            <el-input
              v-manual-update
              @keyup.native="e=> e.target.value = e.target.value * 1 > 100000 ? e.target.value.match(/100000|\d{1,5}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @input="val => setDJMDAll()"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].rltzl"
              placeholder="请输入"
              style="width: 135px;"
            >
              <template slot="append">g</template>
            </el-input>
          </el-form-item>
          <el-form-item label="容量筒体积：" class="flex-item flex-box" >
            <el-input
              v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 100000 ? e.target.value.match(/100000|\d{1,5}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @input="val => setDJMDAll()"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].rlttj" 
              placeholder="请输入"
              style="width: 135px;"
            >
              <template slot="append">m³</template>
            </el-input>
          </el-form-item>
          <el-form-item label="空隙率：" class="flex-item flex-box" >
            <el-input
              v-manual-update
              @keyup.native="e=> e.target.value = e.target.value * 1 > 100000 ? e.target.value.match(/100000|\d{1,5}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].kxl" 
              placeholder="请输入"
              style="width: 135px;"
            >
              <template slot="append">%</template>
            </el-input>
          </el-form-item>
        </div>
        <div v-for="(item, index) in batchForm[activeName].jmmdInfo" :key="index">
          <p class="info-form-title mt16">{{index == 0 ? '第一次试验' : '第二次试验'}}</p>
          <div class="info-form-piece flex-box">
            <el-form-item label="总质量：" class="flex-item flex-box">
              <el-input
                v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 100000 ? e.target.value.match(/100000|\d{1,5}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @input="val => setDJMD(index)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="item.zzl" 
                placeholder="请输入"
              >
                <template slot="append">g</template>
              </el-input>
            </el-form-item>
            <el-form-item label="试样质量：" class="flex-item flex-box">
              <el-input
                v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 100000 ? e.target.value.match(/100000|\d{1,5}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                disabled
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="item.syzl" 
                placeholder="请输入"
              >
                <template slot="append">g</template>
              </el-input>
            </el-form-item>
            <el-form-item label="结果：" class="flex-item flex-box">
              <el-input
                v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 100000 ? e.target.value.match(/100000|\d{1,5}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                disabled
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="item.jg" 
              >
                <template slot="append">g/m³</template>
              </el-input>
            </el-form-item>
          </div>
        </div>
        <div class="line"></div>
        <el-row>
          <el-col :span="10">
            <el-form-item class="flex-box" label="平均值：">
              <el-input
                v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 100000 ? e.target.value.match(/100000|\d{1,5}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                disabled
                v-model="batchForm[activeName].pjz" 
                :style="{'width': 200 + 'px'}"
              >
                <template slot="append">kg/m³</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="14">
            <el-form-item class="flex-box" label="单项结论：">
              <el-input
                :disabled="dxjlDisabled"
                v-model="batchForm[activeName].dxjl" 
                placeholder="请输入"
                :style="{'width': 180 + 'px'}"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row class="mt16">
          <el-col :span="24">
            <el-form-item label="检测图片：">
              <div class="cbo">
                <div class="img-box">
                  <el-upload
                    accept=".jpeg,.png,.jpg,.bmp,.gif"
                    :action="baseUrl + '/upload/file'"
                    list-type="picture-card"
                    :on-preview="handlePreview"
                    :file-list="batchForm[activeName].img"
                    :on-success="handlePicSuccess"
                    :on-remove="handlePicRemove">
                    <i class="el-icon-plus"></i>
                  </el-upload>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <!-- 压碎值指标 -->
    <div v-if="activeName === 'COARSE_AGGREGATE_PARAM_CGL_YSZZB'" class="custom-form">
      <el-form :disabled="loading2 || experimentStatus == 3" :inline="true" label-width="200px">
        <div class="mt16 jcrq-box">
          <el-form-item label="检测日期：">
            <el-date-picker
              type="date"
              v-model="batchForm[activeName].jcrq"
              placeholder="选择日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
        </div>
        <!-- <div class="mt16 flex-box">
          <el-form-item label="取样质量：" class="flex-item flex-box" >
            <el-input
              v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].qyzl" 
              placeholder="请输入(默认3000g)"
            >
              <template slot="append">g</template>
            </el-input>
          </el-form-item>
          <div class="flex-item"></div>
          <div class="flex-item"></div>
        </div> -->
        <div v-for="(item, index) in batchForm[activeName].yszzbInfo" :key="index">
          <p class="info-form-title mt16">{{'第'+ (index + 1 )+'次试验'}}</p>
          <div class="info-form-piece flex-box">
            <el-form-item label="试样质量：" class="flex-item flex-box">
              <el-input
                v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @input="val => setYSZZB(index)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="item.syzl" 
                placeholder="请输入"
              >
                <template slot="append">g</template>
              </el-input>
            </el-form-item>
            <el-form-item label="压碎筛余量：" class="flex-item flex-box">
              <el-input
                v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @input="val => setYSZZB(index)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="item.yssyl" 
                placeholder="请输入"
              >
                <template slot="append">g</template>
              </el-input>
            </el-form-item>
            <el-form-item label="结果：" class="flex-item flex-box">
              <el-input
                v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                disabled
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="item.jg" 
              >
                <template slot="append">%</template>
              </el-input>
            </el-form-item>
          </div>
        </div>
        <div class="line"></div>
        <el-row>
          <el-col :span="10">
            <el-form-item class="flex-box" label="平均值：">
              <el-input
                v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                disabled
                v-model="batchForm[activeName].pjz" 
                :style="{'width': 200 + 'px'}"
              >
                <template slot="append">%</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="14">
            <el-form-item class="flex-box" label="单项结论：">
              <el-input
                :disabled="dxjlDisabled"
                v-model="batchForm[activeName].dxjl" 
                placeholder="请输入"
                :style="{'width': 180 + 'px'}"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row class="mt16">
          <el-col :span="24">
            <el-form-item label="检测图片：">
              <div class="cbo">
                <div class="img-box">
                  <el-upload
                    accept=".jpeg,.png,.jpg,.bmp,.gif"
                    :action="baseUrl + '/upload/file'"
                    list-type="picture-card"
                    :on-preview="handlePreview"
                    :file-list="batchForm[activeName].img"
                    :on-success="handlePicSuccess"
                    :on-remove="handlePicRemove">
                    <i class="el-icon-plus"></i>
                  </el-upload>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    
  
  
    <image-viewer
      v-if="imgViewerVisible"
      :urlList="[previewUrl]"
      :on-close="onClose"
    >
    </image-viewer>
  </div>
</template>

<script>
import {add,  sub,  mul,  div, roundToDecimalPlace, calcEquation, cpEvenRound} from "@/utils/calculate.js"
import moment from "@/utils/moment.js"
import ImageViewer from "element-ui/packages/image/src/image-viewer";
import util from "../../../common/js/util.js";
import { conclusion_CGL_HNL, conclusion_CGL_ZPZKLHL,
conclusion_CGL_NKHL,conclusion_CGL_YSZZB_ss,conclusion_CGL_YSZZB_ls } from "./conclusion.js"
import { XGL_SZXS } from "./config.js"
export default {
  name:'userMgt',
  components: {
    ImageViewer
  },
  props: {
    activeId: {
      type: Number | String,
    },
    experimentStatus: {
      type: Number | String,
    },
    sampleLevel: {
      type: String,
      default: '1'
    },
    entrustTime: {
      type: String,
      default: new Date().toISOString().split('T')[0],
    },
    
  },
  watch: {
    activeId: {
      handler(newValue, oldValue){
        if(newValue){
          // this.clearData();
          // this.setExperimentProject()
        }
      },
      immediate: true
    },


    entrustTime: {
      handler(newValue, oldValue){
        if(newValue){
          this.setDefaultTime(this.batchForm)
        }
      },
      immediate: true
    },
    
  },
  computed: {},
  data() {
    return {
      defaultDate: moment.today(),
      infoForm: '',
      filePrefix: this.$store.state.loginStore.userInfo.filePrefix,
      baseUrl: process.env.NODE_ENV === 'development' ? "/api" : "/mes-platform",
      activeName: 'quick',
      activeInfoName: 'hnlInfo',
      quickForm: {},//快检数据
      quickData: [],//快检列表
      batchForm: {
        COARSE_AGGREGATE_PARAM_CGL_HNL: {
          hnlInfo: []
        },
        COARSE_AGGREGATE_PARAM_CGL_HNKL: {
          hnkInfo: []
        }
      },//批检数据
      batchData: [],//批检列表
      
      loading2: false,
      
      previewUrl: "",
      imgViewerVisible: false,
      accordingObj: {},

      dxjlDisabled: false,
    };
  },
  
  created() {
  },
  methods: {
    //判断结论
    getPJZUtil(data,key,n){//数组，key，保留小数
      let len = 0,num = 0;
      data.forEach(item =>{
        if(item[key] !== ''){
          len++;
          num += item[key] * 1
        }
      })
      //2次结果之和除以2，保留2位小数，四舍五入
      let arr = [
        {
          v: num,
        },{
          k: '/',
          v: len === 0 ? '' : len
        }
      ]
      return calcEquation(arr, n);
    },
    //判断结论
    conclusion(num, key){
      if(num === ''){
        return ''
      }
      const aName = this.activeName;
      if(!key){
        key = aName.match(/[^_]+$/)[0];
        key = key.toLowerCase();
      }
      if(!this.accordingObj[aName][key]){
        return ''
      }
      const min = this.accordingObj[aName][key].min;
      const max = this.accordingObj[aName][key].max;
      
      console.log(num, aName, key, min, max);
      if(min !== null && num < min){
        // this.$parent.testInfoForm.isQualified = 2;
        return '不合格'
      }
      if(max !== null && num > max){
        // this.$parent.testInfoForm.isQualified = 2;
        return '不合格'
      }
      return '合格';
    },
    // 含泥量 泥块含量计算
    setHnl(index){//设置含泥量
      const data = this.batchForm[this.activeName][this.activeInfoName][index];
      if(data.hqsyzl * 1 > 10000){
        this.batchForm[this.activeName][this.activeInfoName][index].hqsyzl = 10000;
        this.$message.warning('烘前试样质量不能大于10000');
      }else if(data.hhsyzl* 1 > 0 && data.hqsyzl* 1 > 0){
        if(data.hhsyzl* 1 > data.hqsyzl* 1){
          this.batchForm[this.activeName][this.activeInfoName][index].hhsyzl = data.hqsyzl;
          this.$message.warning('烘后不能大于烘前');
        }
      }else{
        if(data.hqsyzl* 1 ===0){
          this.$message.error("烘前试样质量需要大于0");
          this.batchForm[this.activeName][this.activeInfoName][index].hhsyzl = '';
        } 
        return false;
      }
      
      
      
      
      let arr = [
        {
          v: data.hqsyzl,
        },{
          k: '-',
          v: data.hhsyzl,
        },{
          k: '/',
          v: data.hqsyzl,
        },{
          k: '*',
          v: 100,
        }
      ]
      let res = calcEquation(arr, 1)
      this.batchForm[this.activeName][this.activeInfoName][index].hnl = res;
      this.setHNLPJZ()
    },
    setHNLPJZ(){
      const data = this.batchForm[this.activeName][this.activeInfoName];
      
      let res = this.getPJZUtil(data, 'hnl', 1);
      this.batchForm[this.activeName].hnlpjz = res;
      this.batchForm[this.activeName].dxjl = this.conclusion(res);
    },
    
    zpzklhlChange(){
      const data = this.batchForm[this.activeName];
      
      if(data.syzl * 1 > 10000){
        this.batchForm[this.activeName].syzl = 10000;
        this.$message.warning('试样质量不能大于10000');
      }else if(data.zpzklzzl * 1 > data.syzl * 1){
        this.batchForm[this.activeName].zpzklzzl = data.syzl;
      }
      
      let arr = [
        {
          v: data.zpzklzzl,
        },{
          k: '/',
          v: data.syzl,
        },{
          k: '*',
          v: 100,
        }
      ]
      let res = calcEquation(arr, 0)
      this.batchForm[this.activeName].zpzkklhl = res;
      this.batchForm[this.activeName].dxjl = this.conclusion(res);
    },
    
    // 表观密度
    setSZXS(val){//修正系数
      // this.batchForm[this.activeName][this.activeInfoName][0].xzxs = XGL_SZXS[val];
      // this.batchForm[this.activeName][this.activeInfoName][1].xzxs = XGL_SZXS[val];
      this.batchForm[this.activeName].xzxs = XGL_SZXS[val];
      this.setBGMD(0)
      this.setBGMD(1)
    },
    setBGMD(index){
      const dataAll = this.batchForm[this.activeName];
      const data = this.batchForm[this.activeName][this.activeInfoName][index];
      let arr = [
        {
          v: data.hgsyzl,
        },{
          k: '/',
          v: sub(add(data.hgsyzl,data.zzlsdl), data.zzlssydl),
        },{
          k: '-',
          v: dataAll.xzxs,
        },{
          k: '*',
          v: 1000,
        }
      ]
      let res = calcEquation(arr, 10)
      this.batchForm[this.activeName][this.activeInfoName][index].jg = res;
      this.setBGMDPJZ(2600);
    },
    setBGMDPJZ(max, type){
      const data = this.batchForm[this.activeName][this.activeInfoName];
      
      let res = this.getPJZUtil(data, 'jg', 10);
      this.batchForm[this.activeName].pjz = res;
      this.batchForm[this.activeName].dxjl = this.conclusion(res);
    },
    
    // 堆积密度,紧密密度
    setDJMDAll(){
      this.setDJMD(0);
      this.setDJMD(1);
    },
    setDJMD(index){
      const data = this.batchForm[this.activeName][this.activeInfoName][index];
      const dataAll = this.batchForm[this.activeName];
      let syzl = sub(data.zzl, dataAll.rltzl)
      let arr = [
        {
          v: syzl,
        },{
          k: '/',
          v: dataAll.rlttj,
        },{
          k: '/',
          v: 1000,
        }
      ]
      let res = calcEquation(arr, 10)
      this.batchForm[this.activeName][this.activeInfoName][index].syzl = syzl;
      this.batchForm[this.activeName][this.activeInfoName][index].jg = res;
      this.setBGMDPJZ(1400, 'min');
    },
    // 压碎值指标
    setYSZZB(index){
      const data = this.batchForm[this.activeName][this.activeInfoName][index];
      if(data.syzl * 1 > 10000){
        this.batchForm[this.activeName][this.activeInfoName][index].syzl = 10000;
        this.$message.warning('试样质量不能大于10000');
      }else if(data.yssyl * 1 > data.syzl){
        this.batchForm[this.activeName][this.activeInfoName][index].yssyl = this.batchForm[this.activeName][this.activeInfoName][index].syzl;
        this.$message.warning('压碎筛余量不能大于试样质量');
      }
      
      let arr = [
        {
          v: data.syzl,
        },{
          k: '-',
          v: data.yssyl,
        },{
          k: '/',
          v: data.syzl,
        },{
          k: '*',
          v: 100,
        }
      ]
      let res = calcEquation(arr, 1)
      this.batchForm[this.activeName][this.activeInfoName][index].jg = res;
      this.setYSZZBLPJZ();
    },
    setYSZZBLPJZ(){
      const data = this.batchForm[this.activeName][this.activeInfoName];
      
      let res = this.getPJZUtil(data, 'jg', 0);
      this.batchForm[this.activeName].pjz = res;
      this.batchForm[this.activeName].dxjl = this.conclusion(res);
    },
    
    
    // 筛分析
    setFJSYAll(){
      const dataAll = this.batchForm[this.activeName][this.activeInfoName]
      for(let i = 0;i <dataAll.length;i++){
        if(dataAll[i].syl){
          this.setFJSY(i, dataAll[i].syl)
        }
      }
    },
    setFJSY(index, val){
      // 分计筛余：筛余量/试样总量*100%，数字校验，不可编辑，精确到0.1，四舍五入
      // 4、累计筛余：比如现在在天1.25mm的筛余量等，那么，累计筛余就是当前分计筛余+上次累计筛余之和，精确到0.1，四舍五入
      const data = this.batchForm[this.activeName][this.activeInfoName][index];
      const dataAll = this.batchForm[this.activeName][this.activeInfoName]
      const dataAll2 = this.batchForm[this.activeName]
      
      let arr = [
        {
          v: data.syl,
        },{
          k: '/',
          v: dataAll2.syzl
        },{
          k: '*',
          v: 100,
        }
      ]
      // 计算分析筛余
      let res = calcEquation(arr, 1)
      this.batchForm[this.activeName][this.activeInfoName][index].fjsy = res;
      // 全部的粗骨料都重新计算
      let flag = false;
      dataAll.map((item, idx) => {
        let numLJSY = 0;
        if(idx > 0){
          let lastLJSY = dataAll[idx - 1].ljsy || 0;
          numLJSY = add(lastLJSY, item.fjsy || 0);
        }else{
          numLJSY = add(0, item.fjsy || 0);
        }

        let ljsy = cpEvenRound(numLJSY, 0);
        // 当累计筛余大于100，则不显示
        if (ljsy >= 100) {
          if (!flag) {
            this.batchForm[this.activeName][this.activeInfoName][idx].ljsy = '100';
            flag = true; 
          }else{
            this.batchForm[this.activeName][this.activeInfoName][idx].ljsy = "100";
          }
        }else{
          if (flag) {
            this.batchForm[this.activeName][this.activeInfoName][idx].ljsy = "";
          }else{
            this.batchForm[this.activeName][this.activeInfoName][idx].ljsy = ljsy;
            flag = false; 
          }
        }
      });

      // 以下是当空行不计算的方式
      // // 计算分析筛余
      // let res = calcEquation(arr, 1)
      // this.batchForm[this.activeName][this.activeInfoName][index].fjsy = res;
      // // 全部的粗骨料都重新计算
      // dataAll.forEach((item, idx) => {
      //   let numLJSY = 0;
      //   if(idx > 0){
      //     let lastLJSY = dataAll[idx - 1].ljsy || 0;
      //     numLJSY = add(lastLJSY, item.fjsy || 0);
      //   }else{
      //     numLJSY = add(0, item.fjsy || 0);
      //   }

      //   if (item.syl) {
      //     this.batchForm[this.activeName][this.activeInfoName][idx].ljsy = cpEvenRound(numLJSY, 1);
      //   }else{
      //     this.batchForm[this.activeName][this.activeInfoName][idx].ljsy = "";
      //     this.batchForm[this.activeName][this.activeInfoName][idx].fjsy = "";
      //   }
      // });

      // 以下是计算累计筛余用累加分析筛余的方式
      // let numLJSY = this.batchForm[this.activeName][this.activeInfoName][0].fjsy || 0;
      // dataAll.forEach((item, idx) => {
      //   // if(idx > 0){
      //   //   let lastLJSY = dataAll[idx - 1].ljsy || 0;
      //   //   numLJSY = add(lastLJSY, item.fjsy || 0);
      //   // }else{
      //   //   numLJSY = add(0, item.fjsy || 0);
      //   // }

      //   numLJSY = item.fjsy || 0;

      //   if (item.syl) {
      //     this.batchForm[this.activeName][this.activeInfoName][idx].ljsy = cpEvenRound(numLJSY, 1);
      //   }else{
      //     this.batchForm[this.activeName][this.activeInfoName][idx].ljsy = "";
      //   }
      // })
    },
    setKljChart() {
      let kljChart = this.$echarts.init(document.getElementById('kljptfx'));
      kljChart.setOption({
        title: {
          text: ''
        },
        tooltip: {},
        legend: {
          data:['销量']
        },
        xAxis: {
          name: '筛孔尺寸（mm）',
          nameLocation: 'middle',
          nameTextStyle: {
              color: '#000000' // 设置x轴名称的颜色
          },
          nameGap: 25, // 设置x轴名称和轴线之间的距离为30
          type: 'category',
          data: ["123","123","123","11","123","123"],
          
          
          axisLine: {
              onZero: true
          },
          axisTick: {
              show: true,
              alignWidthLabel: true
          },
          boundaryGap: false, // 关闭边界间隙，确保数据点按照实际数值的位置进行绘制
          splitLine: {
              show: true
          }
        },
        yAxis: {
          name: '累计筛余百分率（%）',
          nameLocation: 'middle', // Y 轴名称显示位置
          nameRotate: 90, // Y轴名称旋转角度                        
          nameGap: 40, // Y 轴名称与轴线的距离
          nameTextStyle: {
              fontSize: 10,
              // padding: [0, 0, 0, 10]
          },
          axisLine: {
              show: true, // 显示轴线
              lineStyle: {
                  color: '#5799d3' // 设置轴线颜色
              },
              onZero: true
          },
          axisTick: {
              show: true, // 显示刻度线
              lineStyle: {
                  color: '#5799d3' // 设置刻度线颜色
              }
          },
          splitLine: {
              show: true
          }
          
        },
        series: [{
          name: '',
          type: 'line',
          data: [5, 20, 36, 10, 10, 20],
          itemStyle:{
            normal:{
              lineStyle:{
                width:2,
                type:'dotted'  //'dotted'点型虚线 'solid'实线 'dashed'线性虚线
              }
            }
          }, 
        },{
          name: '',
          type: 'line',
          data: [51, 0, 36, 20, 30, 20],
          itemStyle:{
            normal:{
              lineStyle:{
                width:2,
                type:'dashed'  //'dotted'点型虚线 'solid'实线 'dashed'线性虚线
              }
            }
          }, 
        }],
      });
    },
    
    //图片
    handlePicSuccess(response, file, fileList) {
      console.log(fileList)
      let oUploadImg = fileList.map(item =>{
        if(item.response){
          return {
            url: item.response.data.filePath,
            uid: item.uid
          }
        }else{
          return item;
          // if(item.url.startsWith(this.filePrefix)){
          //   return item.url;
          // }else{
          //   return this.filePrefix + item.url;
          // }
        }
      })
      
      if(this.activeName === 'quick'){
        this.quickForm.img = oUploadImg;
      }else{
        this.batchForm[this.activeName].img = oUploadImg;
      }
    },
    handlePicRemove(file, fileList) {
      let oUploadImg = fileList.map(item =>{
        if(item.response){
          return {
            url: item.response.data.filePath,
            uid: item.uid
          }
        }else{
          return item
        }
      })
      if(this.activeName === 'quick'){
        this.quickForm.img = oUploadImg;
      }else{
        this.batchForm[this.activeName].img = oUploadImg;
      }
    },
    
    
    
    //获取所有快检或批检试验项目名称
    async setExperimentProject(id){
      //获取抗压 抗渗 和其它
      const resDetail = await this.$api.getExperimentDetail({
        experimentId: this.activeId,
        // "testProjectCode":"FINE_AGGREGATE",
        // "checkType": 2
      }, this)
      if(resDetail.succ){
        let quickData = [];
        let batchData = [];//批检
        let quickForm = {};
        quickForm.img = [];
        let batchForm = {};
        resDetail.data.list.forEach(item => {
          let oImgArr = [];
          if(item.objImg && item.objImg != 'null'){
            oImgArr = item.objImg.split(',').map(item => {
              if(item.startsWith(this.filePrefix)){
                return {
                  url: item
                }
              }else{
                return {
                  url: this.filePrefix + item
                }
              }
            }) 
          }else{
            oImgArr = [];
          }
          
          if(item.testProjectName === '目测含水率' || item.testProjectName === '目测含泥量' 
            || item.testProjectName === '目测泥块含量'
          ){
            quickData.push(item)
            quickForm[item.testProjectCode] = item;
            quickForm.img = quickForm.img.concat(oImgArr);
          }else{
            if(!item.objJson?.jcrq){
              item.objJson.jcrq = this.defaultDate;
            }
            item.objJson.img = oImgArr;
            batchForm[item.testProjectCode] = JSON.parse(JSON.stringify(item.objJson));
            item.objJson = undefined;
            batchData.push(item);
          }
        })
        this.quickData = quickData;
        this.quickForm = quickForm;
        this.batchData = batchData;
        this.batchForm = this.setDefaultVal(batchForm);
        console.log('粗骨料的数据：', batchForm);
        
        console.log(this.batchForm,this.batchData)
        if(this.quickData.length > 0){
          this.activeName = 'quick'
        }else{
          this.activeName = this.batchData[0].testProjectCode;
          this.getAccordingToHand()
        }
        
      }  
    },
    getAccordingToHand(){
      this.$api.getAccordingTo({
        testProjectCode: this.activeName,
        materialAbbreviation : this.$parent.activeData.materialAbbreviation,
        materialsName	 : this.$parent.activeData.materialsName,
        materialsSpec : this.$parent.activeData.materialsSpecs
      }, this).then(res =>{
        if(res.data.list.length > 0){
          let o = res.data.list[0].objJson;
          this.accordingObj[this.activeName] = JSON.parse(o);
        }else{
          this.accordingObj[this.activeName] = {};
        }
      })
    },
    isEmptyValue(value) {
            // 处理用户输入的特殊字符串
            if (value === 'null') return true;
            if (value === 'undefined') return true;
            if (value === '') return true;
            
            // 处理实际的JavaScript值
            if (value === null) return true;
            if (value === undefined) return true;
            if (value === '') return true;
            
            return false;
        },

        setDefaultTime(val){
          // 粗骨料、细骨料：泥块含量、表观密度=委托时间+1天，其他的都是委托当天
      // 表观密度
      if(val.COARSE_AGGREGATE_PARAM_CGL_BGMD && this.isEmptyValue(val.COARSE_AGGREGATE_PARAM_CGL_BGMD?.jcrq)){
        val.COARSE_AGGREGATE_PARAM_CGL_BGMD['jcrq'] = util.calculateFutureDate(this.entrustTime, 1)
      }
      // 泥块含量
      if(val.COARSE_AGGREGATE_PARAM_CGL_HNKL && this.isEmptyValue(val.COARSE_AGGREGATE_PARAM_CGL_HNKL?.jcrq)){
        val.COARSE_AGGREGATE_PARAM_CGL_HNKL['jcrq'] = util.calculateFutureDate(this.entrustTime, 1)
      }
      // 压碎值指标
      if(val.COARSE_AGGREGATE_PARAM_CGL_YSZZB && this.isEmptyValue(val.COARSE_AGGREGATE_PARAM_CGL_YSZZB?.jcrq)){
        val.COARSE_AGGREGATE_PARAM_CGL_YSZZB['jcrq'] = util.calculateFutureDate(this.entrustTime, 0)
      }
      // 堆积密度
      if(val.COARSE_AGGREGATE_PARAM_CGL_DJMD && this.isEmptyValue(val.COARSE_AGGREGATE_PARAM_CGL_DJMD?.jcrq)){
        val.COARSE_AGGREGATE_PARAM_CGL_DJMD['jcrq'] = util.calculateFutureDate(this.entrustTime, 0)
      }
      // 紧密密度
      if(val.COARSE_AGGREGATE_PARAM_CGL_JMMD && this.isEmptyValue(val.COARSE_AGGREGATE_PARAM_CGL_JMMD?.jcrq)){
        val.COARSE_AGGREGATE_PARAM_CGL_JMMD['jcrq'] = util.calculateFutureDate(this.entrustTime, 0)
      }
      // 含泥量
      if(val.COARSE_AGGREGATE_PARAM_CGL_HNL && this.isEmptyValue(val.COARSE_AGGREGATE_PARAM_CGL_HNL?.jcrq)){
        val.COARSE_AGGREGATE_PARAM_CGL_HNL['jcrq'] = util.calculateFutureDate(this.entrustTime, 0)
      }
      // 针片状
      if(val.COARSE_AGGREGATE_PARAM_CGL_ZPZKLHL && this.isEmptyValue(val.COARSE_AGGREGATE_PARAM_CGL_ZPZKLHL?.jcrq)){
        val.COARSE_AGGREGATE_PARAM_CGL_ZPZKLHL['jcrq'] = util.calculateFutureDate(this.entrustTime, 0)
      }
      // 含水率
      if(val.COARSE_AGGREGATE_PARAM_CGL_BKHL && this.isEmptyValue(val.COARSE_AGGREGATE_PARAM_CGL_BKHL?.jcrq)){
        val.COARSE_AGGREGATE_PARAM_CGL_BKHL['jcrq'] = util.calculateFutureDate(this.entrustTime, 0)
      }
      // 氯离子
      if(val.COARSE_AGGREGATE_PARAM_CGL_LLZHL && this.isEmptyValue(val.COARSE_AGGREGATE_PARAM_CGL_LLZHL?.jcrq)){
        val.COARSE_AGGREGATE_PARAM_CGL_LLZHL['jcrq'] = util.calculateFutureDate(this.entrustTime, 0)
      }

      // 筛分析
      if(val.COARSE_AGGREGATE_PARAM_CGL_SFX && this.isEmptyValue(val.COARSE_AGGREGATE_PARAM_CGL_SFX?.jcrq)){
        val.COARSE_AGGREGATE_PARAM_CGL_SFX['jcrq'] = util.calculateFutureDate(this.entrustTime, 0)
      }
        },
    //设置默认值
    setDefaultVal(val){
      console.log(val,111)
      this.setDefaultTime(val);
      if(val.COARSE_AGGREGATE_PARAM_CGL_BGMD?.qysl === ''){
        val.COARSE_AGGREGATE_PARAM_CGL_BGMD.qysl = '600'
      }
      if(val.COARSE_AGGREGATE_PARAM_CGL_BGMD?.bgmdInfo[0].hgsyzl === ''){
        val.COARSE_AGGREGATE_PARAM_CGL_BGMD.bgmdInfo[0].hgsyzl = '300'
      }
      if(val.COARSE_AGGREGATE_PARAM_CGL_BGMD?.sw === ''){
        val.COARSE_AGGREGATE_PARAM_CGL_BGMD.sw = 15;
        val.COARSE_AGGREGATE_PARAM_CGL_BGMD.xzxs = XGL_SZXS[15];
        // val.COARSE_AGGREGATE_PARAM_CGL_BGMD.xzxs = XGL_SZXS['15'];
      }else if(val.COARSE_AGGREGATE_PARAM_CGL_BGMD?.xzxs === ''){
        // val.COARSE_AGGREGATE_PARAM_CGL_BGMD.bgmdInfo[0].xzxs = XGL_SZXS[val.COARSE_AGGREGATE_PARAM_CGL_BGMD.sw];
        val.COARSE_AGGREGATE_PARAM_CGL_BGMD.xzxs = XGL_SZXS[val.COARSE_AGGREGATE_PARAM_CGL_BGMD.sw];
      }
      if(val.COARSE_AGGREGATE_PARAM_CGL_BGMD?.sbgmd === ''){
        val.COARSE_AGGREGATE_PARAM_CGL_BGMD.sbgmd = '1000';
      }
      if(val.COARSE_AGGREGATE_PARAM_CGL_YSZZB?.qyzl === ''){
        val.COARSE_AGGREGATE_PARAM_CGL_YSZZB.qyzl = '3000';
      }
      if(val.COARSE_AGGREGATE_PARAM_CGL_DJMD && val.COARSE_AGGREGATE_PARAM_CGL_DJMD?.qyzl === ''){
        val.COARSE_AGGREGATE_PARAM_CGL_DJMD.qyzl = '3';
        val.COARSE_AGGREGATE_PARAM_CGL_DJMD.qysl = '3';
      }
      if(val.COARSE_AGGREGATE_PARAM_CGL_JMMD && val.COARSE_AGGREGATE_PARAM_CGL_JMMD?.qysl === ''){
        val.COARSE_AGGREGATE_PARAM_CGL_JMMD.qysl = '3';
      }
      if (val.COARSE_AGGREGATE_PARAM_CGL_HNKL) {
        let hnklObj = val.COARSE_AGGREGATE_PARAM_CGL_HNKL;
        if (hnklObj.hnkInfo && hnklObj.hnkInfo.length > 0) {
          for (let element of hnklObj.hnkInfo) {
            if (element.hnl) {
              element.hnl = cpEvenRound(element.hnl, 1);
            }
          }
        }
        if (hnklObj.hnlpjz) {
          hnklObj.hnlpjz = cpEvenRound(hnklObj.hnlpjz, 1);
        }
      } 

      if (val.COARSE_AGGREGATE_PARAM_CGL_HNL) {
        let hnlObj = val.COARSE_AGGREGATE_PARAM_CGL_HNL;
        if (hnlObj.hnlInfo && hnlObj.hnlInfo.length > 0) {
          for (let element of hnlObj.hnlInfo) {
            if (element.hnl) {
              element.hnl = cpEvenRound(element.hnl, 1);
            }
          }
        }
        if (hnlObj.hnlpjz) {
          hnlObj.hnlpjz = cpEvenRound(hnlObj.hnlpjz, 1);
        }
      } 

      if (val.COARSE_AGGREGATE_PARAM_CGL_SFX) {
        console.log('COARSE_AGGREGATE_PARAM_CGL_SFX:::', val);
        let sfxInfo = val.COARSE_AGGREGATE_PARAM_CGL_SFX.sfxInfo || [];
        for (let element of sfxInfo) {
          if (element.syl) {
            element.syl = cpEvenRound(element.syl, 0);
          }
          if (element.ljsy) {
            element.ljsy = cpEvenRound(element.ljsy, 0);
          }
        }
      }
      
      
      return val;
    },
    handleClick(tab, event){
      this.getAccordingToHand()
      switch (this.activeName){
        case 'COARSE_AGGREGATE_PARAM_CGL_HNL':
          this.activeInfoName = 'hnlInfo'
          break;
        case 'COARSE_AGGREGATE_PARAM_CGL_HNKL':
          this.activeInfoName = 'hnkInfo'
          break;
        case 'COARSE_AGGREGATE_PARAM_CGL_HSL':
          this.activeInfoName = 'hslInfo'
          break;
        case 'COARSE_AGGREGATE_PARAM_CGL_BGMD':
          this.activeInfoName = 'bgmdInfo'
          break;
        case 'COARSE_AGGREGATE_PARAM_CGL_DJMD':
          this.activeInfoName = 'djmdInfo'
          break;
        case 'COARSE_AGGREGATE_PARAM_CGL_JMMD':
          this.activeInfoName = 'jmmdInfo'
          break;
        case 'COARSE_AGGREGATE_PARAM_CGL_YMHL':
          this.activeInfoName = 'ymhlInfo'
          break;
        case 'COARSE_AGGREGATE_PARAM_CGL_BKHL':
          this.activeInfoName = 'bkhlInfo'
          break;
        case 'COARSE_AGGREGATE_PARAM_CGL_LLZHL':
          this.activeInfoName = 'llzhlInfo'
          break;
        case 'COARSE_AGGREGATE_PARAM_CGL_SFX':
          this.activeInfoName = 'sfxInfo'
          break;
        case 'COARSE_AGGREGATE_PARAM_CGL_YSZZB':
          this.activeInfoName = 'yszzbInfo'
          break;
        
        
        default:
          break;
      }
    },
    clearData(){
      this.activeName= 'quick';
      this.quickForm= {};
      this.quickData= [];
      this.batchForm= {};
      this.batchData= [];
    },
    handlePreview(file) {
      this.previewUrl = file.url
      this.imgViewerVisible = true;
    },
    onClose() {
      this.imgViewerVisible = false;
    },
  },
};
</script>
<style scoped lang="scss">
  .flex-item2{
    flex: 2;
  }
  .line{
    height: 1px;
    width: 100%;
    background: #E8E8E8;
    margin: 24px 0;
  }
  .info-form-title{
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 14px;
    color: #1F2329;
    line-height: 20px;
    text-align: left;
    font-style: normal;
    padding: 10px 0 4px;
    margin-bottom: 4px;
  }
  .info-form-piece{
    background: #F8F8F8;
    border-radius: 8px;
    padding: 16px 16px 8px 16px;
  }
  .el-form-item{
    margin-bottom: 8px;
  }
  ::v-deep .el-input-group__append, 
  ::v-deep .el-input-group__prepend{
    padding: 0 5px;
    text-align: center;
    width: 40px;
  }
  ::v-deep .pr0{
    .el-input__inner{
      padding-right: 0;
    }
  }
  ::v-deep .el-input-number.is-controls-right .el-input__inner{
    text-align: left;
  }
  ::v-deep .textspan .el-input__inner{
    background: transparent;
    border: none;
    padding: 0;
    color: #000;
    margin-left: -10px;
    margin-top: -2px;
  }
  
  
  ::v-deep .el-upload-list__item{
    transition: none !important; 
  }
  
  
  ::v-deep .custom-form{
    .el-form--inline{
      .table-box-kzf{
        .el-form-item{
          margin-right: 5px;
        }
      }
      .el-form-item{
        margin-bottom: 8px;
      }
    }
  }
  .table-box-kzf{
    width: 100%;
    background: #FFFFFF;
    border-radius: 4px;
    border: 1px solid #DDDFE6;
    
    .tb-title{
      height: 52px;
      background: #F8F8F8;
      border-radius: 4px 4px 0px 0px;
      border-bottom: 1px solid #DDDFE6;
      line-height: 52px;
      padding-left: 16px;
      font-weight: 600;
      font-size: 14px;
      color: #1F2329;
      .tb-title-lef{
        width: 100px;
        border-right: 1px solid #DDDFE6;
      }
      .tb-title-con{
        text-align: center;
        line-height: 20px;
        padding-top: 8px;
        border-right: 1px solid #DDDFE6;        span{
          display: inline-block;
          width: 100%;
          &:nth-child(2),&:nth-child(3){
            font-weight: 400;
            font-size: 12px;
            color: #6A727D;
            width: 50%;
          }
        }
      }
      .tb-title-bom{
        text-align: center;
        width: 172px;
      }
    }
    .tb-bottom{
      border-top: 1px solid #DDDFE6;
      padding: 16px 16px 8px;
    }
    .tb-left{
      line-height: 40px;
      border-right: 1px solid #DDDFE6;
      width: 116px;
      text-align: center;
    }
    .tb-content{
      .el-form-item{
        padding-left: 16px;
      }
      .tb-item:first-child{
        .tb-left,.el-form-item{
          padding-top: 24px;
        }
      }
      .tb-item:last-child{
        .tb-left,.el-form-item{
          padding-bottom: 16px;
        }
      }
    }
    .tb-center{
      padding: 24px 0;
      margin: 0 8px;
      &>div:first-child{
        margin-bottom: 8px;
      }
    }
    .tb-right{
      height: 88px;
      margin-top: 24px;
      border-left: 1px solid #DDDFE6;
      line-height: 40px;
      padding: 0 16px;
      &>p:first-child{
        margin-bottom: 8px;
      }
    }
  }
</style>