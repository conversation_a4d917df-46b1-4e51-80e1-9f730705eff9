<!--
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-12-01 11:23:26
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2024-12-02 22:14:56
 * @FilePath: /quality_center_web/src/pages/qualityControl/components/updateBatchPrintDetail.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div v-if="showDialog">
        <el-dialog 
            :visible.sync="showDialog" 
            class="claim-dialog-box"
            title="" 
            width="80vw" 
            :before-close="handleClose" 
            :close-on-click-modal="false"
            :close-on-press-escape="false">
            <div>
                <el-row style="font-size: 20px; font-weight: bold;">客户信息</el-row>
                <div class="form-view">
                    <el-form ref="customerForm" :model="customerForm" class="flex-row-start form-box">
                        <el-form-item class="flex-row-start form-item" :label="item.label" :prop="item.prop" v-for="(item, index) in customerFormContent" :key="index">
                            <el-input v-if="item.type === 'input'" v-model="customerForm[item.prop]" :disabled="item.disabled" placeholder="请输入内容" />
                            <el-select 
                                v-else-if="item.type === 'select'" 
                                value-key="id" 
                                v-model="customerForm[item.prop]" 
                                :disabled="item.disabled"
                                clearable
                                @change="item.handle"
                            >
                                <el-option
                                v-for="(option, index) in item.options" 
                                :key="index" 
                                :label="option.label" 
                                :value="option.value" 
                                />
                            </el-select>
                        </el-form-item>
                    </el-form>
                </div>

                <!-- <el-row style="font-size: 20px; font-weight: bold; margin-top: 30px;">配比信息</el-row>
                <div>
                    <el-row class="flex-row" style="margin-bottom: 20px;">
                        <el-col class="row-cell" style="text-align: left; width: 40%;">{{ ' ' }}</el-col>
                        <el-col class="row-cell">报告编号</el-col>
                        <el-col class="row-cell">样品名称</el-col>
                        <el-col class="row-cell">等级</el-col>
                        <el-col class="row-cell">规格</el-col>
                        <el-col class="row-cell">厂家</el-col>
                    </el-row>
                    <el-row v-for="(item, index) in mixProportionFormContent" :key="index" class="flex-row" style="margin-bottom: 20px;">
                        <el-col class="row-cell" style="text-align: left; width: 40%;">{{ item.label }}</el-col>
                        <el-col class="row-cell">
                            <el-input v-model="mixProportionForm[item.prop].bgbh" placeholder="请输入报告编号" />
                        </el-col>
                        <el-col class="row-cell">
                            <el-input v-model="mixProportionForm[item.prop].ypmc" placeholder="请输入样品名称" />
                        </el-col>
                        <el-col class="row-cell">
                            <el-input v-model="mixProportionForm[item.prop].dj" placeholder="请输入等级" />
                        </el-col>
                        <el-col class="row-cell">
                            <el-input v-model="mixProportionForm[item.prop].gg" placeholder="请输入规格" />
                        </el-col>
                        <el-col class="row-cell">
                            <el-input v-model="mixProportionForm[item.prop].cj" placeholder="请输入厂家" />
                        </el-col>
                    </el-row>
                    
                </div> -->

                <el-row style="font-size: 20px; font-weight: bold; margin-top: 30px;">其他信息</el-row>
                <div class="form-view">
                    <el-form ref="otherForm" :model="otherForm" class="flex-row-start form-box">
                        <el-col :span="24">
                            <el-form-item class="flex-row-start form-item" label="抗压委托台账">
                                <div class="flex-row-start">
                                    <div style="width: 60vw; min-height: 40px; background: #f5f5f5; padding: 4px;">
                                        <el-tag
                                            :key="tag"
                                            v-for="tag in otherForm.kytzList"
                                            closable
                                            :disable-transitions="false"
                                            @close="deleteKytz(tag)">
                                            {{tag}}
                                        </el-tag>
                                    </div>
                                    <el-button style="margin-left: 30px;" type="primary" @click="handleSelectKytz()" plain>选择</el-button>
                                </div>
                            </el-form-item>
                        </el-col>
                        <el-form-item class="flex-row-start form-item" :label="item.label" :prop="item.prop" v-for="(item, index) in otherFormContent" :key="index">
                            <el-input v-if="item.type === 'input'" v-model="otherForm[item.prop]" :disabled="item.disabled" placeholder="请输入内容" />
                            
                            <el-input
                                v-else-if="item.type === 'number'"
                                :type="item.type"
                                :disabled="item.disabled"
                                v-model="otherForm[item.prop]" 
                                clearable
                                :placeholder="item.placeholder || `请输入${item.label}`"
                                v-manual-update
                            >
                                <template v-if="item.slot" :slot="item.slot">{{item.slotVal}}</template>
                            </el-input>
                        </el-form-item>
                    </el-form>
                </div>

                <div class="footer-btn">
                    <el-button type="primary" @click="handleClose()"plain>取消</el-button>
                    <el-button type="primary" @click="saveForm()">保存</el-button>
                </div>
            </div>
        </el-dialog>

        <PanelSelecter ref="panelSelecter" materialType="7" @selected="panelSelectionChange" />
    </div>
</template>

<script>
import PanelSelecter from './panelSelecter.vue'
export default {
    components: {
        PanelSelecter
    },
    props: {
        taskSelects: {
            type: Array,
            default: () => []
        },
    },
    data() {
        return {
            showDialog: false,
            customerForm: {
                projectName: "",
                gcdz: "",
                bjbh: "",
                buildName: "",
                jsdwName: "",
                erpProjectId: "",
            },
            customerFormContent: [
                {
                    type: 'select',
                    label: '客户名称',
                    prop: 'customerName',
                    valueKey: 'id',
                    options: this.customerList,
                    handle: (val) => this.changeCustomerSelect(val)
                },
                {
                    type: 'select',
                    label: '工程名称',
                    prop: 'projectName',
                    valueKey: 'id',
                    options: this.engineeringList,
                    handle: (val) => this.changeEngineeringSelect(val)
                },
                //以下四个根据工程名称获取
                {
                    type: 'input',
                    label: '工程地址',
                    prop: 'gcdz',
                    disabled: true
                },
                {
                    type: 'input',
                    label: '报建编号',
                    prop: 'bjbh',
                    disabled: true
                },
                {
                    type: 'input',
                    label: '施工单位',
                    prop: 'buildName',
                    disabled: true
                },
                {
                    type: 'input',
                    label: '建设单位',
                    prop: 'jsdwName',
                    disabled: true
                },
            ],

            otherForm: {
                kytzList: [],
                kstzypbh: "",
                kstzjl: "",
                kztzypbh: "",
                kztzjl: "",
                kllztzypbh: "",
                kllztzjl: "",
                llzhl: ""
            },
            otherFormContent: [
                {
                    type: 'input',
                    label: '抗渗台账编号',
                    prop: 'kstzypbh',
                },
                {
                    type: 'input',
                    label: '抗渗台账结论',
                    prop: 'kstzjl',
                },
                {
                    type: 'input',
                    label: '抗折台账编号',
                    prop: 'kztzypbh',
                },
                {
                    type: 'input',
                    label: '抗折台账结论',
                    prop: 'kztzjl',
                },
                {
                    type: 'input',
                    label: '抗氯离子台账编号',
                    prop: 'kllztzypbh',
                },
                {
                    type: 'input',
                    label: '抗氯离子台账结论',
                    prop: 'kllztzjl',
                },
                {
                    type: 'number',
                    label: '氯离子含量',
                    prop: 'llzhl',
                },
            ],
            
            mixProportionFormContent: [
                {
                    label: '水泥',
                    prop: 'sn',
                    "bgbh": "", // 报告编号
                    "ypmc": "", // 样品名称
                    "dj": "", // 等级
                    "gg": "", // 规格
                    "cj": "", // 厂家
                },
                {
                    label: '水',
                    prop: 'water',
                    "bgbh": "", // 报告编号
                    "ypmc": "", // 样品名称
                    "dj": "", // 等级
                    "gg": "", // 规格
                    "cj": "", // 厂家
                },
                {
                    label: '粉煤灰',
                    prop: 'fmh',
                    "bgbh": "", // 报告编号
                    "ypmc": "", // 样品名称
                    "dj": "", // 等级
                    "gg": "", // 规格
                    "cj": "", // 厂家
                },
                {
                    label: '矿渣粉',
                    prop: 'kzf',
                    "bgbh": "", // 报告编号
                    "ypmc": "", // 样品名称
                    "dj": "", // 等级
                    "gg": "", // 规格
                    "cj": "", // 厂家
                },
                {
                    label: '细骨料',
                    prop: 'xgl',
                    "bgbh": "", // 报告编号
                    "ypmc": "", // 样品名称
                    "dj": "", // 等级
                    "gg": "", // 规格
                    "cj": "", // 厂家
                },
                {
                    label: '外加剂1',
                    prop: 'wjj1',
                    "bgbh": "", // 报告编号
                    "ypmc": "", // 样品名称
                    "dj": "", // 等级
                    "gg": "", // 规格
                    "cj": "", // 厂家
                },
                {
                    label: '外加剂2',
                    prop: 'wjj2',
                    "bgbh": "", // 报告编号
                    "ypmc": "", // 样品名称
                    "dj": "", // 等级
                    "gg": "", // 规格
                    "cj": "", // 厂家
                },
                {
                    label: '外掺料1',
                    prop: 'wcl1',
                    "bgbh": "", // 报告编号
                    "ypmc": "", // 样品名称
                    "dj": "", // 等级
                    "gg": "", // 规格
                    "cj": "", // 厂家
                },
                {
                    label: '外掺料2',
                    prop: 'wcl2',
                    "bgbh": "", // 报告编号
                    "ypmc": "", // 样品名称
                    "dj": "", // 等级
                    "gg": "", // 规格
                    "cj": "", // 厂家
                },
            ],
            mixProportionForm: {
                "sn": {
                    "bgbh": "", // 报告编号
                    "ypmc": "", // 样品名称
                    "dj": "", // 等级
                    "gg": "", // 规格
                    "cj": "", // 厂家
                },
                "water": {
                    "bgbh": "", // 报告编号
                    "ypmc": "", // 样品名称
                    "dj": "", // 等级
                    "gg": "", // 规格
                    "cj": "", // 厂家
                },
                "fmh": {
                    "bgbh": "", // 报告编号
                    "ypmc": "", // 样品名称
                    "dj": "", // 等级
                    "gg": "", // 规格
                    "cj": "", // 厂家
                },
                "kzf": {
                    "bgbh": "", // 报告编号
                    "ypmc": "", // 样品名称
                    "dj": "", // 等级
                    "gg": "", // 规格
                    "cj": "", // 厂家
                },
                "xgl": {
                    "bgbh": "", // 报告编号
                    "ypmc": "", // 样品名称
                    "dj": "", // 等级
                    "gg": "", // 规格
                    "cj": "", // 厂家
                },
                "wcl1": {
                    "bgbh": "", // 报告编号
                    "ypmc": "", // 样品名称
                    "dj": "", // 等级
                    "gg": "", // 规格
                    "cj": "", // 厂家
                },
                "wcl2": {
                    "bgbh": "", // 报告编号
                    "ypmc": "", // 样品名称
                    "dj": "", // 等级
                    "gg": "", // 规格
                    "cj": "", // 厂家
                },
                "wjj1": {
                    "bgbh": "", // 报告编号
                    "ypmc": "", // 样品名称
                    "dj": "", // 等级
                    "gg": "", // 规格
                    "cj": "", // 厂家
                },
                "wjj2": {
                    "bgbh": "", // 报告编号
                    "ypmc": "", // 样品名称
                    "dj": "", // 等级
                    "gg": "", // 规格
                    "cj": "", // 厂家
                },
            },

            // 批量修改用到的筛选信息
            customerList: [],
            engineeringList: [],
        }
    },

    methods: {
        show() {
            this.showDialog = true;
            this.queryEditDetail();
            this.getErpCustomerAllResp();
        },
        handleClose() {
            this.showDialog = false;
        },

        saveForm() {
            console.log(this.customerForm);
            console.log(this.mixProportionForm);
            console.log(this.otherForm);

            this.$api.updateBatchPrintDetail({
                frwdhList: [this.taskSelects[0].frwdh],
                printJson: {
                    customer: this.customerForm,
                    mixProportion: this.mixProportionForm,
                    ...this.otherForm
                }
            }).then(res => {
                if (res.succ) {
                    this.$message.success('保存成功');
                    this.handleClose();
                } else {
                    this.$message.error(res.msg || '保存失败，请重试')
                }
            })
        },

        // 获取所有客户信息
        getErpCustomerAllResp() {
            this.$api.getErpCustomerAll({}, this).then(res => {
                if(res.succ){
                    this.customerList = res.data.list.map(i => {
                        return {
                            label: i.customerName,
                            value: i,
                        }
                    });
                    this.$set(this.customerFormContent[0], "options", this.customerList);
                }else{
                    this.$message.error(res.msg || '查询失败')
                }
            })
        },

        // 获取所有任务信息
        getEngineeringListAllResp(erpCustomerId) {
            this.$api.getEngineeringListAll({
                erpCustomerId: erpCustomerId
            }, this).then(res => {
                if(res.succ){
                    this.engineeringList = res.data.list.map(i => {
                        return {
                            label: i.projectName,
                            value: i,
                        }
                    });
                    this.$set(this.customerFormContent[1], "options", this.engineeringList);
                }else{
                    this.$message.error(res.msg || '查询失败')
                }
            })
        },

         // 选择回调
         changeCustomerSelect(item) {
            this.$set(this.customerForm, 'erpCustomerId', item.id)
            this.$set(this.customerForm,'projectName', '');
            this.$set(this.customerForm,'erpProjectId', '');
            this.$set(this.customerForm,'gcdz', '');
            this.$set(this.customerForm,'jsdwName', '');
            this.$set(this.customerForm,'buildName', '');
            this.$set(this.customerForm,'bjbh', '')
            
            this.getEngineeringListAllResp(item.id);
        },
        changeEngineeringSelect(item) {
            this.$set(this.customerForm,'projectName', item.projectName);
            this.$set(this.customerForm,'erpProjectId', item.id);
            this.$set(this.customerForm,'gcdz', item.projectAddress);
            this.$set(this.customerForm,'jsdwName', item.jsdw);
            this.$set(this.customerForm,'buildName', item.sgdw);
            this.$set(this.customerForm,'bjbh', item.bjbh)
        },

        handleSelectKytz() {
            this.$refs.panelSelecter.show();
        },

        panelSelectionChange(noList) {
            this.updateKytzList(noList);
        },

        updateKytzList(noList) {
            let kytzArr = [].concat(this.otherForm.kytzList);
            noList.forEach(el => {
                if (kytzArr.indexOf(el) < 0) {
                    kytzArr.push(el);
                }
            });
            this.otherForm.kytzList = kytzArr;
        },
        deleteKytz(item) {
            if (this.otherForm.kytzList.indexOf(item) > -1) {
                this.otherForm.kytzList.splice(this.otherForm.kytzList.indexOf(item), 1);
            }
        },

        queryEditDetail() {
            let frwdh = this.taskSelects[0].frwdh;
            this.$api.queryPrintByFrwdh(`frwdh=${frwdh}`).then(res => {
                if (res.succ && res.data) {
                    this.handlePrintJson(res.data.printJson);
                } else {
                    this.$message.error(res.msg || '查询失败')
                }
            })
        },

        handlePrintJson(printJson) {
            if (!printJson) return;
            if (printJson.customer) {
                this.customerForm = {
                    projectName: printJson.customer.projectName || "",
                    erpProjectId: printJson.customer.erpProjectId || "",
                    gcdz: printJson.customer.gcdz || "",
                    jsdwName: printJson.customer.jsdwName || "",
                    buildName: printJson.customer.buildName || "",
                    bjbh: printJson.customer.bjbh || "",
                };
            }
            if (printJson.otherForm) {
                this.otherForm = {
                    kytzList: printJson.kytzList || [],
                    kstzypbh: printJson.kstzypbh || "",
                    kstzjl: printJson.kstzjl || "",
                    kztzypbh: printJson.kztzypbh || "",
                    kztzjl: printJson.kztzjl || "",
                    kllztzypbh: printJson.kllztzypbh || "",
                    kllztzjl: printJson.kllztzjl || "",
                    llzhl: printJson.llzhl || "",
                };
            }
            if (printJson.mixProportion) {
                let allkeys = Object.keys(printJson.mixProportion);
                for (let i = 0; i < allkeys.length; i++) {
                    let key = allkeys[i];
                    this.mixProportionForm[key] = {
                        "bgbh": printJson.mixProportion[key].bgbh || "", // 报告编号
                        "ypmc": printJson.mixProportion[key].ypmc || "", // 样品名称
                        "dj": printJson.mixProportion[key].dj || "", // 等级
                        "gg": printJson.mixProportion[key].gg || "", // 规格
                        "cj": printJson.mixProportion[key].cj || "", // 厂家
                    };
                }
            }
        }
    },
}
</script>

<style lang="scss" scoped>
::v-deep .claim-dialog-box{
    // height: calc(100% - 40px);
    // overflow: hidden;
    .el-dialog__body{
        padding: 30px;
        padding-right: 10px;
    }
}
.form-view {
    margin-top: 30px;
    .form-box {
        flex-wrap: wrap;
        .form-item {
            margin-right: 30px;
        }
    }
}
.row-cell {
    text-align: center;
    font-size: 17px;
    font-weight: 500;
    margin-right: 10px;
}
.footer-btn {
    position: absolute;
    bottom: 10px;
    right: 80px;
}
</style>