<template>
  <div>
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane v-if="quickData.length > 0" label="快检" name="quick"></el-tab-pane>
      <template v-if="batchData.length > 0">
        <el-tab-pane v-for="(item, index) in batchData" :key="item.testProjectCode" :label="item.testProjectName" :name="item.testProjectCode">
        </el-tab-pane>
      </template>
    </el-tabs>
    <div v-show="activeName === 'quick'">
      <el-form ref="quickForm"  :model="quickForm" :disabled="loading2 || experimentStatus == 3">
        <el-row>
          <el-col :span="24" style="margin-bottom: 8px;">
            <el-form-item :label="item.testProjectName" v-for="item in quickData" :key="item.testProjectCode">
               <!-- v-model="item.objJson." -->
              <el-radio-group v-model="quickForm[item.testProjectCode].objJson.val" 
                v-if="item.testProjectName === '流动性' 
                || item.testProjectName === '保水性' 
                || item.testProjectName === '粘聚性'"
              >
                <el-radio label="fcc">非常差</el-radio>
                <el-radio label="yb">一般</el-radio>
                <el-radio label="hh">良好</el-radio>
              </el-radio-group>
              
              <template v-else-if="item.testProjectName === '坍落度'">
                <!-- <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="quickForm[item.testProjectCode].objJson.tld1" 
                  clearable
                  placeholder="坍落度"
                  :style="{'width': 150 + 'px'}"
                >
                </el-input> -->
                <el-select
                  v-model="quickForm[item.testProjectCode].objJson.tld1" 
                  filterable 
                  :style="{'width': 150 + 'px'}"
                  placeholder="坍落度" >
                  <el-option
                    v-for="item in 13"
                    :key="item"
                    :label="70 + item * 10"
                    :value="70 + item * 10">
                  </el-option>
                </el-select>
                
                <span>&nbsp;&nbsp;±&nbsp;&nbsp;</span>
                <el-select
                  v-model="quickForm[item.testProjectCode].objJson.tld2" 
                  filterable 
                  :style="{'width': 80 + 'px'}"
                  placeholder="差值" >
                  <el-option
                    v-for="item in 2"
                    :key="item"
                    :label="10 + item * 10"
                    :value="10 + item * 10">
                  </el-option>
                </el-select>
    
                <span>&nbsp;&nbsp;&nbsp;&nbsp;方式：</span>
                <el-radio-group  v-model="quickForm[item.testProjectCode].objJson.tldfs">
                  <el-radio label="目测">目测</el-radio>
                  <el-radio label="实测">实测</el-radio>
                </el-radio-group>
              </template>
              <template v-else-if="item.testProjectName === '目测砂率'">
                <el-input
                  v-manual-update
                  @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  v-model="quickForm[item.testProjectCode].objJson.sl" 
                  clearable
                  placeholder="目测砂率"
                  style="width: 253px; margin-left: 14px;"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="图片：">
              <div class="cbo">
                <div class="img-box">
                  <el-upload
                    accept=".jpeg,.png,.jpg,.bmp,.gif"
                    :action="baseUrl + '/upload/file'"
                    list-type="picture-card"
                    :on-preview="handlePreview"
                    :file-list="quickForm.img"
                    :on-success="handlePicSuccess"
                    :on-remove="handlePicRemove">
                    <i class="el-icon-plus"></i>
                  </el-upload>
                  <!-- <span>抽样图片</span> -->
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <!-- 含固量-->
    <div v-if="activeName === 'CONCRETE_ADMIXTURE_PARAM_WJJ_HGL'" class="custom-form">
      <el-form :disabled="loading2 || experimentStatus == 3" :inline="true" label-width="100px">
      
        <div class="mt16 flex-box">
          <el-form-item label="检测日期：" class="flex-item flex-box">
            <el-date-picker
              type="date"
              v-model="batchForm[activeName].jcrq"
              placeholder="选择日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="生产厂商控制值(%)：" label-width="150px" class="flex-item flex-box" >
            <el-input
              v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].sccjkzz1" 
              placeholder="请输入"
              @input="setHGLAll()"
              style="width: 100px;"
            >
            </el-input>
            <span>&nbsp;&nbsp;±&nbsp;&nbsp;</span>
            <el-input
              v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].sccjkzz2" 
              placeholder="请输入"
              @input="setHGLAll()"
              style="width: 100px;"
            >
            </el-input>
          </el-form-item>
        </div>
        <div v-for="(item, index) in 2" :key="index">
          <p class="info-form-title ">{{index == 0 ? '第一次试验' : '第二次试验'}}</p>
          <div class="info-form-piece flex-box">
            <el-form-item label="烘干的量瓶：" class="flex-item flex-box">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @input="val => setHGL(item)"
                placeholder="请输入"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="batchForm[activeName]['sy' + item + 'Info'].clp" 
                style="width: 130px;"
              >
                <template slot="append">g</template>
              </el-input>
            </el-form-item>
            <el-form-item label="瓶+试样质量：" class="flex-item flex-box" label-width="110px">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @input="val => setHGL(item)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="batchForm[activeName]['sy' + item + 'Info'].syzl" 
                placeholder="请输入"
                style="width: 130px;"
              >
                <template slot="append">g</template>
              </el-input>
            </el-form-item>
            <el-form-item label="瓶+烘干后质量：" class="flex-item flex-box" label-width="120px">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @input="val => setHGL(item)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="batchForm[activeName]['sy' + item + 'Info'].hghzl" 
                placeholder="请输入"
                style="width: 130px;"
              >
                <template slot="append">g</template>
              </el-input>
            </el-form-item>
            
            <el-form-item label="含固量：" label-width="70px">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                disabled
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="batchForm[activeName]['sy' + item + 'Info'].hgl" 
                style="width: 130px;"
              >
                <template slot="append">%</template>
              </el-input>
            </el-form-item>
          </div>
        </div>
        <div class="line"></div>
        <el-row>
          <el-col :span="10">
            <el-form-item class="flex-box" label="平均值：">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                disabled
                v-model="batchForm[activeName].pjhgl" 
                :style="{'width': 180 + 'px'}"
              >
                <template slot="append">%</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="14">
            <el-form-item class="flex-box" label="单项结论：">
              <el-input
                :disabled="dxjlDisabled"
                v-model="batchForm[activeName].dxjl" 
                placeholder="请输入"
                :style="{'width': 180 + 'px'}"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row class="mt16">
          <el-col :span="24">
            <el-form-item label="检测图片：">
              <div class="cbo">
                <div class="img-box">
                  <el-upload
                    accept=".jpeg,.png,.jpg,.bmp,.gif"
                    :action="baseUrl + '/upload/file'"
                    list-type="picture-card"
                    :on-preview="handlePreview"
                    :file-list="batchForm[activeName].img"
                    :on-success="handlePicSuccess"
                    :on-remove="handlePicRemove">
                    <i class="el-icon-plus"></i>
                  </el-upload>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    
    <!-- 密度 -->
    <div v-if="activeName === 'CONCRETE_ADMIXTURE_PARAM_WJJ_MD'" class="custom-form">
      
      <div class="mt16">
        <!-- <span @click="mdType = 'bzp'" class="tag-btn" :class="{'tag-btn-primary' : mdType === 'bzp'}">比重瓶法</span> -->
        <span @click="mdType = 'jmmd'" class="tag-btn" :class="{'tag-btn-primary' : mdType === 'jmmd'}">精密密度计法</span>
      </div>
      
      <el-form :disabled="loading2 || experimentStatus == 3" :inline="true" label-width="155px">
        <div class="mt16 jcrq-box flex-box">
          <el-form-item label="检测日期：" class="flex-item flex-box">
            <el-date-picker
              type="date"
              v-model="batchForm[activeName].jcrq"
              placeholder="选择日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="生产厂商控制值(%)：" label-width="150px" class="flex-item flex-box" >
            <el-input
              v-manual-update
              @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].sccjkzz1" 
              placeholder="请输入"
              @input="val => setAlltj()"
              style="width: 100px;"
            >
            </el-input>
            <span>&nbsp;&nbsp;±&nbsp;&nbsp;</span>
            <el-input
              v-manual-update
          
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].sccjkzz2" 
              placeholder="请输入"
              @input="val => setAlltj()"
              style="width: 100px;"
            >
            </el-input>
          </el-form-item>
          
        </div>
        <div v-if="mdType == 'bzp'">
          <div class="flex-box mb16 mt24">
            <el-form-item label="干燥比重瓶质量：" class="flex-item flex-box">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="batchForm[activeName].bzpInfo.gzbzpzl" 
                @input="val => setTJ()"
                placeholder="请输入"
              >
                <template slot="append">g</template>
              </el-input>
            </el-form-item>
            <el-form-item class="flex-item flex-box" label="装水后比重瓶质量：">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="batchForm[activeName].bzpInfo.zshbzpzl"
                @input="val => setTJ()"
                placeholder="请输入"
              >
                <template slot="append">g</template>
              </el-input>
            </el-form-item>
            <el-form-item class="flex-item flex-box" label="体积：">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="batchForm[activeName].bzpInfo.tj"
                disabled
              >
                <template slot="append">cm³</template>
              </el-input>
            </el-form-item>
          </div>
          <div class="line"></div>
          <el-row>
            <el-col :span="10">
              <el-form-item class="flex-box" label="装外加剂后比重瓶质量：" label-width="170px">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="batchForm[activeName].bzpInfo.zwjjhbzpzl" 
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  @input="val => setWJJJG()"
                  :style="{'width': 180 + 'px'}"
                >
                  <template slot="append">g</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="10">
              <el-form-item class="flex-box" label="试验结果：" label-width="170px">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="batchForm[activeName].bzpInfo.syjg" 
                  disabled
                  :style="{'width': 180 + 'px'}"
                >
                  <template slot="append">%</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="14">
              <el-form-item class="flex-box" label="单项结论：">
                <el-input
                  v-model="batchForm[activeName].bzpInfo.dxjl" 
                  clearable
                  :disabled="dxjlDisabled"
                  placeholder="请输入"
                  :style="{'width': 180 + 'px'}"
                >
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          
        </div>
        <el-row class="mt16" v-if="mdType == 'jmmd'">
          <el-col :span="8">
            <el-form-item class="mt8" label="试样1密度（g/ml）：" >
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @input="val => setjmmdPJZ()"
                v-model="batchForm[activeName].jmmdInfo.sy1md" 
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                :style="{'width': 180 + 'px'}"
                placeholder="请输入"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" class="mt8">
            <el-form-item class="flex-box" label="试样2密度（g/ml）：">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @input="val => setjmmdPJZ()"
                v-model="batchForm[activeName].jmmdInfo.sy2md" 
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                :style="{'width': 180 + 'px'}"
                placeholder="请输入"
              >
              </el-input>
            </el-form-item>
          </el-col>
          
          <el-col :span="8" class="mt8">
            <el-form-item class="flex-box" label="密度平均值(g/ml)：">
              <el-input
                v-model="batchForm[activeName].jmmdInfo.mdpjz" 
                v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                disabled
                :style="{'width': 180 + 'px'}"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24" class="mt16">
            <el-form-item class="flex-box" label="单项结论：">
              <el-input
                :disabled="dxjlDisabled"
                v-model="batchForm[activeName].dxjl" 
                placeholder="请输入"
                :style="{'width': 180 + 'px'}"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        
        
        <el-row class="mt16">
          <el-col :span="24">
            <el-form-item label="检测图片：">
              <div class="cbo">
                <div class="img-box">
                  <el-upload
                    accept=".jpeg,.png,.jpg,.bmp,.gif"
                    :action="baseUrl + '/upload/file'"
                    list-type="picture-card"
                    :on-preview="handlePreview"
                    :file-list="batchForm[activeName].img"
                    :on-success="handlePicSuccess"
                    :on-remove="handlePicRemove">
                    <i class="el-icon-plus"></i>
                  </el-upload>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    
    <!-- PH值 -->
    <div v-if="activeName === 'CONCRETE_ADMIXTURE_PARAM_WJJ_PHZ'" class="custom-form">
      <el-form :disabled="loading2 || experimentStatus == 3" :inline="true" label-position="right" label-width="100px">
        <el-row class="mt16">
          <el-col :span="8">
            <el-form-item label="检测日期：" class="flex-item flex-box">
              <el-date-picker
                type="date"
                v-model="batchForm[activeName].jcrq"
                placeholder="选择日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8" class="mt8">
            <el-form-item label="生产厂商控制值(%)：" label-width="150px" class="flex-item flex-box" >
              <el-input
                v-manual-update
                @input="val => setPHPJZ('sy1ph')"
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="batchForm[activeName].sccjkzz1" 
                placeholder="请输入"
                style="width: 100px;"
              >
              </el-input>
              <span>&nbsp;&nbsp;±&nbsp;&nbsp;</span>
              <el-input
                v-manual-update
                @input="val => setPHPJZ('sy1ph')"
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="batchForm[activeName].sccjkzz2" 
                placeholder="请输入"
                style="width: 100px;"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row class="mt16">
          <el-col :span="8">
            <el-form-item class="mt8" label="试样1PH：">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @input="val => setPHPJZ('sy1ph')"
                v-model="batchForm[activeName].sy1ph" 
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                :style="{'width': 180 + 'px'}"
                placeholder="请输入"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" class="mt8">
            <el-form-item class="flex-box" label="试样2PH：" label-width="150px">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @input="val => setPHPJZ('sy2ph')"
                v-model="batchForm[activeName].sy2ph" 
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                :style="{'width': 180 + 'px'}"
                placeholder="请输入"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row class="mt16">
          <el-col :span="8" class="mt8">
            <el-form-item class="flex-box" label="平均PH值：">
              <el-input
                v-model="batchForm[activeName].pjph" 
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                disabled
                :style="{'width': 180 + 'px'}"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" class="mt8">
            <el-form-item class="flex-box" label="单项结论：" label-width="150px">
              <el-input
                v-model="batchForm[activeName].dxjl" 
                clearable
                placeholder="请输入"
                :disabled="dxjlDisabled"
                :style="{'width': 180 + 'px'}"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        
        
        <el-row>
          <el-col :span="24">
            <el-form-item label="检测图片：">
              <div class="cbo">
                <div class="img-box">
                  <el-upload
                    accept=".jpeg,.png,.jpg,.bmp,.gif"
                    :action="baseUrl + '/upload/file'"
                    list-type="picture-card"
                    :on-preview="handlePreview"
                    :file-list="batchForm[activeName].img"
                    :on-success="handlePicSuccess"
                    :on-remove="handlePicRemove">
                    <i class="el-icon-plus"></i>
                  </el-upload>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    
    <!-- 凝结时间之差 -->
    <div v-if="activeName === 'CONCRETE_ADMIXTURE_PARAM_WJJ_NJSJZC'" class="custom-form">
      <el-form :disabled="loading2 || experimentStatus == 3" :inline="true">
        <!-- <div class="mt16 jcrq-box">
          <el-form-item label="检测日期：">
            <el-date-picker
              type="date"
              v-model="batchForm[activeName].jcrq"
              placeholder="选择日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
        </div> -->
        
        <div class="mt16">
          <span @click="njsjIndex = 0" class="tag-btn" :class="{'tag-btn-primary' : njsjIndex === 0}">第一次试验</span>
          <span @click="njsjIndex = 1" class="tag-btn" :class="{'tag-btn-primary' : njsjIndex === 1}">第二次试验</span>
          <span @click="njsjIndex = 2" class="tag-btn" :class="{'tag-btn-primary' : njsjIndex === 2}">第三次试验</span>
        </div>
        
        <div class="mt16 flex-box">
          <el-form-item label="检测时间：" class="flex-item flex-box" >
            <el-date-picker
              v-model="batchForm[activeName].njsjzcList[njsjIndex].jcrq"
              type="datetime"
              value-format="yyyy-MM-dd HH:mm"
              format="MM-dd HH:mm"
              :default-value= "new Date()"
              placeholder="时间点"
            >
              <template slot="append">HH:mm</template>
            </el-date-picker>
          </el-form-item>
          <!-- <el-form-item class="flex-item flex-box" label="加水时间：">
            <el-date-picker
              v-model="batchForm[activeName].njsjzcList[njsjIndex].jssj"
              @input="setNJSJ('all')"
              type="datetime"
              value-format="yyyy-MM-dd HH:mm"
              format="MM-dd HH:mm"
              :default-value= "new Date()"
              placeholder="时间点"
            >
              <template slot="append">HH:mm</template>
            </el-date-picker>
          </el-form-item> -->
          <el-form-item class="flex-item flex-box" label="基准-加水时间：">
            <el-date-picker
              v-model="batchForm[activeName].njsjzcList[njsjIndex].jzhntjssj"
              @input="setNJSJ('all')"
              type="datetime"
              value-format="yyyy-MM-dd HH:mm"
              format="MM-dd HH:mm"
              :default-value= "new Date()"
              placeholder="时间点"
            >
              <template slot="append">HH:mm</template>
            </el-date-picker>
          </el-form-item>
          <el-form-item class="flex-item flex-box" label="受检-加水时间：">
            <el-date-picker
              v-model="batchForm[activeName].njsjzcList[njsjIndex].sjhntjssj"
              @input="setNJSJ('all')"
              type="datetime"
              value-format="yyyy-MM-dd HH:mm"
              format="MM-dd HH:mm"
              :default-value= "new Date()"
              placeholder="时间点"
            >
              <template slot="append">HH:mm</template>
            </el-date-picker>
          </el-form-item>
        </div>
        <div style="margin: 10px 0 -40px;">
          <el-form-item class="flex-box" label="">
            <p style="text-align: right;">在第<el-input-number v-model="addNjsjindex"
              :max="batchForm[activeName].njsjzcList[njsjIndex].hntInfoList.length" :min="1"
              style="width: 120px !important"
              :step="1" step-strictly>
            </el-input-number>行&nbsp;<el-button  type="text" @click="addNjsj">新增</el-button></p>
          </el-form-item>
        </div>
        <div v-for="(item, index) in batchForm[activeName].njsjzcList[njsjIndex].hntInfoList" :key="index">
          <p class="info-form-title mt16">序号：{{index + 1}}
            <!-- <el-button style="margin-left: 15px;" type="text" class="fr" @click="addNjsj(index)">新增</el-button> -->
            <el-button v-if="batchForm[activeName].njsjzcList[njsjIndex].hntInfoList.length > 1" type="text" class="fr" @click="delNJSJZC(index)">删除</el-button>
          </p>
          <div class="info-form-piece">
            <div class="flex-box">
              <el-form-item>
                <span>基准混凝土</span>
              </el-form-item>
              <el-form-item label="时间：" class="flex-item flex-box" >
                <el-date-picker
                  v-model="item.jzhntsj"
                  @input="setNJSJ('jz')"
                  type="datetime"
                  value-format="yyyy-MM-dd HH:mm"
                  format="MM-dd HH:mm"
                  placeholder="时间点"
                  style="width: 140px;"
                >
                  <template slot="append">HH:mm</template>
                </el-date-picker>
              </el-form-item>
              <el-form-item label="贯入阻力：" class="flex-item flex-box">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  v-model="item.jzhntgrzl" 
                  @input="setNJSJqd('jz', index)"
                  placeholder="请输入"
                  style="width: 120px;"
                >
                  <template slot="append">kN</template>
                </el-input>
              </el-form-item>
              <el-form-item label="试针面积：" class="flex-item flex-box">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  placeholder="请输入"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  v-model="item.jzhntszmj" 
                  @input="setNJSJqd('jz', index)"
                  style="width: 120px;"
                >
                  <template slot="append">mm²</template>
                </el-input>
              </el-form-item>
              <el-form-item label="强度：" class="flex-item flex-box">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  v-model="item.jzhntqd" 
                  style="width: 120px;"
                  disabled
                >
                  <template slot="append">MPa</template>
                </el-input>
              </el-form-item>
            </div>
            <div class="flex-box">
              <el-form-item>
                <span>受检混凝土</span>
              </el-form-item>
              <el-form-item label="时间：" class="flex-item flex-box" >
                <el-date-picker
                  v-model="item.sjhntsj"
                  @input="setNJSJ('sj')"
                  type="datetime"
                  value-format="yyyy-MM-dd HH:mm"
                  format="MM-dd HH:mm"
                  :default-value= "new Date()"
                  placeholder="时间点"
                  style="width: 140px;"
                >
                  <template slot="append">HH:mm</template>
                </el-date-picker>
              </el-form-item>
              <el-form-item label="贯入阻力：" class="flex-item flex-box">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  v-model="item.sjhntgrzl" 
                  @input="setNJSJqd('sj', index)"
                  placeholder="请输入"
                  style="width: 120px;"
                >
                  <template slot="append">kN</template>
                </el-input>
              </el-form-item>
              <el-form-item label="试针面积：" class="flex-item flex-box">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  placeholder="请输入"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  v-model="item.sjhntszmj" 
                  @input="setNJSJqd('sj', index)"
                  style="width: 120px;"
                >
                  <template slot="append">mm²</template>
                </el-input>
              </el-form-item>
              <el-form-item label="强度：" class="flex-item flex-box">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  v-model="item.sjhntqd" 
                  style="width: 120px;"
                  disabled
                >
                  <template slot="append">MPa</template>
                </el-input>
              </el-form-item>
            </div>
          </div>
        </div>
        
        
        <div class="mt16 flex-box">
          <el-form-item>
            <span>基准混凝土</span>
          </el-form-item>
          <el-form-item label="到达初凝时间：" class="flex-item flex-box" >
            <el-input
              v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              v-model="batchForm[activeName].njsjzcList[njsjIndex].jzhntddcnsj"
              disabled
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
            >
              <template slot="append">min</template>
            </el-input>
          </el-form-item>
          <el-form-item label="到达终凝时间：" class="flex-item flex-box">
            <el-input
              v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              v-model="batchForm[activeName].njsjzcList[njsjIndex].jzhntddznsj"
              disabled
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
            >
              <template slot="append">min</template>
            </el-input>
          </el-form-item>
        </div>
        <div class="mt16 flex-box">
          <el-form-item>
            <span>受检混凝土</span>
          </el-form-item>
          <el-form-item label="到达初凝时间：" class="flex-item flex-box" >
            <el-input
              v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              disabled
              v-model="batchForm[activeName].njsjzcList[njsjIndex].sjhntddcnsj"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
            >
              <template slot="append">min</template>
            </el-input>
          </el-form-item>
          <el-form-item label="到达终凝时间：" class="flex-item flex-box">
            <el-input
              v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              v-model="batchForm[activeName].njsjzcList[njsjIndex].sjhntddznsj"
              disabled
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
            >
              <template slot="append">min</template>
            </el-input>
          </el-form-item>
        </div>
        
        
        <div class="line"></div>
        
        
        <div class="mt16 flex-box">
          <el-form-item>
            <span>基准混凝土</span>
          </el-form-item>
          <el-form-item label="到达初凝时间平均值：" class="flex-item flex-box" >
            <el-input
              v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              v-model="batchForm[activeName].jzhntcnsjpjz"
              disabled
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
            >
              <template slot="append">min</template>
            </el-input>
          </el-form-item>
          <el-form-item label="到达终凝时间平均值：" class="flex-item flex-box">
            <el-input
              v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              v-model="batchForm[activeName].jzhntznsjpjz"
              disabled
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
            >
              <template slot="append">min</template>
            </el-input>
          </el-form-item>
        </div>
        <div class="mt16 flex-box">
          <el-form-item>
            <span>受检混凝土</span>
          </el-form-item>
          <el-form-item label="到达初凝时间平均值：" class="flex-item flex-box" >
            <el-input
              v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              v-model="batchForm[activeName].sjhntcnsjpjz"
              disabled
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
            >
              <template slot="append">min</template>
            </el-input>
          </el-form-item>
          <el-form-item label="到达终凝时间平均值：" class="flex-item flex-box">
            <el-input
              v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              v-model="batchForm[activeName].sjhntznsjpjz"
              disabled
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
            >
              <template slot="append">min</template>
            </el-input>
          </el-form-item>
        </div>
        
        <el-row>
          <el-col :span="10">
            <el-form-item class="flex-box" label="初凝时间之差：">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                disabled
                v-model="batchForm[activeName].cnsjzc" 
                :style="{'width': 180 + 'px'}"
              >
                <template slot="append">min</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="14">
            <el-form-item class="flex-box" label="单项结论：">
              <el-input
                :disabled="dxjlDisabled"
                v-model="batchForm[activeName].cndxjl" 
                placeholder="请输入"
                :style="{'width': 180 + 'px'}"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="10">
            <el-form-item class="flex-box" label="终凝时间之差：">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                disabled
                v-model="batchForm[activeName].znsjzc" 
                :style="{'width': 180 + 'px'}"
              >
                <template slot="append">min</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="14">
            <el-form-item class="flex-box" label="单项结论：">
              <el-input
                :disabled="dxjlDisabled"
                v-model="batchForm[activeName].zndxjl" 
                placeholder="请输入"
                :style="{'width': 180 + 'px'}"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row class="mt16">
          <el-col :span="24">
            <el-form-item label="检测图片：">
              <div class="cbo">
                <div class="img-box">
                  <el-upload
                    accept=".jpeg,.png,.jpg,.bmp,.gif"
                    :action="baseUrl + '/upload/file'"
                    list-type="picture-card"
                    :on-preview="handlePreview"
                    :file-list="batchForm[activeName].img"
                    :on-success="handlePicSuccess"
                    :on-remove="handlePicRemove">
                    <i class="el-icon-plus"></i>
                  </el-upload>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <!-- 泌水率比 -->
    <div v-if="activeName === 'CONCRETE_ADMIXTURE_PARAM_WJJ_QSLB'" class="custom-form">
      <el-form :disabled="loading2 || experimentStatus == 3" :inline="true" class="mt16" label-width="120px">
        <!-- <div class="mt16 jcrq-box">
          <el-form-item label="检测日期：">
            <el-date-picker
              type="date"
              v-model="batchForm[activeName].jcrq"
              placeholder="选择日期"
              format="yyyy-MM-dd"
              :default-value="new Date()"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
        </div> -->
        <div class="mt16">
          <span @click="bslbIndex = 0" class="tag-btn" :class="{'tag-btn-primary' : bslbIndex === 0}">第一次试验</span>
          <span @click="bslbIndex = 1" class="tag-btn" :class="{'tag-btn-primary' : bslbIndex === 1}">第二次试验</span>
          <span @click="bslbIndex = 2" class="tag-btn" :class="{'tag-btn-primary' : bslbIndex === 2}">第三次试验</span>
        </div>
        <div class="mt16 flex-box">
          <el-form-item label="检测时间：" class="flex-item flex-box" >
            <el-date-picker
              v-model="batchForm[activeName].sjhntqslInfoList[bslbIndex].subSjhntqslInfo.jcrq"
              @input="setNJSJ('e')"
              type="datetime"
              value-format="yyyy-MM-dd HH:mm"
              format="MM-dd HH:mm"
              :default-value= "new Date()"
              placeholder="时间点"
            >
              <template slot="append">HH:mm</template>
            </el-date-picker>
          </el-form-item>
          <el-form-item class="flex-item flex-box" label="加水时间：">
            <el-date-picker
              v-model="batchForm[activeName].sjhntqslInfoList[bslbIndex].subSjhntqslInfo.jssj"
              @input="setNJSJ('e')"
              type="datetime"
              value-format="yyyy-MM-dd HH:mm"
              format="MM-dd HH:mm"
              :default-value= "new Date()"
              placeholder="时间点"
            >
              <template slot="append">HH:mm</template>
            </el-date-picker>
          </el-form-item>
          <el-form-item class="flex-item flex-box" label="">
            <el-button class="fr" type="text" @click="addBsl">新增一行试验</el-button>
          </el-form-item>
        </div>
        <div class="table-box-kzf mt16">
          <div class="tb-title flex-box">
            <div class="tb-title-con flex-item"><span>基准混凝土</span><span>时间</span><span>泌水量</span></div>
            <div class="tb-title-con flex-item"><span>受检混凝土</span><span>时间</span><span>泌水量</span></div>
            <div class="tb-title-con" style="width: 120px;">操作</div>
          </div>
          <div class="tb-content">
            <div class="flex-box tb-item" v-for="(item, index) in batchForm[activeName].sjhntqslInfoList[bslbIndex].subSjhntqslInfo.subSjhntqslInfoList" :key="index">
              <div class="flex-item flex-box">
                <el-form-item label="" class="flex-item">
                  <el-date-picker
                    v-model="item.jzhntsj"
                    type="datetime"
                    value-format="yyyy-MM-dd HH:mm"
                    format="MM-dd HH:mm"
                    placeholder="时间"
                    style="width: 180px;"
                  >
                  </el-date-picker>
                </el-form-item>
                <el-form-item label="" class="flex-item">
                  <el-input
                    v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 100000 ? e.target.value.match(/100000|\d{1,5}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                    style="width: 180px;"
                    v-model="item.jzhntqsl"
                    @input="val => setBsl('jz')"
                    @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                    placeholder="请输入"
                  >
                    <template slot="append">g</template>
                  </el-input>
                </el-form-item>
              </div>
              <div class="flex-item flex-box">
                <el-form-item label="" class="flex-item">
                  <el-date-picker
                    v-model="item.sjhntsj"
                    type="datetime"
                    value-format="yyyy-MM-dd HH:mm"
                    format="MM-dd HH:mm"
                    :default-value= "new Date()"
                    placeholder="时间"
                  >
                  </el-date-picker>
                </el-form-item>
                <el-form-item label="" class="flex-item">
                  <el-input
                    v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 100000 ? e.target.value.match(/100000|\d{1,5}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                    v-model="item.sjhntqsl"
                    @input="val => setBsl('sj')"
                    @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                    placeholder="请输入"
                  >
                    <template slot="append">g</template>
                  </el-input>
                </el-form-item>
              </div>
              <div class="" style="width: 120px; text-align: center; padding-top: 10px;">
                <el-button :style="{'padding-top': index === 0 ?'24px' : 0}" type="text" @click="delBsl(index)">删除</el-button>
              </div>
            </div>
          </div>
        </div>
        <div class="mt16 flex-box">
          <el-form-item label="基准混凝土-泌水总量：" class="flex-item flex-box"  label-width="165px">
            <el-input
              v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 100000 ? e.target.value.match(/100000|\d{1,5}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              v-model="batchForm[activeName].sjhntqslInfoList[bslbIndex].subSjhntqslInfo.jzhntqszl"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              disabled
            >
              <template slot="append">g</template>
            </el-input>
          </el-form-item>
          <el-form-item class="flex-item flex-box" label="受检混凝土-泌水总量：" label-width="165px">
            <el-input
              v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 100000 ? e.target.value.match(/100000|\d{1,5}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              v-model="batchForm[activeName].sjhntqslInfoList[bslbIndex].subSjhntqslInfo.sjhntqszl"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              disabled
            >
              <template slot="append">g</template>
            </el-input>
          </el-form-item>
        </div>
        
        
        <div class="table-box-kzf mt16">
          <div class="tb-title">基准混凝土</div>
          <div class="tb-content">
            <div class="flex-box tb-item">
              <el-form-item label="筒重：" class="flex-item flex-box" >
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 100000 ? e.target.value.match(/100000|\d{1,5}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="batchForm[activeName].sjhntqslInfoList[bslbIndex].subSjhntqslInfo.jztz"
                  @input="val => setBSLsyzl('jz')"
                  placeholder="请输入"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                >
                  <template slot="append">g</template>
                </el-input>
              </el-form-item>
              <el-form-item label="筒+试样：" class="flex-item flex-box">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 100000 ? e.target.value.match(/100000|\d{1,5}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="batchForm[activeName].sjhntqslInfoList[bslbIndex].subSjhntqslInfo.jztjsy"
                  @input="val => setBSLsyzl('jz')"
                  placeholder="请输入"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                >
                  <template slot="append">g</template>
                </el-input>
              </el-form-item>
              <el-form-item label="试样重量：" class="flex-item flex-box">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 100000 ? e.target.value.match(/100000|\d{1,5}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="batchForm[activeName].sjhntqslInfoList[bslbIndex].subSjhntqslInfo.jzsyjz"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  disabled
                >
                  <template slot="append">g</template>
                </el-input>
              </el-form-item>
            </div>
            <div class="flex-box tb-item">
              <el-form-item label="拌合物用水量：" class="flex-item flex-box" >
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 100000 ? e.target.value.match(/100000|\d{1,5}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="batchForm[activeName].sjhntqslInfoList[bslbIndex].subSjhntqslInfo.jzbhwysl"
                  @input="val => setBsl('jz')"
                  placeholder="请输入"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                >
                  <template slot="append">g</template>
                </el-input>
              </el-form-item>
              <el-form-item label="拌合物总质量：" class="flex-item flex-box">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000000 ? e.target.value.match(/10000000|\d{1,5}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="batchForm[activeName].sjhntqslInfoList[bslbIndex].subSjhntqslInfo.jzbhwzzl"
                  @input="val => setBsl('jz')"
                  placeholder="请输入"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                >
                  <template slot="append">g</template>
                </el-input>
              </el-form-item>
              <el-form-item label="泌水率：" class="flex-item flex-box">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 100000 ? e.target.value.match(/100000|\d{1,5}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="batchForm[activeName].sjhntqslInfoList[bslbIndex].subSjhntqslInfo.jzqsl"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  disabled
                >
                  <template slot="append">%</template>
                </el-input>
              </el-form-item>
            </div>
          </div>
        </div>
        <div class="table-box-kzf mt16">
          <div class="tb-title">受检混凝土</div>
          <div class="tb-content">
            <div class="flex-box tb-item">
              <el-form-item label="筒重：" class="flex-item flex-box" >
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 100000 ? e.target.value.match(/100000|\d{1,5}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="batchForm[activeName].sjhntqslInfoList[bslbIndex].subSjhntqslInfo.sjtz"
                  @input="val => setBSLsyzl('sj')"
                  placeholder="请输入"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                >
                  <template slot="append">g</template>
                </el-input>
              </el-form-item>
              <el-form-item label="筒+试样：" class="flex-item flex-box">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 100000 ? e.target.value.match(/100000|\d{1,5}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="batchForm[activeName].sjhntqslInfoList[bslbIndex].subSjhntqslInfo.sjtjsy"
                  @input="val => setBSLsyzl('sj')"
                  placeholder="请输入"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                >
                  <template slot="append">g</template>
                </el-input>
              </el-form-item>
              <el-form-item label="试样重量：" class="flex-item flex-box">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 100000 ? e.target.value.match(/100000|\d{1,5}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="batchForm[activeName].sjhntqslInfoList[bslbIndex].subSjhntqslInfo.sjsyjz"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  disabled
                >
                  <template slot="append">g</template>
                </el-input>
              </el-form-item>
            </div>
            <div class="flex-box tb-item">
              <el-form-item label="拌合物用水量：" class="flex-item flex-box" >
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 100000 ? e.target.value.match(/100000|\d{1,5}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="batchForm[activeName].sjhntqslInfoList[bslbIndex].subSjhntqslInfo.sjbhwysl"
                  @input="val => setBsl('sj')"
                  placeholder="请输入"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                >
                  <template slot="append">g</template>
                </el-input>
              </el-form-item>
              <el-form-item label="拌合物总质量：" class="flex-item flex-box">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000000 ? e.target.value.match(/10000000|\d{1,5}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="batchForm[activeName].sjhntqslInfoList[bslbIndex].subSjhntqslInfo.sjbhwzzl"
                  @input="val => setBsl('sj')"
                  placeholder="请输入"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                >
                  <template slot="append">g</template>
                </el-input>
              </el-form-item>
              <el-form-item label="泌水率：" class="flex-item flex-box">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 100000 ? e.target.value.match(/100000|\d{1,5}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="batchForm[activeName].sjhntqslInfoList[bslbIndex].subSjhntqslInfo.sjqsl"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  disabled
                >
                  <template slot="append">%</template>
                </el-input>
              </el-form-item>
            </div>
          </div>
        </div>
        
        
        <div class="line"></div>
        <div class="mt16 flex-box">
          <el-form-item label="基准混凝土平均值：" class="flex-item flex-box"  label-width="165px">
            <el-input
              v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 100000 ? e.target.value.match(/100000|\d{1,5}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              v-model="batchForm[activeName].jzhntpjz"
              disabled
            >
              <template slot="append">g</template>
            </el-input>
          </el-form-item>
          <el-form-item label="受检混凝土平均值：" class="flex-item flex-box" label-width="165px">
            <el-input
              v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 100000 ? e.target.value.match(/100000|\d{1,5}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              v-model="batchForm[activeName].sjhntpjz"
              disabled
            >
              <template slot="append">g</template>
            </el-input>
          </el-form-item>
          <el-form-item label="泌水率比：" class="flex-item flex-box">
            <el-input
              v-manual-update
              @keyup.native="e=> e.target.value = e.target.value * 1 > 100000 ? e.target.value.match(/100000|\d{1,5}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              v-model="batchForm[activeName].qslb"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              disabled
            >
              <template slot="append">%</template>
            </el-input>
          </el-form-item>
        </div>
        
        <div class="mt16 flex-box">
          <el-form-item label="单项结论：" class="flex-item flex-box" >
            <el-input
              type="text"
              v-model="batchForm[activeName].dxjl" 
              :disabled="dxjlDisabled"
            >
            </el-input>
          </el-form-item>
          
        </div>
        
        
        <el-row class="mt16">
          <el-col :span="24">
            <el-form-item label="检测图片：">
              <div class="cbo">
                <div class="img-box">
                  <el-upload
                    accept=".jpeg,.png,.jpg,.bmp,.gif"
                    :action="baseUrl + '/upload/file'"
                    list-type="picture-card"
                    :on-preview="handlePreview"
                    :file-list="batchForm[activeName].img"
                    :on-success="handlePicSuccess"
                    :on-remove="handlePicRemove">
                    <i class="el-icon-plus"></i>
                  </el-upload>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    
    <!-- //减水率 -->
    <div v-if="activeName === 'CONCRETE_ADMIXTURE_PARAM_WJJ_JSL'" class="custom-form">
      <el-form :disabled="loading2 || experimentStatus == 3" :inline="true" class="mt16">
        <div class="mt16 jcrq-box">
          <el-form-item label="检测日期：" label-width="90px">
            <el-date-picker
              type="date"
              v-model="batchForm[activeName].jcrq"
              placeholder="选择日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
        </div>
        <div class="mt16 flex-box">
          <el-form-item label="水泥：" class="flex-item flex-box"  label-width="90px">
              <!-- @input="setSYZL" -->
            <el-input
              v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].sn" 
              placeholder="请输入"
            >
              <template slot="append">kg/m³</template>
            </el-input>
          </el-form-item>
          <el-form-item class="flex-item flex-box" label="砂：">
            <el-input
              v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].sha"
            >
              <template slot="append">kg/m³</template>
            </el-input>
          </el-form-item>
          <!-- 注意 如果样品等级为PI/PII2种类型，默认值为0.500，否则0.53 -->
          <el-form-item label="石：" class="flex-item flex-box" >
              <!-- @input="setSYZL" -->
            <el-input
              v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].shi" 
              placeholder="请输入"
            >
              <template slot="append">kg/m³</template>
            </el-input>
          </el-form-item>
          <el-form-item label="外加剂：" class="flex-item flex-box" >
            <el-input
              v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              placeholder="请输入"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].wjj" 
            >
              <template slot="append">kg/m³</template>
            </el-input>
          </el-form-item>
        </div>
        
        <!-- batchForm[activeName].bbdycsy -->
        <div class="table-box-kzf mt16" v-for="(item, index) in batchForm[activeName].jslList" :key="index">
          <div class="tb-title">第{{index + 1}}次试验</div>
          <div class="tb-content">
            <div class="flex-box tb-item">
              <div class="tb-left" style="width: 88px;">基准混凝土</div>
              <el-form-item label="基准混凝土用水量：" class="flex-item">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="item.jzhntysl"
                  :key="`jzhntysl-${index}`"
                  @input="val => setJSL(index)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  placeholder="请输入"
                  style="width: 135px;"
                >
                  <template slot="append">ml</template>
                </el-input>
              </el-form-item>
              <el-form-item label="基准实测坍落度：" class="flex-item">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="item.jzsctld"
                  :key="`jzSctld-${index}`"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  placeholder="请输入"
                  style="width: 135px;"
                >
                  <template slot="append">mm</template>
                </el-input>
              </el-form-item>
            </div>
            <div class="flex-box tb-item">
              <div class="tb-left" style="width: 88px;">受检混凝土</div>
              <el-form-item label="受检混凝土用水量：" class="flex-item">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="item.sjhntysl"
                  :key="`sjhntysl-${index}`"
                  @input="val => setJSL(index)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  placeholder="请输入"
                  style="width: 135px;"
                >
                  <template slot="append">ml</template>
                </el-input>
              </el-form-item>
              <el-form-item label="受检实测坍落度：" class="flex-item">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="item.sjsctld"
                  :key="`sjSctld-${index}`"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  placeholder="请输入"
                  style="width: 135px;"
                >
                  <template slot="append">mm</template>
                </el-input>
              </el-form-item>
              <!-- <el-form-item label="减水率：" class="flex-item">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="item.bzkqnd"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  disabled
                  style="width: 135px;"
                >
                  <template slot="append">%</template>
                </el-input>
              </el-form-item> -->
            </div>
          </div>
        
          <div class="tb-bottom">
            <el-form-item label="减水率：" class="flex-item">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                v-model="item.jsl"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                disabled
                style="width: 135px;"
              >
                <template slot="append">%</template>
              </el-input>
            </el-form-item>
          </div>
        </div>
        
        <el-row class="mt16">
          <el-col :span="10">
            <el-form-item class="flex-box" label="平均值：">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                disabled
                v-model="batchForm[activeName].pjz" 
                :style="{'width': 180 + 'px'}"
              >
                <template slot="append">%</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="14">
            <el-form-item class="flex-box" label="单项结论：">
              <el-input
                :disabled="dxjlDisabled"
                v-model="batchForm[activeName].dxjl" 
                placeholder="请输入"
                :style="{'width': 180 + 'px'}"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        
        
        <el-row class="mt16">
          <el-col :span="24">
            <el-form-item label="检测图片：">
              <div class="cbo">
                <div class="img-box">
                  <el-upload
                    accept=".jpeg,.png,.jpg,.bmp,.gif"
                    :action="baseUrl + '/upload/file'"
                    list-type="picture-card"
                    :on-preview="handlePreview"
                    :file-list="batchForm[activeName].img"
                    :on-success="handlePicSuccess"
                    :on-remove="handlePicRemove">
                    <i class="el-icon-plus"></i>
                  </el-upload>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    
    
    <!-- //"坍落度1h经时变化量 -->
    <div v-if="activeName === 'CONCRETE_ADMIXTURE_PARAM_WJJ_TLD1HJSBHL'" class="custom-form">
      <el-form :disabled="loading2 || experimentStatus == 3" :inline="true" class="mt16">
        <div class="mt16 jcrq-box">
          <el-form-item label="检测日期：">
            <el-date-picker
              type="date"
              v-model="batchForm[activeName].jcrq"
              placeholder="选择日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
        </div>
        <div class="table-box-kzf mt16">
          <div class="tb-title">基准混凝土</div>
          <div class="tb-content">
            <div class="flex-box tb-item" v-for="(item, index) in batchForm[activeName].jzhntObject.jzhntList" :key="index">
              <div class="tb-left">{{index + 1}}</div>
              <el-form-item label="坍落度：" class="flex-item">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="item.tld"
                  @input="val => setH1bhl('jz',index)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  placeholder="请输入"
                  style="width: 130px;"
                >
                </el-input>
              </el-form-item>
              <el-form-item label="1小时后坍落度：" class="flex-item">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="item.h1htld"
                  @input="val => setH1bhl('jz',index)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  placeholder="请输入"
                  style="width: 130px;"
                >
                  <!-- <template slot="append">%</template> -->
                </el-input>
              </el-form-item>
              <el-form-item label="1h变化量：" class="flex-item">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="item.h1bhl"
                  disabled
                  style="width: 130px;"
                >
                  <!-- <template slot="append">%</template> -->
                </el-input>
              </el-form-item>
            </div>
          </div>
          <div class="tb-bottom">
            <el-form-item label="坍落度平均值：" class="flex-item">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                v-model="batchForm[activeName].jzhntObject.tldpjz"
                disabled
              >
              </el-input>
            </el-form-item>
            <el-form-item label="1小时后坍落度平均值：" class="flex-item">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                v-model="batchForm[activeName].jzhntObject.h1htldpjz"
                disabled
              >
              </el-input>
            </el-form-item>
            <el-form-item label="1h变化量平均值：" class="flex-item">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                v-model="batchForm[activeName].jzhntObject.h1bhlpjz"
                disabled
                style="width: 180px;"
              >
              </el-input>
            </el-form-item>
          </div>
        </div>
        
        <div class="table-box-kzf mt16">
          <div class="tb-title">受检混凝土</div>
          <div class="tb-content">
            <div class="flex-box tb-item" v-for="(item, index) in batchForm[activeName].sjhntObject.sjhntList" :key="index">
              <div class="tb-left">{{index + 1}}</div>
              <el-form-item label="坍落度：" class="flex-item">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="item.tld"
                  @input="val => setH1bhl('sj',index)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  placeholder="请输入"
                  style="width: 130px;"
                >
                </el-input>
              </el-form-item>
              <el-form-item label="1小时后坍落度：" class="flex-item">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="item.h1htld"
                  @input="val => setH1bhl('sj',index)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  placeholder="请输入"
                  style="width: 130px;"
                >
                  <!-- <template slot="append">%</template> -->
                </el-input>
              </el-form-item>
              <el-form-item label="1h变化量：" class="flex-item">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="item.h1bhl"
                  disabled
                  style="width: 130px;"
                >
                  <!-- <template slot="append">%</template> -->
                </el-input>
              </el-form-item>
            </div>
          </div>
          <div class="tb-bottom">
            <el-form-item label="坍落度平均值：" class="flex-item">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                v-model="batchForm[activeName].sjhntObject.tldpjz"
                disabled
              >
              </el-input>
            </el-form-item>
            <el-form-item label="1小时后坍落度平均值：" class="flex-item">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                v-model="batchForm[activeName].sjhntObject.h1htldpjz"
                disabled
              >
              </el-input>
            </el-form-item>
            <el-form-item label="1h变化量平均值：" class="flex-item">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                v-model="batchForm[activeName].sjhntObject.h1bhlpjz"
                disabled
                style="width: 180px;"
              >
              </el-input>
            </el-form-item>
          </div>
        </div>
        
        
        
        <el-row class="mt16">
          <el-col :span="24">
            <el-form-item label="检测图片：">
              <div class="cbo">
                <div class="img-box">
                  <el-upload
                    accept=".jpeg,.png,.jpg,.bmp,.gif"
                    :action="baseUrl + '/upload/file'"
                    list-type="picture-card"
                    :on-preview="handlePreview"
                    :file-list="batchForm[activeName].img"
                    :on-success="handlePicSuccess"
                    :on-remove="handlePicRemove">
                    <i class="el-icon-plus"></i>
                  </el-upload>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <image-viewer
      v-if="imgViewerVisible"
      :urlList="[previewUrl]"
      :on-close="onClose"
    >
    </image-viewer>
  </div>
</template>

<script>
import {add,  sub,  mul,  div, roundToDecimalPlace, calcEquation, cpEvenRound} from "@/utils/calculate.js"
import moment from "@/utils/moment.js"
import moments from 'moment'
import ImageViewer from "element-ui/packages/image/src/image-viewer";
import util from "../../../common/js/util.js";

export default {
  name:'userMgt',
  components: {
    ImageViewer
  },
  props: {
    activeId: {
      type: Number | String,
    },
    experimentStatus: {
      type: Number | String,
    },
    sampleLevel: {
      type: String,
      default: '1'
    },   
     entrustTime: {
      type: String,
      default: new Date().toISOString().split('T')[0],
    },

  },
  watch: {
    activeId: {
      handler(newValue, oldValue){
        if(newValue){
          // this.clearData();
          // this.setExperimentProject()
        }
      },
      immediate: true
    },
    entrustTime: {
      handler(newValue, oldValue){
        if(newValue){
          this.setDefaultTime(this.batchForm)
        }
      },
      immediate: true
    },
    
  },
  computed: {},
  data() {
    return {
      defaultDate: moment.today(),
      infoForm: '',
      filePrefix: this.$store.state.loginStore.userInfo.filePrefix,
      baseUrl: process.env.NODE_ENV === 'development' ? "/api" : "/mes-platform",
      activeName: 'quick',
      activeInfoName: 'hnlInfo',
      quickForm: {},//快检数据
      quickData: [],//快检列表
      batchForm: {
        CONCRETE_PARAM_XGL_HNL: {
          hnlInfo: []
        },
        CONCRETE_PARAM_XGL_NKHL: {
          hnkInfo: []
        }
      },//批检数据
      batchData: [],//批检列表
      
      loading2: false,
      
      previewUrl: "",
      imgViewerVisible: false,
      mdType: 'jmmd',
      bslbIndex: 0,
      njsjIndex: 0,
      addNjsjindex: 1,
      // "jzhntcnsjpjz": "基准混凝土初凝时间平均值",
      // "jzhntznsjpjz": "基准混凝土终凝时间平均值",
      // "sjhntcnsjpjz": "受检混凝土初凝时间平均值",
      // "sjhntznsjpjz": "受检混凝土终凝时间平均值",
      
      accordingObj: {},
      dxjlDisabled: false,
    };
  },
  
  created() {
  },
  methods: {
    
        isEmptyValue(value) {
            // 处理用户输入的特殊字符串
            if (value === 'null') return true;
            if (value === 'undefined') return true;
            if (value === '') return true;
            
            // 处理实际的JavaScript值
            if (value === null) return true;
            if (value === undefined) return true;
            if (value === '') return true;
            
            return false;
        },
    
        setDefaultTime(val){

      // 委托时间
      // 含固量
      if(val.CONCRETE_ADMIXTURE_PARAM_WJJ_HGL && this.isEmptyValue(val.CONCRETE_ADMIXTURE_PARAM_WJJ_HGL?.jcrq)){
        val.CONCRETE_ADMIXTURE_PARAM_WJJ_HGL['jcrq'] = util.calculateFutureDate(this.entrustTime, 0)
      }
      // 密度
      if(val.CONCRETE_ADMIXTURE_PARAM_WJJ_MD && this.isEmptyValue(val.CONCRETE_ADMIXTURE_PARAM_WJJ_MD?.jcrq)){
        val.CONCRETE_ADMIXTURE_PARAM_WJJ_MD['jcrq'] = util.calculateFutureDate(this.entrustTime, 0)
      }
      
      // PH
      if(val.CONCRETE_ADMIXTURE_PARAM_WJJ_PHZ && this.isEmptyValue(val.CONCRETE_ADMIXTURE_PARAM_WJJ_PHZ?.jcrq)){
        val.CONCRETE_ADMIXTURE_PARAM_WJJ_PHZ['jcrq'] = util.calculateFutureDate(this.entrustTime, 0)
      }
          // 凝结时间
      if(val.CONCRETE_ADMIXTURE_PARAM_WJJ_NJSJZC && this.isEmptyValue(val.CONCRETE_ADMIXTURE_PARAM_WJJ_NJSJZC?.jcrq)){
        val.CONCRETE_ADMIXTURE_PARAM_WJJ_NJSJZC['jcrq'] = util.calculateFutureDate(this.entrustTime, 0)
      }
      
      // 泌水率
      if(val.CONCRETE_ADMIXTURE_PARAM_WJJ_QSLB && this.isEmptyValue(val.CONCRETE_ADMIXTURE_PARAM_WJJ_QSLB?.jcrq)){
        val.CONCRETE_ADMIXTURE_PARAM_WJJ_QSLB['jcrq'] = util.calculateFutureDate(this.entrustTime, 0)
      }
      
      // 减水率
      if(val.CONCRETE_ADMIXTURE_PARAM_WJJ_JSL && this.isEmptyValue(val.CONCRETE_ADMIXTURE_PARAM_WJJ_JSL?.jcrq)){
        val.CONCRETE_ADMIXTURE_PARAM_WJJ_JSL['jcrq'] = util.calculateFutureDate(this.entrustTime, 0)
      }
      // 坍落度
      if(val.CONCRETE_ADMIXTURE_PARAM_WJJ_TLD1HJSBHL && this.isEmptyValue(val.CONCRETE_ADMIXTURE_PARAM_WJJ_TLD1HJSBHL?.jcrq)){
        val.CONCRETE_ADMIXTURE_PARAM_WJJ_TLD1HJSBHL['jcrq'] = util.calculateFutureDate(this.entrustTime, 0)
      }

  

        },
    //判断结论
    getPJZUtil(data,key,n){//数组，key，保留小数
      let len = 0,num = 0;
      data.forEach(item =>{
        if(item[key] !== ''){
          len++;
          num += item[key] * 1
        }
      })
      //2次结果之和除以2，保留2位小数，四舍五入
      let arr = [
        {
          v: num,
        },{
          k: '/',
          v: len === 0 ? '' : len
        }
      ]
      return calcEquation(arr, n);
    },
    //判断结论
    conclusion(num, key){
      if(num === ''){
        return ''
      }
      const aName = this.activeName;
      if(!key){
        key = aName.match(/[^_]+$/)[0];
        key = key.toLowerCase();
      }
      let min, max, notContainMax, notContainMin;
      let accObj = this.accordingObj[this.activeName];
      if (aName === 'CONCRETE_ADMIXTURE_PARAM_WJJ_JSL') {
        let jsl = accObj?.jsl || {};

        notContainMax = jsl.notContainMax;
        notContainMin = jsl.notContainMin;
        min = jsl.min;
        max = jsl.max;
      }else if (aName === 'CONCRETE_ADMIXTURE_PARAM_WJJ_QSLB') {
        let qslb = accObj?.qslb || {};
        notContainMax = qslb.notContainMax;
        notContainMin = qslb.notContainMin;
        min = qslb.min;
        max = qslb.max;
      }else if (aName === 'CONCRETE_ADMIXTURE_PARAM_WJJ_HGL') {
        if(!this.batchForm[aName].sccjkzz1 || !this.batchForm[aName].sccjkzz2){
          return ''
        }
        min = sub(this.batchForm[aName].sccjkzz1, this.batchForm[aName].sccjkzz2);
        max = add(this.batchForm[aName].sccjkzz1, this.batchForm[aName].sccjkzz2);
      }
      
      console.log(">>>>>>>>>开始计算是否合格>>>>>>>>");
      console.log(">>>>>>>>是否包含最大值>>>>>>>>>", notContainMax);
      console.log(">>>>>>>>是否包含最小值>>>>>>>>>", notContainMin);
      console.log(">>>>>>>>当前计算值>>>>>>>>>", num);
      console.log(">>>>>>>>合格最大值>>>>>>>>>", max);
      console.log(">>>>>>>>合格最小值>>>>>>>>>", min);

      if(notContainMax === true && num >= notContainMax){
        // this.$parent.testInfoForm.isQualified = 2;
        return '不合格'
      }
      if(notContainMin === true && num <= notContainMin){
        // this.$parent.testInfoForm.isQualified = 2;
        return '不合格'
      }

      if(min !== null && num < min){
        // this.$parent.testInfoForm.isQualified = 2;
        return '不合格'
      }
      if(max !== null && num > max){
        // this.$parent.testInfoForm.isQualified = 2;
        return '不合格'
      }
      return '合格';
    },
    // <!-- 含固量-->
    setHGLAll(){
      this.setHGL(1)
      this.setHGL(2)
    },
    setHGL(key){
      console.log(key)
      const data = key == 1 ? this.batchForm[this.activeName].sy1Info : this.batchForm[this.activeName].sy2Info;
      let arr = [
        {
          v: sub(data.hghzl, data.clp),
        },{
          k: '/',
          v: sub(data.syzl, data.clp)
        },{
          k: '*',
          v: 100,
        }
      ]
      let res = calcEquation(arr, 2)
      
      this.batchForm[this.activeName]['sy' + key + 'Info'].hgl = res;
      this.setHGLPJZ();
    },
    setHGLPJZ(){
      const odata = this.batchForm[this.activeName];
      let data = [{
        hgl: odata.sy1Info.hgl
      },{
        hgl: odata.sy2Info.hgl
      }]
      let res = this.getPJZUtil(data, 'hgl', 2);
      this.batchForm[this.activeName].pjhgl = res;
      this.batchForm[this.activeName].dxjl = this.conclusion(res);
    },
    
    
    
    // 密度
    setAlltj(){
      this.setWJJJG();
      this.setjmmdPJZ();
    },
    setTJ(){
      const data = this.batchForm[this.activeName].bzpInfo;
      let arr = [
        {
          v: data.zshbzpzl,
        },{
          k: '-',
          v: data.gzbzpzl
        },{
          k: '/',
          v: 0.9982,
        }
      ]
      let res = calcEquation(arr, 2)
      this.batchForm[this.activeName].bzpInfo.tj = res;
      this.setWJJJG();
    },
    setWJJJG(){
      const data = this.batchForm[this.activeName].bzpInfo;
      let arr = [
        {
          v: data.zwjjhbzpzl,
        },{
          k: '-',
          v: data.gzbzpzl
        },{
          k: '/',
          v: data.tj,
        }
      ]
      let res = calcEquation(arr, 2)
      this.batchForm[this.activeName].bzpInfo.syjg = res;
      this.batchForm[this.activeName].bzpInfo.dxjl = this.conclusion(res);
    },
    setjmmdPJZ(){
      const odata = this.batchForm[this.activeName].jmmdInfo;
      let arr = [
        {
          v: odata.sy1md,
        },{
          k: '+',
          v: odata.sy2md,
        },{
          k: '/',
          v: 2,
        }
      ]
      let res = calcEquation(arr, 2)
      this.batchForm[this.activeName].jmmdInfo.mdpjz = res;
      this.batchForm[this.activeName].dxjl = this.conclusion(res);
    },
    //PH
    setPHPJZ(type){
      const odata = this.batchForm[this.activeName];
      if(odata[type] > 100){
        this.batchForm[this.activeName][type] =100
      }
      let arr = [
        {
          v: odata.sy1ph,
        },{
          k: '+',
          v: odata.sy2ph,
        },{
          k: '/',
          v: 2,
        }
      ]
      let res = calcEquation(arr, 1)
      this.batchForm[this.activeName].pjph = res;
      this.batchForm[this.activeName].dxjl = this.conclusion(res);
    },
      
    getMinutesBetweenDates(date1, date2) {
      console.log(date1, date2)
      try {
        date1 = new Date(date1)
        date2 = new Date(date2)
        console.log(moments(date1).diff(moments(date2), 'minutes'))
        return moments(date1).diff(moments(date2), 'minutes');
      } catch (error) {
        console.log(error);
      }
    },
     
    //凝结时间
    addNjsj(index){
      index = this.addNjsjindex - 1;
      let sj1 = this.batchForm[this.activeName].njsjzcList[this.njsjIndex].hntInfoList[index].jzhntsj;
      let sj2 = this.batchForm[this.activeName].njsjzcList[this.njsjIndex].hntInfoList[index].sjhntsj;
      let qd1 = this.batchForm[this.activeName].njsjzcList[this.njsjIndex].hntInfoList[index].jzhntqd;
      let qd2 = this.batchForm[this.activeName].njsjzcList[this.njsjIndex].hntInfoList[index].sjhntqd;
      let date1 = new Date(sj1)
      let date2 = new Date(sj2)
      let future = moments(date1).add(60, 'minutes');
      let future2 = moments(date2).add(60, 'minutes');
      let obj = {
          "jzhntsj": future.format('YYYY-MM-DD HH:mm'),
          "jzhntgrzl": "",
          "jzhntszmj": qd1 > 3.5 ? 20 : 100,
          "jzhntqd": "",
          
          "sjhntsj": future2.format('YYYY-MM-DD HH:mm'),
          "sjhntgrzl": "",
          "sjhntszmj": qd2 > 3.5 ? 20 : 100,
          "sjhntqd": ""
        }
      this.batchForm[this.activeName].njsjzcList[this.njsjIndex].hntInfoList.splice(index + 1, 0, obj)
    },
    
    delNJSJZC(index) { //删除个人列
      this.batchForm[this.activeName].njsjzcList[this.njsjIndex].hntInfoList.splice(index, 1);
    },
    setNJSJqd(type, index){
      let odata = this.batchForm[this.activeName].njsjzcList[this.njsjIndex].hntInfoList[index];
      let arr = [
        {
          v: odata[type + 'hntgrzl'],
        },{
          k: '*',
          v: 1000,
        },{
          k: '/',
          v: odata[type + 'hntszmj'],
        }
      ]
      let res = calcEquation(arr, 1)
      this.batchForm[this.activeName].njsjzcList[this.njsjIndex].hntInfoList[index][type + 'hntqd'] = res;
      this.setNJSJ(type)
    },
    setNJSJ(type){
      let data = this.batchForm[this.activeName].njsjzcList[this.njsjIndex].hntInfoList;
      // if (itemKey === 'NJSJZC-sjhntqd') {
      //   if (data && data.length > 0){
      //     if (parseFloat(data[0].sjhntqd) > 3.5 ) {
      //       data[0].sjhntszmj = '20';
      //     } else {
      //       data[0].sjhntszmj = '100';
      //     }
      //   }
      // }else if (itemKey === 'NJSJZC-jzhntqd') {
      //   if (data && data.length > 0) {
      //     if (parseFloat(data[0].jzhntqd) > 3.5 ) {
      //       data[0].jzhntszmj = '20';
      //     } else {
      //       data[0].jzhntszmj = '100';
      //     }
      //   }
      // }
      const jssj1 = this.batchForm[this.activeName].njsjzcList[this.njsjIndex]['jzhntjssj'];
      const jssj2 = this.batchForm[this.activeName].njsjzcList[this.njsjIndex]['sjhntjssj'];
      let jCn = 0,
          jZn = 0,
          sCn = 0,
          sZn = 0;
      data.forEach(item =>{
        if(item.jzhntqd >= 28 && jZn == 0){
          jZn += this.getMinutesBetweenDates(item.jzhntsj, jssj1)
        }else if(item.jzhntqd >= 3.5 && jCn == 0){//初
          jCn += this.getMinutesBetweenDates(item.jzhntsj, jssj1)
        } 
        if(item.sjhntqd >= 28 && sZn == 0){
          sZn += this.getMinutesBetweenDates(item.sjhntsj, jssj2)
        }else if(item.sjhntqd >= 3.5 && sCn == 0){//初
          sCn += this.getMinutesBetweenDates(item.sjhntsj, jssj2)
        } 
      })
      // let l = data.length
      // jCn = div(jCn, l)
      // jZn = div(jZn, l)
      // sCn = div(sCn, l)
      // sZn = div(sZn, l)
      
      this.batchForm[this.activeName].njsjzcList[this.njsjIndex].jzhntddcnsj = roundToDecimalPlace(jCn,0);
      this.batchForm[this.activeName].njsjzcList[this.njsjIndex].jzhntddznsj = roundToDecimalPlace(jZn,0);
      this.batchForm[this.activeName].njsjzcList[this.njsjIndex].sjhntddcnsj = roundToDecimalPlace(sCn,0);
      this.batchForm[this.activeName].njsjzcList[this.njsjIndex].sjhntddznsj = roundToDecimalPlace(sZn,0);
      this.setNJSJpjz()
    },
    setNJSJpjz(){
      const data = this.batchForm[this.activeName].njsjzcList;
      let jCn = 0,
          jZn = 0,
          sCn = 0,
          sZn = 0;
      data.forEach(item =>{
        jCn = add(item.jzhntddcnsj, jCn);
        jZn = add(item.jzhntddznsj, jZn);
      
        sCn = add(item.sjhntddcnsj, sCn);
        sZn = add(item.sjhntddznsj, sZn);
      })
      
      let l = data.length
      jCn = div(jCn, l)
      jZn = div(jZn, l)
      sCn = div(sCn, l)
      sZn = div(sZn, l)
      this.batchForm[this.activeName].jzhntcnsjpjz =  roundToDecimalPlace(jCn,0);
      this.batchForm[this.activeName].jzhntznsjpjz =  roundToDecimalPlace(jZn,0);
      this.batchForm[this.activeName].sjhntcnsjpjz =  roundToDecimalPlace(sCn,0);
      this.batchForm[this.activeName].sjhntznsjpjz =  roundToDecimalPlace(sZn,0);
      this.setNJSJcz()
    },
    setNJSJcz(){
      const data = this.batchForm[this.activeName];
      let cn = 0,
          zn = 0;
      cn = sub(data.sjhntcnsjpjz, data.jzhntcnsjpjz)
      zn = sub(data.sjhntznsjpjz, data.jzhntznsjpjz)

      this.batchForm[this.activeName].cnsjzc =  roundToDecimalPlace(cn,0);
      this.batchForm[this.activeName].znsjzc =  roundToDecimalPlace(zn,0);
      // this.batchForm[this.activeName].cndxjl =  roundToDecimalPlace(cn,0);
      // this.batchForm[this.activeName].zndxjl =  roundToDecimalPlace(zn,0);
      this.batchForm[this.activeName].cndxjl = "合格";
      this.batchForm[this.activeName].zndxjl = "合格";
    },
    
    
    //泌水率比
    addBsl(){
      let index = this.batchForm[this.activeName].sjhntqslInfoList[this.bslbIndex].subSjhntqslInfo.subSjhntqslInfoList.length - 1;
      let sj1 = this.batchForm[this.activeName].sjhntqslInfoList[this.bslbIndex].subSjhntqslInfo.subSjhntqslInfoList[index].jzhntsj || moments().format('YYYY-MM-DD HH:mm:ss');
      let sj2 = this.batchForm[this.activeName].sjhntqslInfoList[this.bslbIndex].subSjhntqslInfo.subSjhntqslInfoList[index].sjhntsj || moments().format('YYYY-MM-DD HH:mm:ss');
      console.log(sj1,sj2)
      let date1 = new Date(sj1)
      let date2 = new Date(sj2)
      let future = moments(date1).add(10, 'minutes');
      let future2 = moments(date2).add(10, 'minutes');
            
      this.batchForm[this.activeName].sjhntqslInfoList[this.bslbIndex].subSjhntqslInfo.subSjhntqslInfoList.push(
        {
          "jzhntsj": future.format('YYYY-MM-DD HH:mm'),
          "jzhntqsl": "",
          "sjhntsj": future2.format('YYYY-MM-DD HH:mm'),
          "sjhntqsl": "",
        }
      )
    },
    
    delBsl(index) { //删除个人列
      this.batchForm[this.activeName].sjhntqslInfoList[this.bslbIndex].subSjhntqslInfo.subSjhntqslInfoList.splice(index, 1);
    },
    
    setBSLsyzl(type){
      const data = this.batchForm[this.activeName].sjhntqslInfoList[this.bslbIndex].subSjhntqslInfo;
      let arr = [
        {
          v: data[type + 'tjsy'],
        },{
          k: '-',
          v: data[type + 'tz'],
        }
      ]
      let res = calcEquation(arr, 2)
      this.batchForm[this.activeName].sjhntqslInfoList[this.bslbIndex].subSjhntqslInfo[type + 'syjz'] = res;
      this.setBsl(type);
    },
    setBsl(type){
      const data = this.batchForm[this.activeName].sjhntqslInfoList[this.bslbIndex].subSjhntqslInfo.subSjhntqslInfoList;
      const obj = this.batchForm[this.activeName].sjhntqslInfoList[this.bslbIndex].subSjhntqslInfo;
      
      let Zl = 0;
      data.forEach(item =>{
        Zl += item[type + 'hntqsl'] * 1;
      })
      this.batchForm[this.activeName].sjhntqslInfoList[this.bslbIndex].subSjhntqslInfo[type + 'hntqszl'] = Zl;
      
      let arr = [
        {
          v: Zl
        },{
          k: '/',
          v: roundToDecimalPlace(div(obj[type + 'bhwysl'], obj[type + 'bhwzzl']),4),
        },{
          k: '/',
          v: obj[type + 'syjz'],
        },{
          k: '*',
          v: 100,
        }
      ]
      let res = calcEquation(arr, 1)
      this.batchForm[this.activeName].sjhntqslInfoList[this.bslbIndex].subSjhntqslInfo[type + 'qsl'] = res;
      this.setQslPjz(type)
    },
    setQslPjz(type){
      const data = this.batchForm[this.activeName].sjhntqslInfoList;
      let ja = data[0].subSjhntqslInfo[type + 'qsl'],
          jb = data[1].subSjhntqslInfo[type + 'qsl'],
          jc = data[2].subSjhntqslInfo[type + 'qsl'];
          
      let jzZl = 0;
      let arr = [
        {
          v: ja
        },{
          k: '+',
          v: jb,
        },{
          k: '+',
          v: jc,
        },{
          k: '/',
          v: 3,
        }
      ]
      jzZl = calcEquation(arr, 1)
      if((Math.abs(ja -jzZl) > 15 && Math.abs(jb -jzZl) > 15) || (Math.abs(ja -jzZl) > 15 && Math.abs(jc -jzZl) > 15) || (Math.abs(jb -jzZl) > 15 && Math.abs(jc -jzZl) > 15)){
        //则试验无效
        this.batchForm[this.activeName][type + 'hntpjz'] = '试验无效'
      }else if(Math.abs(ja -jzZl) > 15 || Math.abs(ja -jzZl) > 15 || Math.abs(ja -jzZl) > 15){
        let ar = [ja,jb,jc].sort()
        //取中间值
        this.batchForm[this.activeName][type + 'hntpjz'] = jb
      }else{
        this.batchForm[this.activeName][type + 'hntpjz'] = jzZl
      }
      this.setqslB()
    },
    setqslB(){//受检除以基准乘以100
      const data = this.batchForm[this.activeName];
      let arr = [
        {
          v: data.sjhntpjz,
        },{
          k: '/',
          v: data.jzhntpjz
        },{
          k: '*',
          v: 100,
        }
      ]
      let res = calcEquation(arr, 1)
      this.batchForm[this.activeName].qslb = res;
      this.batchForm[this.activeName].dxjl = this.conclusion(res);
    },
    // 计算精确至1%。泌水率取三个试样的算术平均值。
    // 如果最大值与最小值与中间值之差均大于中间值的15%时，则试验无效。
    // 如果其中一个与平均值之差超过中间值的15%，则以中间值作为结果，
    
    // 减水率
    setJSL(index){
      const data = this.batchForm[this.activeName].jslList[index];
      let arr = [
        {
          v: data.jzhntysl,
        },{
          k: '-',
          v: data.sjhntysl,
        },{
          k: '/',
          v: data.jzhntysl,
        },{
          k: '*',
          v: 100,
        }
      ]
      let res = calcEquation(arr, 1)
      this.batchForm[this.activeName].jslList[index].jsl = res;
      this.setJSLPJZ();
    },
    setJSLPJZ(){
      const data = this.batchForm[this.activeName];
      
      let res = this.getPJZUtil(data.jslList, 'jsl', 1);
      this.batchForm[this.activeName].pjz = res;
      this.batchForm[this.activeName].dxjl = this.conclusion(res);
      
    },
    // 坍落度1h经时变化量 
    setH1bhl(type,index){
      const data = this.batchForm[this.activeName][type + 'hntObject'][type + 'hntList'][index];
      let arr = [
        {
          v: data.tld,
        },{
          k: '-',
          v: data.h1htld,
        }
      ]
      let res = calcEquation(arr, 5)
      this.batchForm[this.activeName][type + 'hntObject'][type + 'hntList'][index].h1bhl = res;
      this.setH1bhlPJZ(type);
    },
    setH1bhlPJZ(type){
      const data = this.batchForm[this.activeName][type + 'hntObject'][type + 'hntList'];
      let tlds = 0,
          h1bs = 0,
          h1hs = 0;
      data.forEach(item =>{
        tlds += item.tld * 1;
        h1bs += item.h1bhl * 1;
        h1hs += item.h1htld * 1;
      })
      let arr = [
        {
          v: tlds,
        },{
          k: '/',
          v: data.length,
        }
      ]
      let arr2 = [
        {
          v: h1hs,
        },{
          k: '/',
          v: data.length,
        }
      ]
      
      let res = calcEquation(arr, 5)
      let res2 = calcEquation(arr2, 5)

      let res3 = sub(res, res2);
      this.batchForm[this.activeName][type + 'hntObject'].tldpjz = res;
      this.batchForm[this.activeName][type + 'hntObject'].h1htldpjz= res2;
      this.batchForm[this.activeName][type + 'hntObject'].h1bhlpjz = res3;
    },
    
    
    //图片
    handlePicSuccess(response, file, fileList) {
      console.log(fileList)
      let oUploadImg = fileList.map(item =>{
        if(item.response){
          return {
            url: item.response.data.filePath,
            uid: item.uid
          }
        }else{
          return item;
          // if(item.url.startsWith(this.filePrefix)){
          //   return item.url;
          // }else{
          //   return this.filePrefix + item.url;
          // }
        }
      })
      
      if(this.activeName === 'quick'){
        this.quickForm.img = oUploadImg;
      }else{
        this.batchForm[this.activeName].img = oUploadImg;
      }
    },
    handlePicRemove(file, fileList) {
      let oUploadImg = fileList.map(item =>{
        if(item.response){
          return {
            url: item.response.data.filePath,
            uid: item.uid
          }
        }else{
          return item
        }
      })
      if(this.activeName === 'quick'){
        this.quickForm.img = oUploadImg;
      }else{
        this.batchForm[this.activeName].img = oUploadImg;
      }
    },
    
    
    //获取所有快检或批检试验项目名称
    async setExperimentProject(id){
      //获取抗压 抗渗 和其它
      const resDetail = await this.$api.getExperimentDetail({
        experimentId: this.activeId,
        // "testProjectCode":"FINE_AGGREGATE",
        // "checkType": 2
      }, this)
      if(resDetail.succ){
        let quickData = [];
        let batchData = [];//批检
        let quickForm = {};
        quickForm.img = [];
        let batchForm = {};
        resDetail.data.list.forEach(item => {
          let oImgArr = [];
          if(item.objImg && item.objImg != 'null'){
            oImgArr = item.objImg.split(',').map(item => {
              if(item.startsWith(this.filePrefix)){
                return {
                  url: item
                }
              }else{
                return {
                  url: this.filePrefix + item
                }
              }
            }) 
          }else{
            oImgArr = [];
          }
          
          if(item.testProjectName === '流动性' || item.testProjectName === '保水性' 
            || item.testProjectName === '粘聚性'|| item.testProjectName === '坍落度' 
            || item.testProjectName === '目测砂率' 
          ){
            if (item.testProjectName === '坍落度') {
              if (!item.objJson.tldfs) {
                item.objJson.tldfs = "目测";
              }
            }
            if((item.testProjectName === '流动性' || item.testProjectName === '保水性' 
            || item.testProjectName === '粘聚性')){
              // 接口没有val 字段，根据判断转化一个加进去自己用
              item.objJson.val = 'hh'
              if (item.objJson.hh == 1) {
                item.objJson.val = 'hh'
              }else if (item.objJson.yb == 1) {
                item.objJson.val = 'yb'
              }else if (item.objJson.fcc == 1) {
                item.objJson.val = 'fcc'
              }
            }
            quickData.push(item)
            quickForm[item.testProjectCode] = item;
            quickForm.img = quickForm.img.concat(oImgArr);
          }else{
            if(!item.objJson?.jcrq){
              item.objJson.jcrq = this.defaultDate;
            }
            item.objJson.img = oImgArr;
            batchForm[item.testProjectCode] = JSON.parse(JSON.stringify(item.objJson));
            item.objJson = undefined;
            batchData.push(item);
          }
        })
        this.quickData = quickData;
        this.quickForm = quickForm;
        this.batchData = batchData;
        this.batchForm = this.setDefaultVal(batchForm);
        
        console.log(this.batchForm,this.batchData)
        if(this.quickData.length > 0){
          this.activeName = 'quick'
        }else{
          this.activeName = this.batchData[0].testProjectCode;
          this.getAccordingToHand()
        }
        
      }  
    },
    getAccordingToHand(){
      this.$api.getAccordingTo({
        testProjectCode: this.activeName,
        materialAbbreviation : this.$parent.activeData.materialAbbreviation,
        materialsName	 : this.$parent.activeData.materialsName,
        materialsSpec : this.$parent.activeData.materialsSpecs
      }, this).then(res =>{
        if(res.data.list.length > 0){
          let o = res.data.list[0].objJson;
          this.accordingObj[this.activeName] = JSON.parse(o);
        }else{
          this.accordingObj[this.activeName] = {};
        }
      })
    },
    //设置默认值
    setDefaultVal(val){
      this.setDefaultTime(val);
      if(val.CONCRETE_ADMIXTURE_PARAM_WJJ_NJSJZC?.sjhntqslInfoList){
        val.CONCRETE_ADMIXTURE_PARAM_WJJ_QSLB?.sjhntqslInfoList.map((item) => {
          if (item.subSjhntqslInfo) {
            item.subSjhntqslInfo.subSjhntqslInfoList.map((subItem, index) => {
              if(subItem.jzhntsj){
                subItem.jzhntsj = moments(subItem.jzhntsj).format('YYYY-MM-DD HH:mm')
              }
              if (subItem.sjhntsj) {
                subItem.sjhntsj = moments(subItem.sjhntsj).format('YYYY-MM-DD HH:mm')
              }
              
              if(index === 0 && !subItem.jzhntsj){
                subItem.jzhntsj = moments().format('YYYY-MM-DD HH:mm')
              }
              if(index === 0 && !subItem.sjhntsj){
                subItem.sjhntsj = moments().format('YYYY-MM-DD HH:mm')
              }
            });
          }
        });
      }
      
      
      if(val.CONCRETE_ADMIXTURE_PARAM_WJJ_NJSJZC?.njsjzcList){
        val.CONCRETE_ADMIXTURE_PARAM_WJJ_NJSJZC?.njsjzcList.map((item) => {
          if (item.hntInfoList) {
            item.hntInfoList.map((subItem, index) => {
              if (subItem.jzhntsj) {
                subItem.jzhntsj = moments(subItem.jzhntsj).format('YYYY-MM-DD HH:mm')
              }
              if (subItem.sjhntsj) {
                subItem.sjhntsj = moments(subItem.sjhntsj).format('YYYY-MM-DD HH:mm')
              }

              if(index === 0 && !subItem.jzhntsj){
                subItem.jzhntsj = moments().format('YYYY-MM-DD HH:mm')
              }
              if(index === 0 && !subItem.sjhntsj){
                subItem.sjhntsj = moments().format('YYYY-MM-DD HH:mm')
              }
              
              if (index === 0 && !subItem.jzhntszmj) {
                subItem.jzhntszmj = '100';
              }
              if (index === 0 && !subItem.sjhntszmj) {
                subItem.sjhntszmj = '100';
              }
            });
          }
        });
      }
      // 减水率
      if (val.CONCRETE_ADMIXTURE_PARAM_WJJ_JSL?.jslList) {
        val.CONCRETE_ADMIXTURE_PARAM_WJJ_JSL?.jslList.map((item) => {
          if (item.jsl) {
            item.jsl = cpEvenRound(item.jsl, 1);
          }
        })
        if (val.CONCRETE_ADMIXTURE_PARAM_WJJ_JSL?.pjz) {
          val.CONCRETE_ADMIXTURE_PARAM_WJJ_JSL.pjz = cpEvenRound(val.CONCRETE_ADMIXTURE_PARAM_WJJ_JSL.pjz, 1);
        }
      }
      return val;
    },
    handleClick(tab, event){
      this.getAccordingToHand()
      if(this.activeName === 'CONCRETE_PARAM_XGL_HNL'){
        this.activeInfoName = 'hnlInfo'
      }else if(this.activeName === 'CONCRETE_PARAM_XGL_NKHL'){
        this.activeInfoName = 'hnkInfo'
      }
    },
    clearData(){
      this.activeName= 'quick';
      this.quickForm= {};
      this.quickData= [];
      this.batchForm= {};
      this.batchData= [];
    },
    handlePreview(file) {
      this.previewUrl = file.url
      this.imgViewerVisible = true;
    },
    onClose() {
      this.imgViewerVisible = false;
    },
  },
};
</script>
<style scoped lang="scss">
  .flex-item2{
    flex: 2;
  }
  .line{
    height: 1px;
    width: 100%;
    background: #E8E8E8;
    margin: 24px 0;
  }
  .info-form-title{
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 14px;
    color: #1F2329;
    line-height: 20px;
    text-align: left;
    font-style: normal;
    padding: 10px 0 4px;
    margin-bottom: 4px;
  }
  .info-form-piece{
    background: #F8F8F8;
    border-radius: 8px;
    padding: 16px 16px 8px 16px;
  }
  ::v-deep .el-form .el-form-item{
    margin-bottom: 8px;
    margin-right: 10px;
    .el-form-item__content{
      flex: 1;
    }
  }
  ::v-deep .el-input-group__append, 
  ::v-deep .el-input-group__prepend{
    padding: 0 5px;
    text-align: center;
    width: 40px;
  }
  ::v-deep .pr0{
    .el-input__inner{
      padding-right: 0;
    }
  }
  ::v-deep .el-input-number.is-controls-right .el-input__inner{
    text-align: left;
  }
  ::v-deep .textspan .el-input__inner{
    background: transparent;
    border: none;
    padding: 0;
    color: #000;
    margin-left: -10px;
    margin-top: -2px;
  }
  
  
  ::v-deep .el-upload-list__item{
    transition: none !important; 
  }
  
  
  .el-row{
    margin-bottom: 24px;
    &:last-child{
      margin-bottom: 0;
    }
  }
  .mb16{
    margin-bottom: 16px;
  }
  .mt24{
    margin-top: 24px;
  }
  .el-tabs{
    height: 40px;
  }
  ::v-deep .custom-form{
    .el-form--inline{
      .table-box-kzf{
        .el-form-item{
          margin-right: 5px;
        }
      }
      .el-form-item{
        margin-bottom: 8px;
      }
    }
  }
  .table-box-kzf{
    width: 100%;
    background: #FFFFFF;
    border-radius: 4px;
    border: 1px solid #DDDFE6;
    
    .tb-title{
      height: 52px;
      background: #F8F8F8;
      border-radius: 4px 4px 0px 0px;
      border-bottom: 1px solid #DDDFE6;
      line-height: 52px;
      padding-left: 16px;
      font-weight: 600;
      font-size: 14px;
      color: #1F2329;
    }
    .tb-bottom{
      border-top: 1px solid #DDDFE6;
      padding: 16px 16px 8px;
    }
    .tb-left{
      line-height: 40px;
      border-right: 1px solid #DDDFE6;
      width: 72px;
      text-align: center;
    }
    .tb-content{
      .el-form-item{
        padding-left: 16px;
        text-align: center;
      }
      .tb-item:first-child{
        .tb-left,.el-form-item{
          padding-top: 24px;
        }
      }
      .tb-item:last-child{
        .tb-left,.el-form-item{
          padding-bottom: 16px;
        }
      }
    }
    .tb-center{
      padding: 24px 0;
      margin: 0 8px;
      &>div:first-child{
        margin-bottom: 8px;
      }
    }
    .tb-right{
      height: 88px;
      margin-top: 24px;
      border-left: 1px solid #DDDFE6;
      line-height: 40px;
      padding: 0 16px;
      &>p:first-child{
        margin-bottom: 8px;
      }
    }
  }
  
  .tag-btn{
    display: inline-block;
    cursor: pointer;
    height: 32px;
    border: 1px solid #F8F8F8;
    line-height: 30px;
    padding: 0 8px;
    background: #F8F8F8;
    border-radius: 4px;
    margin-right: 12px;
    font-weight: 500;
    box-sizing: border-box;
    &.tag-btn-succ{
      background: #BBFADA;
      color: #13D466;
      border: 1px solid #13D466;
    }
    &.tag-btn-warn{
      background: rgba(255,206,80,0.26);
      border: 1px solid #FCCB83;
      color: #FFB803;
    }
    &.tag-btn-error{
      background: rgba(255, 95, 88, 0.26);
      border: 1px solid #FF5F58;
      color: #FF5F58;
    }
    &.tag-btn-primary{
      background: rgba(31, 87, 179, .1);
      color: #1F57B3;
      border: 1px dashed rgba(31, 87, 179, .1);
    }
    &.tag-btn-disabled{
      color: rgba(153,153,153,0.85);
    }
    &.tag-btn-dotted{
      padding: 0 13px;
      border-style: dashed;
    }
    &.tag-btn-mini{
      font-size: 16px;
      height: 28px;
      line-height: 26px;
    }
    &:last-child{
      margin: 0;
    }
  }
  
  
  ::v-deep .custom-form{
    .el-form--inline{
      .table-box-kzf{
        .el-form-item{
          margin-right: 5px;
        }
      }
      .el-form-item{
        margin-bottom: 8px;
      }
    }
  }
  .table-box-kzf{
    width: 100%;
    background: #FFFFFF;
    border-radius: 4px;
    border: 1px solid #DDDFE6;
    
    .tb-title{
      height: 52px;
      background: #F8F8F8;
      border-radius: 4px 4px 0px 0px;
      border-bottom: 1px solid #DDDFE6;
      line-height: 52px;
      padding-left: 16px;
      font-weight: 600;
      font-size: 14px;
      color: #1F2329;
      .tb-title-lef{
        width: 100px;
        border-right: 1px solid #DDDFE6;
      }
      .tb-title-con{
        text-align: center;
        line-height: 20px;
        padding-top: 8px;
        border-right: 1px solid #DDDFE6;        span{
          display: inline-block;
          width: 100%;
          &:nth-child(2),&:nth-child(3){
            font-weight: 400;
            font-size: 12px;
            color: #6A727D;
            width: 50%;
          }
        }
      }
      .tb-title-bom{
        text-align: center;
        width: 172px;
      }
    }
    .tb-bottom{
      border-top: 1px solid #DDDFE6;
      padding: 16px 16px 8px;
    }
    .tb-left{
      line-height: 40px;
      border-right: 1px solid #DDDFE6;
      width: 116px;
      text-align: center;
    }
    .tb-content{
      .tb-left,.el-form-item{
        // padding-bottom: 8px;
      }
      .el-form-item{
        padding-left: 16px;
      }
      .tb-item:first-child{
        .tb-left,.el-form-item{
          padding-top: 24px;
        }
      }
      .tb-item:last-child{
        .tb-left,.el-form-item{
          padding-bottom: 16px;
        }
      }
    }
    .tb-center{
      padding: 24px 0;
      margin: 0 8px;
      &>div:first-child{
        margin-bottom: 8px;
      }
    }
    .tb-right{
      height: 88px;
      margin-top: 24px;
      border-left: 1px solid #DDDFE6;
      line-height: 40px;
      padding: 0 16px;
      &>p:first-child{
        margin-bottom: 8px;
      }
    }
  }
// ::v-deep .el-form-item__label {
//   text-align: left;
// }
</style>