<template>
  <div>
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane v-if="quickData.length > 0" label="快检" name="quick"></el-tab-pane>
      <template v-if="batchData.length > 0">
        <el-tab-pane v-for="(item, index) in batchData" :key="item.testProjectCode" :label="item.testProjectName" :name="item.testProjectCode">
        </el-tab-pane>
      </template>
    </el-tabs>
    <div v-show="activeName === 'quick'">
      <el-form ref="quickForm"  :model="quickForm" :disabled="loading2 || experimentStatus == 3">
        <el-row class="mt16">
          <el-col :span="24" style="margin-bottom: 8px;">
            <el-form-item :label="item.testProjectName" v-for="item in quickData" :key="item.testProjectCode">
              
              <template v-if="item.testProjectName === '表观密度'">
                <el-input
                  v-manual-update
                  @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  v-model="quickForm[item.testProjectCode].objJson.bgmd" 
                  clearable
                  placeholder="表观密度"
                  style="width: 253px; margin-left: 14px;"
                >
                </el-input>
              </template>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="图片：">
              <div class="cbo">
                <div class="img-box">
                  <el-upload
                    accept=".jpeg,.png,.jpg,.bmp,.gif"
                    :action="baseUrl + '/upload/file'"
                    list-type="picture-card"
                    :on-preview="handlePreview"
                    :file-list="quickForm.img"
                    :on-success="handlePicSuccess"
                    :on-remove="handlePicRemove">
                    <i class="el-icon-plus"></i>
                  </el-upload>
                  <!-- <span>抽样图片</span> -->
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <!-- 样品密度-->
    <div v-if="activeName === 'SLAG_PARAM_KZF_YPMD'" class="custom-form">
      <el-form :disabled="loading2 || experimentStatus == 3" :inline="true">
        <div class="mt16 jcrq-box">
          <el-form-item label="检测日期：">
            <el-date-picker
              type="date"
              v-model="batchForm[activeName].jcrq"
              placeholder="选择日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
        </div>
        <div v-for="(item, index) in batchForm[activeName].ypmdInfo" :key="index">
          <p class="info-form-title mt16">{{index == 0 ? '第一次试验' : '第二次试验'}}</p>
          <div class="info-form-piece flex-box">
            <el-form-item label="试验质量：" class="flex-item flex-box">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @input="val => setYPMD(index)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="item.syzl" 
                placeholder="请输入"
              >
                <template slot="append">g</template>
              </el-input>
            </el-form-item>
            <el-form-item class="flex-item flex-box" label="第一次读数v1：">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="item.dycdsv1"
                @input="val => setYPMD(index)"
                placeholder="请输入"
              >
                <template slot="append">ml</template>
              </el-input>
            </el-form-item>
            <el-form-item label="第二次读数v2：" class="flex-item flex-box">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @input="val => setYPMD(index)"
                v-model="item.decdsv2"
                placeholder="请输入"
              >
                <template slot="append">ml</template>
              </el-input>
            </el-form-item>
            <el-form-item label="检验结果：" class="flex-item flex-box">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                v-model="item.sywz"
                disabled
                placeholder="请输入"
              >
                <template slot="append">g/cm³</template>
              </el-input>
            </el-form-item>
          </div>
        </div>
        <div class="line"></div>
        <el-row>
          <el-col :span="10">
            <el-form-item class="flex-box" label="平均值：">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                v-model="batchForm[activeName].pjz" 
                disabled
                :style="{'width': 200 + 'px'}"
              >
                <template slot="append">%</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="14">
            <el-form-item class="flex-box" label="单项结论：">
              <el-input
                v-model="batchForm[activeName].dxjl" 
                :disabled="dxjlDisabled"
                placeholder="请输入"
                :style="{'width': 180 + 'px'}"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row>
          <el-col :span="24">
            <el-form-item label="检测图片：">
              <div class="cbo">
                <div class="img-box">
                  <el-upload
                    accept=".jpeg,.png,.jpg,.bmp,.gif"
                    :action="baseUrl + '/upload/file'"
                    list-type="picture-card"
                    :on-preview="handlePreview"
                    :file-list="batchForm[activeName].img"
                    :on-success="handlePicSuccess"
                    :on-remove="handlePicRemove">
                    <i class="el-icon-plus"></i>
                  </el-upload>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    
    <!-- 比表面积 -->
    <!-- <div v-if="activeName === 'SLAG_PARAM_KZF_BBMJ'" class="custom-form">
      <el-form :disabled="loading2 || experimentStatus == 3" :inline="true" class="mt16">
        <div class="mt16 jcrq-box">
          <el-form-item label="检测日期：">
            <el-date-picker
              type="date"
              v-model="batchForm[activeName].jcrq"
              placeholder="选择日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
        </div>
        <div class="mt16 flex-box">
          <el-form-item label="试料层体积：" class="flex-item flex-box" >
            <el-input
              v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @input="setSYZL"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].slctj" 
              placeholder="请输入"
            >
              <template slot="append">cm³</template>
            </el-input>
          </el-form-item>
          <el-form-item class="flex-item flex-box" label="密度：">
            <el-input
              v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @input="setBbmjAll"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].md"
              disabled
            >
              <template slot="append">g/ml</template>
            </el-input>
          </el-form-item>
          <el-form-item label="试料层空隙率：" class="flex-item flex-box" >
            <el-input
              v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @input="setBbmjAll"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].slckxl" 
              placeholder="请输入"
            >
              <template slot="append">%</template>
            </el-input>
          </el-form-item>
        </div>
        <div class="mt16 flex-box">
          <el-form-item label="试样质量：" class="flex-item flex-box" >
            <el-input
              v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              v-model="batchForm[activeName].syzl" 
              disabled
            >
              <template slot="append">g</template>
            </el-input>
          </el-form-item>
          <div class="flex-item"></div>
          <div class="flex-item"></div>
        </div>
        
        <div class="table-box-kzf mt16" v-for="(item, index) in batchForm[activeName].bbdycsy" :key="index">
          <div class="tb-title">{{index == 0 ? '第一次试验': '第二次试验'}} </div>
          <div class="tb-content">
            <div class="flex-box tb-item">
              <div class="tb-left" style="width: 88px;"></div>
              <el-form-item label="试验温度：" class="flex-item">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="item.bzsywd"
                  @input="val => setBBMJ(index)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  placeholder="请输入"
                  style="width: 135px;"
                >
                  <template slot="append">°C</template>
                </el-input>
              </el-form-item>
              <el-form-item label="标准密度：" class="flex-item">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="item.bzmd"
                  @input="val => setBBMJ(index)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  placeholder="请输入"
                  style="width: 135px;"
                >
                  <template slot="append">g/ml</template>
                </el-input>
              </el-form-item>
              <el-form-item label="标准空隙率：" class="flex-item">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="item.bzkxl"
                  @input="val => setBBMJ(index)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  placeholder="请输入"
                  style="width: 135px;"
                >
                  <template slot="append">%</template>
                </el-input>
              </el-form-item>
            </div>
            <div class="flex-box tb-item">
              <div class="tb-left" style="width: 88px; margin-top: -24px;">标准样品</div>
              <el-form-item label="降落时间：" class="flex-item">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="item.bzjlsj"
                  @input="val => setBBMJ(index)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+','.'].includes(e.key))?false:e.returnValue"
                  placeholder="请输入"
                  style="width: 135px;"
                >
                  <template slot="append">s</template>
                </el-input>
              </el-form-item>
              <el-form-item label="空气粘度：" class="flex-item">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="item.bzkqnd"
                  @input="val => setBBMJ(index)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  placeholder="请输入"
                  style="width: 135px;"
                >
                  <template slot="append">μPa·s</template>
                </el-input>
              </el-form-item>
              <el-form-item label="比表面积：" class="flex-item">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="item.bzbbmj"
                  @input="val => setBBMJ(index)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  disabled
                  style="width: 135px;"
                >
                  <template slot="append">c㎡/g</template>
                </el-input>
              </el-form-item>
            </div>
            <div class="flex-box tb-item">
              <div class="tb-left pt16" style="width: 88px;">被测样品</div>
              <el-form-item label="校准温度：" class="flex-item pt16">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="item.bcjywd"
                  @input="val => setBBMJ(index)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  placeholder="请输入"
                  style="width: 135px;"
                >
                  <template slot="append">°C</template>
                </el-input>
              </el-form-item>
              <el-form-item label="降落时间：" class="flex-item pt16">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="item.bcjlsj"
                  @input="val => setBBMJ(index)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+','.'].includes(e.key))?false:e.returnValue"
                  placeholder="请输入"
                  style="width: 135px;"
                >
                  <template slot="append">s</template>
                </el-input>
              </el-form-item>
              <el-form-item label="空气粘度：" class="flex-item pt16">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="item.bckqnd"
                  @input="val => setBBMJ(index)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  placeholder="请输入"
                  style="width: 135px;"
                >
                  <template slot="append">μPa·s</template>
                </el-input>
              </el-form-item>
              <el-form-item label="比表面积：" class="flex-item pt16">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="item.bcbbmj"
                  disabled
                  style="width: 135px;"
                >
                  <template slot="append">c㎡/g</template>
                </el-input>
              </el-form-item>
            </div>
          </div>
        </div>
        
        <el-row class="mt16">
          <el-col :span="10">
            <el-form-item class="flex-box" label="平均值：">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                disabled
                v-model="batchForm[activeName].pjz" 
                :style="{'width': 200 + 'px'}"
              >
                <template slot="append">c㎡/g</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="14">
            <el-form-item class="flex-box" label="单项结论：">
              <el-input
                :disabled="dxjlDisabled"
                v-model="batchForm[activeName].dxjl" 
                placeholder="请输入"
                :style="{'width': 180 + 'px'}"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row class="mt16">
          <el-col :span="24">
            <el-form-item label="检测图片：">
              <div class="cbo">
                <div class="img-box">
                  <el-upload
                    accept=".jpeg,.png,.jpg,.bmp,.gif"
                    :action="baseUrl + '/upload/file'"
                    list-type="picture-card"
                    :on-preview="handlePreview"
                    :file-list="batchForm[activeName].img"
                    :on-success="handlePicSuccess"
                    :on-remove="handlePicRemove">
                    <i class="el-icon-plus"></i>
                  </el-upload>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div> -->
    
    <div v-if="activeName === 'SLAG_PARAM_KZF_BBMJ'" class="custom-form">
      <el-form :disabled="loading2 || experimentStatus == 3" :inline="true" class="mt16">
        <div class="mt16 jcrq-box">
          <el-form-item label="检测日期：">
            <el-date-picker
              type="date"
              v-model="batchForm[activeName].jcrq"
              placeholder="选择日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="计算方法：">
            <el-select v-model="batchForm[activeName].wa" placeholder="请选择" style="width: 155px;">
              <el-option label="试验时温差小于等于3度" value="试验时温差小于等于3度"></el-option>
            </el-select>
          </el-form-item>
        </div>
        
        <div class="table-box-kzf mt16" v-for="(item, index) in batchForm[activeName].bbmjsy" :key="index">
          <div class="tb-title">{{index == 0 ? '第一次试验': '第二次试验'}} </div>
          <div class="tb-content">
            <div class="flex-box tb-item">
              <div class="tb-left" style="width: 88px;">标准样品</div>
              <el-row>
                <el-form-item label="密度(ρs)：" class="flex-item">
                  <el-input
                    v-manual-update

                  @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                    v-model="item.bzypmd"
                    @input="val => setNewBBMJ(index, 'bz')"
                    @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                    placeholder="请输入"
                    style="width: 115px;"
                  >
                  </el-input>
                </el-form-item>
                <el-form-item label="空隙率(εs)：" class="flex-item">
                  <el-input
                    v-manual-update

                  @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                    v-model="item.bzypkxl"
                    @input="val => setNewBBMJ(index, 'bz')"
                    @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                    placeholder="请输入"
                    style="width: 115px;"
                  >
                  </el-input>
                </el-form-item>
                <el-form-item label="页面降落时间(Ts)：" class="flex-item">
                  <el-input
                    v-manual-update

                  @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                    v-model="item.bzypymjlsj"
                    @input="val => setNewBBMJ(index, 'bz')"
                    @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                    placeholder="请输入"
                    style="width: 115px;"
                  >
                    <template slot="append">s</template>
                  </el-input>
                </el-form-item>
                <el-form-item label="比表面积(Ss)：" class="flex-item">
                  <el-input
                    v-manual-update

                  @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                    v-model="item.bzypbbmj"
                    @input="val => setNewBBMJ(index, 'bz')"
                    @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                    style="width: 115px;"
                  >
                    <template slot="append">c㎡/g</template>
                  </el-input>
                </el-form-item>
              </el-row>
            </div>
            <div style="margin-top: 20px;"></div>
            <div class="flex-box tb-item">
              <div class="tb-left" style="width: 88px;">被测样品</div>
              <el-row>
                <el-form-item label="密度(ρ)：" class="flex-item">
                  <el-input
                    v-manual-update

                  @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                    v-model="item.bcypmd"
                    @input="val => setNewBBMJ(index, 'bc')"
                    @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                    placeholder="请输入"
                    style="width: 115px;"
                  >
                  </el-input>
                </el-form-item>
                <el-form-item label="空隙率(ε)：" class="flex-item">
                  <el-input
                    v-manual-update

                  @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                    v-model="item.bcypkxl"
                    @input="val => setNewBBMJ(index, 'bc')"
                    @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                    placeholder="请输入"
                    style="width: 115px;"
                  >
                  </el-input>
                </el-form-item>
                <el-form-item label="页面降落时间(T)：" class="flex-item">
                  <el-input
                    v-manual-update

                  @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                    v-model="item.bcypymjlsj"
                    @input="val => setNewBBMJ(index, 'bc')"
                    @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                    placeholder="请输入"
                    style="width: 115px;"
                  >
                    <template slot="append">s</template>
                  </el-input>
                </el-form-item>
                <el-form-item label="比表面积(Ss)：" class="flex-item">
                  <el-input
                    v-manual-update

                  @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                    v-model="item.bcypbbmj"
                    @input="val => setNewBBMJ(index, 'bc')"
                    @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                    disabled
                    style="width: 115px;"
                  >
                    <template slot="append">c㎡/g</template>
                  </el-input>
                </el-form-item>
              </el-row>
            </div>
          </div>
        </div>
        
        <el-row class="mt16">
          <el-col :span="10">
            <el-form-item class="flex-box" label="平均值：">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                disabled
                v-model="batchForm[activeName].pjz" 
                :style="{'width': 200 + 'px'}"
              >
                <template slot="append">c㎡/g</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="14">
            <el-form-item class="flex-box" label="单项结论：">
              <el-input
                :disabled="dxjlDisabled"
                v-model="batchForm[activeName].dxjl" 
                placeholder="请输入"
                :style="{'width': 180 + 'px'}"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row class="mt16">
          <el-col :span="24">
            <el-form-item label="检测图片：">
              <div class="cbo">
                <div class="img-box">
                  <el-upload
                    accept=".jpeg,.png,.jpg,.bmp,.gif"
                    :action="baseUrl + '/upload/file'"
                    list-type="picture-card"
                    :on-preview="handlePreview"
                    :file-list="batchForm[activeName].img"
                    :on-success="handlePicSuccess"
                    :on-remove="handlePicRemove">
                    <i class="el-icon-plus"></i>
                  </el-upload>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <!-- 流动度比 -->
    <div v-if="activeName === 'SLAG_PARAM_KZF_LDDB'" class="custom-form">
      <el-form :disabled="loading2 || experimentStatus == 3" :inline="true">
        <div class="mt16 jcrq-box">
          <el-form-item label="检测日期：">
            <el-date-picker
              type="date"
              v-model="batchForm[activeName].jcrq"
              placeholder="选择日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
        </div>
        <div class="mt16 flex-box">
          <el-form-item label-width="130px" label="试验水泥用量：" class="flex-item flex-box" >
            <el-input
              v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @input="val => setSYLZLFS()"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].sysnl" 
              placeholder="请输入"
            >
              <template slot="append">g</template>
            </el-input>
          </el-form-item>
          <el-form-item label-width="130px" class="flex-item flex-box" label="试验矿渣粉用量：">
            <el-input
              v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].sykzfyl"
              @input="val => setSYLZLFS()"
              placeholder="请输入"
            >
              <template slot="append">g</template>
            </el-input>
          </el-form-item>
          <el-form-item class="flex-item flex-box">
          </el-form-item>
        </div>
        <div class="mt16 flex-box">
          <el-form-item label-width="130px" label="细骨料用量：" class="flex-item flex-box" >
            <el-input
              v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              disabled
              v-model="batchForm[activeName].xglyl" 
              placeholder="1350"
            >
              <template slot="append">g</template>
            </el-input>
          </el-form-item>
          <el-form-item label-width="130px" class="flex-item flex-box" label="水用量：">
            <el-input
              v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              disabled
              v-model="batchForm[activeName].syl"
              placeholder="225"
            >
              <template slot="append">g</template>
            </el-input>
          </el-form-item>
          <el-form-item class="flex-item flex-box">
          </el-form-item>
        </div>
        
        <div class="table-box-kzf table-box-kzf2 mt16">
          <div class="tb-title">试验胶砂流动度</div>
          <div class="tb-content flex-box" style="padding: 24px 0 8px;">
            <el-form-item class="flex-item" label="直径1：">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="batchForm[activeName].syjsldd.zj1"
                @input="val => setLDX('syjsldd')"
                placeholder="请输入"
              >
                <template slot="append">mm</template>
              </el-input>
            </el-form-item>
            <el-form-item class="flex-item" label="直径2：">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="batchForm[activeName].syjsldd.zj2"
                @input="val => setLDX('syjsldd')"
                placeholder="请输入"
              >
                <template slot="append">mm</template>
              </el-input>
            </el-form-item>
            <!-- <el-form-item class="flex-item" label="矿渣粉用量：">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="batchForm[activeName].syjsldd.kzfyl"
                placeholder="请输入"
                style="width: 130px;"
              >
                <template slot="append">g</template>
              </el-input>
            </el-form-item> -->
            <el-form-item class="flex-item" label="流动度：">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                disabled
                v-model="batchForm[activeName].syjsldd.ldd"
              >
                <template slot="append">mm</template>
              </el-input>
            </el-form-item>
          </div>
        </div>
        
        <div class="table-box-kzf table-box-kzf2 mt16">
          <div class="tb-title">
            <!-- <el-button class="fr" type="text" @click="selectInvoice">选择对比样品</el-button> -->
            <span>对比胶砂流动度</span>
          </div>
          <div class="tb-content flex-box" style="padding: 24px 0 8px;">
            <el-form-item class="flex-item" label="直径1：">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="batchForm[activeName].dbjsldd.zj1"
                @input="val => setLDX('dbjsldd')"
                placeholder="请输入"
              >
                <template slot="append">mm</template>
              </el-input>
            </el-form-item>
            <el-form-item class="flex-item" label="直径2：">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="batchForm[activeName].dbjsldd.zj2"
                @input="val => setLDX('dbjsldd')"
                placeholder="请输入"
              >
                <template slot="append">mm</template>
              </el-input>
            </el-form-item>
            <el-form-item class="flex-item" label="流动度：">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                disabled
                v-model="batchForm[activeName].dbjsldd.ldd"
              >
                <template slot="append">mm</template>
              </el-input>
            </el-form-item>
            <!-- <el-form-item class="flex-item">
            </el-form-item> -->
          </div>
        </div>
        
        <!-- <div class="table-box-kzf mt16">
          <div class="tb-title">对比流动度</div>
          <div class="tb-content flex-box">
            <div class="tb-left">
              <div>试验胶砂流动度</div>
              <div>对比胶砂流动度</div>
            </div>
            <div class="tb-center flex-item">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                placeholder="请输入"
                v-model="batchForm[activeName].syyp" 
              >
              </el-input>
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                placeholder="请输入"
                v-model="batchForm[activeName].syyp" 
              >
              </el-input>
            </div>
            <div class="tb-center flex-item">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                placeholder="请输入"
                v-model="batchForm[activeName].syyp" 
              >
              </el-input>
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                placeholder="请输入"
                v-model="batchForm[activeName].syyp" 
              >
              </el-input>
            </div>
            <div class="tb-center flex-item">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                placeholder="请输入"
                v-model="batchForm[activeName].syyp" 
              >
              </el-input>
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                placeholder="请输入"
                v-model="batchForm[activeName].syyp" 
              >
              </el-input>
            </div>
            <div class="tb-center flex-item">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                placeholder="请输入"
                v-model="batchForm[activeName].syyp" 
              >
              </el-input>
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                placeholder="请输入"
                v-model="batchForm[activeName].syyp" 
              >
              </el-input>
            </div>
            <div class="tb-center flex-item">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                placeholder="请输入"
                v-model="batchForm[activeName].syyp" 
              >
              </el-input>
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                placeholder="请输入"
                v-model="batchForm[activeName].syyp" 
              >
              </el-input>
            </div>
            <div class="tb-center flex-item">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                placeholder="请输入"
                v-model="batchForm[activeName].syyp" 
              >
              </el-input>
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                placeholder="请输入"
                v-model="batchForm[activeName].syyp" 
              >
              </el-input>
            </div>
            <div class="tb-right">
              <p>代表值：<el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  placeholder="请输入"
                  v-model="batchForm[activeName].syyp" 
                  :style="{'width': 142 + 'px'}"
                >
                  <template slot="append">%</template>
                </el-input>
              </p>
              <p>代表值：<el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  placeholder="请输入"
                  v-model="batchForm[activeName].syyp" 
                  :style="{'width': 142 + 'px'}"
                >
                  <template slot="append">%</template>
                </el-input>
              </p>
            </div>
          </div>
        </div> -->
        
        <div class="line"></div>
        <el-row>
          <el-col :span="10">
            <el-form-item class="flex-box" label="试验结果：">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                v-model="batchForm[activeName].syjg" 
                disabled
                :style="{'width': 200 + 'px'}"
              >
                <template slot="append">%</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="14">
            <el-form-item class="flex-box" label="单项结论：">
              <el-input
                v-model="batchForm[activeName].dxjl" 
                clearable
                placeholder="请输入"
                :disabled="dxjlDisabled"
                :style="{'width': 180 + 'px'}"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row>
          <el-col :span="24">
            <el-form-item label="检测图片：">
              <div class="cbo">
                <div class="img-box">
                  <el-upload
                    accept=".jpeg,.png,.jpg,.bmp,.gif"
                    :action="baseUrl + '/upload/file'"
                    list-type="picture-card"
                    :on-preview="handlePreview"
                    :file-list="batchForm[activeName].img"
                    :on-success="handlePicSuccess"
                    :on-remove="handlePicRemove">
                    <i class="el-icon-plus"></i>
                  </el-upload>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <!-- 活性指数 -->
    <div v-if="activeName === 'SLAG_PARAM_KZF_HXZS' && batchForm[activeName].hxzsInfoList.length > 0" class="custom-form">
      <div class="mt16">
        <span @click="dayNum = 0" class="tag-btn" :class="{'tag-btn-primary' : dayNum === 0}">活性指数-7d</span>
        <span @click="dayNum = 1" class="tag-btn" :class="{'tag-btn-primary' : dayNum === 1}">活性指数-28d</span>
      </div>
      
      <el-form :disabled="loading2 || experimentStatus == 3" :inline="true" class="mt16">
        <div class="mt16 jcrq-box">
          <el-form-item label="检测日期：">
            <el-date-picker
              type="date"
              v-model="batchForm[activeName].hxzsInfoList[dayNum].jcrq"
              placeholder="选择日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
        </div>
        <div class="table-box-kzf mt16">
          <div class="tb-title">试验胶砂对比强度(kN)</div>
          <div class="tb-content">
            <div class="flex-box tb-item">
              <el-form-item class="flex-item">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="batchForm[activeName].hxzsInfoList[dayNum].syjskdbqd.qd1"
                  @input="val => setHXZSPJZ('sy')"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  placeholder="请输入"
                  style="width: 80px;"
                >
                </el-input>
              </el-form-item>
              <el-form-item class="flex-item">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="batchForm[activeName].hxzsInfoList[dayNum].syjskdbqd.qd2"
                  @input="val => setHXZSPJZ('sy')"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  placeholder="请输入"
                  style="width: 80px;"
                >
                </el-input>
              </el-form-item>
              <el-form-item class="flex-item">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="batchForm[activeName].hxzsInfoList[dayNum].syjskdbqd.qd3"
                  @input="val => setHXZSPJZ('sy')"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  placeholder="请输入"
                  style="width: 80px;"
                >
                </el-input>
              </el-form-item>
              <el-form-item class="flex-item">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="batchForm[activeName].hxzsInfoList[dayNum].syjskdbqd.qd4"
                  @input="val => setHXZSPJZ('sy')"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  placeholder="请输入"
                  style="width: 80px;"
                >
                </el-input>
              </el-form-item>
              <el-form-item class="flex-item">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="batchForm[activeName].hxzsInfoList[dayNum].syjskdbqd.qd5"
                  @input="val => setHXZSPJZ('sy')"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  placeholder="请输入"
                  style="width: 80px;"
                >
                </el-input>
              </el-form-item>
              <el-form-item class="flex-item">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="batchForm[activeName].hxzsInfoList[dayNum].syjskdbqd.qd6"
                  @input="val => setHXZSPJZ('sy')"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  placeholder="请输入"
                  style="width: 80px;"
                >
                </el-input>
              </el-form-item>
            </div>
          </div>
          <div class="tb-bottom">
            <el-form-item label="平均值：" class="flex-item">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                v-model="batchForm[activeName].hxzsInfoList[dayNum].syjsbfb"
                disabled
              >
                <template slot="append">MPa</template>
              </el-input>
            </el-form-item>
          </div>
        </div>
        
        <p class="info-form-title mt16">对比程度（MPa）</p>
        <div class="table-box-kzf mt16">
          <div class="tb-title">对比胶砂对比强度(kN)</div>
          <div class="tb-content">
            <div class="flex-box tb-item">
              <el-form-item class="flex-item">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="batchForm[activeName].hxzsInfoList[dayNum].dbjsdbqd.qd1"
                  @input="val => setHXZSPJZ('db')"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  placeholder="请输入"
                  style="width: 80px;"
                >
                </el-input>
              </el-form-item>
              <el-form-item class="flex-item">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="batchForm[activeName].hxzsInfoList[dayNum].dbjsdbqd.qd2"
                  @input="val => setHXZSPJZ('db')"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  placeholder="请输入"
                  style="width: 80px;"
                >
                </el-input>
              </el-form-item>
              <el-form-item class="flex-item">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="batchForm[activeName].hxzsInfoList[dayNum].dbjsdbqd.qd3"
                  @input="val => setHXZSPJZ('db')"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  placeholder="请输入"
                  style="width: 80px;"
                >
                </el-input>
              </el-form-item>
              <el-form-item class="flex-item">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="batchForm[activeName].hxzsInfoList[dayNum].dbjsdbqd.qd4"
                  @input="val => setHXZSPJZ('db')"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  placeholder="请输入"
                  style="width: 80px;"
                >
                </el-input>
              </el-form-item>
              <el-form-item class="flex-item">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="batchForm[activeName].hxzsInfoList[dayNum].dbjsdbqd.qd5"
                  @input="val => setHXZSPJZ('db')"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  placeholder="请输入"
                  style="width: 80px;"
                >
                </el-input>
              </el-form-item>
              <el-form-item class="flex-item">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="batchForm[activeName].hxzsInfoList[dayNum].dbjsdbqd.qd6"
                  @input="val => setHXZSPJZ('db')"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  placeholder="请输入"
                  style="width: 80px;"
                >
                </el-input>
              </el-form-item>
            </div>
          </div>
          <div class="tb-bottom">
            <el-form-item label="平均值：" class="flex-item">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                v-model="batchForm[activeName].hxzsInfoList[dayNum].dbjsbfb"
                disabled
              >
                <template slot="append">MPa</template>
              </el-input>
            </el-form-item>
          </div>
        </div>
      </el-form>
      <div class="line"></div>
      <p class="info-form-title mt16">结论</p>
      <el-form :disabled="loading2 || experimentStatus == 3" :inline="true" class="mt16">
        <el-row class="mt16" v-if="dayNum === 0">
          <el-col :span="10">
            <el-form-item class="flex-box" label="7d活性指数：">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                v-model="batchForm[activeName].hxzsInfoList[dayNum].hxzs" 
                disabled
                :style="{'width': 200 + 'px'}"
              >
                <template slot="append">%</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="14">
            <el-form-item class="flex-box" label="7d单项结论：">
              <el-input
                v-model="batchForm[activeName].hxzsInfoList[dayNum].dxjl" 
                :disabled="dxjlDisabled"
                :style="{'width': 180 + 'px'}"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row class="mt16" v-else>
          <el-col :span="10">
            <el-form-item class="flex-box" label="28d活性指数：">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                v-model="batchForm[activeName].hxzsInfoList[dayNum].hxzs" 
                disabled
                :style="{'width': 200 + 'px'}"
              >
                <template slot="append">%</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="14">
            <el-form-item class="flex-box" label="28d单项结论：">
              <el-input
                v-model="batchForm[activeName].hxzsInfoList[dayNum].dxjl" 
                :disabled="dxjlDisabled"
                :style="{'width': 180 + 'px'}"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="检测图片：">
              <div class="cbo">
                <div class="img-box">
                  <el-upload
                    accept=".jpeg,.png,.jpg,.bmp,.gif"
                    :action="baseUrl + '/upload/file'"
                    list-type="picture-card"
                    :on-preview="handlePreview"
                    :file-list="batchForm[activeName].img"
                    :on-success="handlePicSuccess"
                    :on-remove="handlePicRemove">
                    <i class="el-icon-plus"></i>
                  </el-upload>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    
    
    <!-- 含水量 -->
    <div v-if="activeName === 'SLAG_PARAM_KZF_HSL'" class="custom-form">
      <el-form :disabled="loading2 || experimentStatus == 3" :inline="true" label-width="200px">
        <div class="mt16 jcrq-box">
          <el-form-item label="检测日期：">
            <el-date-picker
              type="date"
              v-model="batchForm[activeName].jcrq"
              placeholder="选择日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
        </div>
        <div class="flex-box mb16 mt24">
          <el-form-item label="称量器皿质量：" class="flex-item flex-box">
            <el-input
              v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @input="setHSL"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].clqmzl" 
              style="width: 200px;"
              placeholder="请输入"
            >
              <template slot="append">g</template>
            </el-input>
          </el-form-item>
        </div>
        <div class="flex-box mb16 mt24">
          <el-form-item class="flex-item flex-box" label="试料+称量器皿质量：">
            <el-input
              v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @input="setHSL"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].slclqmzl"
              placeholder="请输入"
              style="width: 200px;"
            >
              <template slot="append">g</template>
            </el-input>
          </el-form-item>
          
          <el-form-item label="烘前试样质量：" class="flex-item flex-box">
            <el-input
              v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].cyzl" 
              disabled
              style="width: 200px;"
            >
              <template slot="append">g</template>
            </el-input>
          </el-form-item>
        </div>
        
        <div class="flex-box mb16 mt24">
          <el-form-item class="flex-item flex-box" label="烘干后试料+称量器皿质量：">
            <el-input
              v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @input="setHSL"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].hghclqmzl"
              placeholder="请输入"
              style="width: 200px;"
            >
              <template slot="append">g</template>
            </el-input>
          </el-form-item>
          
          
          <el-form-item class="flex-item flex-box" label="烘后试样质量：">
            <el-input
              v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              disabled
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].hghsyzl"
              style="width: 200px;"
            >
              <template slot="append">g</template>
            </el-input>
          </el-form-item>
          
        </div>
        <el-row>
          <el-col :span="12">
            <el-form-item class="flex-item flex-box" label="含水率：">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="batchForm[activeName].hsl"
                disabled
                placeholder="请输入"
                style="width: 200px;"
              >
                <template slot="append">%</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item class="flex-box" label="单项结论：">
              <el-input
                v-model="batchForm[activeName].dxjl" 
                :disabled="dxjlDisabled"
                placeholder="请输入"
                style="width: 200px;"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row>
          <el-col :span="24">
            <el-form-item label="检测图片：">
              <div class="cbo">
                <div class="img-box">
                  <el-upload
                    accept=".jpeg,.png,.jpg,.bmp,.gif"
                    :action="baseUrl + '/upload/file'"
                    list-type="picture-card"
                    :on-preview="handlePreview"
                    :file-list="batchForm[activeName].img"
                    :on-success="handlePicSuccess"
                    :on-remove="handlePicRemove">
                    <i class="el-icon-plus"></i>
                  </el-upload>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    
    
    <!-- 三氧化硫 -->
    <div v-if="activeName === 'SLAG_PARAM_KZF_SYHL'" key="SLAG_PARAM_KZF_SYHL"class="custom-form">
      <el-form :disabled="loading2 || experimentStatus == 3" :inline="true">
        <div class="flex-row mt16 jcrq-box">
          <el-form-item label="检测日期：">
            <el-date-picker
              type="date"
              v-model="batchForm[activeName].jcrq"
              placeholder="选择日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
        </div>
        <div class="line"></div>
        <div v-for="(item, index) in batchForm[activeName].syhlList" :key="index">
          <p class="info-form-title mt16">{{index == 0 ? '第一次试验' : '第二次试验'}}</p>
          <div class="info-form-piece flex-box" style="flex-direction: column;">
            <div class="flex-box" style="width: 100%;">
              <el-form-item label="称量器皿质量：" class="flex-item flex-box" label-width="120px">
                <el-input
                  v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  @input="val => setSYHL(item, index, 'sy')"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  v-model="item.clqmzl"
                  :key="`clqmzl-${index}`"
                  placeholder="请输入"
                >
                  <template slot="append">g</template>
                </el-input>
              </el-form-item>
              <el-form-item class="flex-item flex-box" label="试样+称量器皿质量：" label-width="180px">
                <el-input
                  v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  v-model="item.slqmzl"
                  :key="`slqmzl-${index}`"
                  @input="val => setSYHL(item, index, 'sy')"
                  placeholder="请输入"
                >
                  <template slot="append">g</template>
                </el-input>
              </el-form-item>
              <el-form-item class="flex-item flex-box" label="试样质量：" label-width="130px">
                <el-input
                  v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  v-model="item.syzl"
                  disabled
                  placeholder="请输入"
                >
                  <template slot="append">g</template>
                </el-input>
              </el-form-item>
            </div>
            <div class="flex-box" style="width: 100%;">
              <el-form-item label="瓷坩埚质量：" class="flex-item flex-box" label-width="120px">
                <el-input
                  v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  @input="val => setSYHL(item, index, 'cd')"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  v-model="item.zggzl"
                  placeholder="请输入"
                >
                  <template slot="append">g</template>
                </el-input>
              </el-form-item>
              <el-form-item class="flex-item flex-box" label="灼烧后沉淀+瓷坩埚质量：" label-width="180px">
                <el-input
                  v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  v-model="item.zshcdcggzl"
                  @input="val => setSYHL(item, index, 'cd')"
                  placeholder="请输入"
                >
                  <template slot="append">g</template>
                </el-input>
              </el-form-item>
              <el-form-item class="flex-item flex-box" label="灼烧后沉淀质量：" label-width="130px">
                <el-input
                  v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  v-model="item.zshcdzl"
                  @input="val => setSYHL(item, index, 'cd')"
                  disabled
                  placeholder="请输入"
                >
                  <template slot="append">g</template>
                </el-input>
              </el-form-item>
            </div>
            <div class="flex-box" style="width: 100%;">
              <el-form-item label="换算系数：" class="flex-item flex-box" label-width="120px">
                <el-input
                  v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  @input="val => setSYHL(item, index, 'cd')"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  v-model="item.hsxs" 
                  :value="0.343"
                  disabled
                  placeholder="请输入"
                >
                  <template slot="append"></template>
                </el-input>
              </el-form-item>
              <el-form-item class="flex-item flex-box" label="质量分数：" label-width="180px">
                <el-input
                  v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  v-model="item.zlfs"
                  disabled
                  @input="val => setSYHL(item, index, 'cd')"
                  placeholder="请输入"
                >
                  <template slot="append"></template>
                </el-input>
              </el-form-item>
              <div class="flex-item flex-box"></div>
            </div>
          </div>
        </div>
        <div class="line"></div>
        <el-row>
          <el-col :span="8">
            <el-form-item class="flex-box" label="平均值：">
              <el-input
                v-model="batchForm[activeName].pjz" 
                disabled
                placeholder="请输入"
                :style="{'width': 180 + 'px'}"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="14">
            <el-form-item class="flex-box" label="单项结论：">
              <el-input
                v-model="batchForm[activeName].dxjl" 
                :disabled="dxjlDisabled"
                placeholder="请输入"
                :style="{'width': 180 + 'px'}"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row>
          <el-col :span="24">
            <el-form-item label="检测图片：">
              <div class="cbo">
                <div class="img-box">
                  <el-upload
                    accept=".jpeg,.png,.jpg,.bmp,.gif"
                    :action="baseUrl + '/upload/file'"
                    list-type="picture-card"
                    :on-preview="handlePreview"
                    :file-list="batchForm[activeName].img"
                    :on-success="handlePicSuccess"
                    :on-remove="handlePicRemove">
                    <i class="el-icon-plus"></i>
                  </el-upload>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    
    
    <!-- 游离氧化钙公式 -->
    <div v-if="activeName === 'SLAG_PARAM_KZF_YLYHG'" key="SLAG_PARAM_KZF_YLYHG"class="custom-form">
      <el-form :disabled="loading2 || experimentStatus == 3" :inline="true"  label-width="100px" label-position="right">
        <div class="mt16 jcrq-box">
          <el-form-item label="检测日期：">
            <el-date-picker
              type="date"
              v-model="batchForm[activeName].jcrq"
              placeholder="选择日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
        </div>
        
        <div v-for="(item, index) in batchForm[activeName].ylyhgList" :key="index">
          <p class="info-form-title mt16">{{index == 0 ? '第一次试验' : '第二次试验'}}</p>
          <div class="info-form-piece flex-box">
            <el-form-item label="称量器皿质量：" class="flex-item flex-box" label-width="170px">
              <el-input
                v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @input="val => setYLYHG(index)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="item.clqmzl" 
                placeholder="请输入"
              >
                <template slot="append">g</template>
              </el-input>
            </el-form-item>
            <el-form-item label="试料+称量器皿质量：" class="flex-item flex-box"  label-width="150px">
              <el-input
                v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @input="val => setYLYHG(index)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="item.slclqmzl" 
                placeholder="请输入"
              >
                <template slot="append">g</template>
              </el-input>
            </el-form-item>
            <el-form-item label="试样质量：" class="flex-item flex-box" >
              <el-input
                v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="item.syzl" 
                disabled
              >
                <template slot="append">g</template>
              </el-input>
            </el-form-item>
          </div>
          <div class="info-form-piece flex-box">
            <el-form-item class="flex-item flex-box" label="苯甲酸无水乙醇滴定度：" label-width="170px">
              <el-input
                v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="item.bjswsycddd"
                @input="val => setYLYHG(index)"
                placeholder="请输入"
              >
                <template slot="append">mg/ml</template>
              </el-input>
            </el-form-item>
            <el-form-item class="flex-item flex-box" label="标准滴定溶液体积：" label-width="150px">
              <el-input
                v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="item.bzddrytj"
                @input="val => setYLYHG(index)"
                placeholder="请输入"
              >
                <template slot="append">ml</template>
              </el-input>
            </el-form-item>
            <el-form-item class="flex-item flex-box" label="结果：">
              <el-input
                v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="item.syjg"
                disabled
                placeholder="请输入"
              >
                <template slot="append">%</template>
              </el-input>
            </el-form-item>
          </div>
        </div>
        
        <div class="mt16 flex-box">
          
        </div>
        <div class="line"></div>
        <el-row>
          <el-col :span="10">
            <el-form-item class="flex-box" label="平均值：">
              <el-input
                v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                v-model="batchForm[activeName].pjz" 
                disabled
                :style="{'width': 200 + 'px'}"
              >
                <template slot="append">%</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="14">
            <el-form-item class="flex-box" label="单项结论：">
              <el-input
                v-model="batchForm[activeName].dxjl" 
                :disabled="dxjlDisabled"
                placeholder="请输入"
                :style="{'width': 180 + 'px'}"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row>
          <el-col :span="24">
            <el-form-item label="检测图片：">
              <div class="cbo">
                <div class="img-box">
                  <el-upload
                    accept=".jpeg,.png,.jpg,.bmp,.gif"
                    :action="baseUrl + '/upload/file'"
                    list-type="picture-card"
                    :on-preview="handlePreview"
                    :file-list="batchForm[activeName].img"
                    :on-success="handlePicSuccess"
                    :on-remove="handlePicRemove">
                    <i class="el-icon-plus"></i>
                  </el-upload>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    
    
    
    
    <!-- 烧矢量 -->
    <div v-if="activeName === 'SLAG_PARAM_KZF_SSL'" class="custom-form">
      <el-form label-width="120px" :disabled="loading2 || experimentStatus == 3" :inline="true">
        <div class="mt16 jcrq-box">
          <el-form-item label="检测日期：">
            <el-date-picker
              type="date"
              v-model="batchForm[activeName].jcrq"
              placeholder="选择日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
        </div>
        <div v-for="(item, index) in batchForm[activeName].sslInfo" :key="index">
          <p class="info-form-title mt16">{{index == 0 ? '第一次试验' : '第二次试验'}}</p>
          <div class="info-form-piece flex-box" style="padding-bottom: 0;">
            
            <el-form-item label="称量器皿质量：" class="flex-item flex-box">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @input="val => setSSL(index)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="item.clqmzl" 
                placeholder="请输入"
              >
                <template slot="append">g</template>
              </el-input>
            </el-form-item>
            <el-form-item label="试料+称量器皿质量：" class="flex-item flex-box"  label-width="180px">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @input="val => setSSL(index)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="item.slclqmzl" 
                placeholder="请输入"
              >
                <template slot="append">g</template>
              </el-input>
            </el-form-item>
            
            
            <!-- 11 -->
            <el-form-item label="试料质量：" class="flex-item flex-box" label-width="130px">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="item.slzl"
                disabled
              >
                <template slot="append">g</template>
              </el-input>
            </el-form-item>
          </div>
          <div class="info-form-piece flex-box" style="padding: 0 16px;">
            <el-form-item label="瓷坩埚质量：" class="flex-item flex-box">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @input="val => setSSL(index)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="item.cggzl" 
                placeholder="请输入"
              >
                <template slot="append">g</template>
              </el-input>
            </el-form-item>
            <el-form-item label="灼烧后试料+瓷坩埚质量：" class="flex-item flex-box"  label-width="180px">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @input="val => setSSL(index)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="item.zshcggzl" 
                placeholder="请输入"
              >
                <template slot="append">g</template>
              </el-input>
            </el-form-item>
            
            <!-- 11 -->
            <el-form-item class="flex-item flex-box" label="灼烧后试样质量："  label-width="130px">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="item.zshsyzl"
                disabled
              >
                <template slot="append">g</template>
              </el-input>
            </el-form-item>
          </div>
          <div class="info-form-piece flex-box" style="padding: 0 16px 16px 16px;">
            <el-form-item label="试验结果：" class="flex-item flex-box">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                disabled
                v-model="item.ssl"
              >
                <template slot="append">%</template>
              </el-input>
            </el-form-item>
          </div>
        </div>
        
        <div class="line"></div>
        <!-- <el-row>
          <el-col :span="12">
            <el-form-item label-width="280px" class="flex-box" label="矿渣灼烧测得的三氧化硫质量分数：">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                v-model="batchForm[activeName].zssyhl" 
                disabled
                :style="{'width': 130 + 'px'}"
              >
                <template slot="append">%</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label-width="280px" class="flex-box" label="矿渣未经灼烧测得的三氧化硫质量分数：">
              <el-input
                v-model="batchForm[activeName].wzssyhl" 
                disabled
                placeholder="请输入"
                :style="{'width': 130 + 'px'}"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12" class="mt16">
            <el-form-item label-width="280px" class="flex-box mt8" label="矿渣灼烧过程中吸收空气中氧的质量分数：">
              <el-input
                v-model="batchForm[activeName].zskqy" 
                disabled
                placeholder="请输入"
                :style="{'width': 130 + 'px'}"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row> -->
        
        <el-row>
          <el-col :span="12">
            <el-form-item label-width="138px" class="flex-box" label="烧失量平均值：">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                v-model="batchForm[activeName].sslpjz" 
                disabled
              >
                <template slot="append">%</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <!-- <el-form-item label-width="280px" class="flex-box" label="烧失量矫正值：">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                v-model="batchForm[activeName].ssljzz" 
                disabled
                :style="{'width': 130 + 'px'}"
              >
                <template slot="append">%</template>
              </el-input>
            </el-form-item> -->
          </el-col>
          <!-- <el-col :span="8">
            <el-form-item class="flex-box" label="单项结论：">
              <el-input
                v-model="batchForm[activeName].dxjl" 
                disabled
                placeholder="请输入"
                :style="{'width': 130 + 'px'}"
              >
              </el-input>
            </el-form-item>
          </el-col> -->
        </el-row>
        
        <el-row>
          <el-col :span="24">
            <el-form-item label="检测图片：" label-width="138px">
              <div class="cbo">
                <div class="img-box">
                  <el-upload
                    accept=".jpeg,.png,.jpg,.bmp,.gif"
                    :action="baseUrl + '/upload/file'"
                    list-type="picture-card"
                    :on-preview="handlePreview"
                    :file-list="batchForm[activeName].img"
                    :on-success="handlePicSuccess"
                    :on-remove="handlePicRemove">
                    <i class="el-icon-plus"></i>
                  </el-upload>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    
  
    <image-viewer
      v-if="imgViewerVisible"
      :urlList="[previewUrl]"
      :on-close="onClose"
    >
    </image-viewer>
    
    <TableDialog @setData="setReceiptData" ref="tableDialog" :tableColumn="dialogTableColumn"></TableDialog>
  </div>
</template>

<script>
import ImageViewer from "element-ui/packages/image/src/image-viewer";
import {
conclusion_KZF_HXZS_7d,
conclusion_KZF_HXZS_28d
} from "./conclusion.js"
import {add,  sub,  mul,  div, roundToDecimalPlace, calcEquation} from "@/utils/calculate.js"
import moment from "@/utils/moment.js"
import { standard, HXZS_config } from "./config.js"
import util from "../../../common/js/util.js";

import TableDialog from "@/components/tableDialog3.vue";
import { panelColumn } from "../config.js"

export default {
  name:'userMgt',
  components: {
    ImageViewer,
    TableDialog
  },
  props: {
    activeId: {
      type: Number | String,
    },
    experimentStatus: {
      type: Number | String,
    },
    sampleLevel: {
      type: String,
      default: '1'
    },
    entrustTime: {
      type: String,
      default: new Date().toISOString().split('T')[0],
    },
    
  },
  watch: {
    activeId: {
      handler(newValue, oldValue){
        if(newValue){
          // this.clearData();
          // this.setExperimentProject()
        }
      },
      immediate: true
    },
    entrustTime: {
      handler(newValue, oldValue){
        if(newValue){
          this.setDefaultTime(this.batchForm)
        }
      },
      immediate: true
    },
    
  },
  computed: {},
  data() {
    return {
      defaultDate: moment.today(),
      infoForm: '',
      filePrefix: this.$store.state.loginStore.userInfo.filePrefix,
      baseUrl: process.env.NODE_ENV === 'development' ? "/api" : "/mes-platform",
      activeName: 'quick',
      activeInfoName: 'ypmdInfo',
      quickForm: {},//快检数据
      quickData: [],//快检列表
      batchForm: {
        CONCRETE_PARAM_XGL_HNL: {
          hnlInfo: []
        },
        CONCRETE_PARAM_XGL_NKHL: {
          hnkInfo: []
        }
      },//批检数据
      batchData: [],//批检列表
      
      loading2: false,
      
      previewUrl: "",
      imgViewerVisible: false,
      
      dayNum: 0,
      sampleLevel2: 'PO42.5',//流动度等级
      HXZSLevel: 'S105',
      
      
      dialogTableColumn: panelColumn,
      accordingObj: {},
      dxjlDisabled: false,
    };
  },
  
  created() {
  },
  methods: {
        isEmptyValue(value) {
            // 处理用户输入的特殊字符串
            if (value === 'null') return true;
            if (value === 'undefined') return true;
            if (value === '') return true;
            
            // 处理实际的JavaScript值
            if (value === null) return true;
            if (value === undefined) return true;
            if (value === '') return true;
            
            return false;
        },
    
        setDefaultTime(val){

      // 活性指数=委托时间+28天
      // 样品密度
      if(val.SLAG_PARAM_KZF_YPMD && this.isEmptyValue(val.SLAG_PARAM_KZF_YPMD?.jcrq)){
        val.SLAG_PARAM_KZF_YPMD['jcrq'] = util.calculateFutureDate(this.entrustTime, 0)
      }
      // 比表面积
      if(val.SLAG_PARAM_KZF_BBMJ && this.isEmptyValue(val.SLAG_PARAM_KZF_BBMJ?.jcrq)){
        val.SLAG_PARAM_KZF_BBMJ['jcrq'] = util.calculateFutureDate(this.entrustTime, 0)
      }
      
      // 流动度
      if(val.SLAG_PARAM_KZF_LDDB && this.isEmptyValue(val.SLAG_PARAM_KZF_LDDB?.jcrq)){
        val.SLAG_PARAM_KZF_LDDB['jcrq'] = util.calculateFutureDate(this.entrustTime, 0)
      }
          // 活性指数
      if(val.SLAG_PARAM_KZF_HXZS && this.isEmptyValue(val.SLAG_PARAM_KZF_HXZS?.jcrq)){
        val.SLAG_PARAM_KZF_HXZS['jcrq'] = util.calculateFutureDate(this.entrustTime, 28)
      }
      
      // 含水率
      if(val.SLAG_PARAM_KZF_HSL && this.isEmptyValue(val.SLAG_PARAM_KZF_HSL?.jcrq)){
        val.SLAG_PARAM_KZF_HSL['jcrq'] = util.calculateFutureDate(this.entrustTime, 0)
      }
      
      // 三氧化硫
      if(val.SLAG_PARAM_KZF_SYHL && this.isEmptyValue(val.SLAG_PARAM_KZF_SYHL?.jcrq)){
        val.SLAG_PARAM_KZF_SYHL['jcrq'] = util.calculateFutureDate(this.entrustTime, 0)
      }
      // 氧化钙
      if(val.SLAG_PARAM_KZF_YLYHG && this.isEmptyValue(val.SLAG_PARAM_KZF_YLYHG?.jcrq)){
        val.SLAG_PARAM_KZF_YLYHG['jcrq'] = util.calculateFutureDate(this.entrustTime, 0)
      }


      // 烧失量
      if(val.SLAG_PARAM_KZF_SSL && this.isEmptyValue(val.SLAG_PARAM_KZF_SSL?.jcrq)){
        val.SLAG_PARAM_KZF_SSL['jcrq'] = util.calculateFutureDate(this.entrustTime, 0)
      }
      

  

        },
    //判断结论
    getPJZUtil(data,key,n){//数组，key，保留小数
      let len = 0,num = 0;
      data.forEach(item =>{
        if(item[key] !== ''){
          len++;
          num += item[key] * 1
        }
      })
      //2次结果之和除以2，保留2位小数，四舍五入
      let arr = [
        {
          v: num,
        },{
          k: '/',
          v: len === 0 ? '' : len
        }
      ]
      return calcEquation(arr, n);
    },
    //判断结论
    conclusion(num, key){
      if(num === ''){
        return ''
      }
      const aName = this.activeName;
      if(!key){
        key = aName.match(/[^_]+$/)[0];
        key = key.toLowerCase();
      }
      if(!this.accordingObj[aName][key]){
        return ''
      }
      const min = this.accordingObj[aName][key]?.min;
      const max = this.accordingObj[aName][key]?.max;
      
      console.log(num, aName, key, min, max);
      if(min !== null && num < min){
        // this.$parent.testInfoForm.isQualified = 2;
        return '不合格'
      }
      if(max !== null && num > max){
        // this.$parent.testInfoForm.isQualified = 2;
        return '不合格'
      }
      return '合格';
    },
    //绑定运单
    selectInvoice() {
      this.$refs.tableDialog.initData('getExperimentList',{
        params:{
        }
      })
    },
    setReceiptData(row){
      console.log(row);
    },
    // 样品密度
    setYPMD(index){
      //质量除以（第二次读数-第一次读数），保留2位小数，不可编辑
      const data = this.batchForm[this.activeName][this.activeInfoName][index];
      let cz = sub(data.decdsv2, data.dycdsv1);
      let arr = [
        {
          v: data.syzl,
        },{
          k: '/',
          v: cz,
        }
      ]
      let res = calcEquation(arr, 3)
      this.batchForm[this.activeName][this.activeInfoName][index].sywz = res;
      this.setPJZ();
    },
    setPJZ(){
      // 平均值：两次检测结果之和的平均值，不可编辑
      // 单项结论：大于等于2.8视为合格，反之不合格
      // PS：如两次结果只差绝对值大于0.02g/cm3，则不允许完成试验  提交注意
      const data = this.batchForm[this.activeName][this.activeInfoName];
      
      let res = this.getPJZUtil(data, 'sywz', 2);
      this.batchForm[this.activeName].pjz = res;
      this.batchForm[this.activeName].dxjl = this.conclusion(res);
    },
    // 流动度比
    // 校验：水泥用量+矿渣粉用量等于对比砂浆水泥用量，不等于的情况下：完成时提示：矿渣粉流动度比取样异常！
    // <!-- 流动度 -->
    setLDX(type){
      const data = this.batchForm[this.activeName][type];
      let arr = [
        {
          v: data.zj1,
        },{
          k: '+',
          v: data.zj2,
        },{
          k: '/',
          v: 2,
        }
      ]
      let res = calcEquation(arr, '0')
      this.batchForm[this.activeName][type].ldd = res;
      this.setLDXJG()
    },
    setLDXJG(){
      // 试验砂浆流动度*100/水泥胶砂流动度（%）
      // 单项结论：大于等于95视为合格，否则视为不合格
      const data = this.batchForm[this.activeName];
      let arr = [
        {
          v: data.syjsldd.ldd,
        },{
          k: '*',
          v: 100,
        },{
          k: '/',
          v: data.dbjsldd.ldd,
        }
      ]
      let res = calcEquation(arr, 0)
      this.batchForm[this.activeName].syjg = res;
      this.batchForm[this.activeName].dxjl = this.conclusion(res);
    },
    
    //活性指数
    setHXZSPJZ(type){
      let data;
      if(type == 'sy'){
        data = this.batchForm[this.activeName].hxzsInfoList[this.dayNum][type + 'jskdbqd']
      }else{
        data = this.batchForm[this.activeName].hxzsInfoList[this.dayNum][type + 'jsdbqd']
      }
      let res = this.setHxzsPjzZzz(data);
      
      this.batchForm[this.activeName].hxzsInfoList[this.dayNum][type + 'jsbfb'] = res;
      
      let arr2 = [
        {
          v: this.batchForm[this.activeName].hxzsInfoList[this.dayNum].syjsbfb,
        },{
          k: '/',
          v: this.batchForm[this.activeName].hxzsInfoList[this.dayNum].dbjsbfb,
        },{
          k: '*',
          v: 100,
        }
      ]
      let res2 = calcEquation(arr2, 0)
      this.batchForm[this.activeName].hxzsInfoList[this.dayNum].hxzs = res2;
      
      let key = this.dayNum === 0 ? 'hxzs7d' :'hxzs28d'
      this.batchForm[this.activeName].hxzsInfoList[this.dayNum].dxjl = this.conclusion(res2, key);
    },
    // 上面输入6个，变更为输入kN值，每个KN值 / 1600 * 100 = 单个MPa，  然后平均值等于六个MPa值的平均值
    setHxzsPjzZzz(data){
      let len = 0;
      let num = 0;
      for(let item in data){
        if(data[item] && data[item]>=0){
          len++;
          num += ((data[item] * 1) / 160) * 100;
        }
      }
      let arr = [
        {
          v: num,
        },{
          k: '/',
          v: len,
        },
      ]
      let pjInit = calcEquation(arr, 1);
      
      return pjInit;
    },

    // 20250805 以前版本
    setHxzsPjzZzz_20250805(odata, pjz){
      console.log(odata,pjz)
      let data = JSON.parse(JSON.stringify(odata))
      let len = 0;//可用值数量
      let num = 0;//可用值 之和
      let isLast = 0;
      for(let item in data){
        if(data[item] && data[item]>=0){
          if(pjz){
            if(Math.abs(pjz - data[item] * 1) <= mul(pjz, 0.1)){
              len++;
              num += data[item] * 1;
            }else{
              data[item] = undefined;
              isLast++;
            }
          }else{
            len++;
            num += data[item] * 1;
          }
        }
      }
      if(isLast >= 2 || len < 5){
        return '';
      }
      let arr = [
        {
          v: num,
        },{
          k: '/',
          v: len,
        }
      ]
      let pj = calcEquation(arr, 1);
      if(pjz && isLast === 0){
        return pj;
      }else{
        return this.setHxzsPjzZzz(data,pj);
      }
    },

    setHxzsPjz_old(data){
      let len = 0;
      let num = 0;
      for(let item in data){
        if(data[item] && data[item]>=0){
          len++;
          num += data[item] * 1;
        }
      }
      let arr = [
        {
          v: num,
        },{
          k: '/',
          v: len,
        }
      ]
      let pjInit = calcEquation(arr, 1);
      
      let len2 = 0;
      let num2 = 0;
      let min = mul(pjInit * 1, 0.9);
      let max = mul(pjInit * 1, 1.1);

      for(let item in data){
        if(data[item] && data[item]>=0 && data[item] >= min && data[item] <= max){
          len2++;
          num2 += data[item] * 1;
        }
      }

      let arr2 = [
        {
          v: num2,
        },{
          k: '/',
          v: len2,
        }
      ]
      let pjz = calcEquation(arr2, 1);
      return isNaN(Number(pjz)) ? "" : pjz;
    },
    
    //活性指数
    // setKYQD(index){
    //   const data = this.batchForm[this.activeName]['hxzs' + this.dayNum + 'Info'].syjskysy[index];
    //   let arr = [
    //     {
    //       v: data.hz,
    //     },{
    //       k: '/',
    //       v: data.symj,
    //     }
    //   ]
    //   let res = calcEquation(arr, 1)
    //   this.batchForm[this.activeName]['hxzs' + this.dayNum + 'Info'].syjskysy[index].kyqd = res;
    //   this.setKZQDpjz();
    // },
    // setKZQDpjz(){
    //   const data = this.batchForm[this.activeName]['hxzs' + this.dayNum + 'Info'];
    //   let num = data.syjskysy.reduce((pre, cur) => {
    //     return add(pre, cur.kyqd)
    //   }, 0);
    //   let arr = [
    //     {
    //       v: num,
    //     },{
    //       k: '/',
    //       v: data.syjskysy.length,
    //     }
    //   ]
    //   let res = calcEquation(arr, 1)
    //   this.batchForm[this.activeName]['hxzs' + this.dayNum + 'Info'].syjspjz = res;
      
    //   if(res >= standard[this.sampleLevel2]['kzqdsy' + this.dayNum]){
    //     this.batchForm[this.activeName]['hxzs' + this.dayNum + 'Info'].syjsdxjl = '合格'
    //   }else{
    //     this.batchForm[this.activeName]['hxzs' + this.dayNum + 'Info'].syjsdxjl = '不合格'
    //   }
    //   this.setHXZSJL(this.dayNum);
    // },
    // setHXZSDBZ(type){
    //   const data = this.batchForm[this.activeName]['hxzs' + type + 'Info'].dbjsdbqd;
    //   let num = 0;
    //   for (let i = 1; i<=6; i++) {
    //     num = num + data['qd'+ i] * 1
    //   }
    //   let arr = [
    //     {
    //       v: num,
    //     },{
    //       k: '/',
    //       v: 6,
    //     }
    //   ]
    //   let res = calcEquation(arr, 1)
    //   this.batchForm[this.activeName]['hxzs' + type + 'Info'].dbjsdbqd.dbz = res;
      
    //   this.setHXZSJL(type);
    // },
    // setHXZSJL(type){
    //   // 对应天数活性指数：
    //   // 活性指数=试验胶砂对应天数强度/对比胶砂对应天数强度*100
    //   const data = this.batchForm[this.activeName]['hxzs' + type + 'Info'];
    //   let arr = [
    //     {
    //       v: data.syjspjz,
    //     },{
    //       k: '/',
    //       v: data.dbjsdbqd.dbz,
    //     },{
    //       k: '*',
    //       v: 100,
    //     }
    //   ]
    //   let res = calcEquation(arr, 0)
    //   console.log(data,type)
    //   this.batchForm[this.activeName]['hxzs' + type] = res;
      
    //   if(type === '7d'){
    //     this.batchForm[this.activeName]['dxjl' + type] = this.conclusion(res,conclusion_KZF_HXZS_7d);;
    //   }else{
    //     this.batchForm[this.activeName]['dxjl' + type] = this.conclusion(res,conclusion_KZF_HXZS_28d);;
    //   }
      
    // },
    
    
    
    // 含泥量 泥块含量计算
    hhsyzlChange(val, beforeVal, index){//设置烘后质量范围
      if(!beforeVal){
        this.$message.error("烘前试样质量需要大于0");
        this.batchForm[this.activeName][this.activeInfoName][index].hhsyzl = '';
      }else if(Number(val) > Number(beforeVal)){
        this.batchForm[this.activeName][this.activeInfoName][index].hhsyzl = Number(beforeVal);
      }
      this.setHnl(beforeVal, this.batchForm[this.activeName][this.activeInfoName][index].hhsyzl, index);
    },
    hqsyzlChange(val, afterVal, index){//设置烘前质量范围
      this.setHnl(val, afterVal, index);
    },
    setHnl(beforeVal, afterVal, index){//设置含泥量
      if(beforeVal > 0 && afterVal > 0){
        //（烘前-烘后）/烘前*100%，不可编辑
        this.batchForm[this.activeName][this.activeInfoName][index].hnl = Math.round((beforeVal - afterVal) / beforeVal * 10000) / 100;
      }
      
      this.batchForm[this.activeName].hnlpjz = 
        Math.round((this.batchForm[this.activeName][this.activeInfoName][0].hnl * 1 + this.batchForm[this.activeName][this.activeInfoName][1].hnl * 1) / 2 * 100) / 100;
    },
    // 三氧化硫
    setSYHL(item, index, type) {
      console.log(item, index, type)
      const data = this.batchForm[this.activeName].syhlList[index];
      let arrsyzl = [
        {
          v: data.slqmzl,
        },{
          k: '-',
          v: data.clqmzl,
        }
      ]
      let syzl = calcEquation(arrsyzl, 4);
      
      this.batchForm[this.activeName].syhlList[index].syzl = syzl
      // 灼烧沉淀质量 = 灼烧后沉淀+瓷坩埚质量 - 瓷坩埚质量
      let zshcdzl = calcEquation([
        {
          v: data.zshcdcggzl,
        },{
          k: '-',
          v: data.zggzl,
        },
      ], 4);
      this.batchForm[this.activeName].syhlList[index].zshcdzl = zshcdzl
      let hsxs = data.hsxs || '0.343';
      let arr = [
        {
          v: zshcdzl,
        },{
          k: '*',
          v: hsxs,
        },{
          k: '*',
          v: 100,
        },{
          k: '/',
          v: syzl,
        }
      ]
      this.batchForm[this.activeName].syhlList[index].zlfs = calcEquation(arr, 2);
      this.setSYHLPjz()
    },
    setSYHLPjz(){
      const syhlList = this.batchForm[this.activeName].syhlList;
      
      let res = this.getPJZUtil(syhlList, 'zlfs', 2);
      this.batchForm[this.activeName].pjz = res;
      this.batchForm[this.activeName].dxjl = this.conclusion(res);
    },
    isEmpty(val) {
      if (typeof val === "boolean") {
        return false;
      }
      if (typeof val === "number") {
        return false;
      }
      if (val instanceof Array) {
        if (val.length === 0) return true;
      } else if (val instanceof Object) {
        if (JSON.stringify(val) === "{}") return true;
      } else {
        if (
          val === "null" ||
          val == null ||
          val === "undefined" ||
          val === undefined ||
          val === ""
        )
          return true;
        return false;
      }
      return false;
    },
    
    // 游离氧化钙公式
    setYLYHG(index){
      const data = this.batchForm[this.activeName].ylyhgList[index];
      let syzl = sub(data.slclqmzl, data.clqmzl)
      this.batchForm[this.activeName].ylyhgList[index].syzl = syzl;
      //粉煤灰需水量/水泥需水量 % 100，保留2位小数，四舍五入，不可编辑
      let arr = [
        {
          v: data.bzddrytj,
        },{
          k: '*',
          v: data.bjswsycddd,
        },{
          k: '/',
          v: syzl * 1000,
        },{
          k: '*',
          v: 100,
        }
      ]
      let res = calcEquation(arr, 2)
      this.batchForm[this.activeName].ylyhgList[index].syjg = res;
      this.setYLYHGPJZ();
    },
    setYLYHGPJZ(){
      const data = this.batchForm[this.activeName].ylyhgList;
      
      let res = this.getPJZUtil(data, 'syjg', 1);
      this.batchForm[this.activeName].pjz = res;
      this.batchForm[this.activeName].dxjl = this.conclusion(res);
    },
    
    //烧矢量
    setSSL(index){
      const data = this.batchForm[this.activeName][this.activeInfoName][index];
      
      let a = sub(data.slclqmzl,data.clqmzl);
      let b = sub(data.zshcggzl,data.cggzl);
      this.batchForm[this.activeName][this.activeInfoName][index].slzl = a;
      this.batchForm[this.activeName][this.activeInfoName][index].zshsyzl = b;
      
      //2次结果之和除以2，保留2位小数，四舍五入
      let arr = [
        {
          v: a
        },{
          k: '-',
          v: b,
        },{
          k: '/',
          v: a,
        },{
          k: '*',
          v: 100,
        }
      ]
      let res = calcEquation(arr, 2)
      this.batchForm[this.activeName][this.activeInfoName][index].ssl = res;
      this.setSSLPJZ();
    },
    setSSLPJZ(){
      const oarr = this.batchForm[this.activeName][this.activeInfoName];
      //2次结果之和除以2，保留2位小数，四舍五入
      let arr = [
        {
          v: oarr[0].ssl,
          k: '+'
        },{
          v: oarr[1].ssl,
          k: '+'
        },{
          v: 2,
          k: '/'
        }
      ]
      let pjz = calcEquation(arr, 1)
      this.batchForm[this.activeName].sslpjz = pjz;
      // this.conclusion('dxjl', pjz, conclusion_FMH_SSL);
    },
    // 含水量
    setHSL(){
      const data = this.batchForm[this.activeName];
      let hq = sub(data.slclqmzl, data.clqmzl)
      let hh = sub(data.hghclqmzl, data.clqmzl)
      
      //粉煤灰需水量/水泥需水量 % 100，保留2位小数，四舍五入，不可编辑
      let arr = [
        {
          v: hq,
        },{
          k: '-',
          v: hh,
        },{
          k: '/',
          v: hq,
        },{
          k: '*',
          v: 100,
        }
      ]
      let res = calcEquation(arr, 1)
      this.batchForm[this.activeName].hsl = res;
      this.batchForm[this.activeName].cyzl = hq;
      this.batchForm[this.activeName].hghsyzl = hh;
      this.batchForm[this.activeName].dxjl = this.conclusion(res);
    },
    
    
    //比表面积
    //试样质量=密度*试料层体积*（1-空隙率）
    setBbmjAll(){
      this.setSYZL()
      this.setBBMJ(0)
      this.setBBMJ(1)
    },
    setSYZL(){
      const data = this.batchForm[this.activeName];
      //密度：只读，数字类型，数据来源于水泥密度试验
      
      let arr = [
        {
          v: data.md || 1,
        },{
          k: '*',
          v: data.slctj,
        },{
          k: '*',
          v: sub(1, data.slckxl),
        }
      ]
      let res = calcEquation(arr, 2)
      this.batchForm[this.activeName].syzl = res;
    },
    setBBMJ(index){
      const data = this.batchForm[this.activeName].bbdycsy[index];
      const obj = this.batchForm[this.activeName];
      // 1、当标准样品的试验温度与被测试样温度之差绝对值小于等于3，且空隙率、密度相同的情况下，计算公式如下
      // 2、当空隙率、密度相同，温度之差绝对值大于3
      // 3、密度相同，被测空隙率与标准空隙率不同，且温度之差小于等于3
      // 4、密度相同，被测空隙率与标准空隙率不同，且温度之差大于3
      // 5、如果密度、空隙率不同，且温度之差小于等于3
      // 6、如果密度、空隙率不同，且温度之差大于3
      let mdEqu = parseFloat(data.bzmd) == parseFloat(obj.md);
      let kxlEqu = parseFloat(data.bzkxl) == parseFloat(obj.slckxl);
      let wdc = Math.abs(data.bzsywd *1 - data.bcjywd *1);
      console.log(mdEqu, kxlEqu, data.bzmd,obj.md, data.bzkxl, obj.slckxl)
      
      let arr = [];
      if(wdc <= 3){
        if(mdEqu && kxlEqu){//1
          arr = [
            {
              v: data.bzbbmj,
            },{
              k: '*',
              v: Math.sqrt(data.bcjlsj),
            },{
              k: '/',
              v: Math.sqrt(data.bzjlsj),
            }
          ]
        }else if(mdEqu && !kxlEqu){//3
          //data.bzbbmj*zdata.bcjlsj*(1-data.bzkxl)*zMath.pow(obj.slckxl, 3)/zdata.bzjlsj/(1-obj.slckxl)/zMath.pow(data.bzkxl, 3)
          arr = [
            {
              v: data.bzbbmj,
            },{
              k: '*',
              v: Math.sqrt(data.bcjlsj),
            },{
              k: '*',
              v: (1-data.bzkxl),
            },{
              k: '*',
              v: Math.sqrt(Math.pow(obj.slckxl, 3)),
            },{
              k: '/',
              v: Math.sqrt(data.bzjlsj),
            },{
              k: '/',
              v: (1-obj.slckxl),
            },{
              k: '/',
              v: Math.sqrt(Math.pow(data.bzkxl, 3)),
            }
          ]
        }else if(!mdEqu && !kxlEqu){//5
          //data.bzbbmj*data.bzmd*zdata.bcjlsj*(1-data.bzkxl)*zMath.pow(obj.slckxl, 3)/obj.md/zdata.bzjlsj/(1-obj.slckxl)/zMath.pow(data.bzkxl, 3)
          arr = [
            {
              v: data.bzbbmj,
            },{
              k: '*',
              v: data.bzmd,
            },{
              k: '*',
              v: Math.sqrt(data.bcjlsj),
            },{
              k: '*',
              v: (1-data.bzkxl),
            },{
              k: '*',
              v: Math.sqrt(Math.pow(obj.slckxl, 3)),
            },{
              k: '/',
              v: obj.md,
            },{
              k: '/',
              v: Math.sqrt(data.bzjlsj),
            },{
              k: '/',
              v: (1-obj.slckxl),
            },{
              k: '/',
              v: Math.sqrt(Math.pow(data.bzkxl, 3)),
            }
          ]
        }else{
          this.$message.warning('参数有误，请确认参数！')
        }
      }else{
        if(mdEqu && kxlEqu){//2
          //data.bzbbmj*zdata.bzkqnd*zdata.bcjlsj/zdata.bckqnd/zdata.bzjlsj
          arr = [
            {
              v: data.bzbbmj,
            },{
              k: '*',
              v: Math.sqrt(data.bzkqnd),
            },{
              k: '*',
              v: Math.sqrt(data.bcjlsj),
            },{
              k: '/',
              v: Math.sqrt(data.bckqnd),
            },{
              k: '/',
              v: Math.sqrt(data.bzjlsj),
            }
          ]
        }else if(mdEqu && !kxlEqu){//4
          //data.bzbbmj*zdata.bzkqnd*zdata.bcjlsj*(1-data.bzkxl)*zMath.pow(obj.slckxl, 3)/zdata.bckqnd/zdata.bzjlsj/(1-obj.slckxl)/zMath.pow(data.bzkxl, 3)
          arr = [
            {
              v: data.bzbbmj,
            },{
              k: '*',
              v: Math.sqrt(data.bzkqnd),
            },{
              k: '*',
              v: Math.sqrt(data.bcjlsj),
            },{
              k: '*',
              v: (1-data.bzkxl),
            },{
              k: '*',
              v: Math.sqrt(Math.pow(obj.slckxl, 3)),
            },{
              k: '/',
              v: Math.sqrt(data.bckqnd),
            },{
              k: '/',
              v: Math.sqrt(data.bzjlsj),
            },{
              k: '/',
              v: (1-obj.slckxl),
            },{
              k: '/',
              v: Math.sqrt(Math.pow(data.bzkxl, 3)),
            }
          ]
        }else if(!mdEqu && !kxlEqu){//6
          //data.bzbbmj*data.bzmd*zdata.bzkqnd*zdata.bcjlsj*(1-data.bzkxl)*zMath.pow(obj.slckxl, 3)/obj.md/zdata.bckqnd/zdata.bzjlsj/(1-obj.slckxl)/zMath.pow(data.bzkxl, 3)
          arr = [
            {
              v: data.bzbbmj,
            },{
              k: '*',
              v: data.bzmd,
            },{
              k: '*',
              v: Math.sqrt(data.bzkqnd),
            },{
              k: '*',
              v: Math.sqrt(data.bcjlsj),
            },{
              k: '*',
              v: (1-data.bzkxl),
            },{
              k: '*',
              v: Math.sqrt(Math.pow(obj.slckxl, 3)),
            },{
              k: '/',
              v: obj.md,
            },{
              k: '/',
              v: Math.sqrt(data.bckqnd),
            },{
              k: '/',
              v: Math.sqrt(data.bzjlsj),
            },{
              k: '/',
              v: (1-obj.slckxl),
            },{
              k: '/',
              v: Math.sqrt(Math.pow(data.bzkxl, 3)),
            }
          ]
        }else{
          this.$message.warning('参数有误，请确认参数！')
        }
      }
       
      let res = calcEquation(arr, 2)
      this.batchForm[this.activeName].bbdycsy[index].bcbbmj = res;
      //注意：：2次相差2%，无法完成试验，提示：水泥比表面积异常！
      this.setBbmjPJZ();
    },
    setBbmjPJZ(){
      const data = this.batchForm[this.activeName].bbdycsy;
      
      let res = this.getPJZUtil(data, 'bcbbmj', 2);
      this.batchForm[this.activeName].pjz = res;
      this.batchForm[this.activeName].dxjl = this.conclusion(res);
    },

    setNewBBMJ(index, type){
      // bzypmd": "标准样品密度",
      // "bzypkxl": "标准样品空隙率",
      // "bzypymjlsj": "标准样品页面降落时间",
      // "bzypbbmj": "标准样品比表面积",
      // "bcypmd": "被测样品密度",
      // "bcypkxl": "被测样品空隙率",
      // "bcypymjlsj": "被测样品页面降落时间",

      // 当以上字段都有值的时候开始计算 bcypbbmj
      const data = this.batchForm[this.activeName].bbmjsy[index];
      if(data.bzypmd && data.bzypkxl && data.bzypymjlsj && data.bzypbbmj && data.bcypmd && data.bcypkxl && data.bcypymjlsj){
        // mol = SsPs\sqrt {T}(1-Es)\sqrt {{E}^{3}}
        // deno = P\sqrt {Ts}(1-E)\sqrt {{Es}^{3}}

        // Ss = bzypbbmj
        // Ps = bzypmd
        // Ts = bzypymjlsj
        // Es = bzypkxl
        // E = bcypkxl
        // T = bcypymjlsj
        // P = bcypmd
        let mol = (data.bzypbbmj * data.bzypmd * Math.sqrt(data.bcypymjlsj) * (1 - data.bzypkxl) * Math.sqrt(Math.pow(data.bcypkxl, 3)))
        let deno = (data.bcypmd * Math.sqrt(data.bzypymjlsj) * (1 - data.bcypkxl) * Math.sqrt(Math.pow(data.bzypkxl, 3)))
        
        let arr = [
          {
            v: mol,
          },{
            k: '/',
            v: deno,
          }
        ]
        let res = calcEquation(arr, 0)
        this.batchForm[this.activeName].bbmjsy[index].bcypbbmj = res;
      } else {
        this.batchForm[this.activeName].bbmjsy[index].bcypbbmj = '';
      }

      // 当第一次试验和第二次试验的 bcypbbmj 都有值的时候则计算平均值
      if(this.batchForm[this.activeName].bbmjsy[0].bcypbbmj && this.batchForm[this.activeName].bbmjsy[1].bcypbbmj){
        let arr = [
          {
            v: this.batchForm[this.activeName].bbmjsy[0].bcypbbmj,
          },{
            k: '+',
            v: this.batchForm[this.activeName].bbmjsy[1].bcypbbmj,
          },{
            k: '/',
            v: 2,
          }
        ]
        let res = calcEquation(arr, 0)
        this.batchForm[this.activeName].pjz = res;
        this.batchForm[this.activeName].dxjl = this.conclusion(res);
      }else{
        this.batchForm[this.activeName].pjz = '';
        this.batchForm[this.activeName].dxjl = '';
      }
    },
    
    
    
    //图片
    handlePicSuccess(response, file, fileList) {
      console.log(fileList)
      let oUploadImg = fileList.map(item =>{
        if(item.response){
          return {
            url: item.response.data.filePath,
            uid: item.uid
          }
        }else{
          return item;
          // if(item.url.startsWith(this.filePrefix)){
          //   return item.url;
          // }else{
          //   return this.filePrefix + item.url;
          // }
        }
      })
      
      if(this.activeName === 'quick'){
        this.quickForm.img = oUploadImg;
      }else{
        this.batchForm[this.activeName].img = oUploadImg;
      }
    },
    handlePicRemove(file, fileList) {
      let oUploadImg = fileList.map(item =>{
        if(item.response){
          return {
            url: item.response.data.filePath,
            uid: item.uid
          }
        }else{
          return item
        }
      })
      if(this.activeName === 'quick'){
        this.quickForm.img = oUploadImg;
      }else{
        this.batchForm[this.activeName].img = oUploadImg;
      }
    },
    
    
    //获取所有快检或批检试验项目名称
    async setExperimentProject(id){
      //获取抗压 抗渗 和其它
      const resDetail = await this.$api.getExperimentDetail({
        experimentId: this.activeId,
        // "testProjectCode":"FINE_AGGREGATE",
        // "checkType": 2
      }, this)
      if(resDetail.succ){
        let quickData = [];
        let batchData = [];//批检
        let quickForm = {};
        quickForm.img = [];
        let batchForm = {};
        resDetail.data.list.forEach(item => {
          let oImgArr = [];
          if(item.objImg && item.objImg != 'null'){
            oImgArr = item.objImg.split(',').map(item => {
              if(item.startsWith(this.filePrefix)){
                return {
                  url: item
                }
              }else{
                return {
                  url: this.filePrefix + item
                }
              }
            }) 
          }else{
            oImgArr = [];
          }
          
          if(item.testProjectName === '表观密度' || item.testProjectName === '目测含泥量'
            || item.testProjectName === '目测泥块含量'
          ){
            
            quickData.push(item)
            quickForm[item.testProjectCode] = item;
            quickForm.img = quickForm.img.concat(oImgArr);
          }else{
            if(!item.objJson?.jcrq){
              item.objJson.jcrq = this.defaultDate;
            }
            if (item.testProjectCode === 'SLAG_PARAM_KZF_HXZS') {
              item.objJson.jcrq = undefined;
            }
            item.objJson.img = oImgArr;
            batchForm[item.testProjectCode] = JSON.parse(JSON.stringify(item.objJson));
            item.objJson = undefined;
            batchData.push(item);
          }
        })
        this.quickData = quickData;
        this.quickForm = quickForm;
        this.batchData = batchData;
        this.batchForm = this.setDefaultVal(batchForm);
        
        console.log(this.batchForm,this.batchData)
        if(this.quickData.length > 0){
          this.activeName = 'quick'
        }else{
          this.activeName = this.batchData[0].testProjectCode;
          this.getAccordingToHand()
        }
        
      }  
    },
    getAccordingToHand(){
      this.$api.getAccordingTo({
        testProjectCode: this.activeName,
        materialAbbreviation : this.$parent.activeData.materialAbbreviation,
        materialsName	 : this.$parent.activeData.materialsName,
        materialsSpec : this.$parent.activeData.materialsSpecs
      }, this).then(res =>{
        if(res.data.list.length > 0){
          let o = res.data.list[0].objJson;
          this.accordingObj[this.activeName] = JSON.parse(o);
        }else{
          this.accordingObj[this.activeName] = {};
        }
      })
    },
    //设置默认值
    setDefaultVal(val){
      
      this.setDefaultTime(val);
      if(val.SLAG_PARAM_KZF_HXZS?.hxzs7dInfo){
        val.SLAG_PARAM_KZF_HXZS?.hxzs7dInfo?.syjskysy.forEach(item =>{
          if(item.symj == ''){
            item.symj = '1600'
          }
        })
      }
      if(val.SLAG_PARAM_KZF_HXZS?.hxzs28dInfo){
        val.SLAG_PARAM_KZF_HXZS?.hxzs28dInfo?.syjskysy.forEach(item =>{
          if(item.symj == ''){
            item.symj = '1600'
          }
        })
      }
      
      if(val.SLAG_PARAM_KZF_YLYHG?.syzl == ''){
        val.SLAG_PARAM_KZF_YLYHG.syzl = '0.05'
      }
      
      if(val.SLAG_PARAM_KZF_SYHL?.syhlList){
        val.SLAG_PARAM_KZF_SYHL.syhlList.forEach(item =>{
          if(item.hsxs == ''){
            item.hsxs = '0.343'
          }
        })
      }
      
      if(val.SLAG_PARAM_KZF_SYHL?.syzl == ''){
        val.SLAG_PARAM_KZF_SYHL.syzl = '0.05'
      }
      
      if(val.SLAG_PARAM_KZF_LDDB?.xglyl == ''){
        val.SLAG_PARAM_KZF_LDDB.xglyl = '1350'
      }
      if(val.SLAG_PARAM_KZF_LDDB?.syl == ''){
        val.SLAG_PARAM_KZF_LDDB.syl = '225'
      }
      
      if(val.SLAG_PARAM_KZF_BBMJ?.slckxl == ''){
        if(this.sampleLevel == 'PI/PII2'){
          val.SLAG_PARAM_KZF_BBMJ.slckxl = '0.5'
        }else{
          val.SLAG_PARAM_KZF_BBMJ.slckxl = '0.53'
        }
      }
      return val;
    },
    handleClick(tab, event){
      this.getAccordingToHand()
      switch (this.activeName){
        case 'SLAG_PARAM_KZF_YPMD':
          this.activeInfoName = 'ypmdInfo'
          break;
        case 'SLAG_PARAM_KZF_SSL':
          this.activeInfoName = 'sslInfo'
          break;
        default:
          break;
      }
    },
    clearData(){
      this.activeName= 'quick';
      this.quickForm= {};
      this.quickData= [];
      this.batchForm= {};
      this.batchData= [];
    },
    handlePreview(file) {
      this.previewUrl = file.url
      this.imgViewerVisible = true;
    },
    onClose() {
      this.imgViewerVisible = false;
    },
  },
};
</script>
<style scoped lang="scss">
  .line{
    height: 1px;
    width: 100%;
    background: #E8E8E8;
    margin: 24px 0;
  }
  .info-form-title{
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 14px;
    color: #1F2329;
    line-height: 20px;
    text-align: left;
    font-style: normal;
    padding: 10px 0 4px;
    margin-bottom: 4px;
  }
  .info-form-piece{
    background: #F8F8F8;
    border-radius: 8px;
    padding: 16px 16px 8px 16px;
  }
  ::v-deep .custom-form .el-form .el-form-item{
    margin-bottom: 8px;
    margin-right: 10px;
    .el-form-item__content{
      flex: 1;
    }
  }
  ::v-deep .el-input-group__append, 
  ::v-deep .el-input-group__prepend{
    padding: 0 5px;
    text-align: center;
    width: 40px;
  }
  ::v-deep .pr0{
    .el-input__inner{
      padding-right: 0;
    }
  }
  ::v-deep .el-input-number.is-controls-right .el-input__inner{
    text-align: left;
  }
  ::v-deep .textspan .el-input__inner{
    background: transparent;
    border: none;
    padding: 0;
    color: #000;
    margin-left: -10px;
    margin-top: -2px;
  }
  
  
  ::v-deep .el-upload-list__item{
    transition: none !important; 
  }
  
  
  .el-row{
    margin-bottom: 24px;
    &:last-child{
      margin-bottom: 0;
    }
  }
  .mb16{
    margin-bottom: 16px;
  }
  .mt24{
    margin-top: 24px;
  }
  .el-tabs{
    height: 40px;
  }
  
  
  
  
  .table-box-kzf{
    width: 100%;
    background: #FFFFFF;
    border-radius: 4px;
    border: 1px solid #DDDFE6;
    
    .tb-title{
      height: 52px;
      background: #F8F8F8;
      border-radius: 4px 4px 0px 0px;
      border-bottom: 1px solid #DDDFE6;
      line-height: 52px;
      padding: 0 16px;
      font-weight: 600;
      font-size: 14px;
      color: #1F2329;
    }
    .tb-bottom{
      border-top: 1px solid #DDDFE6;
      padding: 16px;
    }
    .tb-left{
      line-height: 40px;
      border-right: 1px solid #DDDFE6;
      width: 72px;
      text-align: center;
    }
    .tb-left2{
      width: 118px;
      padding: 24px 0;
    }
    .tb-content{
      .tb-left,.el-form-item{
        padding-bottom: 8px;
      }
      .el-form-item{
        padding-left: 16px;
      }
      .tb-item:first-child{
        .tb-left,.el-form-item{
          padding-top: 24px;
        }
      }
      .tb-item:last-child{
        .tb-left,.el-form-item{
          padding-bottom: 24px;
        }
      }
    }
    .tb-center{
      padding: 24px 0;
      margin: 0 8px;
      &>div:first-child{
        margin-bottom: 8px;
      }
    }
    .tb-right{
      height: 88px;
      margin-top: 24px;
      border-left: 1px solid #DDDFE6;
      line-height: 40px;
      padding: 0 16px;
      &>p:first-child{
        margin-bottom: 8px;
      }
    }
  }
  
  
  .tag-btn{
    display: inline-block;
    cursor: pointer;
    height: 32px;
    border: 1px solid #F8F8F8;
    line-height: 30px;
    padding: 0 8px;
    background: #F8F8F8;
    border-radius: 4px;
    margin-right: 12px;
    font-weight: 500;
    box-sizing: border-box;
    &.tag-btn-succ{
      background: #BBFADA;
      color: #13D466;
      border: 1px solid #13D466;
    }
    &.tag-btn-warn{
      background: rgba(255,206,80,0.26);
      border: 1px solid #FCCB83;
      color: #FFB803;
    }
    &.tag-btn-error{
      background: rgba(255, 95, 88, 0.26);
      border: 1px solid #FF5F58;
      color: #FF5F58;
    }
    &.tag-btn-primary{
      background: rgba(31, 87, 179, .1);
      color: #1F57B3;
      border: 1px dashed rgba(31, 87, 179, .1);
    }
    &.tag-btn-disabled{
      color: rgba(153,153,153,0.85);
    }
    &.tag-btn-dotted{
      padding: 0 13px;
      border-style: dashed;
    }
    &.tag-btn-mini{
      font-size: 16px;
      height: 28px;
      line-height: 26px;
    }
    &:last-child{
      margin: 0;
    }
  }
</style>