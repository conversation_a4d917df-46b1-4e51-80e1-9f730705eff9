.el-button {
  padding-left: 13px;
  padding-right: 13px;
}

.el-tabs {
  margin-top: 10px;
}

.search-box {
  padding: 16px;
  width: 100%;
  background-color: #fff;
}

.content {
  width: 100%;
  height: 100%;
  padding: 14px 0 16px;

  .status-view {
    background-color: white;
    margin: 0 16px;
    padding: 12px;
    border-top-left-radius: 16px;
    border-top-right-radius: 16px;
    font-size: 14px;
    justify-content: flex-start;
  }

  .con-lef,
  .con-right {
    width: 360px;
    background: #D2D8DB;
    // border-radius: 16px;
    border-bottom-left-radius: 16px;
    border-bottom-right-radius: 16px;
    padding: 8px 8px 0;
    padding-bottom: 180px;
    margin: 0 16px;
    height: calc(100vh - 180px);
    overflow-y: scroll;
    scrollbar-width: none;
    /* firefox */
    -ms-overflow-style: none;
    /* IE 10+ */
    position: relative;
  }

  .con-center {
    background: #D2D8DB;
    border-radius: 16px;
    padding: 8px;
    height: calc(100vh - 180px);
    scrollbar-width: none;
    /* firefox */
    -ms-overflow-style: none;
    /* IE 10+ */
    position: relative;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    flex: 1; /* 确保占据剩余空间 */
    min-width: 0; /* 防止内容溢出 */
    
    .center-content {
      flex: 1;
      overflow-y: scroll;
      padding-bottom: 20px;
      scrollbar-width: none;
      /* firefox */
      -ms-overflow-style: none;
      /* IE 10+ */
      
      &::-webkit-scrollbar {
        display: none;
        /* Chrome Safari */
      }
    }
  }
}

::-webkit-scrollbar {
  display: none;
  /* Chrome Safari */
}


.g-card {
  width: 100%;
  background: #FFFFFF;
  border-radius: 8px;
  padding: 8px;
  margin-bottom: 8px;
  cursor: pointer;

  &.active {
    background: #DDEFEA;
  }

  p {
    color: $color-txt;
    line-height: 20px;
    letter-spacing: 1px;
    padding-bottom: 4px;

    &:last-child {
      padding: 0;
    }
  }

  .gc-main {
    font-size: 14px;
    font-weight: 600;
    color: #1F2329;
    line-height: 20px;
    letter-spacing: 1px;

    span {
      line-height: 20px;
      height: 20px;
      background: #FFE9D1;
      border-radius: 10px;
      font-size: 12px;
      font-weight: 600;
      color: #FF7B2F;
      background: #FFE9D1;
      padding: 0 7px;

      &.succ {
        color: $color-success;
        background: #DDEFEA;
      }
    }
  }

  .span-label {
    font-size: 14px;
    font-weight: 600;
    color: #1F2329;
    line-height: 20px;
    letter-spacing: 1px;
  }
}



.cc-box {
  width: 100%;
  background: #FFFFFF;
  border-radius: 8px;
  padding: 16px 16px 12px;
  margin-bottom: 8px;

  &:last-child {
    margin-bottom: 0;
  }

  h4 {
    font-size: 16px;
    font-weight: 600;
    color: #1F2329;
    line-height: 22px;
    letter-spacing: 1px;
    padding-bottom: 24px;
  }

  .cc-info {
    margin: 0 16px 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #E8E8E8;

    &:last-child {
      margin-bottom: 0;
      border: none;
      padding: 0;
    }

    p {
      color: #6A727D;
      line-height: 20px;
      letter-spacing: 1px;
      padding-bottom: 4px;

      span {
        margin-right: 60px;
      }
    }

    .cci-main {
      font-weight: 600;
      line-height: 20px;
      letter-spacing: 1px;
      color: #1F2329;
    }
  }
}

.pb16 {
  padding-bottom: 16px !important;
}


.center-footer {
  position: sticky;
  bottom: 0px;
  /* 距离视口底部8px */
  margin: 0px;
  /* 左右边距8px，顶部边距8px */
  background: #FFFFFF;
  border-radius: 8px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 16px 20px;
  z-index: 100;
  /* 确保悬浮在内容之上 */
  flex-shrink: 0;
  /* 防止被压缩 */

  .el-button {
    width: 98px;
    margin-left: 16px;

    &:first-child {
      margin-left: 0;
    }
  }
}