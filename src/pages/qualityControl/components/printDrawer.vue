<template>
    <el-drawer
        title="选择打印页面"
        :visible.sync="showFlag"
        direction="rtl"
        :before-close="handleClose"
    >
        <div v-if="showFlag">
            <div  v-for="group in printOptions" :key="group.gid" style="padding: 20px; box-sizing: border-box;">
                <div class="flex-row">
                    <span style="font-weight: 500;font-size: 14px;color: #2A264F;">{{ group.label }}：</span>
                    <el-checkbox v-model="group.checkAll" @change="handleCheckAllChange(group)">
                        全选
                    </el-checkbox>
                </div>
                <el-checkbox-group v-model="printChecked" style="margin-top: 10px;" @change="handleCheckedChange">
                    <el-checkbox v-for="item in group.group" :label="item.id" :key="item.id" style="margin-top: 8px;">{{item.label}}</el-checkbox>
                </el-checkbox-group>
            </div>

            <div style="text-align: right; margin-top: 20px; margin-right: 40px;">
                <el-button type="primary" @click="handleSure">确定</el-button>
            </div>
        </div>
    </el-drawer>
</template>

<script>
const printOptionsOrg = [
    {
        gid: "b",
        label: '原材料',
        checkAll: false,
        isIndeterminate: false,
        group: [
            {
                id: "b1",
                label: '水泥试验报告'
            },
            {
                id: "b2",
                label: '矿渣粉试验报告'
            },
            {
                id: "b3",
                label: '粉煤灰试验报告'
            },
            {
                id: "b4",
                label: '细骨料试验报告'
            },
            {
                id: "b5",
                label: '粗骨料试验报告'
            },
            {
                id: "b6",
                label: '外加剂检测报告'
            }
        ]
    },
    {
        gid: "a",
        label: '混凝土报告',
        checkAll: false,
        isIndeterminate: false,
        group: [
            {
                id: "a3N",
                label: '混凝土抗压报告'
            },
            {
                id: "a4",
                label: '混凝土抗渗报告'
            },
            {
                id: "a5",
                label: '混凝土抗折报告'
            },
            // {
            //     id: "a7",
            //     label: '混凝土综合性能报告'
            // },
            {
                id: "a11",
                label: '混凝土氯离子报告'
            },
            {
                id: "a12",
                label: '综合性能报告'
            },
        ],
    },
    {
        gid: "c",
        label: '交工资料',
        checkAll: false,
        isIndeterminate: false,
        group: [
            {
                id: "a1",
                label: '预拌混凝土出厂质量证明书'
            },
            {
                id: "a1N",
                label: '预拌混凝土出厂质量证明书(新)'
            },
            {
                id: "a2",
                label: '开盘鉴定表'
            },
            {
                id: "a8",
                label: '配合比设计报告'
            },
            {
                id: "a9",
                label: '配合比调整通知'
            }
        ]
    },
    // {
    //     gid: "d",
    //     label: '其他',
    //     group: [
    //         {
    //             id: "d1",
    //             label: '强度评定表'
    //         },
    //     ]
    // }
];
export default {
    props: {
        show: {
            type: Boolean,
            default: false
        },
    },
    data() {
        return {
            printOptions: [],
            printChecked: [],
            showFlag: false,
        }
    },

    watch: {
        show(val) {
            this.showFlag = val;
            if (this.showFlag) {
                this.printOptions = JSON.parse(JSON.stringify(printOptionsOrg));
            }else{
                this.printChecked = [];
                this.$emit('close', false);
                this.showFlag = false;
            }
        }
    },

    methods: {
        handleClose(done) {
            this.$confirm('确认关闭？')
            .then(_ => {
                if(done){
                    done();
                }
                this.printChecked = [];
                this.$emit('close', false);
                this.showFlag = false;

            })
            .catch(_ => {});
        },
        handleSure() {
            this.$emit('sure', this.printChecked);
        },

        handleCheckedChange(item) {
            this.printChecked = item;
            this.$emit('change', this.printChecked);
        },

        removeCheckItem(item) {
            let index = this.printChecked.indexOf(item);
            if (index > -1) {
                this.printChecked.splice(index, 1);
            }
        },

        handleCheckAllChange(item) {
            if (item.checkAll) {
                item.group.forEach(item => {
                    if (this.printChecked.indexOf(item.id) === -1) {
                        this.printChecked.push(item.id);
                    }
                });
            } else {
                item.group.forEach(item => {
                    let index = this.printChecked.indexOf(item.id);
                    if (index > -1) {
                        this.printChecked.splice(index, 1);
                    }
                });
            }

            this.$emit('change', this.printChecked);
        }
    }
}
</script>

<style lang="scss" scoped>

</style>