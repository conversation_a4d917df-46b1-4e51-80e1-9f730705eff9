<template>
  <div>
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane v-if="quickData.length > 0" label="快检" name="quick"></el-tab-pane>
      <template v-if="batchData.length > 0">
        <el-tab-pane v-for="(item, index) in batchData" :key="item.testProjectCode" :label="item.testProjectName" :name="item.testProjectCode">
        </el-tab-pane>
      </template>
    </el-tabs>
    <div v-show="activeName === 'quick'">
      <el-form ref="quickForm"  :model="quickForm" :disabled="loading2 || experimentStatus == 3" label-width="120px">
        
        <el-row>
          <el-col :span="24" style="margin-bottom: 8px;">
            <el-form-item :label="item.testProjectName" v-for="item in quickData" :key="item.testProjectCode">
              <template v-if="item.testProjectName === '目测含水率'"
              >
                <el-input
                  v-manual-update
                  @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  v-model="quickForm[item.testProjectCode].objJson.muhsl" 
                  clearable
                  :placeholder="item.testProjectName"
                  style="width: 253px; margin-left: 14px;"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
              <template v-if="item.testProjectName === '目测含泥量'"
              >
                <el-input
                  v-manual-update
                  @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  v-model="quickForm[item.testProjectCode].objJson.muhnl" 
                  clearable
                  :placeholder="item.testProjectName"
                  style="width: 253px; margin-left: 14px;"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
              <template v-if="item.testProjectName === '目测泥块含量'"
              >
                <el-input
                  v-manual-update
                  @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  v-model="quickForm[item.testProjectCode].objJson.muhnkl" 
                  clearable
                  :placeholder="item.testProjectName"
                  style="width: 253px; margin-left: 14px;"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="图片：">
              <div class="cbo">
                <div class="img-box">
                  <el-upload
                    accept=".jpeg,.png,.jpg,.bmp,.gif"
                    :action="baseUrl + '/upload/file'"
                    list-type="picture-card"
                    :on-preview="handlePreview"
                    :file-list="quickForm.img"
                    :on-success="handlePicSuccess"
                    :on-remove="handlePicRemove">
                    <i class="el-icon-plus"></i>
                  </el-upload>
                  <!-- <span>抽样图片</span> -->
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    
    <!-- 含泥量1 -->
    <div v-if="activeName === 'FINE_AGGREGATE_PARAM_XGL_HNL'">
      <el-form :disabled="loading2 || experimentStatus == 3" :inline="true">
        
        <el-row class="mt16">
          <el-col :span="24">
            <el-form-item label="检测日期：">
              <el-date-picker
                type="date"
                v-model="batchForm[activeName].jcrq"
                placeholder="选择日期"
                format="yyyy-MM-dd"
                :default-value="defaultDate"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row class="mt16" v-if="materialsName !== '天然砂'">
          <!-- <el-col :span="6">
            <el-form-item label="试样质量：">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="batchForm[activeName].syjgInfo.syzl" 
                clearable
                placeholder="请输入"
                :style="{'width': 136 + 'px'}"
              >
                <template slot="append">g</template>
              </el-input>
            </el-form-item>
          </el-col> -->
          <el-col :span="8">
            <el-form-item label="溶液加入总量：">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="batchForm[activeName].jryjlryzl" 
                disabled
              >
                <template slot="append">ml</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <!-- <el-form-item label="亚甲蓝值：">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="batchForm[activeName].yjlz" 
                disabled
              >
                <template slot="append">g</template>
              </el-input>
            </el-form-item> -->
            <el-form-item label="试验质量：">
              <el-input
                v-manual-update
            
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="batchForm[activeName].syjgInfo.syzl" 
                disabled
              >
                <template slot="append">g</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item v-if="batchForm[activeName].syjgInfo" label="溶液加入量（ml）：" label-width="200px" style="padding: 5px 0;">
              <el-input
                v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                @input="mbTJchange"
                v-model="batchForm[activeName].syjgInfo.jryjlrytj1" 
                placeholder="请输入"
                :style="{'width': 80 + 'px', 'margin-right': '15px'}"
              >
              </el-input>
              <el-input
                v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @input="mbTJchange"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="batchForm[activeName].syjgInfo.jryjlrytj2" 
                placeholder="请输入"
                :style="{'width': 80 + 'px', 'margin-right': '15px'}"
              >
              </el-input>
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @input="mbTJchange"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="batchForm[activeName].syjgInfo.jryjlrytj3" 
                placeholder="请输入"
                :style="{'width': 80 + 'px', 'margin-right': '15px'}"
              >
              </el-input>
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @input="mbTJchange"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="batchForm[activeName].syjgInfo.jryjlrytj4" 
                placeholder="请输入"
                :style="{'width': 80 + 'px', 'margin-right': '15px'}"
              >
              </el-input>
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @input="mbTJchange"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="batchForm[activeName].syjgInfo.jryjlrytj5" 
                placeholder="请输入"
                :style="{'width': 80 + 'px', 'margin-right': '15px'}"
              >
              </el-input>
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @input="mbTJchange"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="batchForm[activeName].syjgInfo.jryjlrytj6" 
                placeholder="请输入"
                :style="{'width': 80 + 'px', 'margin-right': '15px'}"
              >
              </el-input>
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @input="mbTJchange"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="batchForm[activeName].syjgInfo.jryjlrytj7" 
                placeholder="请输入"
                :style="{'width': 80 + 'px', 'margin-right': '15px'}"
              >
              </el-input>
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @input="mbTJchange"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="batchForm[activeName].syjgInfo.jryjlrytj8" 
                placeholder="请输入"
                :style="{'width': 80 + 'px', 'margin-right': '15px'}"
              >
              </el-input>
            </el-form-item>
            
            <el-form-item v-if="batchForm[activeName].syjgInfo" label="色晕持续时间（min）：" label-width="200px">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="batchForm[activeName].syjgInfo.sycxsj1" 
                placeholder="请输入"
                :style="{'width': 80 + 'px', 'margin-right': '15px'}"
              >
              </el-input>
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="batchForm[activeName].syjgInfo.sycxsj2" 
                placeholder="请输入"
                :style="{'width': 80 + 'px', 'margin-right': '15px'}"
              >
              </el-input>
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="batchForm[activeName].syjgInfo.sycxsj3" 
                placeholder="请输入"
                :style="{'width': 80 + 'px', 'margin-right': '15px'}"
              >
              </el-input>
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="batchForm[activeName].syjgInfo.sycxsj4" 
                placeholder="请输入"
                :style="{'width': 80 + 'px', 'margin-right': '15px'}"
              >
              </el-input>
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="batchForm[activeName].syjgInfo.sycxsj5" 
                placeholder="请输入"
                :style="{'width': 80 + 'px', 'margin-right': '15px'}"
              >
              </el-input>
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="batchForm[activeName].syjgInfo.sycxsj6" 
                placeholder="请输入"
                :style="{'width': 80 + 'px', 'margin-right': '15px'}"
              >
              </el-input>
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="batchForm[activeName].syjgInfo.sycxsj7" 
                placeholder="请输入"
                :style="{'width': 80 + 'px', 'margin-right': '15px'}"
              >
              </el-input>
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="batchForm[activeName].syjgInfo.sycxsj8" 
                placeholder="请输入"
                :style="{'width': 80 + 'px', 'margin-right': '15px'}"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col>
            <div class="line"></div>
          </el-col>
          <el-col :span="8">
            <el-form-item v-if="batchForm[activeName].syjgInfo" label="MB值：">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                v-model="batchForm[activeName].syjgInfo.mbz" 
                disabled
                placeholder="请输入"
                :style="{'width': 200 + 'px'}"
              >
                <template slot="append">mm</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item v-if="batchForm[activeName].syjgInfo" label="单项结论：">
              <el-input
                type="text"
                v-model="batchForm[activeName].syjgInfo.dxjl" 
                :disabled="dxjlDisabled"
                :style="{'width': 180 + 'px'}"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        
        <div v-for="(item, index) in batchForm[activeName].hnljzsInfo" :key="index">
          <p class="info-form-title mt16">{{index == 0 ? '第一次试验' : '第二次试验'}}</p>
          <div class="info-form-piece">
            <el-row>
              <el-col :span="6">
                <el-form-item label="烘前试样质量：">
                  <el-input
                    v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                    @input="val => setHnl(index)"
                    @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                    v-model="item.hqsyzl" 
                    clearable
                    placeholder="请输入"
                    :style="{'width': 120 + 'px'}"
                  >
                    <template slot="append">g</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="烘后试样质量：" :prop="activeName + activeInfoName + 'hhsyzl'">
                  <el-input
                    v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                    @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                    v-model="item.hhsyzl"
                    @input="val => setHnl(index)"
                    clearable
                    placeholder="请输入"
                    :style="{'width': 120 + 'px'}"
                  >
                    <template slot="append">g</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="泥块/石粉质量：">
                  <el-input
                    v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                    disabled
                    v-model="item.sfzl"
                    clearable
                    placeholder="请输入"
                    :style="{'width': 120 + 'px'}"
                  >
                    <template slot="append">g</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="泥块/石粉含量：">
                  <el-input
                    v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                    disabled
                    v-model="item.sfhl"
                    placeholder="请输入"
                    :style="{'width': 120 + 'px'}"
                  >
                  <template slot="append">%</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
              </el-col>
            </el-row>
          </div>
        </div>
        <div class="line"></div>
        <el-row>
          <el-col :span="8">
            <el-form-item label="平均值：">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                v-model="batchForm[activeName].hnlpjz" 
                disabled
                :style="{'width': 200 + 'px'}"
              >
                <template slot="append">%</template>
              </el-input>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="16">
            <el-form-item label="单项结论：">
              <el-input
                type="text"
                v-model="batchForm[activeName].dxjl" 
                :disabled="dxjlDisabled"
                :style="{'width': 180 + 'px'}"
              >
              </el-input>
            </el-form-item>
          </el-col> -->
        </el-row>
        <el-row class="mt16">
          <el-col :span="24">
            <el-form-item label="检测图片：">
              <div class="cbo">
                <div class="img-box">
                  <el-upload
                    accept=".jpeg,.png,.jpg,.bmp,.gif"
                    :action="baseUrl + '/upload/file'"
                    list-type="picture-card"
                    :on-preview="handlePreview"
                    :file-list="batchForm[activeName].img"
                    :on-success="handlePicSuccess"
                    :on-remove="handlePicRemove">
                    <i class="el-icon-plus"></i>
                  </el-upload>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    
    <!-- 泥块含量1-->
    <div v-if="activeName === 'FINE_AGGREGATE_PARAM_XGL_NKHL'">
      <el-form :disabled="loading2 || experimentStatus == 3">
        <div class="mt16 jcrq-box">
          <el-form-item label="检测日期：">
            <el-date-picker
              type="date"
              v-model="batchForm[activeName].jcrq"
              placeholder="选择日期"
              format="yyyy-MM-dd"
              :default-value="new Date()"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
        </div>
        <div v-for="(item, index) in batchForm[activeName][activeInfoName]" :key="index">
          <p class="info-form-title mt16">{{index == 0 ? '第一次试验' : '第二次试验'}}</p>
          <div class="info-form-piece">
            <el-row>
              <el-col :span="8">
                <el-form-item label="烘前试样质量：">
                  <el-input
                    v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                    @input="val => setHnl(index)"
                    @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                    v-model="item.hqsyzl" 
                    clearable
                    placeholder="请输入"
                    :style="{'width': 200 + 'px'}"
                  >
                    <template slot="append">g</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="烘后试样质量：" :prop="activeName + activeInfoName + 'hhsyzl'">
                  <el-input
                    v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                    @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                    v-model="item.hhsyzl"
                    @input="val => setHnl(index)"
                    clearable
                    placeholder="请输入"
                    :style="{'width': 200 + 'px'}"
                  >
                    <template slot="append">g</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="含泥量：">
                  <el-input
                    v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                    disabled
                    v-model="item.hnl"
                    clearable
                    placeholder="请输入"
                    :style="{'width': 200 + 'px'}"
                  >
                    <template slot="append">%</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>
        <div class="line"></div>
        <el-row>
          <el-col :span="8">
            <el-form-item label="平均值：">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                v-model="batchForm[activeName].pjz" 
                disabled
                :style="{'width': 200 + 'px'}"
              >
                <template slot="append">%</template>
              </el-input>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="16">
            <el-form-item label="单项结论：">
              <el-input
                type="text"
                v-model="batchForm[activeName].dxjl" 
                :disabled="dxjlDisabled"
                :style="{'width': 180 + 'px'}"
              >
              </el-input>
            </el-form-item>
          </el-col> -->
        </el-row>
        
        <el-row>
          <el-col :span="24">
            <el-form-item label="检测图片：">
              <div class="cbo">
                <div class="img-box">
                  <el-upload
                    accept=".jpeg,.png,.jpg,.bmp,.gif"
                    :action="baseUrl + '/upload/file'"
                    list-type="picture-card"
                    :on-preview="handlePreview"
                    :file-list="batchForm[activeName].img"
                    :on-success="handlePicSuccess"
                    :on-remove="handlePicRemove">
                    <i class="el-icon-plus"></i>
                  </el-upload>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    
    
    <!-- 石粉含量1 -->
    <div v-if="activeName === 'FINE_AGGREGATE_PARAM_XGL_SFHL'">
      <el-form :disabled="loading2 || experimentStatus == 3">
        <div class="mt16 jcrq-box">
          <el-form-item label="检测日期：">
            <el-date-picker
              type="date"
              v-model="batchForm[activeName].jcrq"
              placeholder="选择日期"
              format="yyyy-MM-dd"
              :default-value="new Date()"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
        </div>
        <div v-for="(item, index) in batchForm[activeName].sfhlInfo" :key="index">
          <p class="info-form-title mt16">第一次试验</p>
          <div class="info-form-piece">
            <el-row>
              <el-col :span="8">
                <el-form-item label="烘前试样质量：">
                  <el-input
                    v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                    v-model="item.hqsyzl" 
                    clearable
                    placeholder="请输入"
                    :style="{'width': 200 + 'px'}"
                  >
                    <template slot="append">g</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="烘后试样质量：">
                  <el-input
                    v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                    v-model="item.hhsyzl" 
                    clearable
                    placeholder="请输入"
                    :style="{'width': 200 + 'px'}"
                  >
                    <template slot="append">g</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="石粉质量：">
                  <el-input
                    v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                    v-model="item.sfzl" 
                    clearable
                    placeholder="请输入"
                    :style="{'width': 200 + 'px'}"
                  >
                    <template slot="append">g</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="石粉含量：">
                  <el-input
                    v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                    v-model="item.sfhl" 
                    clearable
                    placeholder="请输入"
                    :style="{'width': 200 + 'px'}"
                  >
                    <template slot="append">g</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="16">
                <el-form-item label="试验结果：">
                  <el-input
                    v-model="item.syjg" 
                    clearable
                    placeholder="请输入"
                    :style="{'width': 180 + 'px'}"
                  >
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>
        <div class="line"></div>
        <el-row>
          <el-col :span="8">
            <el-form-item label="平均值：">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                v-model="batchForm[activeName].pjz" 
                clearable
                placeholder="请输入"
                :style="{'width': 200 + 'px'}"
              >
                <template slot="append">mm</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item label="结论：">
              <el-input
                v-model="batchForm[activeName].dxjl" 
                clearable
                placeholder="请输入"
                :style="{'width': 180 + 'px'}"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row>
          <el-col :span="24">
            <el-form-item label="检测图片：">
              <div class="cbo">
                <div class="img-box">
                  <el-upload
                    accept=".jpeg,.png,.jpg,.bmp,.gif"
                    :action="baseUrl + '/upload/file'"
                    list-type="picture-card"
                    :on-preview="handlePreview"
                    :file-list="batchForm[activeName].img"
                    :on-success="handlePicSuccess"
                    :on-remove="handlePicRemove">
                    <i class="el-icon-plus"></i>
                  </el-upload>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
  
    <!-- 含水率1  完成-->
    <div v-if="activeName === 'FINE_AGGREGATE_PARAM_XGL_HSL'" class="custom-form">
      <el-form :disabled="loading2 || experimentStatus == 3" :inline="true" label-width="110px">
        <div class="mt16 jcrq-box">
          <el-form-item label="检测日期：">
            <el-date-picker
              type="date"
              v-model="batchForm[activeName].jcrq"
              placeholder="选择日期"
              format="yyyy-MM-dd"
              :default-value="new Date()"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
        </div>
        <div v-for="(item, index) in batchForm[activeName].hslInfo" :key="index">
          <p class="info-form-title mt16">{{index == 0 ? '第一次试验' : '第二次试验'}}</p>
          <div class="info-form-piece flex-box">
            <el-form-item label="烘前试样质量：" class="flex-item flex-box">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @input="val => setHSL(index)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="item.hqsyzl" 
                placeholder="请输入"
              >
                <template slot="append">g</template>
              </el-input>
            </el-form-item>
            <el-form-item class="flex-item flex-box" label="烘后试样质量：">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                @input="val => setHSL(index)"
                v-model="item.hhsyzl"
                placeholder="请输入"
              >
                <template slot="append">g</template>
              </el-input>
            </el-form-item>
            <el-form-item label="含水率：" class="flex-item flex-box">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                disabled
                v-model="item.hsl"
              >
                <template slot="append">%</template>
              </el-input>
            </el-form-item>
          </div>
        </div>
        <div class="line"></div>
        <el-row>
          <el-col :span="10">
            <el-form-item class="flex-box" label="平均值：">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                disabled
                v-model="batchForm[activeName].pjz" 
                :style="{'width': 200 + 'px'}"
              >
                <template slot="append">%</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="14">
            <el-form-item class="flex-box" label="单项结论：">
              <el-input
                :disabled="dxjlDisabled"
                v-model="batchForm[activeName].dxjl" 
                placeholder="请输入"
                :style="{'width': 180 + 'px'}"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row class="mt16">
          <el-col :span="24">
            <el-form-item label="检测图片：">
              <div class="cbo">
                <div class="img-box">
                  <el-upload
                    accept=".jpeg,.png,.jpg,.bmp,.gif"
                    :action="baseUrl + '/upload/file'"
                    list-type="picture-card"
                    :on-preview="handlePreview"
                    :file-list="batchForm[activeName].img"
                    :on-success="handlePicSuccess"
                    :on-remove="handlePicRemove">
                    <i class="el-icon-plus"></i>
                  </el-upload>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
  
    <!-- 表观密度1 FINE_AGGREGATE_PARAM_XGL_BGMD-->
    <div v-if="activeName === 'FINE_AGGREGATE_PARAM_XGL_BGMD'" class="custom-form">
      <el-form :disabled="loading2 || experimentStatus == 3" :inline="true" label-width="200px">
        
        <div class="mt16 flex-box">
          <el-form-item label="检测日期：" class="flex-item flex-box" label-width="100px">
            <el-date-picker
              type="date"
              v-model="batchForm[activeName].jcrq"
              placeholder="选择日期"
              format="yyyy-MM-dd"
              :default-value="new Date()"
              value-format="yyyy-MM-dd"
              :style="{'width': 140 + 'px'}"
            >
            </el-date-picker>
          </el-form-item>
          <!-- <el-form-item label="取样数量：" class="flex-item flex-box" label-width="100px">
            <el-input
              v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].qysl" 
              placeholder="请输入(默认660克)"
              :style="{'width': 120 + 'px'}"
            >
              <template slot="append">g</template>
            </el-input>
          </el-form-item> -->
          <el-form-item class="flex-item flex-box" label="水温：" label-width="100px">
            <!-- <el-input
              v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].md"
              disabled
            >
              <template slot="append">℃</template>
            </el-input> -->
            <el-input-number v-model="batchForm[activeName].sw"  
              :max="25" :min="15"
              @input="setSZXS"
              style="width: 120px !important"
              :step="1" step-strictly>
            </el-input-number>

          </el-form-item>
          <el-form-item class="flex-item flex-box" label="温度修正系数：" label-width="110px">
            <el-input
              v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].xzxs"
              disabled
              :style="{'width': 120 + 'px'}"
            >
            </el-input>
          </el-form-item>
          <el-form-item label="水表观密度：" class="flex-item flex-box"  label-width="100px">
            <el-input
              v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].sbgmd" 
              disabled
              :style="{'width': 120 + 'px'}"
              placeholder="请输入"
            >
              <template slot="append">kg/m³</template>
            </el-input>
          </el-form-item>
        </div>
        <div v-for="(item, index) in batchForm[activeName].bgmdInfo" :key="index">
          <p class="info-form-title mt16">{{index == 0 ? '第一次试验' : '第二次试验'}}</p>
          <div class="info-form-piece">
            <div class="flex-box">
              <el-form-item label="烘干试样质量：" class="flex-item flex-box">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  @input="val => setBGMD(index)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  v-model="item.hgsyzl" 
                  placeholder="请输入(默认300克)"
                >
                  <template slot="append">g</template>
                </el-input>
              </el-form-item>
              <div class="flex-item "></div>
            </div>
            <div class="flex-box">
              <el-form-item class="flex-item flex-box" label="总质量(水+吊篮)：">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  @input="val => setBGMD(index)"
                  v-model="item.zzlsdl"
                  placeholder="请输入"
                >
                  <template slot="append">g</template>
                </el-input>
              </el-form-item>
              <el-form-item class="flex-item flex-box" label="总质量(水+试样+吊篮)：">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  @input="val => setBGMD(index)"
                  v-model="item.zzlssydl"
                  placeholder="请输入"
                >
                  <template slot="append">g</template>
                </el-input>
              </el-form-item>
            </div>
            <div class="flex-box">
              <el-form-item class="flex-item flex-box" label="修正系数：">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  v-model="batchForm[activeName].xzxs"
                  disabled
                >
                </el-input>
              </el-form-item>
              <el-form-item label="结果：" class="flex-item flex-box">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  disabled
                  v-model="item.jg"
                >
                  <template slot="append">kg/m³</template>
                </el-input>
              </el-form-item>
            </div>
          </div>
        </div>
        <div class="line"></div>
        <el-row>
          <el-col :span="10">
            <el-form-item class="flex-box" label="平均值：">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                disabled
                v-model="batchForm[activeName].pjz" 
                :style="{'width': 200 + 'px'}"
              >
                <template slot="append">kg/m³</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="14">
            <el-form-item class="flex-box" label="单项结论：">
              <el-input
                :disabled="dxjlDisabled"
                v-model="batchForm[activeName].dxjl" 
                placeholder="请输入"
                :style="{'width': 180 + 'px'}"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row class="mt16">
          <el-col :span="24">
            <el-form-item label="检测图片：">
              <div class="cbo">
                <div class="img-box">
                  <el-upload
                    accept=".jpeg,.png,.jpg,.bmp,.gif"
                    :action="baseUrl + '/upload/file'"
                    list-type="picture-card"
                    :on-preview="handlePreview"
                    :file-list="batchForm[activeName].img"
                    :on-success="handlePicSuccess"
                    :on-remove="handlePicRemove">
                    <i class="el-icon-plus"></i>
                  </el-upload>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    
    <!-- 堆积密度 FINE_AGGREGATE_PARAM_XGL_DJMD -->
    <div v-if="activeName === 'FINE_AGGREGATE_PARAM_XGL_DJMD'" class="custom-form">
      <el-form :disabled="loading2 || experimentStatus == 3" :inline="true" label-width="100px">
        <div class="mt16 flex-box">
          <el-form-item label="检测日期：" class="flex-item flex-box">
            <el-date-picker
              type="date"
              v-model="batchForm[activeName].jcrq"
              placeholder="选择日期"
              format="yyyy-MM-dd"
              :default-value="new Date()"
              value-format="yyyy-MM-dd"
              style="width: 135px;"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item class="flex-item flex-box" label="容量筒质量：">
            <el-input
              v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 100000 ? e.target.value.match(/100000|\d{1,5}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @input="val => setDJMDAll()"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].rltzl"
              placeholder="请输入"
              style="width: 135px;"
            >
              <template slot="append">g</template>
            </el-input>
          </el-form-item>
          <el-form-item label="容量筒体积：" class="flex-item flex-box" >
            <el-input
              v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 100000 ? e.target.value.match(/100000|\d{1,5}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @input="val => setDJMDAll()"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].rlttj" 
              placeholder="请输入"
              style="width: 135px;"
            >
              <template slot="append">m³</template>
            </el-input>
          </el-form-item>
          <el-form-item label="空隙率：" class="flex-item flex-box" >
            <el-input
              v-manual-update
              @keyup.native="e=> e.target.value = e.target.value * 1 > 100000 ? e.target.value.match(/100000|\d{1,5}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].kxl" 
              placeholder="请输入"
              style="width: 135px;"
            >
              <template slot="append">%</template>
            </el-input>
          </el-form-item>
        </div>
        <div v-for="(item, index) in batchForm[activeName].djmdInfo" :key="index">
          <p class="info-form-title mt16">{{index == 0 ? '第一次试验' : '第二次试验'}}</p>
          <div class="info-form-piece flex-box">
            <el-form-item label="总质量：" class="flex-item flex-box">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 100000 ? e.target.value.match(/100000|\d{1,5}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @input="val => setDJMD(index)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="item.zzl" 
                placeholder="请输入"
              >
                <template slot="append">g</template>
              </el-input>
            </el-form-item>
            <el-form-item label="试样质量：" class="flex-item flex-box">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 100000 ? e.target.value.match(/100000|\d{1,5}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                disabled
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="item.syzl" 
                placeholder="请输入"
              >
                <template slot="append">g</template>
              </el-input>
            </el-form-item>
            <el-form-item label="结果：" class="flex-item flex-box">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 100000 ? e.target.value.match(/100000|\d{1,5}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                disabled
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="item.jg" 
              >
                <template slot="append">g/m³</template>
              </el-input>
            </el-form-item>
          </div>
        </div>
        <div class="line"></div>
        <el-row>
          <el-col :span="10">
            <el-form-item class="flex-box" label="平均值：">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 100000 ? e.target.value.match(/100000|\d{1,5}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                disabled
                v-model="batchForm[activeName].pjz" 
                :style="{'width': 200 + 'px'}"
              >
                <template slot="append">kg/m³</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="14">
            <el-form-item class="flex-box" label="单项结论：">
              <el-input
                :disabled="dxjlDisabled"
                v-model="batchForm[activeName].dxjl" 
                placeholder="请输入"
                :style="{'width': 180 + 'px'}"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row class="mt16">
          <el-col :span="24">
            <el-form-item label="检测图片：">
              <div class="cbo">
                <div class="img-box">
                  <el-upload
                    accept=".jpeg,.png,.jpg,.bmp,.gif"
                    :action="baseUrl + '/upload/file'"
                    list-type="picture-card"
                    :on-preview="handlePreview"
                    :file-list="batchForm[activeName].img"
                    :on-success="handlePicSuccess"
                    :on-remove="handlePicRemove">
                    <i class="el-icon-plus"></i>
                  </el-upload>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    
    <!-- 紧密密度 -->
    <div v-if="activeName === 'FINE_AGGREGATE_PARAM_XGL_JMMD'" class="custom-form">
      <el-form :disabled="loading2 || experimentStatus == 3" :inline="true" label-width="100px">
        
        <div class="mt16 flex-box">
          <el-form-item label="检测日期：" class="flex-item flex-box">
            <el-date-picker
              type="date"
              v-model="batchForm[activeName].jcrq"
              placeholder="选择日期"
              format="yyyy-MM-dd"
              :default-value="new Date()"
              value-format="yyyy-MM-dd"
              style="width: 135px;"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item class="flex-item flex-box" label="容量筒质量：">
            <el-input
              v-manual-update
              @keyup.native="e=> e.target.value = e.target.value * 1 > 100000 ? e.target.value.match(/100000|\d{1,5}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @input="val => setDJMDAll()"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].rltzl"
              placeholder="请输入"
              style="width: 135px;"
            >
              <template slot="append">g</template>
            </el-input>
          </el-form-item>
          <el-form-item label="容量筒体积：" class="flex-item flex-box" >
            <el-input
              v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 100000 ? e.target.value.match(/100000|\d{1,5}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @input="val => setDJMDAll()"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].rlttj" 
              placeholder="请输入"
              style="width: 135px;"
            >
              <template slot="append">m³</template>
            </el-input>
          </el-form-item>
          <el-form-item label="空隙率：" class="flex-item flex-box" >
            <el-input
              v-manual-update
              @keyup.native="e=> e.target.value = e.target.value * 1 > 100000 ? e.target.value.match(/100000|\d{1,5}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].kxl" 
              placeholder="请输入"
              style="width: 135px;"
            >
              <template slot="append">%</template>
            </el-input>
          </el-form-item>
        </div>
        <div v-for="(item, index) in batchForm[activeName].jmmdInfo" :key="index">
          <p class="info-form-title mt16">{{index == 0 ? '第一次试验' : '第二次试验'}}</p>
          <div class="info-form-piece flex-box">
            <el-form-item label="总质量：" class="flex-item flex-box">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 100000 ? e.target.value.match(/100000|\d{1,5}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @input="val => setDJMD(index)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="item.zzl" 
                placeholder="请输入"
              >
                <template slot="append">g</template>
              </el-input>
            </el-form-item>
            <el-form-item label="试样质量：" class="flex-item flex-box">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 100000 ? e.target.value.match(/100000|\d{1,5}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                disabled
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="item.syzl" 
                placeholder="请输入"
              >
                <template slot="append">g</template>
              </el-input>
            </el-form-item>
            <el-form-item label="结果：" class="flex-item flex-box">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 100000 ? e.target.value.match(/100000|\d{1,5}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                disabled
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="item.jg" 
              >
                <template slot="append">g/m³</template>
              </el-input>
            </el-form-item>
          </div>
        </div>
        <div class="line"></div>
        <el-row>
          <el-col :span="10">
            <el-form-item class="flex-box" label="平均值：">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 100000 ? e.target.value.match(/100000|\d{1,5}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                disabled
                v-model="batchForm[activeName].pjz" 
                :style="{'width': 200 + 'px'}"
              >
                <template slot="append">kg/m³</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="14">
            <el-form-item class="flex-box" label="单项结论：">
              <el-input
                :disabled="dxjlDisabled"
                v-model="batchForm[activeName].dxjl" 
                placeholder="请输入"
                :style="{'width': 180 + 'px'}"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row class="mt16">
          <el-col :span="24">
            <el-form-item label="检测图片：">
              <div class="cbo">
                <div class="img-box">
                  <el-upload
                    accept=".jpeg,.png,.jpg,.bmp,.gif"
                    :action="baseUrl + '/upload/file'"
                    list-type="picture-card"
                    :on-preview="handlePreview"
                    :file-list="batchForm[activeName].img"
                    :on-success="handlePicSuccess"
                    :on-remove="handlePicRemove">
                    <i class="el-icon-plus"></i>
                  </el-upload>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
  
    <!-- 云母含量 -->
    <div v-if="activeName === 'FINE_AGGREGATE_PARAM_XGL_YMHL'" class="custom-form">
      <el-form :disabled="loading2 || experimentStatus == 3" :inline="true" label-width="120px">
        <div class="mt16 jcrq-box">
          <el-form-item label="检测日期：">
            <el-date-picker
              type="date"
              v-model="batchForm[activeName].jcrq"
              placeholder="选择日期"
              format="yyyy-MM-dd"
              :default-value="new Date()"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
        </div>
        <!-- <div class="mt16 flex-box">
          <el-form-item label="试样质量：" class="flex-item flex-box" >
            <el-input
              v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].syzl" 
              placeholder="请输入(默认150)"
            >
              <template slot="append">g</template>
            </el-input>
          </el-form-item>
          <div class="flex-item2">*备注：该试样为烘干后，且筛除了4.75mm以上及300um以下质量的试样</div>
        </div> -->
        <div v-for="(item, index) in batchForm[activeName].ymhlInfo" :key="index">
          <p class="info-form-title mt16">{{index == 0 ? '第一次试验' : '第二次试验'}}</p>
          <div class="info-form-piece flex-box">
            <el-form-item label="云母质量：" class="flex-item flex-box">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @input="val => setYMZL(index)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="item.ymzl" 
                placeholder="请输入"
              >
                <template slot="append">g</template>
              </el-input>
            </el-form-item>
            <el-form-item label="总质量：" class="flex-item flex-box">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @input="val => setYMZL(index)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="item.zzl" 
                placeholder="请输入"
              >
                <template slot="append">g</template>
              </el-input>
            </el-form-item>
            <el-form-item label="结果：" class="flex-item flex-box">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                disabled
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="item.jg" 
              >
                <template slot="append">%</template>
              </el-input>
            </el-form-item>
          </div>
        </div>
        <div class="line"></div>
        <el-row>
          <el-col :span="10">
            <el-form-item class="flex-box" label="平均值：">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                disabled
                v-model="batchForm[activeName].pjz" 
                :style="{'width': 200 + 'px'}"
              >
                <template slot="append">%</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="14">
            <el-form-item class="flex-box" label="单项结论：">
              <el-input
                :disabled="dxjlDisabled"
                v-model="batchForm[activeName].dxjl" 
                placeholder="请输入"
                :style="{'width': 180 + 'px'}"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row class="mt16">
          <el-col :span="24">
            <el-form-item label="检测图片：">
              <div class="cbo">
                <div class="img-box">
                  <el-upload
                    accept=".jpeg,.png,.jpg,.bmp,.gif"
                    :action="baseUrl + '/upload/file'"
                    list-type="picture-card"
                    :on-preview="handlePreview"
                    :file-list="batchForm[activeName].img"
                    :on-success="handlePicSuccess"
                    :on-remove="handlePicRemove">
                    <i class="el-icon-plus"></i>
                  </el-upload>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    
    <!-- 压碎值指标 -->
    <div v-if="activeName === 'FINE_AGGREGATE_PARAM_XGL_YSZZB'" class="custom-form">
      <el-form :disabled="loading2 || experimentStatus == 3" :inline="true" class="mt16">
        <div class="mt16 jcrq-box">
          <el-form-item label="检测日期：">
            <el-date-picker
              type="date"
              v-model="batchForm[activeName].jcrq"
              placeholder="选择日期"
              format="yyyy-MM-dd"
              :default-value="new Date()"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
        </div>
        <div class="table-box-kzf mt16" v-for="(itemF, indexF) in batchForm[activeName].yszzbList" :key="itemF.type">
          <div class="tb-title">{{itemF.typeName}}</div>
          <div class="tb-content">
            <div class="flex-box tb-item" v-for="(item, index) in itemF.data" :key="index">
              <div class="tb-left">{{item.target}}</div>
              <el-form-item label="试样质量：" class="flex-item">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="item.syzl"
                  @input="val => setYSZZB(indexF, index)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  placeholder="请输入"
                  style="width: 120px;"
                >
                  <template slot="append">g</template>
                </el-input>
              </el-form-item>
              <el-form-item label="筛余量：" class="flex-item">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="item.syl"
                  @input="val => setYSZZB(indexF, index)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  placeholder="请输入"
                  style="width: 120px;"
                >
                  <template slot="append">g</template>
                </el-input>
              </el-form-item>
              <el-form-item label="通过量：" class="flex-item">
                  <!-- @input="val => setYSZZB(indexF, index)" -->
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="item.tgl"
                  disabled
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  placeholder=""
                  style="width: 120px;"
                >
                  <template slot="append">g</template>
                </el-input>
              </el-form-item>
              <el-form-item label="结果：" class="flex-item">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="item.jg"
                  disabled
                  style="width: 120px;"
                >
                  <template slot="append">%</template>
                </el-input>
              </el-form-item>
            </div>
          </div>
        </div>
        
        <div class="flex-box mt16">
          <template v-if="batchForm[activeName].yszzbList[0].data">
            <el-form-item v-for="(item, index) in batchForm[activeName].yszzbList[0].data" :key="item.target+index" :label="item.target+'平均值：'" class="flex-item">
              <el-input
                v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                v-model="batchForm[activeName]['pjz' + index]"
                disabled
              >
                <template slot="append">%</template>
              </el-input>
            </el-form-item>
          </template>
        </div>
        
        <el-row>
          <el-col :span="12">
            <el-form-item class="flex-box" label="总压碎指标值：">
              <el-input
                :disabled="dxjlDisabled"
                disabled
                v-model="batchForm[activeName].zyszb" 
                placeholder="请输入"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item class="flex-box" label="单项结论：">
              <el-input
                :disabled="dxjlDisabled"
                v-model="batchForm[activeName].dxjl" 
                placeholder="请输入"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row class="mt16">
          <el-col :span="24">
            <el-form-item label="检测图片：">
              <div class="cbo">
                <div class="img-box">
                  <el-upload
                    accept=".jpeg,.png,.jpg,.bmp,.gif"
                    :action="baseUrl + '/upload/file'"
                    list-type="picture-card"
                    :on-preview="handlePreview"
                    :file-list="batchForm[activeName].img"
                    :on-success="handlePicSuccess"
                    :on-remove="handlePicRemove">
                    <i class="el-icon-plus"></i>
                  </el-upload>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    
    <!-- 贝壳含量 -->
    <div v-if="activeName === 'FINE_AGGREGATE_PARAM_XGL_BKHL'" class="custom-form">
      <el-form :disabled="loading2 || experimentStatus == 3" :inline="true"  label-width="100px">
        
        <div class="mt16 flex-box plr16">
          <el-form-item label="检测日期：" class="flex-item flex-box">
            <el-date-picker
              type="date"
              v-model="batchForm[activeName].jcrq"
              placeholder="选择日期"
              format="yyyy-MM-dd"
              :default-value="new Date()"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
          
          <!-- <el-form-item label="试样质量：" class="flex-item flex-box" >
            <el-input
              v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].syzl" 
              placeholder="请输入(默认500g)"
            >
              <template slot="append">g</template>
            </el-input>
          </el-form-item> -->
          <el-form-item label="含泥量：" class="flex-item flex-box" >
              <!-- @input="val => setBKHLAll()" -->
            <el-input
              v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].hnl" 
              disabled
              placeholder="请输入"
            >
              <template slot="append">%</template>
            </el-input>
          </el-form-item>
          <div class="flex-item flex-box"></div>
        </div>
        <div v-for="(item, index) in batchForm[activeName].bkhlInfo" :key="index">
          <p class="info-form-title mt16">{{index == 0 ? '第一次试验' : '第二次试验'}}</p>
          <div class="info-form-piece flex-box">
            <el-form-item label="总质量：" class="flex-item flex-box">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @input="val => setBKHL(index)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="item.zzl" 
                placeholder="请输入"
              >
                <template slot="append">g</template>
              </el-input>
            </el-form-item>
            <el-form-item label="去贝壳质量：" class="flex-item flex-box">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @input="val => setBKHL(index)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="item.qbkzl" 
                placeholder="请输入"
              >
                <template slot="append">g</template>
              </el-input>
            </el-form-item>
            <el-form-item label="结果：" class="flex-item flex-box">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                disabled
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="item.jg" 
              >
                <template slot="append">%</template>
              </el-input>
            </el-form-item>
          </div>
        </div>
        <div class="line"></div>
        <el-row>
          <el-col :span="10">
            <el-form-item class="flex-box" label="平均值：">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                disabled
                v-model="batchForm[activeName].pjz" 
                :style="{'width': 200 + 'px'}"
              >
                <template slot="append">%</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="14">
            <el-form-item class="flex-box" label="单项结论：">
              <el-input
                :disabled="dxjlDisabled"
                v-model="batchForm[activeName].dxjl" 
                placeholder="请输入"
                :style="{'width': 180 + 'px'}"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row class="mt16">
          <el-col :span="24">
            <el-form-item label="检测图片：">
              <div class="cbo">
                <div class="img-box">
                  <el-upload
                    accept=".jpeg,.png,.jpg,.bmp,.gif"
                    :action="baseUrl + '/upload/file'"
                    list-type="picture-card"
                    :on-preview="handlePreview"
                    :file-list="batchForm[activeName].img"
                    :on-success="handlePicSuccess"
                    :on-remove="handlePicRemove">
                    <i class="el-icon-plus"></i>
                  </el-upload>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <!-- 氯离子含量 -->
    <div v-if="activeName === 'FINE_AGGREGATE_PARAM_XGL_LLZHL'" class="custom-form">
      <el-form :disabled="loading2 || experimentStatus == 3" :inline="true" label-width="100px">
        
        <div class="mt16 flex-box">
          
          <el-form-item label="检测日期：" class="flex-item">
            <el-date-picker
              type="date"
              v-model="batchForm[activeName].jcrq"
              placeholder="选择日期"
              format="yyyy-MM-dd"
              :default-value="new Date()"
              value-format="yyyy-MM-dd"
              style="width: 135px;"
            >
            </el-date-picker>
          </el-form-item>
          
          <el-form-item label="换算系数：" class="flex-item flex-box" >
            <el-input
              v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].hsxs" 
              disabled
              placeholder="0.0355"
              style="width: 120px;"
            >
              <template slot="append">g</template>
            </el-input>
          </el-form-item>
          <el-form-item label="体积比：" class="flex-item flex-box" >
            <el-input
              v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].tjb" 
              disabled
              placeholder="10"
              style="width: 120px;"
            >
              <template slot="append">%</template>
            </el-input>
          </el-form-item>
          <!-- <el-form-item label="取样数量：" class="flex-item flex-box" >
            <el-input
              v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @input="val => setLLZHLAll()"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].qysl" 
              placeholder="请输入(默认500g)"
              style="width: 120px;"
            >
              <template slot="append">g</template>
            </el-input>
          </el-form-item> -->
        </div>
        <div v-for="(item, index) in batchForm[activeName].llzhlInfo" :key="index">
          <p class="info-form-title mt16">{{index == 0 ? '第一次试验' : '第二次试验'}}</p>
          <div class="info-form-piece flex-box">
            <el-form-item label="样品重量：" class="flex-item flex-box" >
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @input="val => setLLZHL(index)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="item.ypzl" 
                placeholder="请输入"
                style="width: 120px;"
              >
                <template slot="append">g</template>
              </el-input>
            </el-form-item>
            <el-form-item label="硝酸银浓度：" class="flex-item flex-box">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @input="val => setLLZHL(index)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="item.xsynd" 
                placeholder="请输入"
                style="width: 120px;"
              >
                <template slot="append">mol/L</template>
              </el-input>
            </el-form-item>
            <div class="flex-item flex-box"></div>
          </div>
          <div class="info-form-piece flex-box">
            <el-form-item label="样品体积：" class="flex-item flex-box">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @input="val => setLLZHL(index)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="item.yptj" 
                placeholder="请输入"
                style="width: 120px;"
              >
                <template slot="append">ml</template>
              </el-input>
            </el-form-item>
            <el-form-item label="空白体积：" class="flex-item flex-box">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @input="val => setLLZHL(index)"
                placeholder="请输入"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="item.kbtj" 
                style="width: 120px;"
              >
                <template slot="append">ml</template>
              </el-input>
            </el-form-item>
            <el-form-item label="氯离子含量：" class="flex-item flex-box">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                disabled
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="item.llzhl" 
                style="width: 120px;"
              >
                <template slot="append">ml</template>
              </el-input>
            </el-form-item>
          </div>
        </div>
        <div class="line"></div>
        <el-row>
          <el-col :span="10">
            <el-form-item class="flex-box" label="平均值：">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                disabled
                v-model="batchForm[activeName].pjz" 
                :style="{'width': 200 + 'px'}"
              >
                <template slot="append">mol/L</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="14">
            <el-form-item class="flex-box" label="单项结论：">
              <el-input
                :disabled="dxjlDisabled"
                v-model="batchForm[activeName].dxjl" 
                placeholder="请输入"
                :style="{'width': 180 + 'px'}"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row class="mt16">
          <el-col :span="24">
            <el-form-item label="检测图片：">
              <div class="cbo">
                <div class="img-box">
                  <el-upload
                    accept=".jpeg,.png,.jpg,.bmp,.gif"
                    :action="baseUrl + '/upload/file'"
                    list-type="picture-card"
                    :on-preview="handlePreview"
                    :file-list="batchForm[activeName].img"
                    :on-success="handlePicSuccess"
                    :on-remove="handlePicRemove">
                    <i class="el-icon-plus"></i>
                  </el-upload>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    
    <!-- 筛分析 -->
     <!-- -->
    <div v-if="activeName === 'FINE_AGGREGATE_PARAM_XGL_SFX'" class="custom-form">
      <el-form :disabled="loading2 || experimentStatus == 3" :inline="true" class="mt16" label-width="165px">
        <div class="mt16 jcrq-box">
          <el-form-item label="检测日期：">
            <el-date-picker
              type="date"
              v-model="batchForm[activeName].jcrq"
              placeholder="选择日期"
              format="yyyy-MM-dd"
              :default-value="new Date()"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
        </div>
        <!-- <div class="mt16 flex-box">
          <el-form-item label="烘干筛分后试样重量：" class="flex-item flex-box" >
            <el-input
              v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].hgsfhsyzl" 
              placeholder="请输入"
            >
              <template slot="append">g</template>
            </el-input>
          </el-form-item>
          <el-form-item class="flex-item flex-box" label="筛面面积：">
            <el-input
              v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].smmj"
              placeholder="请输入"
            >
              <template slot="append">mm²</template>
            </el-input>
          </el-form-item>
          
        </div> -->
        <div class="mt16 flex-box">
          <el-form-item label="第一份试样重量：" class="flex-item flex-box" >
            <el-input
              v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @input="setFJSYAll(1)"
              v-model="batchForm[activeName].dyfsyzl" 
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              placeholder="请输入(499-501g)"
            >
              <template slot="append">g</template>
            </el-input>
          </el-form-item>
          <el-form-item label="第二份试样重量：" class="flex-item flex-box" >
            <el-input
              v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @input="setFJSYAll(2)"
              v-model="batchForm[activeName].defsyzl" 
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              placeholder="请输入(499-501g)"
            >
              <template slot="append">g</template>
            </el-input>
          </el-form-item>
        </div>
        
        <div class="table-box-kzf mt16">
          <div class="tb-title flex-box">
            <div class="tb-title-lef">筛孔公称直径</div>
            <div class="tb-title-con flex-item">
              <span>筛余量（g）</span><span>第一次</span><span>第二次</span>
            </div>
            <div class="tb-title-con flex-item"><span>分计筛余（%）</span><span>第一次</span><span>第二次</span></div>
            <div class="tb-title-con flex-item"><span>累计筛余（%）</span><span>第一次</span><span>第二次</span></div>
            <div class="tb-title-bom">累计筛余平均值（%）</div>
          </div>
          <div class="tb-content">
            <div class="flex-box tb-item" v-for="(item, index) in batchForm[activeName].sfxInfo" :key="index">
              <div class="tb-left">{{item.skgjzj}}</div>
              <div class="flex-item flex-box">
                <el-form-item label="" class="flex-item">
                  <el-input
                    v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                    v-model="item.syl1"
                    style="width: 90%;"
                    @input="val => setFJSYAll(1)"
                    @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                    placeholder="请输入"
                  >
                  </el-input>
                </el-form-item>
                <el-form-item label="" class="flex-item">
                  <el-input
                    v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                    v-model="item.syl2"
                    style="width: 90%;"
                    @input="val => setFJSYAll(2)"
                    @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                    placeholder="请输入"
                  >
                  </el-input>
                </el-form-item>
              </div>
              <div class="flex-item flex-box">
                <el-form-item label="" class="flex-item">
                  <el-input
                    v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                    v-model="item.fjsy1"
                    style="width: 90%;"
                    @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                    disabled
                  >
                  </el-input>
                </el-form-item>
                <el-form-item label="" class="flex-item">
                  <el-input
                    v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                    v-model="item.fjsy2"
                    style="width: 90%;"
                    @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                    disabled
                  >
                  </el-input>
                </el-form-item>
              </div>
              <div class="flex-item flex-box">
                <el-form-item label="" class="flex-item">
                  <el-input
                    v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                    v-model="item.ljsy1"
                    style="width: 90%;"
                    disabled
                    @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                    placeholder="请输入"
                  >
                  </el-input>
                </el-form-item>
                <el-form-item label="" class="flex-item">
                  <el-input
                    v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                    v-model="item.ljsy2"
                    style="width: 90%;"
                    disabled
                    @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                    placeholder="请输入"
                  >
                  </el-input>
                </el-form-item>
              </div>
              <div class="" style="width: 172px;">
                <el-form-item label="">
                  <el-input
                    v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                    v-model="item.ljsyppjz"
                    @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                    disabled
                    style="width: 140px;"
                  >
                  </el-input>
                </el-form-item>
              </div>
            </div>
          </div>
        </div>
        
        
        <div class="mt16 flex-box">
          <el-form-item label="细度模数1：" class="flex-item flex-box" >
            <el-input
              v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              v-model="batchForm[activeName].xdms1" 
              disabled
            >
            </el-input>
          </el-form-item>
          <el-form-item label="细度模数2：" class="flex-item flex-box" >
            <el-input
              v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              v-model="batchForm[activeName].xdms2" 
              disabled
            >
            </el-input>
          </el-form-item>
          <el-form-item label="细度模数平均值：" class="flex-item flex-box" >
            <el-input
              v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              v-model="batchForm[activeName].xdmspjz" 
              placeholder="µf=（µf1+µf2）/2"
              disabled
            >
            </el-input>
          </el-form-item>
        </div>
        <div class="mt16 flex-box">
          <el-form-item label="砂质量：" class="flex-item flex-box" >
            <el-input
              v-manual-update
              @input="set10ysklhl()"
              @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].hgsfhsyzl" 
              placeholder="请输入"
            >
              <template slot="append">g</template>
            </el-input>
          </el-form-item>
          <el-form-item label="10.0mm以上颗粒质量：" class="flex-item flex-box" >
            <el-input
              v-manual-update
              @input="set10ysklhl()"
              @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              v-model="batchForm[activeName].ysklzl10" 
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
            >
            </el-input>
          </el-form-item>
          
          <el-form-item label="10.0mm以上颗粒含量：" class="flex-item flex-box" >
            <el-input
              v-manual-update
              disabled
              @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              v-model="batchForm[activeName].ysklhl10" 
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
            >
            </el-input>
          </el-form-item>
        </div>
        <div class="mt16 flex-box">
          <el-form-item label="单项结论：" class="flex-item" >
            <el-input
              type="text"
              v-model="batchForm[activeName].dxjl" 
              :disabled="dxjlDisabled"
              style="width: 800px"
            >
            </el-input>
          </el-form-item>
        </div>
        
        <!-- <div class="table-box-kzf mt16">
          <div class="tb-title">
            <span @click="setKljChart">颗粒级配分析图</span>
            <span>1,23</span>
          </div>
          <div class="tb-content">
            <div id="kljptfx" style="height: 400px;">
              
            </div>
          </div>
        </div> -->
        
        <el-row class="mt16">
          <el-col :span="24">
            <el-form-item label="检测图片：">
              <div class="cbo">
                <div class="img-box">
                  <el-upload
                    accept=".jpeg,.png,.jpg,.bmp,.gif"
                    :action="baseUrl + '/upload/file'"
                    list-type="picture-card"
                    :on-preview="handlePreview"
                    :file-list="batchForm[activeName].img"
                    :on-success="handlePicSuccess"
                    :on-remove="handlePicRemove">
                    <i class="el-icon-plus"></i>
                  </el-upload>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    
    <image-viewer
      v-if="imgViewerVisible"
      :urlList="[previewUrl]"
      :on-close="onClose"
    >
    </image-viewer>
  </div>
</template>

<script>
import {add,  sub,  mul,  div, roundToDecimalPlace, calcEquation, cpEvenRound} from "@/utils/calculate.js"
import moment from "@/utils/moment.js"
import ImageViewer from "element-ui/packages/image/src/image-viewer";
import util from "../../../common/js/util.js";

import { XGL_SZXS } from "./config.js"
import { conclusion_XGL_HNL,
conclusion_XGL_NKHL,conclusion_XGL_MBZ, conclusion_XGL_YSZZB, conclusion_XGL_YMHL, conclusion_XGL_BKHL, conclusion_XGL_LLZHL } from "./conclusion.js"
export default {
  name:'userMgt',
  components: {
    ImageViewer
  },
  props: {
    materialsName: {
      type: String,
    },
    activeId: {
      type: Number | String,
    },
    experimentStatus: {
      type: Number | String,
    },
    sampleLevel: {
      type: String,
      default: '1'
    },
    entrustTime: {
      type: String,
      default: new Date().toISOString().split('T')[0],
    },
    
  },
  watch: {
    activeId: {
      handler(newValue, oldValue){
        if(newValue){
          // this.clearData();
          // this.setExperimentProject()
        }
      },
      immediate: true
    },

    entrustTime: {
      handler(newValue, oldValue){
        if(newValue){
          this.setDefaultTime(this.batchForm)
        }
      },
      immediate: true
    },
    
  },
  computed: {},
  data() {
    return {
      defaultDate: moment.today(),
      infoForm: '',
      filePrefix: this.$store.state.loginStore.userInfo.filePrefix,
      baseUrl: process.env.NODE_ENV === 'development' ? "/api" : "/mes-platform",
      activeName: 'FINE_AGGREGATE_PARAM_XGL_HNL',
      activeInfoName: 'hnlInfo',
      quickForm: {},//快检数据
      quickData: [],//快检列表
      batchForm: {
        FINE_AGGREGATE_PARAM_XGL_HNL: {
          hnlInfo: []
        },
        FINE_AGGREGATE_PARAM_XGL_NKHL: {
          hnkInfo: []
        },
        CONCRETE_PARAM_FMH_YLYHG: {
          dycsy: {
            yszzbInfo: [{},{},{}]
          },
          decsy: {
            yszzbInfo: [{},{},{}]
          },
          dscsy: {
            yszzbInfo: [{},{},{}]
          },
          dsicsy: {
            yszzbInfo: [{},{},{}]
          }
        },
        FINE_AGGREGATE_PARAM_XGL_SFX: {
          
        }
        
      },//批检数据
      sfxInfo: [{
        skgczj: '4.75mm',
        syl1: '',
        syl2: '',
        fjsy1: '',
        fjsy2: '',
        ljsy1: '',
        ljsy2: '',
        ljsypjz: '',
      },{
        skgczj: '2.36mm',
        syl1: '',
        syl2: '',
        fjsy1: '',
        fjsy2: '',
        ljsy1: '',
        ljsy2: '',
        ljsy1: '',
        ljsypjz: '',
      }],
      
      batchData: [],//批检列表
      
      loading2: false,
      
      previewUrl: "",
      imgViewerVisible: false,
      
      accordingObj: {},
      dxjlDisabled: false,
    };
  },
  
  created() {
  },
  methods: {

    isEmptyValue(value) {
            // 处理用户输入的特殊字符串
            if (value === 'null') return true;
            if (value === 'undefined') return true;
            if (value === '') return true;
            
            // 处理实际的JavaScript值
            if (value === null) return true;
            if (value === undefined) return true;
            if (value === '') return true;
            
            return false;
        },
    //判断结论
    getPJZUtil(data,key,n){//数组，key，保留小数
      let len = 0,num = 0;
      data.forEach(item =>{
        if(item[key] !== ''){
          len++;
          num += item[key] * 1
        }
      })
      //2次结果之和除以2，保留2位小数，四舍五入
      let arr = [
        {
          v: num,
        },{
          k: '/',
          v: len === 0 ? '' : len
        }
      ]
      return calcEquation(arr, n);
    },
    //判断结论
    conclusion(num, key){
      if(num === ''){
        return ''
      }
      const aName = this.activeName;
      if(!key){
        key = aName.match(/[^_]+$/)[0];
        key = key.toLowerCase();
      }
      if(!this.accordingObj[aName][key]){
        return ''
      }
      const min = this.accordingObj[aName][key].min;
      const max = this.accordingObj[aName][key].max;
      
      console.log(num, aName, key, min, max);
      if(min !== null && num < min){
        // this.$parent.testInfoForm.isQualified = 2;
        return '不合格'
      }
      if(max !== null && num > max){
        // this.$parent.testInfoForm.isQualified = 2;
        return '不合格'
      }
      return '合格';
    },
    // 含泥量 含MB值计算
    mbTJchange(){
      const data = this.batchForm[this.activeName];
      let tj = 0;
      for(let i = 10; i > 0; i-- ){
        if(this.batchForm[this.activeName].syjgInfo['jryjlrytj' + i]){
          tj += this.batchForm[this.activeName].syjgInfo['jryjlrytj' + i] * 1;
        }
      }
      this.batchForm[this.activeName].jryjlryzl = tj;
      console.log(tj);
      //体积/重量*10，结果精确到0.1，四舍五入
      let arr = [
        {
          v: tj,
        },{
          k: '/',
          v: data.syjgInfo.syzl,
        },{
          k: '*',
          v: 10
        }
      ]
      let res = calcEquation(arr, 2)
      this.batchForm[this.activeName].syjgInfo.mbz = res;
      this.batchForm[this.activeName].yjlz = res;
      this.batchForm[this.activeName].syjgInfo.dxjl = this.conclusion(res);
    },
    
    // 泥块含量计算 含泥量
    setHnl(index){//设置含泥量
      const data = this.batchForm[this.activeName][this.activeInfoName][index];
      if(data.hqsyzl * 1 > 10000){
        this.batchForm[this.activeName][this.activeInfoName][index].hqsyzl = 10000;
        this.$message.warning('烘前试样质量不能大于10000');
      }else if(data.hhsyzl * 1 > 0 && data.hqsyzl * 1 > 0){
        if(data.hhsyzl * 1 > data.hqsyzl * 1){
          this.batchForm[this.activeName][this.activeInfoName][index].hhsyzl = data.hqsyzl;
          this.$message.warning('烘后不能大于烘前');
        }
      }else{
        if(data.hqsyzl == 0){
          this.$message.error("烘前试样质量需要大于0");
          this.batchForm[this.activeName][this.activeInfoName][index].hhsyzl = '';
        } 
        return false;
      }
      
      let arr = [
        {
          v: data.hqsyzl,
        },{
          k: '-',
          v: data.hhsyzl,
        },{
          k: '/',
          v: data.hqsyzl,
        },{
          k: '*',
          v: 100,
        }
      ]
      let res = calcEquation(arr, 1)
      this.batchForm[this.activeName][this.activeInfoName][index].hnl = res;
      this.batchForm[this.activeName][this.activeInfoName][index].nkhl = res;
      
      this.batchForm[this.activeName][this.activeInfoName][index].sfhl = res;
      this.batchForm[this.activeName][this.activeInfoName][index].sfzl = sub(data.hqsyzl,data.hhsyzl);
      this.setHNLPJZ()
    },
    setHNLPJZ(){
      const data = this.batchForm[this.activeName][this.activeInfoName];
      
      let res = this.getPJZUtil(data, 'hnl', 1);
      // if(this.activeName === 'FINE_AGGREGATE_PARAM_XGL_NKHL'){
      //   this.getPJZUtil(data, 'nkhl', 1);
      // }else{
        
      // }
      // this.batchForm[this.activeName].hnlpjz = res;
      this.batchForm[this.activeName].dxjl = this.conclusion(res);
      this.batchForm[this.activeName].hnlpjz = res;
      this.batchForm[this.activeName].pjz = res;
    },
    
    
    //含水率
    setHSL(index){
      const data = this.batchForm[this.activeName][this.activeInfoName][index];
      if(data.hhsyzl * 1 > data.hqsyzl * 1){
        this.$message.warning('不能大于烘前试样');
        return false;
      }
      
      let arr = [
        {
          v: data.hqsyzl,
        },{
          k: '-',
          v: data.hhsyzl,
        },{
          k: '/',
          v: data.hqsyzl,
        },{
          k: '*',
          v: 100,
        }
      ]
      let res = calcEquation(arr, 1)
      this.batchForm[this.activeName][this.activeInfoName][index].hsl = res;
      this.setHSLPJZ();
    },
    setHSLPJZ(){
      const data = this.batchForm[this.activeName][this.activeInfoName];
      
      let res = this.getPJZUtil(data, 'hsl', 1);
      this.batchForm[this.activeName].pjz = res;
      this.batchForm[this.activeName].dxjl = this.conclusion(res);
    },
    // 表观密度
    setSZXS(val){//修正系数
      console.log(val)
      // this.batchForm[this.activeName][this.activeInfoName][0].xzxs = XGL_SZXS[val];
      // this.batchForm[this.activeName][this.activeInfoName][1].xzxs = XGL_SZXS[val];
      this.batchForm[this.activeName].xzxs = XGL_SZXS[val];
      this.setBGMD(0)
      this.setBGMD(1)
    },
    setBGMD(index){
      const dataAll = this.batchForm[this.activeName];
      const data = this.batchForm[this.activeName][this.activeInfoName][index];
      
      let xzxs = this.batchForm[this.activeName].xzxs;
      let arr = [
        {
          v: data.hgsyzl,
        },{
          k: '/',
          v: sub(add(data.hgsyzl,data.zzlsdl), data.zzlssydl),
        },{
          k: '-',
          v: xzxs,
        },{
          k: '*',
          v: 1000,//data.sbgmd
        }
      ]
      let res = calcEquation(arr, 10)
      this.batchForm[this.activeName][this.activeInfoName][index].jg = res;
      this.setBGMDPJZ(2500);
    },
    setBGMDPJZ(max, type){
      const data = this.batchForm[this.activeName][this.activeInfoName];
      
      let res = this.getPJZUtil(data, 'jg', 10);
      this.batchForm[this.activeName].pjz = res;
      this.batchForm[this.activeName].dxjl = this.conclusion(res);
    },
    // 堆积密度 紧密密度
    setDJMDAll(){
      this.setDJMD(0);
      this.setDJMD(1);
    },
    setDJMD(index){
      const data = this.batchForm[this.activeName][this.activeInfoName][index];
      const dataAll = this.batchForm[this.activeName];
      let syzl = sub(data.zzl, dataAll.rltzl)
      let arr = [
        {
          v: syzl,
        },{
          k: '/',
          v: dataAll.rlttj,
        },{
          k: '/',
          v: 1000,
        }
      ]
      let res = calcEquation(arr, 10)
      this.batchForm[this.activeName][this.activeInfoName][index].syzl = syzl;
      this.batchForm[this.activeName][this.activeInfoName][index].jg = res;
      this.setBGMDPJZ(1400, 'min');
    },
    // 云母含量
    setYMZL(index){
      const data = this.batchForm[this.activeName][this.activeInfoName][index];
      if(data.zzl * 1 > 10000){
        this.batchForm[this.activeName][this.activeInfoName][index].zzl = 10000;
        this.$message.warning('总质量 不能大于10000');
      }else if(data.ymzl * 1 > data.zzl * 1){
        this.batchForm[this.activeName][this.activeInfoName][index].ymzl = this.batchForm[this.activeName][this.activeInfoName][index].zzl;
        this.$message.warning('云母质量不能大于总质量');
      }
      let arr = [
        {
          v: data.ymzl,
        },{
          k: '/',
          v: data.zzl,
        },{
          k: '*',
          v: 100,
        }
      ]
      let res = calcEquation(arr, 2)
      this.batchForm[this.activeName][this.activeInfoName][index].jg = res;
      this.setYMZLPJZ();
    },
    setYMZLPJZ(){
      const data = this.batchForm[this.activeName][this.activeInfoName];
      
      let res = this.getPJZUtil(data, 'jg', 2);
      this.batchForm[this.activeName].pjz = res;
      this.batchForm[this.activeName].dxjl = this.conclusion(res);
    },
    // 压碎值指标
    setYSZZB(indexF, index){
      const data = this.batchForm[this.activeName][this.activeInfoName][indexF].data[index];
      
      let tgl = sub(data.syzl, data.syl)
      //结果=通过量/（晒余量+通过量）*100
      let arr = [
        {
          v: tgl,
        },{
          k: '/',
          v: data.syzl,
        },{
          k: '*',
          v: 100,
        }
      ]
      let res = calcEquation(arr, 1)
      this.batchForm[this.activeName][this.activeInfoName][indexF].data[index].tgl = tgl;
      this.batchForm[this.activeName][this.activeInfoName][indexF].data[index].jg = res;
      this.setYSZZBPJZ(index);
    },
    setYSZZBPJZ(index){
      const odata = this.batchForm[this.activeName].yszzbList;
      let len = 0,num = 0;
      
      odata.forEach(item =>{
        if(item.data[index].jg !== ''){
          len++;
          num += item.data[index].jg * 1
        }
      })
      //2次结果之和除以2，保留2位小数，四舍五入
      let arr = [
        {
          v: num,
        },{
          k: '/',
          v: len === 0 ? '' : len
        }
      ]
      let res = calcEquation(arr, 1);
      this.batchForm[this.activeName]['pjz' + index] = res;
      this.setYSZZBzys();
    },
    // setYSZZBzys(){
    //   const odata = this.batchForm[this.activeName].yszzbList;
    //   const obj = this.batchForm[this.activeName];
      
      
      
      
    //   let isR = false;
    //   odata.forEach(item =>{
    //     item.data.forEach((v,i)=>{
    //       // if(v.syl !== ''){
    //       //   sylArr[i] += v.syl * 1
    //       // }else{
    //       //   isR = true;
    //       // }
    //       if(v.jg === ''){
    //         isR = true;
    //       }
    //     })
    //   })
      
    //   let fjsyArr = Array.from({ length: odata[0].data.length }, () => 0);
    //   const dataSFX = this.batchForm.FINE_AGGREGATE_PARAM_XGL_SFX.sfxInfo;
    //   for (var i = 0; i < dataSFX.length; i++) {
    //     if(dataSFX[i].skgjzj == '5.00'){
    //       if(dataSFX[i].fjsy1 !== '' && dataSFX[i].fjsy2 !== ''){
    //         fjsyArr[0] = div(add(dataSFX[i].fjsy1,dataSFX[i].fjsy2), 2)
    //       }else{
    //         isR = true;
    //       }
    //     }
    //     if(dataSFX[i].skgjzj == '2.50'){
    //       if(dataSFX[i].fjsy1 !== '' && dataSFX[i].fjsy2 !== ''){
    //         fjsyArr[1] = div(add(dataSFX[i].fjsy1,dataSFX[i].fjsy2), 2)
    //       }else{
    //         isR = true;
    //       }
    //     }
    //     if(dataSFX[i].skgjzj == '1.25'){
    //       if(dataSFX[i].fjsy1 !== '' && dataSFX[i].fjsy2 !== ''){
    //         fjsyArr[2] = div(add(dataSFX[i].fjsy1,dataSFX[i].fjsy2), 2)
    //       }else{
    //         isR = true;
    //       }
    //     }
    //     if(dataSFX[i].skgjzj == '0.630'){
    //       if(dataSFX[i].fjsy1 !== '' && dataSFX[i].fjsy2 !== ''){
    //         fjsyArr[3] = div(add(dataSFX[i].fjsy1,dataSFX[i].fjsy2), 2)
    //       }else{
    //         isR = true;
    //       }
    //     }
    //   }
      
    //   if(isR){
    //     this.batchForm[this.activeName].zyszb = '';
    //     return false;
    //   }
    //   console.log(fjsyArr);
    //   let fz = 0,
    //       fm = 0,
    //       len = odata.length;
    //   fjsyArr.forEach((item,idx)=>{
    //     let pjzb = div(obj['pjz' + idx], 100)
    //     fz = add(fz, mul(item, pjzb));
    //     fm = add(fm, item * 1);
    //   })
    //   console.log(fz,fm);
    //   let arr = [
    //     {
    //       v: fz,
    //     },{
    //       k: '/',
    //       v: fm
    //     },{
    //       k: '*',
    //       v: 100
    //     }
    //   ]
    //   let res = calcEquation(arr, 1);
    //   this.batchForm[this.activeName].zyszb = res;
    // },
    setYSZZBzys(){
      const odata = this.batchForm[this.activeName].yszzbList;
      const obj = this.batchForm[this.activeName];
      
      
      
      
      let isR = false;
      odata.forEach(item =>{
        item.data.forEach((v,i)=>{
          // if(v.syl !== ''){
          //   sylArr[i] += v.syl * 1
          // }else{
          //   isR = true;
          // }
          if(v.jg === ''){
            isR = true;
          }
        })
      })
      
      let fjsyArr1 = Array.from({ length: odata[0].data.length }, () => 0);
      let fjsyArr2 = Array.from({ length: odata[0].data.length }, () => 0);
      const dataSFX = this.batchForm.FINE_AGGREGATE_PARAM_XGL_SFX.sfxInfo;
      for (var i = 0; i < dataSFX.length; i++) {
        if(dataSFX[i].skgjzj == '2.50'){
          if(dataSFX[i].syl1 !== '' && dataSFX[i].syl2 !== ''){
            fjsyArr1[0] = dataSFX[i].syl1;
            fjsyArr2[0] = dataSFX[i].syl2;
          }else{
            isR = true;
          }
        }
        if(dataSFX[i].skgjzj == '1.25'){
          if(dataSFX[i].syl1 !== '' && dataSFX[i].syl2 !== ''){
            fjsyArr1[1] = dataSFX[i].syl1;
            fjsyArr2[1] = dataSFX[i].syl2;
          }else{
            isR = true;
          }
        }
        if(dataSFX[i].skgjzj == '0.630'){
          if(dataSFX[i].syl1 !== '' && dataSFX[i].syl2 !== ''){
            fjsyArr1[2] = dataSFX[i].syl1;
            fjsyArr2[2] = dataSFX[i].syl2;
          }else{
            isR = true;
          }
        }
        if(dataSFX[i].skgjzj == '0.315'){
          if(dataSFX[i].syl1 !== '' && dataSFX[i].syl2 !== ''){
            fjsyArr1[3] = dataSFX[i].syl1;
            fjsyArr2[3] = dataSFX[i].syl2;
          }else{
            isR = true;
          }
        }
      }
      
      if(isR){
        this.batchForm[this.activeName].zyszb = '';
        return false;
      }
      let fjsySum1 = fjsyArr1.reduce((a,b)=>{
        return add(a,b)
      });
      let fjsySum2 = fjsyArr2.reduce((a,b)=>{
        return add(a,b)
      });
      let fz = 0,
          fm = 0,
          len = odata.length;
      let a1=0, a2=0, a3=0, a4=0;
      let a11=0, a21=0, a31=0, a41=0;
      let a12=0, a22=0, a32=0, a42=0;
      fjsyArr1.forEach((item,idx)=>{
        if (idx == 0) {
          a11 = div(item, fjsySum1);
        }else if (idx == 1) {
          a21 = div(item, fjsySum1);
        }else if (idx == 2) {
          a31 = div(item, fjsySum1);
        }else if (idx == 3) {
          a41 = div(item, fjsySum1);
        }
      })
      fjsyArr2.forEach((item,idx)=>{
        if (idx == 0) {
          a12 = div(item, fjsySum2);
        }else if (idx == 1) {
          a22 = div(item, fjsySum2);
        }else if (idx == 2) {
          a32 = div(item, fjsySum2);
        }else if (idx == 3) {
          a42 = div(item, fjsySum2);
        }
      })
      
      a1 = div(add(a11, a12), 2);
      a2 = div(add(a21, a22), 2);
      a3 = div(add(a31, a32), 2);
      a4 = div(add(a41, a42), 2);

      fz = mul(div(obj['pjz0'], 100), a1) + mul(div(obj['pjz1'], 100), a2) + mul(div(obj['pjz2'], 100), a3) + mul(div(obj['pjz3'], 100), a4);
      fm = a1 + a2 + a3 + a4;
      console.log(fz,fm);
      let arr = [
        {
          v: fz,
        },{
          k: '/',
          v: fm
        },{
          k: '*',
          v: 100
        }
      ]
      let res = calcEquation(arr, 1);
      this.batchForm[this.activeName].zyszb = res;
    },
    // 贝壳含量
    setBKHLAll(){
      this.setBKHL(0)
      this.setBKHL(1)
    },
    setBKHL(index){
      const data = this.batchForm[this.activeName][this.activeInfoName][index];
      
      if(data.zzl * 1 > 10000){
        this.batchForm[this.activeName][this.activeInfoName][index].zzl = 10000;
        this.$message.warning('总质量 不能大于10000');
      }else if(data.qbkzl * 1 > data.zzl * 1){
        this.batchForm[this.activeName][this.activeInfoName][index].qbkzl = this.batchForm[this.activeName][this.activeInfoName][index].zzl;
        this.$message.warning('去贝壳质量不能大于总质量');
      }
      
      // 贝壳含量=（总质量-去贝壳质量）/ 总质量 * 100 - 含泥量
      let arr = [
        {
          v: data.zzl,
        },{
          k: '-',
          v: data.qbkzl,
        },{
          k: '/',
          v: data.zzl,
        },{
          k: '*',
          v: 100,
        },{
          k: '-',
          v: this.batchForm[this.activeName].hnl,
        }
      ]
      let res = calcEquation(arr, 2)
      this.batchForm[this.activeName][this.activeInfoName][index].jg = res;
      this.setBKHLPJZ();
    },
    setBKHLPJZ(){
      const data = this.batchForm[this.activeName][this.activeInfoName];
      
      let res = this.getPJZUtil(data, 'jg', 2);
      this.batchForm[this.activeName].pjz = res;
      this.batchForm[this.activeName].dxjl = this.conclusion(res);
      //注意：： 如2次结果相差0.5%，则不允许完成
    },
    
    // 氯离子含量
    setLLZHLAll(){
      this.setLLZHL(0)
      this.setLLZHL(1)
    },
    setLLZHL(index){
      // 氯离子含量=硝酸银浓度*（样品体积-空白体积）*换算系数*体积比/样品重量*100
      const data = this.batchForm[this.activeName][this.activeInfoName][index];
      const dataObj = this.batchForm[this.activeName];
      
      if(data.xsynd * 1 > 10000){
        this.batchForm[this.activeName][this.activeInfoName][index].xsynd = 10000;
        this.$message.warning('硝酸银浓度 不能大于10000');
      }
      if(data.yptj * 1 > 10000){
        this.batchForm[this.activeName][this.activeInfoName][index].yptj = 10000;
        this.$message.warning('样品体积 不能大于10000');
      }
      if(data.kbtj * 1 > 10000){
        this.batchForm[this.activeName][this.activeInfoName][index].kbtj = 10000;
        this.$message.warning('空白体积 不能大于10000');
      }
      
      let arr = [
        {
          v: data.xsynd,
        },{
          k: '*',
          v: sub(data.yptj, data.kbtj),
        },{
          k: '*',
          v: dataObj.hsxs || 0.0355,
        },{
          k: '*',
          v: dataObj.tjb || 10,
        },{
          k: '/',
          v: data.ypzl,
        },{
          k: '*',
          v: 100,
        }
      ]
      console.log(arr)
      let res = calcEquation(arr, 3)
      this.batchForm[this.activeName][this.activeInfoName][index].llzhl = res;
      this.setLLZHLPJZ();
    },
    setLLZHLPJZ(){
      const data = this.batchForm[this.activeName][this.activeInfoName];
      
      let res = this.getPJZUtil(data, 'llzhl', 3);
      this.batchForm[this.activeName].pjz = res;
      this.batchForm[this.activeName].dxjl = this.conclusion(res);
    },
    
    // 筛分析
    // 注意 筛余量：手填，数字校验，精确到0.1g
    // 校验公式：筛余量 <= (A * d½) / 200(如超出，文本框标红，保存的时候提示：筛余量异常，请检查！
    // ½：d的平方根
    // A：筛面面积，单位m㎡
    // d：筛孔尺寸，单位mm
    setFJSYAll(type){
      const dataAll = this.batchForm[this.activeName][this.activeInfoName]
      console.log(dataAll)
      for(let i = 0;i <dataAll.length;i++){
        if(dataAll[i]['syl' + type]){
          this.setFJSY(i, type, dataAll[i]['syl' + type], dataAll[i].skgjzj)
        }
      }
    },
    setFJSY(index, type, val, name){
      // if(name == '筛底'){
      //   return false;
      // }
      const data = this.batchForm[this.activeName][this.activeInfoName][index];
      const dataAll = this.batchForm[this.activeName][this.activeInfoName]
      const dataAll2 = this.batchForm[this.activeName]
      
      
      let arr = [
        {
          v: data['syl' + type],
        },{
          k: '/',
          v: type == 1 ? dataAll2.dyfsyzl : dataAll2.defsyzl
        },{
          k: '*',
          v: 100,
        }
      ]
      let res = calcEquation(arr, 1)
      this.batchForm[this.activeName][this.activeInfoName][index]['fjsy' + type] = res;
      
      let num = 0;
      let numLJSY = 0;
      if(data['syl' + type] === ''){
        this.batchForm[this.activeName][this.activeInfoName][index]['ljsy' + type] = '';
      }
      
      if(val){
        if(index > 0){
          numLJSY = add(dataAll[index - 1]['ljsy' + type] || 0, res)
          this.batchForm[this.activeName][this.activeInfoName][index]['ljsy' + type] = numLJSY;
        }else{
          this.batchForm[this.activeName][this.activeInfoName][index]['ljsy' + type] = res;
        }
      }else{
        num = data['ljsy' + type]
      }
      
      this.setFJSYPJZ(index);
    },
    setFJSYPJZ(index){
      const data = this.batchForm[this.activeName][this.activeInfoName][index];
      let arr = [
        {
          v: data.ljsy1,
        },{
          k: '+',
          v: data.ljsy2,
        },{
          k: '/',
          v: 2,
        }
      ]
      let res = calcEquation(arr, 0)
      this.batchForm[this.activeName][this.activeInfoName][index].ljsyppjz = res;
      // this.batchForm[this.activeName].dxjl = this.conclusion(res,conclusion_XGL_LLZHL);
      this.setXDMSPJZ();
    },
    setXDMSPJZ(){
      const data = this.batchForm[this.activeName];
      let num1 = data.sfxInfo.reduce((pre, cur) => {
        let a = cur.skgjzj === '5.00' || cur.skgjzj === '筛底' || !cur.ljsy2 ? 0 : cur.ljsy1
        return add(pre, a)
      }, 0);
      let num2 = data.sfxInfo.reduce((pre, cur) => {
        let a = cur.skgjzj === '5.00' || cur.skgjzj === '筛底' || !cur.ljsy2 ? 0 : cur.ljsy2
        return add(pre, a)
      }, 0);
      
      let jcsfxInfo = {};
      for (var i = 0; i < data.sfxInfo.length; i++) {
        if(data.sfxInfo[i].skgjzj == '5.00'){
          jcsfxInfo = data.sfxInfo[i]
          break;
        }
      }
      console.log(jcsfxInfo)
      let arr1 = [
        {
          v: num1,
        },{
          k: '-',
          v: mul(5, jcsfxInfo.ljsy1),
        },{
          k: '/',
          v: sub(100, jcsfxInfo.ljsy1),
        }
      ]
      let arr2 = [
        {
          v: num2,
        },{
          k: '-',
          v: mul(5, jcsfxInfo.ljsy2),
        },{
          k: '/',
          v: sub(100, jcsfxInfo.ljsy2),
        }
      ]
      let res1 = calcEquation(arr1, 2)
      let res2 = calcEquation(arr2, 2)
      this.batchForm[this.activeName].xdms1 = res1;
      this.batchForm[this.activeName].xdms2 = res2;
      
      let arr3 = [
        {
          v: res1,
        },{
          k: '+',
          v: res2,
        },{
          k: '/',
          v: 2,
        }
      ]
      let res3 = calcEquation(arr3, 1)
      this.batchForm[this.activeName].xdmspjz = res3;
    },
    setKljChart() {
      let kljChart = this.$echarts.init(document.getElementById('kljptfx'));
      kljChart.setOption({
        title: {
          text: ''
        },
        tooltip: {},
        legend: {
          data:['销量']
        },
        xAxis: {
          name: '筛孔尺寸（mm）',
          nameLocation: 'middle',
          nameTextStyle: {
              color: '#000000' // 设置x轴名称的颜色
          },
          nameGap: 25, // 设置x轴名称和轴线之间的距离为30
          type: 'category',
          data: ["123","123","123","11","123","123"],
          
          
          axisLine: {
              onZero: true
          },
          axisTick: {
              show: true,
              alignWidthLabel: true
          },
          boundaryGap: false, // 关闭边界间隙，确保数据点按照实际数值的位置进行绘制
          splitLine: {
              show: true
          }
        },
        yAxis: {
          name: '累计筛余百分率（%）',
          nameLocation: 'middle', // Y 轴名称显示位置
          nameRotate: 90, // Y轴名称旋转角度                        
          nameGap: 40, // Y 轴名称与轴线的距离
          nameTextStyle: {
              fontSize: 10,
              // padding: [0, 0, 0, 10]
          },
          axisLine: {
              show: true, // 显示轴线
              lineStyle: {
                  color: '#5799d3' // 设置轴线颜色
              },
              onZero: true
          },
          axisTick: {
              show: true, // 显示刻度线
              lineStyle: {
                  color: '#5799d3' // 设置刻度线颜色
              }
          },
          splitLine: {
              show: true
          }
          
        },
        series: [{
          name: '',
          type: 'line',
          data: [5, 20, 36, 10, 10, 20],
          itemStyle:{
            normal:{
              lineStyle:{
                width:2,
                type:'dotted'  //'dotted'点型虚线 'solid'实线 'dashed'线性虚线
              }
            }
          }, 
        },{
          name: '',
          type: 'line',
          data: [51, 0, 36, 20, 30, 20],
          itemStyle:{
            normal:{
              lineStyle:{
                width:2,
                type:'dashed'  //'dotted'点型虚线 'solid'实线 'dashed'线性虚线
              }
            }
          }, 
        }],
      });
    },
    set10ysklhl(){
      const data = this.batchForm[this.activeName];
      let arr = [
        {
          v: data.ysklzl10,
        },{
          k: '/',
          v: data.hgsfhsyzl,
        },{
          k: '*',
          v: 100,
        }
      ]
      let res = calcEquation(arr, 0)
      this.batchForm[this.activeName].ysklhl10 = res;
    },
    
    
    //图片
    handlePicSuccess(response, file, fileList) {
      console.log(fileList)
      let oUploadImg = fileList.map(item =>{
        if(item.response){
          return {
            url: item.response.data.filePath,
            uid: item.uid
          }
        }else{
          return item;
          // if(item.url.startsWith(this.filePrefix)){
          //   return item.url;
          // }else{
          //   return this.filePrefix + item.url;
          // }
        }
      })
      
      if(this.activeName === 'quick'){
        this.quickForm.img = oUploadImg;
      }else{
        this.batchForm[this.activeName].img = oUploadImg;
      }
    },
    handlePicRemove(file, fileList) {
      let oUploadImg = fileList.map(item =>{
        if(item.response){
          return {
            url: item.response.data.filePath,
            uid: item.uid
          }
        }else{
          return item
        }
      })
      if(this.activeName === 'quick'){
        this.quickForm.img = oUploadImg;
      }else{
        this.batchForm[this.activeName].img = oUploadImg;
      }
    },
    
    //获取所有快检或批检试验项目名称
    async setExperimentProject(id){
      //获取抗压 抗渗 和其它
      const resDetail = await this.$api.getExperimentDetail({
        experimentId: this.activeId,
        // "testProjectCode":"FINE_AGGREGATE",
        // "checkType": 2
      }, this)
      if(resDetail.succ){
        let quickData = [];
        let batchData = [];//批检
        let quickForm = {};
        quickForm.img = [];
        let batchForm = {};
        resDetail.data.list.forEach(item => {
          let oImgArr = [];
          if(item.objImg && item.objImg != 'null'){
            oImgArr = item.objImg.split(',').map(item => {
              if(item.startsWith(this.filePrefix)){
                return {
                  url: item
                }
              }else{
                return {
                  url: this.filePrefix + item
                }
              }
            }) 
          }else{
            oImgArr = [];
          }
          if(item.testProjectName === '目测含水率' || item.testProjectName === '目测含泥量'
            || item.testProjectName === '目测泥块含量'
          ){
            
            quickData.push(item)
            quickForm[item.testProjectCode] = item;
            quickForm.img = quickForm.img.concat(oImgArr);
          }else{
            if(!item.objJson?.jcrq){
              item.objJson.jcrq = this.defaultDate;
            }
            item.objJson.img = oImgArr;
            batchForm[item.testProjectCode] = JSON.parse(JSON.stringify(item.objJson));
            item.objJson = undefined;
            batchData.push(item);
          }
        })
        this.quickData = quickData;
        this.quickForm = quickForm;
        this.batchData = batchData;
        this.batchForm = this.setDefaultVal(batchForm);
        
        console.log(this.batchForm,this.batchData)
        if(this.quickData.length > 0){
          this.activeName = 'quick'
        }else{
          this.activeName = this.batchData[0].testProjectCode;
          this.handleClick();
        }
        
      }  
    },
    getAccordingToHand(){
      this.$api.getAccordingTo({
        testProjectCode: this.activeName,
        materialAbbreviation : this.$parent.activeData.materialAbbreviation,
        materialsName	 : this.$parent.activeData.materialsName,
        materialsSpec : this.$parent.activeData.materialsSpecs
      }, this).then(res =>{
        if(res.data.list.length > 0){
          let o = res.data.list[0].objJson;
          this.accordingObj[this.activeName] = JSON.parse(o);
        }else{
          this.accordingObj[this.activeName] = {};
        }
      })
    },

        setDefaultTime(val){

          // 粗骨料、细骨料：泥块含量、表观密度=委托时间+1天，其他的都是委托当天
      // 表观密度
      if(val.FINE_AGGREGATE_PARAM_XGL_BGMD && this.isEmptyValue(val.FINE_AGGREGATE_PARAM_XGL_BGMD?.jcrq)){
        val.FINE_AGGREGATE_PARAM_XGL_BGMD['jcrq'] = util.calculateFutureDate(this.entrustTime, 1)
      }
      // 泥块含量
      if(val.FINE_AGGREGATE_PARAM_XGL_NKHL && this.isEmptyValue(val.FINE_AGGREGATE_PARAM_XGL_NKHL?.jcrq)){
        val.FINE_AGGREGATE_PARAM_XGL_NKHL['jcrq'] = util.calculateFutureDate(this.entrustTime, 1)
      }
      
      // 含泥量
      if(val.FINE_AGGREGATE_PARAM_XGL_HNL && this.isEmptyValue(val.FINE_AGGREGATE_PARAM_XGL_HNL?.jcrq)){
        val.FINE_AGGREGATE_PARAM_XGL_HNL['jcrq'] = util.calculateFutureDate(this.entrustTime, 0)
      }
          // 石粉
      if(val.FINE_AGGREGATE_PARAM_XGL_SFHL && this.isEmptyValue(val.FINE_AGGREGATE_PARAM_XGL_SFHL?.jcrq)){
        val.FINE_AGGREGATE_PARAM_XGL_SFHL['jcrq'] = util.calculateFutureDate(this.entrustTime, 0)
      }
      
      // 含水率
      if(val.FINE_AGGREGATE_PARAM_XGL_HSL && this.isEmptyValue(val.FINE_AGGREGATE_PARAM_XGL_HSL?.jcrq)){
        val.FINE_AGGREGATE_PARAM_XGL_HSL['jcrq'] = util.calculateFutureDate(this.entrustTime, 0)
      }
      
      // 堆积密度
      if(val.FINE_AGGREGATE_PARAM_XGL_DJMD && this.isEmptyValue(val.FINE_AGGREGATE_PARAM_XGL_DJMD?.jcrq)){
        val.FINE_AGGREGATE_PARAM_XGL_DJMD['jcrq'] = util.calculateFutureDate(this.entrustTime, 0)
      }
      // 紧密密度
      if(val.FINE_AGGREGATE_PARAM_XGL_JMMD && this.isEmptyValue(val.FINE_AGGREGATE_PARAM_XGL_JMMD?.jcrq)){
        val.FINE_AGGREGATE_PARAM_XGL_JMMD['jcrq'] = util.calculateFutureDate(this.entrustTime, 0)
      }


      // 压碎值指标
      if(val.FINE_AGGREGATE_PARAM_XGL_YSZZB && this.isEmptyValue(val.FINE_AGGREGATE_PARAM_XGL_YSZZB?.jcrq)){
        val.FINE_AGGREGATE_PARAM_XGL_YSZZB['jcrq'] = util.calculateFutureDate(this.entrustTime, 0)
      }
      
      // 云母
      if(val.FINE_AGGREGATE_PARAM_XGL_YMHL && this.isEmptyValue(val.FINE_AGGREGATE_PARAM_XGL_YMHL?.jcrq)){
        val.FINE_AGGREGATE_PARAM_XGL_YMHL['jcrq'] = util.calculateFutureDate(this.entrustTime, 0)
      }
      
      // 氯离子
      if(val.FINE_AGGREGATE_PARAM_XGL_LLZHL && this.isEmptyValue(val.FINE_AGGREGATE_PARAM_XGL_LLZHL?.jcrq)){
        val.FINE_AGGREGATE_PARAM_XGL_LLZHL['jcrq'] = util.calculateFutureDate(this.entrustTime, 0)
      }

      // 贝壳
      if(val.FINE_AGGREGATE_PARAM_XGL_BKHL && this.isEmptyValue(val.FINE_AGGREGATE_PARAM_XGL_BKHL?.jcrq)){
        val.FINE_AGGREGATE_PARAM_XGL_YMHL['jcrq'] = util.calculateFutureDate(this.entrustTime, 0)
      }
      
      // 筛分析
      if(val.FINE_AGGREGATE_PARAM_XGL_SFX && this.isEmptyValue(val.FINE_AGGREGATE_PARAM_XGL_SFX?.jcrq)){
        val.FINE_AGGREGATE_PARAM_XGL_SFX['jcrq'] = util.calculateFutureDate(this.entrustTime, 0)
      }

  

        },
    //设置默认值
    setDefaultVal(val){
      console.log(val)
      this.setDefaultTime(val)
      if(val.FINE_AGGREGATE_PARAM_XGL_BGMD?.qysl === ''){
        val.FINE_AGGREGATE_PARAM_XGL_BGMD.qysl = '600'
      }
      if(val.FINE_AGGREGATE_PARAM_XGL_BGMD?.bgmdInfo[0].hgsyzl === ''){
        val.FINE_AGGREGATE_PARAM_XGL_BGMD.bgmdInfo[0].hgsyzl = '300'
      }
      if(val.FINE_AGGREGATE_PARAM_XGL_BGMD?.sw === ''){
        val.FINE_AGGREGATE_PARAM_XGL_BGMD.sw = 15;
        val.FINE_AGGREGATE_PARAM_XGL_BGMD.xzxs = XGL_SZXS[15];
        // val.FINE_AGGREGATE_PARAM_XGL_BGMD.bgmdInfo[0].xzxs = XGL_SZXS['15'];
        // val.FINE_AGGREGATE_PARAM_XGL_BGMD.bgmdInfo[1].xzxs = XGL_SZXS['15'];
      }else if(val.FINE_AGGREGATE_PARAM_XGL_BGMD?.xzxs === ''){
        val.FINE_AGGREGATE_PARAM_XGL_BGMD.xzxs = XGL_SZXS[val.FINE_AGGREGATE_PARAM_XGL_BGMD.sw];
        // val.FINE_AGGREGATE_PARAM_XGL_BGMD.bgmdInfo[0].xzxs = XGL_SZXS[val.FINE_AGGREGATE_PARAM_XGL_BGMD.sw];
        // val.FINE_AGGREGATE_PARAM_XGL_BGMD.bgmdInfo[1].xzxs = XGL_SZXS[val.FINE_AGGREGATE_PARAM_XGL_BGMD.sw];
      }
      if(val.FINE_AGGREGATE_PARAM_XGL_BGMD?.sbgmd === ''){
        val.FINE_AGGREGATE_PARAM_XGL_BGMD.sbgmd = '1000';
      }
      if(val.FINE_AGGREGATE_PARAM_XGL_YSZZB?.yszzbList){
        // val.FINE_AGGREGATE_PARAM_XGL_YSZZB?.yszzbList?.forEach(item =>{
        //   item?.data.forEach(itemC =>{
        //     if(itemC.syzl == ''){
        //       itemC.syzl = '1000'
        //     }
        //   })
        // })
      }
      if(val.FINE_AGGREGATE_PARAM_XGL_DJMD && val.FINE_AGGREGATE_PARAM_XGL_DJMD?.qyzl === ''){
        val.FINE_AGGREGATE_PARAM_XGL_DJMD.qyzl = '3';
        val.FINE_AGGREGATE_PARAM_XGL_DJMD.qysl = '3';
      }
      if(val.FINE_AGGREGATE_PARAM_XGL_JMMD && val.FINE_AGGREGATE_PARAM_XGL_JMMD?.qysl === ''){
        val.FINE_AGGREGATE_PARAM_XGL_JMMD.qysl = '3';
      }
      
      
      if(val.FINE_AGGREGATE_PARAM_XGL_SFX?.dyfsyzl === ''){
        val.FINE_AGGREGATE_PARAM_XGL_SFX.dyfsyzl = '500';
      }
      if(val.FINE_AGGREGATE_PARAM_XGL_SFX?.defsyzl === ''){
        val.FINE_AGGREGATE_PARAM_XGL_SFX.defsyzl = '500';
      }
      
      // if(val.FINE_AGGREGATE_PARAM_XGL_LLZHL?.qysl === ''){
      //   val.FINE_AGGREGATE_PARAM_XGL_LLZHL.qysl = '500';
      // }
      if(val.FINE_AGGREGATE_PARAM_XGL_LLZHL?.hsxs === ''){
        val.FINE_AGGREGATE_PARAM_XGL_LLZHL.hsxs = '0.0355';
      }
      if(val.FINE_AGGREGATE_PARAM_XGL_LLZHL?.tjb === ''){
        val.FINE_AGGREGATE_PARAM_XGL_LLZHL.tjb = '10';
      }
      if(val?.FINE_AGGREGATE_PARAM_XGL_HNL){
        val.FINE_AGGREGATE_PARAM_XGL_HNL.syjgInfo.syzl = '200';
      }
      if (val.FINE_AGGREGATE_PARAM_XGL_SFX) {
        let sfxInfo = val.FINE_AGGREGATE_PARAM_XGL_SFX.sfxInfo || [];
        for (let element of sfxInfo) {
          if (element.syl1) {
            element.syl1 = cpEvenRound(element.syl1, 0);
          }
          if (element.syl2) {
            element.syl2 = cpEvenRound(element.syl2, 0);
          }
          if (element.ljsy1) {
            element.ljsy1 = cpEvenRound(element.ljsy1, 1);
          }
          if (element.ljsy2) {
            element.ljsy2 = cpEvenRound(element.ljsy2, 1);
          }
          if (element.fjsy1) {
            element.fjsy1 = cpEvenRound(element.fjsy1, 1);
          }
          if (element.fjsy2) {
            element.fjsy2 = cpEvenRound(element.fjsy2, 1);
          }
          if (element.ljsyppjz) {
            element.ljsyppjz = cpEvenRound(element.ljsyppjz, 0);
          }
        }
      }
      return val;
    },
    
    handleClick(tab, event){
      this.getAccordingToHand()
      
      if(this.activeName === 'FINE_AGGREGATE_PARAM_XGL_BKHL'){
        if(!this.batchForm.FINE_AGGREGATE_PARAM_XGL_HNL.hnlpjz ){
           this.$message.warning("请先做含泥量试验。")
        }else{
          this.batchForm.FINE_AGGREGATE_PARAM_XGL_BKHL.hnl = this.batchForm.FINE_AGGREGATE_PARAM_XGL_HNL.hnlpjz
        }
      }
      switch (this.activeName){
        case 'FINE_AGGREGATE_PARAM_XGL_HNL':
          this.activeInfoName = 'hnljzsInfo'
          break;
        case 'FINE_AGGREGATE_PARAM_XGL_NKHL':
          this.activeInfoName = 'hnkInfo'
          break;
        case 'FINE_AGGREGATE_PARAM_XGL_HSL':
          this.activeInfoName = 'hslInfo'
          break;
        case 'FINE_AGGREGATE_PARAM_XGL_BGMD':
          this.activeInfoName = 'bgmdInfo'
          break;
        case 'FINE_AGGREGATE_PARAM_XGL_DJMD':
          this.activeInfoName = 'djmdInfo'
          break;
        case 'FINE_AGGREGATE_PARAM_XGL_JMMD':
          this.activeInfoName = 'jmmdInfo'
          break;
        case 'FINE_AGGREGATE_PARAM_XGL_YMHL':
          this.activeInfoName = 'ymhlInfo'
          break;
        case 'FINE_AGGREGATE_PARAM_XGL_BKHL':
          this.activeInfoName = 'bkhlInfo'
          break;
        case 'FINE_AGGREGATE_PARAM_XGL_LLZHL':
          this.activeInfoName = 'llzhlInfo'
          break;
        case 'FINE_AGGREGATE_PARAM_XGL_SFX':
          this.activeInfoName = 'sfxInfo'
          break;
        case 'FINE_AGGREGATE_PARAM_XGL_YSZZB':
          this.activeInfoName = 'yszzbList'
          break;
          
          
          
        default:
          break;
      }
    },
    clearData(){
      this.activeName= 'quick';
      this.quickForm= {};
      this.quickData= [];
      this.batchForm= {};
      this.batchData= [];
    },
    handlePreview(file) {
      this.previewUrl = file.url
      this.imgViewerVisible = true;
    },
    onClose() {
      this.imgViewerVisible = false;
    },
  },
};
</script>
<style scoped lang="scss">
  .flex-item2{
    flex: 2;
  }
  .line{
    height: 1px;
    width: 100%;
    background: #E8E8E8;
    margin: 24px 0;
  }
  .info-form-title{
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 14px;
    color: #1F2329;
    line-height: 20px;
    text-align: left;
    font-style: normal;
    padding: 10px 0 4px;
    margin-bottom: 4px;
  }
  .info-form-piece{
    background: #F8F8F8;
    border-radius: 8px;
    padding: 16px 16px 8px 16px;
  }
  .el-form-item{
    margin-bottom: 8px;
  }
  
  ::v-deep .el-input-group__append, 
  ::v-deep .el-input-group__prepend{
    padding: 0 5px;
    text-align: center;
    width: 40px;
  }
  ::v-deep .pr0{
    .el-input__inner{
      padding-right: 0;
    }
  }
  ::v-deep .el-input-number.is-controls-right .el-input__inner{
    text-align: left;
  }
  ::v-deep .textspan .el-input__inner{
    background: transparent;
    border: none;
    padding: 0;
    color: #000;
    margin-left: -10px;
    margin-top: -2px;
  }
  
  
  ::v-deep .el-upload-list__item{
    transition: none !important; 
  }
  
  
  ::v-deep .custom-form{
    .el-form--inline{
      .table-box-kzf{
        .el-form-item{
          margin-right: 5px;
        }
      }
      .el-form-item{
        margin-bottom: 8px;
      }
    }
  }
  .table-box-kzf{
    width: 100%;
    background: #FFFFFF;
    border-radius: 4px;
    border: 1px solid #DDDFE6;
    
    .tb-title{
      height: 52px;
      background: #F8F8F8;
      border-radius: 4px 4px 0px 0px;
      border-bottom: 1px solid #DDDFE6;
      line-height: 52px;
      padding-left: 16px;
      font-weight: 600;
      font-size: 14px;
      color: #1F2329;
      .tb-title-lef{
        width: 100px;
        border-right: 1px solid #DDDFE6;
      }
      .tb-title-con{
        text-align: center;
        line-height: 20px;
        padding-top: 8px;
        border-right: 1px solid #DDDFE6;        span{
          display: inline-block;
          width: 100%;
          &:nth-child(2),&:nth-child(3){
            font-weight: 400;
            font-size: 12px;
            color: #6A727D;
            width: 50%;
          }
        }
      }
      .tb-title-bom{
        text-align: center;
        width: 172px;
      }
    }
    .tb-bottom{
      border-top: 1px solid #DDDFE6;
      padding: 16px 16px 8px;
    }
    .tb-left{
      line-height: 40px;
      border-right: 1px solid #DDDFE6;
      width: 116px;
      text-align: center;
    }
    .tb-content{
      .tb-left,.el-form-item{
        // padding-bottom: 8px;
      }
      .el-form-item{
        padding-left: 16px;
      }
      .tb-item:first-child{
        .tb-left,.el-form-item{
          padding-top: 24px;
        }
      }
      .tb-item:last-child{
        .tb-left,.el-form-item{
          padding-bottom: 16px;
        }
      }
    }
    .tb-center{
      padding: 24px 0;
      margin: 0 8px;
      &>div:first-child{
        margin-bottom: 8px;
      }
    }
    .tb-right{
      height: 88px;
      margin-top: 24px;
      border-left: 1px solid #DDDFE6;
      line-height: 40px;
      padding: 0 16px;
      &>p:first-child{
        margin-bottom: 8px;
      }
    }
  }
</style>