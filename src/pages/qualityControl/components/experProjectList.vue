<template>
  <div>
    <el-tabs v-model="activeName">
      <el-tab-pane v-if="quickData.length > 0 && isXnbg != 1" label="快检" name="quick"></el-tab-pane>
      <template v-if="projectList.length > 0">
        <el-tab-pane v-for="(item, index) in projectList" :key="item.testProjectCode" :label="item.testProjectName" :name="item.testProjectCode">
        </el-tab-pane>
      </template>
      
      <!-- <el-tab-pane label="凝结时间" name="third"></el-tab-pane>
      <el-tab-pane label="安定性" name="fourth">定时任务补偿</el-tab-pane>
      <el-tab-pane label="强度测定" name="5">定时任务补偿</el-tab-pane>
      <el-tab-pane label="细度" name="6">定时任务补偿</el-tab-pane> -->
    </el-tabs>
    <div v-show="activeName === 'quick'">
      <el-form ref="quickForm"  :model="quickForm" :disabled="loading2 || experimentStatus == 3">
        <el-row>
          <el-col :span="24" style="margin-bottom: 8px;">
            <el-form-item :label="item.testProjectName" v-for="item in quickData" :key="item.testProjectCode">
               <!-- v-model="item.objJson." -->
              <el-radio-group v-model="quickForm[item.testProjectCode].objJson.val" 
                v-if="item.testProjectName === '流动性' 
                || item.testProjectName === '保水性' 
                || item.testProjectName === '粘聚性'"
              >
                <el-radio label="fcc">非常差</el-radio>
                <el-radio label="yb">一般</el-radio>
                <el-radio label="hh">良好</el-radio>
              </el-radio-group>
              
              <template v-else-if="item.testProjectName === '实测坍落度' || item.testProjectName === '坍落度'">
                <el-input
                  type="number"
                  v-model="quickForm[item.testProjectCode].objJson.tld1" 
                  clearable
                  placeholder="请输入"
                  :style="{'width': 150 + 'px'}"
                >
                </el-input>
                <!-- <el-select
                  v-model="quickForm[item.testProjectCode].objJson.tld1" 
                  filterable 
                  :style="{'width': 150 + 'px'}"
                  placeholder="请选择" >
                  <el-option
                    v-for="item in 13"
                    :key="item"
                    :label="70 + item * 10"
                    :value="70 + item * 10">
                  </el-option>
                </el-select> -->
                
                <!-- <span>&nbsp;&nbsp;±&nbsp;&nbsp;</span>
                <el-select
                  v-model="quickForm[item.testProjectCode].objJson.tld2" 
                  filterable 
                  :style="{'width': 80 + 'px'}"
                  placeholder="差值" >
                  <el-option
                    v-for="item in 2"
                    :key="item"
                    :label="10 + item * 10"
                    :value="10 + item * 10">
                  </el-option>
                </el-select>

                <span>&nbsp;&nbsp;&nbsp;&nbsp;方式：</span>
                <el-radio-group  v-model="quickForm[item.testProjectCode].objJson.tldfs">
                  <el-radio label="目测">目测</el-radio>
                  <el-radio label="实测">实测</el-radio>
                </el-radio-group> -->
              </template>
              <template v-else-if="item.testProjectName === '目测砂率'">
                <el-input
                  v-model="quickForm[item.testProjectCode].objJson.sl" 
                  clearable
                  placeholder="目测砂率"
                  style="width: 253px; margin-left: 14px;"
                >
                  <template slot="append">%</template>
                </el-input>
              </template>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="图片：">
              <div class="cbo">
                <div class="img-box">
                  <el-upload
                    accept=".jpeg,.png,.jpg,.bmp,.gif"
                    :action="baseUrl + '/upload/file'"
                    list-type="picture-card"
                    :file-list="quickForm.img"
                    :on-preview="handlePreview"
                    :on-success="kjHandlePicSuccess"
                    :on-remove="kjHandlePicRemove">
                    <i class="el-icon-plus"></i>
                  </el-upload>
                  <!-- <span>抽样图片</span> -->
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div v-show="activeName === 'CONCRETE_PARAM_KYQD'">
      <el-form label-width="90px" :inline="true" ref="searchForm6" :model="searchForm6" :disabled="loading2 || experimentStatus == 3">
        <el-row :gutter="12">
          <el-col :span="8" >
            <el-form-item label="检测日期：" class="flex-item flex-box">
              <el-date-picker
                type="date"
                v-model="searchForm6.jcrq"
                placeholder="选择日期"
                format="yyyy-MM-dd"
                :default-value="new Date()"
                value-format="yyyy-MM-dd"
                style="width: 135px;"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8" style="margin-bottom: 8px;">
            <el-form-item label="试件尺寸：">
              <!-- <el-input v-model="searchForm6.sjcc" >
              </el-input> -->
              <el-select
                style="height: 40px;"
                v-model="searchForm6.sjcc" 
                filterable 
                @change="changeSJCC"
                placeholder="请选择试件尺寸" >
                <el-option
                  v-for="item in sjccOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" style="margin-bottom: 8px;">
            <el-form-item label="样品等级：">
              <span style="display: inline-block; min-width: 150px;">{{sampleLevel}}</span>
            </el-form-item>
          </el-col>
          
          <el-col :span="12" style="margin-bottom: 8px;">
           <div style="background: #F8F8F8;border-radius: 8px; padding: 16px;">
             <p>强度-7d</p>
             <div v-for="(item,index) in qdInfoList7d">
               <el-form-item :label="index + 1 + '、 荷载：'">
                 <!-- v-manual-update
                 @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                 @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue" -->
                 <el-input v-model="item.hz" 
                  v-manual-update
                  @keyup.native="e=> e.target.value = e.target.value * 1 > 1501 ? e.target.value.match(/100|\d{1,1}/) : e.target.value.match(/\d+\.?\d{0,1}/)"
                  @change="qdInfoList7dComHandleChange(index)"
                  style="width: 130px; margin-bottom: 8px;">
                   <template slot="append">kN</template>
                 </el-input>
               </el-form-item>
               <el-form-item label="强度：">
                 <el-input disabled v-model="qdInfoList7dCom[index].qd" style="width: 120px; margin-bottom: 8px;">
                   <template slot="append">MPa</template>
                 </el-input>
               </el-form-item>
             </div>
             
             <el-form-item label="平均值：">
               <el-input disabled v-model="searchForm6.pjz7d" style="width: 130px; margin-bottom: 8px;">
                 <template slot="append">MPa</template>
               </el-input>
             </el-form-item>
             <el-form-item label="折算系数：">
               <el-input v-model="searchForm6.zsxs7d" style="width: 120px; margin-bottom: 8px;" disabled>
                 <template slot="append">%</template>
               </el-input>
             </el-form-item>
             <el-form-item label="折算标准强度：" label-width="130px">
               <el-input v-model="searchForm6.zsbzqd7d" style="width: 130px; margin-bottom: 8px;" disabled>
                 <template slot="append">MPa</template>
               </el-input>
             </el-form-item>
             <div style="width: 110px;"></div>
             <el-form-item label="到达设计强度：" label-width="130px">
               <el-input disabled v-model="searchForm6.fyxs7d" style="width: 130px; margin-bottom: 8px;">
                 <template slot="append">%</template>
               </el-input>
             </el-form-item>
             
             <el-form-item label="单项结论：">
               <el-input v-model="searchForm6.dxjl7d" 
                style="width: 120px; margin-bottom: 8px;">
               </el-input>
             </el-form-item>
           </div>
            
            
            
          </el-col>
          
          <el-col :span="12" style="margin-bottom: 8px;">
            <div style="background: #F8F8F8;border-radius: 8px; padding: 16px;">
              <!-- <p>强度-28d</p> -->
              <p>强度-{{ searchForm6.lq || '28' }}d</p>
              <div v-for="(item, index) in qdInfoList28d">
                <el-form-item :label="index + 1 + '、 荷载：'">
                  <el-input 
                    v-manual-update
                    @keyup.native="e=> e.target.value = e.target.value * 1 > 1501 ? e.target.value.match(/100|\d{1,1}/) : e.target.value.match(/\d+\.?\d{0,1}/)"
                    @change="qdInfoList28dComHandleChange(index)"
                    v-model="item.hz" style="width: 130px; margin-bottom: 8px;">
                    <template slot="append">kN</template>
                  </el-input>
                </el-form-item>
                <el-form-item label="强度：">
                  <el-input disabled v-model="qdInfoList28dCom[index].qd" style="width: 120px; margin-bottom: 8px;">
                    <template slot="append">MPa</template>
                  </el-input>
                </el-form-item>
              </div>
              
              <el-form-item label="平均值：">
                <el-input disabled v-model="searchForm6.pjz28d" style="width: 130px; margin-bottom: 8px;">
                  <template slot="append">MPa</template>
                </el-input>
              </el-form-item>
              <el-form-item label="折算系数：">
                <el-input v-model="searchForm6.zsxs28d" style="width: 120px; margin-bottom: 8px;" disabled>
                  <template slot="append">%</template>
                </el-input>
              </el-form-item>
              
              <el-form-item label="折算标准强度：" label-width="130px">
                <el-input v-model="searchForm6.zsbzqd28d" style="width: 130px; margin-bottom: 8px;" disabled>
                  <template slot="append">MPa</template>
                </el-input>
              </el-form-item>
              <div style="width: 110px;"></div>
              
              <el-form-item label="到达设计强度：" label-width="130px">
                <el-input disabled v-model="searchForm6.fyxs28d" style="width: 130px; margin-bottom: 8px;">
                  <template slot="append">%</template>
                </el-input>
              </el-form-item>
              <el-form-item label="单项结论：">
                <el-input v-model="searchForm6.dxjl28d" 
                 style="width: 120px; margin-bottom: 8px;">
                </el-input>
              </el-form-item>
            </div>
          </el-col>
        </el-row>
        
        <el-form-item label="图片：">
          <div class="cbo">
            <div class="img-box">
                <!-- :show-file-list="false" -->
              <el-upload
                accept=".jpeg,.png,.jpg,.bmp,.gif"
                :action="baseUrl + '/upload/file'"
                list-type="picture-card"
                :file-list="searchForm6.img"
                :on-preview="handlePreview"
                :on-success="kyqdHandlePicSuccess"
                :on-remove="kyqdHandlePicRemove">
                <i class="el-icon-plus avatar-uploader-icon"></i>
              </el-upload>
              <!-- <span>抽样时颜色</span> -->
            </div>
            <!-- <div class="img-box">
              <img src="" alt="" />
              <span>抽样时颜色抽样时颜色抽样时颜色抽样时颜色</span>
            </div> -->
          </div>
        </el-form-item>
      </el-form>
    </div>
    <div v-show="activeName === 'CONCRETE_PARAM_KSDJ'">
      <el-form :inline="true" ref="searchForm7" :model="searchForm7" :disabled="loading2 || experimentStatus == 3">
        <el-row>
          <el-col :span="6" >
            <el-form-item label="检测日期：" class="flex-item flex-box">
              <el-date-picker
                type="date"
                v-model="searchForm7.jcrq"
                placeholder="选择日期"
                format="yyyy-MM-dd"
                :default-value="new Date()"
                value-format="yyyy-MM-dd"
                style="width: 135px;"
                @input="$forceUpdate()"
                @change="ksdjJcrqChange"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="16" style="margin-bottom: 8px;">
            <el-form-item label="龄期：">
              <el-input-number style="width: 200px; text-align: left;" v-model="searchForm7.lq" controls-position="right" :min="1" :step="1" step-strictly>
                <template slot="append">天</template>
              </el-input-number><span style="margin-left: 10px;">天</span>

            </el-form-item>
            
            <el-form-item label="抗渗要求：">
              <el-input class="textspan" type="text" disabled style="width: 100px; text-align: left; background: none; border: none;" v-model="searchForm7.ksyq" >
              </el-input>
            </el-form-item>
            <el-form-item label="实测抗渗等级：">
              <el-input class="textspan" type="text" disabled style="width: 100px; text-align: left; background: none; border: none;" v-model="searchForm7.ssksdj" >
              </el-input>
            </el-form-item>
            
          </el-col>
          <el-col :span="2" style="margin-bottom: 8px; text-align: right;">
            <el-button type="primary" @click="addRow()">新增渗水记录</el-button>
          </el-col>
        </el-row>
        
        
        <el-table class="noempty" ref="KSDJTable" :data="ssjlInfoList" border winth="100%;">
          <el-table-column type="index" align="center" width="40">
          </el-table-column>
          
          <el-table-column prop="syyl" label="试验压力" width="120" align="center">
            <template slot-scope="{row}">
              <el-input
                type="number"
                v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                :disabled="experimentStatus == 3"
                class="pr0"
                v-model="row.syyl" 
                :placeholder="`试验压力`"
                style="width: 90px;"
              >
              </el-input>
            </template>
          </el-table-column>
          
          <el-table-column prop="jysj" label="加压时间" min-width="180" align="center">
            <template slot-scope="{row,$index}">
              <el-date-picker
                v-model="row.jysj"
                :disabled="experimentStatus == 3"
                style="width: 200px;"
                type="datetime"
                format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyy-MM-dd HH:mm:ss"
                placeholder="选择日期时间">
              </el-date-picker>
            </template>
          </el-table-column>
          
          
          <el-table-column :prop="item.prop" v-for="(item,index) in ssjlInfoListColumn" :key="index" :label="item.label"align="center">
            <template slot-scope="{row}">
              <div v-if="item.prop == 'zbr'">
                <el-select v-model="row[item.prop]" :disabled="experimentStatus == 3" placeholder="请选择">
                  <el-option v-for="(zbr,index) in zbrOptions" :key="index" :label="zbr" :value="zbr">
                  </el-option>
                </el-select>
              </div>
              <div v-else>
                <el-select v-model="row[item.prop]" :disabled="experimentStatus == 3" placeholder="请选择">
                  <el-option label="是" value="是">
                  </el-option>
                  <el-option label="否" value="否">
                  </el-option>
                </el-select>
              </div>
            </template>
          </el-table-column>
          <!-- <el-button type="primary" plain size="mini" @click="addRow(scope.$index,scope.row)"
            style="margin-left: 4px">添加</el-button> -->
          <el-table-column label="操作" align="center" width="80" fixed="right">
            <template slot-scope="scope">
              <div style="display: flex; justify-content: center">
                <el-button type="text" plain size="mini"
                  @click="deleteRow(scope.$index,scope.row)" style="margin-left: 4px">删除</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
        
        
        <el-form-item label="图片：">
          <div class="cbo">
            <div class="img-box" style="margin-top: 10px;">
              <el-upload
                accept=".jpeg,.png,.jpg,.bmp,.gif"
                :action="baseUrl + '/upload/file'"
                list-type="picture-card"
                :on-preview="handlePreview"
                :file-list="searchForm7.img"
                :on-success="ksdjHandlePicSuccess"
                :on-remove="ksdjHandlePicRemove">
                <i class="el-icon-plus"></i>
              </el-upload>
              <!-- <span>抽样时颜色</span> -->
            </div>
            <!-- <div class="img-box">
              <img src="" alt="" />
              <span>抽样时颜色抽样时颜色抽样时颜色抽样时颜色</span>
            </div> -->
          </div>
        </el-form-item>
      </el-form>
    </div>
    <!-- 氯离子含量 -->
    <div v-if="activeName === 'CONCRETE_PARAM_XNBG_LLZHL'">
      <el-form :disabled="loading2 || experimentStatus == 3" :inline="true" class="mt16" label-width="100px">
        <el-row :gutter="12">
          <el-col :span="8" class="flex-row" style="justify-content: flex-start;">
            <el-form-item label-width="110" label="检测日期：" class="flex-item flex-box">
              <el-date-picker
                type="date"
                v-model="llzFormData.jcrq"
                placeholder="选择日期"
                format="yyyy-MM-dd"
                :default-value="new Date()"
                value-format="yyyy-MM-dd"
                style="width: 135px;"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8" style="margin-bottom: 8px;">
            <el-form-item label-width="110" label="滴定滤液量：">
              <el-input @input="(value)=>{handleInput(value, 2, -1, 'llz-ddlyhl')}" @blur="()=>{llzTextFieldBlur('ddl',-1)}" v-model="llzFormData.ddlyhl" style="width: 120px; margin-bottom: 8px;">
                <template slot="append">ml</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" style="margin-bottom: 8px;">
            <el-form-item label-width="240" label="硝酸银标准溶液浓度：">
              <el-input @input="(value)=>{handleInput(value, 4, -1, 'llz-rynd')}" @blur="()=>{llzTextFieldBlur('rynd',-1)}" v-model="llzFormData.xsybzrynd" style="width: 160px; margin-bottom: 8px;">
                <template slot="append">mol/L</template>
              </el-input>
            </el-form-item>
          </el-col>

        </el-row>
        <el-row :gutter="12">
          
          <el-col :span="24" style="margin-bottom: 8px;">
            <div v-if="llzFormData.phbInfo" style="background: #F8F8F8;border-radius: 8px; padding: 16px;">
              <p style="font-weight: 600;">配比信息</p>
              <p style="margin-top: 10px; font-weight: 600;">凝胶材料</p>
              <div style="margin-top: 10px;">
                  <el-form-item label-width="110" label="矿渣粉：">
                    <el-input @blur="()=>{llzTextFieldBlur('kzf', -1)}" @input="(value)=>{handleInput(value, 0, -1, 'llz-kzf')}" v-model="llzFormData.phbInfo.kzf" style="width: 120px; margin-bottom: 8px;">
                      <template slot="append">kg</template>
                    </el-input>
                  </el-form-item>
                  <el-form-item label-width="110" label="粉煤灰：">
                    <el-input @blur="()=>{llzTextFieldBlur('fmh', -1)}" @input="(value)=>{handleInput(value, 0, -1, 'llz-fmh')}" v-model="llzFormData.phbInfo.fmh" style="width: 120px; margin-bottom: 8px;">
                      <template slot="append">kg</template>
                    </el-input>
                  </el-form-item>
                  <el-form-item label-width="110" label="水泥：">
                    <el-input  @blur="()=>{llzTextFieldBlur('sn', -1)}" @input="(value)=>{handleInput(value, 0, -1, 'llz-sn')}" v-model="llzFormData.phbInfo.sn" style="width: 120px; margin-bottom: 8px;">
                      <template slot="append">kg</template>
                    </el-input>
                  </el-form-item>
                  <el-form-item label-width="110" label="胶凝材料总和：">
                    <el-input disabled v-model="llzFormData.phbInfo.njclzl" style="width: 120px; margin-bottom: 8px;">
                      <template slot="append">kg</template>
                    </el-input>
                  </el-form-item>
                </div>
              <p style="margin-top: 10px; font-weight: 600;">其他</p>
              <div style="margin-top: 10px;">
                  <el-form-item label-width="110" :label="'粗骨料：'">
                    <el-input @blur="()=>{llzTextFieldBlur('cgl', -1)}" @input="(value)=>{handleInput(value, 0, -1, 'llz-cgl')}" v-model="llzFormData.phbInfo.cgl" style="width: 120px; margin-bottom: 8px;">
                      <template slot="append">kg</template>
                    </el-input>
                  </el-form-item>
                  <el-form-item label-width="110" label="细骨料：">
                    <el-input @input="(value)=>{handleInput(value, 0, -1, 'llz-xgl')}" v-model="llzFormData.phbInfo.xgl" style="width: 120px; margin-bottom: 8px;">
                      <template slot="append">kg</template>
                    </el-input>
                  </el-form-item>
                  <el-form-item label-width="310" label="水：">
                    <el-input @blur="()=>{llzTextFieldBlur('s', -1)}" @input="(value)=>{handleInput(value, 0, -1, 'llz-s')}" v-model="llzFormData.phbInfo.s" style="width: 120px; margin-bottom: 8px;">
                      <template slot="append">kg</template>
                    </el-input>
                  </el-form-item>
                  <el-form-item label-width="110" label="外加剂：">
                    <el-input @input="(value)=>{handleInput(value, 2, -1, 'llz-wjj')}" v-model="llzFormData.phbInfo.wjj" style="width: 120px; margin-bottom: 8px;">
                      <template slot="append"><kbd></kbd>kg</template>
                    </el-input>
                  </el-form-item>
                </div>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="12">
          <el-col :span="24" style="margin-bottom: 8px;">
            <div style="background: #F8F8F8;border-radius: 8px; padding: 16px;">
              <p style="margin-top: 10px; font-weight: 600;">试验</p>
              <el-row>
                <el-form-item label-width="110" :label="'滴定值1：'">
                  <el-input @input="(value)=>{handleInput(value, 2, -1, 'llz-ddz1')}" v-model="llzFormData.synInfo.ddz1" style="width: 120px; margin-bottom: 8px; margin-left: 20px;">
                    <template slot="append">ml</template>
                  </el-input>
                </el-form-item>
                <el-form-item label-width="110" label="滴定值2：">
                  <el-input @input="(value)=>{handleInput(value, 2, -2, 'llz-ddz2')}" v-model="llzFormData.synInfo.ddz2" style="width: 120px; margin-bottom: 8px; margin-left: 20px;">
                    <template slot="append">ml</template>
                  </el-input>
                </el-form-item>
                <el-form-item label-width="110" label="滴定值平均值：">
                  <el-input disabled v-model="llzFormData.synInfo.ddzpjz" style="width: 120px; margin-bottom: 8px;">
                    <template slot="append">ml</template>
                  </el-input>
                </el-form-item>
              </el-row>
              <el-row>
                <el-form-item label-width="110" label="氯离子质量：">
                  <el-input @input="(value)=>{handleInput(value, 2, 2, 'llz-llzzl')}" v-model="llzFormData.synInfo.llzzl" style="width: 120px; margin-bottom: 8px;">
                    <template slot="append">kg</template>
                  </el-input>
                </el-form-item>
                <el-form-item label-width="110" label="氯离子含量：">
                  <el-input disabled v-model="llzFormData.synInfo.llzhl" style="width: 120px; margin-bottom: 8px;">
                    <template slot="append">%</template>
                  </el-input>
                </el-form-item>
          
                <el-form-item label-width="110" label="单项结论：">
                  <el-input v-model="llzFormData.dxjl" 
                  style="width: 120px; margin-bottom: 8px; margin-left: 27px;">
                  </el-input>
                </el-form-item>
              </el-row>
            </div>
          </el-col>
        </el-row>
        
        <el-form-item label="图片：">
          <div class="cbo">
            <div class="img-box" style="margin-top: 10px;">
              <el-upload
                accept=".jpeg,.png,.jpg,.bmp,.gif"
                :action="baseUrl + '/upload/file'"
                list-type="picture-card"
                :on-preview="handlePreview"
                :file-list="handlePicSuccess.img"
                :on-success="llzHandlePicSuccess"
                :on-remove="llzHandlePicRemove">
                <i class="el-icon-plus"></i>
              </el-upload>
            </div>
          </div>
        </el-form-item>
      </el-form>
    </div>
    <!-- 抗折强度 曾卫珍 CONCRETE_PARAM_KZQD -->
    <div v-if="activeName === 'CONCRETE_PARAM_KZQD'">
      <el-form :disabled="loading2 || experimentStatus == 3" :inline="true" class="mt16" label-width="100px">
        <el-row :gutter="12">
          <el-col :span="8" >
            <el-form-item label-width="110" label="检测日期：" class="flex-item flex-box">
              <el-date-picker
                type="date"
                v-model="kzqdFormData.jcrq"
                placeholder="选择日期"
                format="yyyy-MM-dd"
                :default-value="new Date()"
                value-format="yyyy-MM-dd"
                style="width: 135px;"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8" style="margin-bottom: 8px;">
            <el-form-item label-width="110" label="试件尺寸：">
              <el-select
                style="height: 40px;"
                v-model="kzqdFormData.sjcc" 
                filterable 
                @change="changeKZSJCC"
                placeholder="请选择试件尺寸" >
                <el-option
                  v-for="item in kzqdSjccOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" style="margin-bottom: 8px;">
            <el-form-item label-width="110" label="折算系数：">
              <el-input v-model="kzqdFormData.zsxs" style="width: 120px; margin-bottom: 8px;" disabled>
              </el-input>
            </el-form-item>
          </el-col>

        </el-row>
        <el-row :gutter="12">
          <el-col :span="8" style="margin-bottom: 8px;">
            <el-form-item label-width="110" label="样品等级：">
              <span style="display: inline-block; min-width: 150px;">{{conversionFactor}}</span>
            </el-form-item>
          </el-col>

          <el-col :span="8" style="margin-bottom: 8px;">
            <el-form-item label-width="110" label="龄期：">
              <span style="display: inline-block; min-width: 150px;">{{kzqdFormData.lq}}天</span>
            </el-form-item>
          </el-col>

        </el-row>
        <el-row :gutter="12">
          
          <el-col :span="24" style="margin-bottom: 8px;">
            <div style="background: #F8F8F8;border-radius: 8px; padding: 16px;">
              <p>强度-{{ kzqdFormData.lq || '28' }}d</p>
              <div style="margin-top: 10px;" v-for="(item, index) in 3">
                <div>
                  <el-form-item label-width="10" :label="index + 1 + '、 宽：'" style="margin-right: 10px;">
                    <el-input 
                      style="width: 110px; margin-bottom: 8px; margin-left: 16px;"
                      @blur="()=>{changeZJ(index)}"
                      v-model.number="kzqdFormData.kz28d[index].kz" 
                      @input="val => kzqdFormData.kz28d[index].kz = Math.min(parseInt(val) || 0, 100000000)"
                    >
                      <template slot="append">mm</template>
                    </el-input>
                  </el-form-item>
                  <el-form-item label-width="10" label="高：" style="margin-right: 10px;">
                    <el-input 
                      v-model.number="kzqdFormData.kz28d[index].gz" 
                      @input="val => kzqdFormData.kz28d[index].gz = Math.min(parseInt(val) || 0, 100000000)"
                      style="width: 110px; margin-bottom: 8px;"
                      @blur="()=>{changeZJ(index)}"
                    >
                      <template slot="append">mm</template>
                    </el-input>
                  </el-form-item>
                  <el-form-item label-width="10" label="支距：" style="margin-right: 10px;">
                    <el-input 
                      v-model.number="kzqdFormData.kz28d[index].zj" 
                      @input="val => kzqdFormData.kz28d[index].zj = Math.min(parseInt(val) || 0, 100000000)"
                      style="width: 110px; margin-bottom: 8px;"
                      @blur="()=>{changeZJ(index)}"
                      @keyup.native="e => { const raw = e.target.value.replace(/\D/g, ''); e.target.value = raw === '' ? '' : parseInt(raw).toString() }"
                    >
                      <template slot="append">mm</template>
                    </el-input>
                  </el-form-item>
                  <el-form-item label-width="10" :label="'破坏荷载：'" style="margin-right: 10px;">
                    <el-input @blur="()=>{changePHHZ(index)}" @input="(value)=>{handleInput(value, 2, index, 'kzqd')}" v-model="kzqdFormData.kz28d[index].phhz" style="width: 110px; margin-bottom: 8px;">
                      <template slot="append">kN</template>
                    </el-input>
                  </el-form-item>
                  <el-form-item label-width="10" label="抗折强度：" style="margin-right: 10px;">
                    <el-input v-model="kzqdFormData.kz28d[index].kzqd" disabled style="width: 110px; margin-bottom: 8px;">
                      <template slot="append">MPa</template>
                    </el-input>
                  </el-form-item>
                  <el-form-item label-width="10" label="压前情况：" style="margin-right: 10px;">
                    <el-select v-model="kzqdFormData.kz28d[index].yqqk" style="width: 110px;" placeholder="请选择">
                        <el-option  v-for="option in [{label:'完好', value:'完好'}]" :key="option.value" :label="option.label" :value="option.value"></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label-width="10" label="压后情况：" style="margin-right: 10px;">
                    <el-select  v-model="kzqdFormData.kz28d[index].yhqk" style="width: 110px;" placeholder="请选择">
                        <el-option v-for="option in [{label:'破损', value:'破损'}]" :key="option.value" :label="option.label" :value="option.value"></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label-width="10" label="断裂位置：">
                    <el-select  v-model="kzqdFormData.kz28d[index].zt" style="width: 160px;" placeholder="请选择">
                        <el-option v-for="option in [{label:'两个集中荷载之外', value:'两个集中荷载之外'},{label:'两个集中荷载之内', value:'两个集中荷载之内'}]" :key="option.value" :label="option.label" :value="option.value"></el-option>
                    </el-select>
                  </el-form-item>
                </div>
                
              </div>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="12">
          
          <el-col :span="24" style="margin-bottom: 8px;">
            <div style="background: #F8F8F8;border-radius: 8px; padding: 16px;">
              <el-form-item label="平均值：" label-width="110">
                <el-input disabled v-model="kzqdFormData.pjz" style="width: 130px; margin-bottom: 8px;">
                  <template slot="append">MPa</template>
                </el-input>
              </el-form-item>
              
              <el-form-item label="折算标准强度：" label-width="110">
                <el-input v-model="kzqdFormData.zsbzqd" style="width: 130px; margin-bottom: 8px;" disabled>
                  <template slot="append">MPa</template>
                </el-input>
              </el-form-item>
              <el-form-item label="到达设计强度：" label-width="110">
                <el-input disabled v-model="kzqdFormData.ddsjqd" style="width: 130px; margin-bottom: 8px;">
                  <template slot="append">%</template>
                </el-input>
              </el-form-item>
              <el-form-item label-width="110" label="单项结论：">
                <el-input v-model="kzqdFormData.dxjl" 
                 style="width: 120px; margin-bottom: 8px;">
                </el-input>
              </el-form-item>
            </div>
          </el-col>
        </el-row>
        
        <el-form-item label="图片：">
          <div class="cbo">
            <div class="img-box" style="margin-top: 10px;">
              <el-upload
                accept=".jpeg,.png,.jpg,.bmp,.gif"
                :action="baseUrl + '/upload/file'"
                list-type="picture-card"
                :on-preview="handlePreview"
                :file-list="handlePicSuccess.img"
                :on-success="kzqdHandlePicSuccess"
                :on-remove="kzqdHandlePicRemove">
                <i class="el-icon-plus"></i>
              </el-upload>
            </div>
          </div>
        </el-form-item>
      </el-form>
    </div>

    <div v-if="activeName === 'CONCRETE_PARAM_XNBG_TLD'
      // || activeName === 'CONCRETE_PARAM_XNBG_LLZHL'
      // || activeName === 'CONCRETE_PARAM_KZQD'
      || activeName === 'CONCRETE_PARAM_XNBG_HQL'
      || activeName === 'CONCRETE_PARAM_XNBG_CNSJ'
      || activeName === 'CONCRETE_PARAM_XNBG_ZNSJ'
      || activeName === 'CONCRETE_PARAM_XNBG_MSL'
      || activeName === 'CONCRETE_PARAM_XNBG_KZD'
    " class="custom-form">
      <el-form :disabled="loading2 || experimentStatus == 3" :inline="true" class="mt16" label-width="100px">
        <div class="mt16 jcrq-box">
          <el-form-item label="检测日期：">
            <el-date-picker
              type="date"
              v-model="batchForm[activeName].jcrq"
              placeholder="选择日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
        </div>
        <el-row class="mt16">
          <el-col :span="10">
            <!-- <el-form-item class="flex-box" label="检测值：">
              <el-input
                v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                placeholder="请输入"
                v-model="batchForm[activeName].jcz" 
                :style="{'width': 220 + 'px'}"
              >
              </el-input>
            </el-form-item> -->
            <el-form-item class="flex-box" label="检测值：">
              <el-select
                v-if="activeName === 'CONCRETE_PARAM_XNBG_TLD'"
                v-model="batchForm[activeName].jcz" 
                filterable 
                :style="{'width': 220 + 'px'}"
                placeholder="坍落度" >
                <el-option
                  v-for="item in 13"
                  :key="item"
                  :label="70 + item * 10"
                  :value="70 + item * 10">
                </el-option>
              </el-select>
              <el-input
                v-else
                v-manual-update
                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                placeholder="请输入"
                v-model="batchForm[activeName].jcz" 
                :style="{'width': 220 + 'px'}"
              >
                <!-- <template slot="append">c㎡/g</template> -->
              </el-input>
              
              
            </el-form-item>
          </el-col>
          <el-col :span="14">
            <el-form-item class="flex-box" label="单项结论：">
              <el-input
                v-model="batchForm[activeName].dxjl" 
                placeholder="请输入"
                :style="{'width': 300 + 'px'}"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row class="mt16">
          <el-col :span="24">
            <el-form-item label="检测图片：">
              <div class="cbo">
                <div class="img-box">
                  <el-upload
                    accept=".jpeg,.png,.jpg,.bmp,.gif"
                    :action="baseUrl + '/upload/file'"
                    list-type="picture-card"
                    :on-preview="handlePreview"
                    :file-list="batchForm[activeName].img"
                    :on-success="handlePicSuccess"
                    :on-remove="handlePicRemove">
                    <i class="el-icon-plus"></i>
                  </el-upload>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <image-viewer
      v-if="imgViewerVisible"
      :urlList="[previewUrl]"
      :on-close="onClose"></image-viewer>
  </div>
</template>

<script>
  import moment from 'moment'
  import moments from "@/utils/moment.js"
  import getOpt from "@/common/js/getListData.js"
  import ImageViewer from "element-ui/packages/image/src/image-viewer";
  import {add,  sub,  mul,  div, roundToDecimalPlace, calcEquation, cpEvenRound} from "@/utils/calculate.js"
export default {
  name:'userMgt',
  components: {
    ImageViewer
  },
  props: {
    activeId: {
      type: Number | String,
    },
    isXnbg: {
      type: Number | String,
    },
    experimentStatus: {
      type: Number | String,
    },
    sampleTotal: {
      type: Number | String,
    },
    sampleLevel: {
      type: String,
      default: '1'
    },
    conversionFactor: {
      type: String,
      default: '--'
    },
    proportionQllz: {
      type: String,
      default: '-1'
    },
    proportionMaterial: {
      type: Object,
      default: () => { return {} }
    },
    ksdj: {
      type: String,
      default: ''
    },
    moldingTime: {
      type: String,
      default: ''
    },
    entrustTime: {
      type: String,
      default: ''
    }
  },
  watch: {
    proportionMaterial:{
      handler(val){
        console.log('传递过来的原材料配合比信息：', val);
      }
    },

    proportionQllz:{
      handler(val){
        console.log('传递过来的氯离子含量：', val);
      }
    },
    
    activeId: {
      handler(newValue, oldValue){
        if(newValue){
          // this.clearData();
          // this.setExperimentProject()
        }
      },
      immediate: true
    },
    sampleTotal: {
      handler(newValue, oldValue){
        //this.setSStotal();
      },
      immediate: true
    },
    ssjlInfoList: {
      handler(newValue, oldValue){
        let oSsjList= newValue;
        let oSsjListIndex = -1;
        let ossksdj;
        if(oSsjList && oSsjList.length > 0){
          // ossksdj = oSsjList[oSsjList.length - 1].syyl * 10 - 1
          ossksdj = oSsjList[oSsjList.length - 1].syyl * 10;
          
          for(let i=0;i<oSsjList.length; i++){
            let total = 0;
            for(let j =0; j < this.sfssListColumn?.length; j++){
              if(oSsjList[i][this.sfssListColumn[j]] == '是'){
                total = total + 1;
              }
            }
            if(total >= 3){
              oSsjListIndex = i;
              // ossksdj = oSsjList[i].syyl * 10 - 1;
              ossksdj = oSsjList[i].syyl * 10;
              break;
            }
          }
        }
        if(ossksdj || ossksdj === 0){
          if(parseInt(ossksdj) % 2 !== 0){
            ossksdj = parseInt(ossksdj) - 1
          }
          this.searchForm7.ssksdj = 'P' + parseInt(ossksdj);
        }else{
          this.searchForm7.ssksdj = 'P6以下'
        }
        // 龄期=第一条加压时间-成型时间
        if (newValue.length > 0) {
          let jysj = newValue[0].jysj;
          this.searchForm7.lq = moment(jysj).diff(moment(moment(this.moldingTime).format('YYYY-MM-DD')), 'day');
        }
      },
      deep: true
    },
    
    
    // qdInfoList7dCom: {
    //   handler(newValue, oldValue){
    //     if (!newValue || newValue.length == 0) return;
    //     let odata = JSON.parse(JSON.stringify(newValue))
    //     let sum = 0
    //     for(let i = 0; i< odata.length; i++){
    //       sum = this.add(sum,odata[i].qd)
    //       let qdArr = [
    //         {
    //           v: odata[i].qd,
    //         },{
    //           k: '*',
    //           v: 1,
    //         }
    //       ]
    //       let qdRes = calcEquation(qdArr, 1)
    //       this.qdInfoList7d[i].qd = qdRes;
    //     }
    //     // this.searchForm6.pjz7d = Math.round(this.div(sum, odata.length) * 100) / 100;
        
    //     let arr2 = [
    //       {
    //         v: sum,
    //       },{
    //         k: '/',
    //         v: odata.length,
    //       }
    //     ]
    //     let res2 = calcEquation(arr2, 1)
        
    //     this.searchForm6.pjz7d = isNaN(Number(res2)) ? "" : (parseFloat(res2) > 0 ? res2 : "");
        
        
    //     // sti 开始下标 eni 结束下标
    //     let stI = 0
    //     let edI = 0
    //     let tempStr = `${this.sampleLevel}.`

    //     for (let i = 0; i < tempStr.length; i++) {
    //       let item = tempStr[i];
    //       if (item == 'C' || item == 'c') {
    //         stI = i+1;
    //         continue;
    //       }
    //       if (stI > 0 && isNaN(item)) {
    //         edI = i;
    //         break;
    //       }
    //     }
    //     let olevel = tempStr.substring(stI, edI);
    //     if (this.searchForm6.pjz7d === '') {
    //       this.searchForm6.dxjl7d = "";
    //     }else{
    //       this.searchForm6.dxjl7d = this.searchForm6.pjz7d * 1 > (olevel * 0.75) ? '合格':'不合格';
    //     }
        
    //     let arr = [
    //       {
    //         v: this.searchForm6.pjz7d,
    //       },{
    //         k: '*',
    //         v: this.searchForm6.zsxs7d,
    //       },{
    //         k: '/',
    //         v: 100,
    //       }
    //     ]
    //     let res = calcEquation(arr, 1)
        
    //     this.searchForm6.zsbzqd7d = isNaN(Number(res)) ? "" : (parseFloat(res) > 0 ? res : "");
    //     if (this.searchForm6.zsbzqd7d === '') {
    //       this.searchForm6.fyxs7d = "";
    //     }else{
    //       // 判断样品等级是否有“水下”
    //       if(this.sampleLevel.indexOf('水下') == -1){
    //         this.searchForm6.fyxs7d = Math.round(this.div(this.searchForm6.zsbzqd7d, parseInt(olevel)) * 100);
    //       }else{
    //         // 判断样品等级的数字部分是否大于等于40
    //         if(parseInt(olevel) >= 40){
    //           this.searchForm6.fyxs7d = this.add(olevel, 10);
    //         }else{
    //           this.searchForm6.fyxs7d = this.add(olevel, 5);
    //         }
   
    //       }
    //     }
    //   },
    //   deep: true
    // },
    // qdInfoList28dCom: {
    //   handler(newValue, oldValue){
    //     if (!newValue || newValue.length == 0) return;
    //     let odata = JSON.parse(JSON.stringify(newValue))
    //     let sum = 0
    //     for(let i = 0; i< odata.length; i++){
    //       sum = this.add(sum,odata[i].qd)
    //       let qdArr = [
    //         {
    //           v: odata[i].qd,
    //         },{
    //           k: '*',
    //           v: 1,
    //         }
    //       ]
    //       let qdRes = calcEquation(qdArr, 1)
    //       this.qdInfoList28d[i].qd = qdRes;
    //     }
    //     // this.searchForm6.pjz28d = Math.round(this.div(sum, odata.length) * 100) / 100;
        
    //     let arr2 = [
    //       {
    //         v: sum,
    //       },{
    //         k: '/',
    //         v: odata.length,
    //       }
    //     ]
    //     let res2 = calcEquation(arr2, 1)
        
    //     this.searchForm6.pjz28d = isNaN(Number(res2)) ? "" : (parseFloat(res2) > 0 ? res2 : "");
        
    //     // sti 开始下标 eni 结束下标
    //     let stI = 0
    //     let edI = 0
    //     let tempStr = `${this.sampleLevel}.`

    //     for (let i = 0; i < tempStr.length; i++) {
    //       let item = tempStr[i];
    //       if (item == 'C' || item == 'c') {
    //         stI = i+1;
    //         continue;
    //       }
    //       if (stI > 0 && isNaN(item)) {
    //         edI = i;
    //         break;
    //       }
    //     }
    //     let olevel = tempStr.substring(stI, edI);
    //     if (this.searchForm6.pjz28d === '') {
    //       this.searchForm6.dxjl28d = "";
    //     }else{
    //       this.searchForm6.dxjl28d = this.searchForm6.pjz28d * 1 > olevel * 1 ?'合格':'不合格';
    //     }
        
    //     let arr = [
    //       {
    //         v: this.searchForm6.pjz28d,
    //       },{
    //         k: '*',
    //         v: this.searchForm6.zsxs28d,
    //       },{
    //         k: '/',
    //         v: 100,
    //       }
    //     ]
    //     let res = calcEquation(arr, 1)
        
    //     this.searchForm6.zsbzqd28d = isNaN(Number(res)) ? "" : (parseFloat(res) > 0 ? res : "");;
    //     if (this.searchForm6.zsbzqd28d === '') {
    //       this.searchForm6.fyxs28d = "";
    //     }else{
    //       this.searchForm6.fyxs28d = Math.round(this.div(this.searchForm6.zsbzqd28d, parseInt(olevel)) * 100);
    //     }
    //   },
    //   deep: true
    // },
    
    'llzFormData.synInfo.ddz1': {
      handler(newValue, oldValue){
        if (this.llzFormData.synInfo.ddz2 != '' && newValue != '') {
          let pjzArr = [
            {
              v: this.llzFormData.synInfo.ddz2,
            },{
              k: '+',
              v: newValue,
            },{
              k: '/',
              v: 2,
            }
          ]
          let pjzRes = calcEquation(pjzArr, 1)
          this.llzFormData.synInfo.ddzpjz = pjzRes;
          this.competleLlzhl(-1);
        }
      },
      deep: true
    },
    'llzFormData.synInfo.ddz2': {
      handler(newValue, oldValue){
        if (this.llzFormData.synInfo.ddz1 != '' && newValue != '') {
          let pjzArr = [
            {
              v: this.llzFormData.synInfo.ddz1,
            },{
              k: '+',
              v: newValue,
            },{
             k: '/',
             v: 2,
            }
          ]
          let pjzRes = calcEquation(pjzArr, 1)
          this.llzFormData.synInfo.ddzpjz = pjzRes;
          this.competleLlzhl(-1);
        }
      },
      deep: true
    },
    'llzFormData.synInfo.ddzpjz': {
      handler(newValue, oldValue){
        
      },
      deep: true
    },

    'searchForm7.jcrq': {
      handler(newValue, oldValue){
        if (!newValue) return;
        // 龄期=第一条加压时间-成型时间
        if (!!this.moldingTime) {
          this.searchForm7.lq = this.diffDays(this.moldingTime, newValue);//moment(jysj).diff(moment(moment(newValue).format('YYYY-MM-DD')), 'day');
        }
      },
      deep: true
    },

    moldingTime: {
      handler(newValue, oldValue){
        if (!newValue) return;
        // 龄期=第一条加压时间-成型时间
        if (!!this.searchForm7.jcrq) {
          this.searchForm7.lq = this.diffDays(newValue, this.searchForm7.jcrq);//moment(jysj).diff(moment(moment(newValue).format('YYYY-MM-DD')), 'day');
        }
      },
      deep: true
    },
  },
  computed: {
    qdInfoList7dCom: {
      get(){
        let odata = JSON.parse(JSON.stringify(this.qdInfoList7d))
        // odata = odata.map(item => {
        //   if(this.searchForm6.sjcc && item.hz){
        //     const osjcc = this.searchForm6.sjcc.split("*");
        //     item.qd = Math.round(this.div(item.hz * 100,this.mul(osjcc[0], osjcc[1])) * 100) / 10;
        //   }else{
        //     item.qd = "";
        //   }
        //   return item;
        // })
        return odata
      },
      set(newValue){}
    },
    qdInfoList28dCom: {
      get(){
        let odata = JSON.parse(JSON.stringify(this.qdInfoList28d))
        // odata = odata.map(item => {
        //   if(this.searchForm6.sjcc && item.hz){
        //     const osjcc = this.searchForm6.sjcc.split("*");
        //     item.qd = Math.round(this.div(item.hz * 100,this.mul(osjcc[0], osjcc[1])) * 100) / 10;
        //   }else{
        //     item.qd = "";
        //   }
        //   return item;
        // })
        return odata
      },
      set(newValue){}
    }    
  },
  data() {
    return {
      // 抗折试验 时试件尺寸
      kzqdSjccOption:[{label:'150*150*600', value:'150*150*600'},{label:'150*150*550', value:'150*150*550'},{label:'100*100*400', value:'100*100*400'}],
      defaultDate: moments.today(),
      sfssListColumn: ["sfss1", "sfss2", "sfss3", "sfss4",  "sfss5", "sfss6"],
      sjccOption: [],
      filePrefix: this.$store.state.loginStore.userInfo.filePrefix,
      baseUrl: process.env.NODE_ENV === 'development' ? "/api" : "/mes-platform",
      activeName: 'quick',
      quickForm: {},//快检数据
      quickData: [],
      ssjlInfoList: [],
      searchForm6:{},
      qdInfoList7d: [],
      qdInfoList7d2: [],
      qdInfoList28d: [],
      searchForm7:{},
      loading2: false,
      projectList:[],
      originalDataList: [],

      kzqdFormData:{
        "lq":"28",
        "jcrq": "",
        "sjcc": "",
        "zsxs": "",
        "zsbzqd": "",
        "pjz": "",
        "ddsjqd": "",
        "dxjl": "",
        "kz28d": [
            {
                "index": "1",
                "phhz": "",
                "kzqd": "",
                "yqqk": "",
                "yhqk": "",
                "zt": ""
            },
            {
                "index": "2",
                "phhz": "",
                "kzqd": "",
                "yqqk": "",
                "yhqk": "",
                "zt": ""
            },
            {
                "index": "3",
                "phhz": "",
                "kzqd": "",
                "yqqk": "",
                "yhqk": "",
                "zt": ""
            }
        ]
      },
      llzFormData:{
          "jcrq": "",
          "xsybzrynd": "0.0141",
          "ddlyhl": "20",
          "pjz": "",
          "dxjl": "",
          "phbInfo":{
              "sn":"",
              "kzf":"",
              "fmh":"",
              "cgl":"",
              "xgl":"",
              "s":"",
              "wjj":"",
              "njclzl":""
          },
          "synInfo": {
            "ddz1": "",
            "ddz2": "",
            "ddzpjz": "",
            "llzzl": "",
            "llzhl": "",
          },
      },
      
      ssjlInfoOriginal: [],
      ssjlInfoListColumn: [
        // {
        //   label: '是否渗水(YP0001)',
        //   prop: 'sfss1',
        // },{
        //   label: '是否渗水(YP0002)',
        //   prop: 'sfss2',
        // },{
        //   label: '是否渗水(YP0003)',
        //   prop: 'sfss3',
        // },{
        //   label: '是否渗水(YP0004)',
        //   prop: 'sfss4',
        // },{
        //   label: '是否渗水(YP0005)',
        //   prop: 'sfss5',
        // },{
        //   label: '是否渗水(YP0006)',
        //   prop: 'sfss6',
        // }
      ],
      zbrOptions: [], // 值班人数据
      previewUrl: "",
      imgViewerVisible: false,
      
      batchForm: {}
    };
  },
  created() {
    this.getsfccOption();
  },
  methods: {
    qdInfoList7dComHandleChange(cuIndex) {
      this.qdInfoList7dComQD(cuIndex);

      if (!this.qdInfoList7dCom || this.qdInfoList7dCom.length == 0) return;
      let odata = JSON.parse(JSON.stringify(this.qdInfoList7dCom))
      let sum = 0
      for(let i = 0; i< odata.length; i++){
        sum = this.add(sum,odata[i].qd)
        let qdArr = [
          {
            v: odata[i].qd,
          },{
            k: '*',
            v: 1,
          }
        ]
        let qdRes = calcEquation(qdArr, 1)
        this.qdInfoList7d[i].qd = qdRes;
      }
      // this.searchForm6.pjz7d = Math.round(this.div(sum, odata.length) * 100) / 100;
      
      let arr2 = [
        {
          v: sum,
        },{
          k: '/',
          v: odata.length,
        }
      ]
      let res2 = calcEquation(arr2, 1)
      
      this.searchForm6.pjz7d = isNaN(Number(res2)) ? "" : (parseFloat(res2) > 0 ? res2 : "");
      
      
      // sti 开始下标 eni 结束下标
      let stI = 0
      let edI = 0
      let tempStr = `${this.sampleLevel}.`

      for (let i = 0; i < tempStr.length; i++) {
        let item = tempStr[i];
        if (item == 'C' || item == 'c') {
          stI = i+1;
          continue;
        }
        if (stI > 0 && isNaN(item)) {
          edI = i;
          break;
        }
      }
      let olevel = tempStr.substring(stI, edI);
      if (this.searchForm6.pjz7d === '') {
        this.searchForm6.dxjl7d = "";
      }else{
        this.searchForm6.dxjl7d = this.searchForm6.pjz7d * 1 > (olevel * 0.75) ? '合格':'不合格';
      }
      
      let arr = [
        {
          v: this.searchForm6.pjz7d,
        },{
          k: '*',
          v: this.searchForm6.zsxs7d,
        },{
          k: '/',
          v: 100,
        }
      ]
      let res = calcEquation(arr, 1)
      
      this.searchForm6.zsbzqd7d = isNaN(Number(res)) ? "" : (parseFloat(res) > 0 ? res : "");
      if (this.searchForm6.zsbzqd7d === '') {
        this.searchForm6.fyxs7d = "";
      }else{
        // 判断样品等级是否有“水下”
        if(this.sampleLevel.indexOf('水下') == -1){
          this.searchForm6.fyxs7d = Math.round(this.div(this.searchForm6.zsbzqd7d, parseInt(olevel)) * 100);
        }else{
          // 判断样品等级的数字部分是否大于等于40
          if(parseInt(olevel) >= 40){
            this.searchForm6.fyxs7d = this.add(olevel, 10);
          }else{
            this.searchForm6.fyxs7d = this.add(olevel, 5);
          }
  
        }
      }
    },
    qdInfoList28dComHandleChange(cuIndex) {
      this.qdInfoList28dComQD(cuIndex);

      if (!this.qdInfoList28dCom || this.qdInfoList28dCom.length == 0) return;
      let odata = JSON.parse(JSON.stringify(this.qdInfoList28dCom))
      let sum = 0
      for(let i = 0; i< odata.length; i++){
        sum = this.add(sum,odata[i].qd)
        let qdArr = [
          {
            v: odata[i].qd,
          },{
            k: '*',
            v: 1,
          }
        ]
        let qdRes = calcEquation(qdArr, 1)
        this.qdInfoList28d[i].qd = qdRes;
      }
      // this.searchForm6.pjz28d = Math.round(this.div(sum, odata.length) * 100) / 100;
      
      let arr2 = [
        {
          v: sum,
        },{
          k: '/',
          v: odata.length,
        }
      ]
      let res2 = calcEquation(arr2, 1)
      
      this.searchForm6.pjz28d = isNaN(Number(res2)) ? "" : (parseFloat(res2) > 0 ? res2 : "");
      
      // sti 开始下标 eni 结束下标
      let stI = 0
      let edI = 0
      let tempStr = `${this.sampleLevel}.`

      for (let i = 0; i < tempStr.length; i++) {
        let item = tempStr[i];
        if (item == 'C' || item == 'c') {
          stI = i+1;
          continue;
        }
        if (stI > 0 && isNaN(item)) {
          edI = i;
          break;
        }
      }
      let olevel = tempStr.substring(stI, edI);
      if (this.searchForm6.pjz28d === '') {
        this.searchForm6.dxjl28d = "";
      }else{
        this.searchForm6.dxjl28d = this.searchForm6.pjz28d * 1 > olevel * 1 ?'合格':'不合格';
      }
      
      let arr = [
        {
          v: this.searchForm6.pjz28d,
        },{
          k: '*',
          v: this.searchForm6.zsxs28d,
        },{
          k: '/',
          v: 100,
        }
      ]
      let res = calcEquation(arr, 1)
      
      this.searchForm6.zsbzqd28d = isNaN(Number(res)) ? "" : (parseFloat(res) > 0 ? res : "");;
      if (this.searchForm6.zsbzqd28d === '') {
        this.searchForm6.fyxs28d = "";
      }else{
        this.searchForm6.fyxs28d = Math.round(this.div(this.searchForm6.zsbzqd28d, parseInt(olevel)) * 100);
      }
    },
    qdInfoList7dComQD(cuIndex) {
      let odata = JSON.parse(JSON.stringify(this.qdInfoList7d))
      odata = odata.map((item, index) => {
        if(this.searchForm6.sjcc && item.hz && cuIndex == index){
          const osjcc = this.searchForm6.sjcc.split("*");
          item.qd = Math.round(this.div(item.hz * 100,this.mul(osjcc[0], osjcc[1])) * 100) / 10;
        }
        return item;
      })
      this.qdInfoList7d = odata;
    },
    qdInfoList28dComQD(cuIndex) {
      let odata = JSON.parse(JSON.stringify(this.qdInfoList28d))
      odata = odata.map((item, index) => {
        if(this.searchForm6.sjcc && item.hz && cuIndex == index){
          const osjcc = this.searchForm6.sjcc.split("*");
          item.qd = Math.round(this.div(item.hz * 100,this.mul(osjcc[0], osjcc[1])) * 100) / 10;
        }
        return item;
      })
      this.qdInfoList28d = odata;
    },
    // 没有两次试验了 现在 index 都传值 -1 计算
    competleLlzhl(index){
      var syObj = this.llzFormData.synInfo;
      var phbObj = this.llzFormData.phbInfo;
      var rynd = Number(this.llzFormData.xsybzrynd);
      var njclzl = Number(phbObj.njclzl);
      var sha = Number(phbObj.xgl);
      var shui = Number(phbObj.s);
      var shuini =  Number(phbObj.sn);
      var pjz0 = Number(syObj.ddzpjz);
      var ddlyl0 = Number(this.llzFormData.ddlyhl);
      
      if(index == -1){
        // 计算全部
        console.log('---', rynd, '----', pjz0, '---', njclzl, '----', sha, '----', shui);
        let arr0 = [
          {
            v: rynd * pjz0 * 0.03545 * (njclzl + sha + 2 * shui),
          },{
            k: '/',
            v: ddlyl0,
          }
        ]
        var comLlzhl = calcEquation(arr0, 2);
        syObj.llzzl = comLlzhl || '';
        var v1 = rynd * pjz0 * 0.03545  * (njclzl + sha + 2 * shui);
        var v2 = ddlyl0;
        console.log('v1：', v1);
        console.log('v2：', v2);
        console.log('计算氯离子含量（现在的氯离子质量）：', syObj.llzzl);

        // var pjz1 = Number(syObj[1].xsybzrypjz);
        // var ddlyl1 = Number(syObj[1].ddlhl);
        // let arr1 = [
        //   {
        //     v: rynd * pjz1 * 0.03545 * (njclzl + sha + 2 * shui),
        //   },{
        //     k: '/',
        //     v: ddlyl1,
        //   }
        // ]
        // var comLlzhl = calcEquation(arr1, 1);
        // syObj[1].llzhl = comLlzhl  || '';
      } else {
        var pjz = Number(syObj[index].xsybzrypjz);
        var ddlyl = Number(syObj[index].ddlyhl);
        let arr1 = [
          {
            v: rynd * pjz * 0.03545 * (njclzl + sha + 2 * shui),
          },{
            k: '/',
            v: ddlyl,
          }
        ]
        var comLlzhl = calcEquation(arr1, 1);
        syObj[index].llzhl = comLlzhl  || '';
      }
      
      let arr01 = [
          {
            v: rynd * pjz0 * 0.03545 * (njclzl + sha + 2 * shui),
          },{
            k: '/',
            v: ddlyl0,
          }
        ]
        var comLlzhl1 = calcEquation(arr01, 2);
        console.log('计算氯离子含量（现在的氯离子质量）修月后：', comLlzhl1);
        console.log('计算氯离子含量（现在的氯离子质量）修月前：', arr01);
      let arr2 = [
        {
          v: Number(comLlzhl1) * 100,
        },{
          k: '/',
          v: Number(shuini),
        }
      ]
      var result = calcEquation(arr2, 3);
      this.llzFormData.synInfo.llzhl = (Number(comLlzhl) != 0) ? result : ''
      this.llzFormData.dxjl = Number(this.llzFormData.synInfo.llzzl) > Number(this.proportionQllz) || Number(this.proportionQllz) == 0 ? '合格' : '不合格';
    },

    llzTextFieldBlur(key, index){
      console.log('文本框编辑');
      if(key == 'pjz') {
        // 输入的是平均值 计算总平局值和氯离子含量
        this.competleLlzhl(index);
      } else if(key == 'ddl') {
        // 输入的是滴定
        this.competleLlzhl(index);
      } else if(key == 'rynd') {
        // 输入的是浓度
        this.competleLlzhl(index);
      } else if(key == 's') {
        // 输入的是水 需要计算氯离子
        this.competleLlzhl(index);
      } else if(key == 'cgl') {
        // 输入的是粗骨料 需要计算氯离子
        this.competleLlzhl(index);
      } else if(index == -1){
        var phbObj = this.llzFormData.phbInfo;
        var fmfNum = Number(phbObj.fmh);
        var snNum = Number(phbObj.sn);
        var kzfNum = Number(phbObj.kzf);
        phbObj.njclzl = fmfNum + snNum + kzfNum;
      } 
    },

    
    handleInput(value, num, index, type) {
      // 使用正则表达式限制小数点后的位数
      var regex = new RegExp(`^\\d+(\\.\\d{0,${num}})?$`);
      if(num == 0){
        regex = new RegExp('^[0-9]*$');
      }
      if (!regex.test(value)) {
        // 如果输入无效，则将值设置回上一个有效值
        if(type == 'kzqd'){
          this.kzqdFormData.kz28d[index].phhz = value.substring(0, value.length - 1);
        } else if(type == 'llz-rynd'){
          // 硝酸银溶液浓度
          this.llzFormData.xsybzrynd = value.substring(0, value.length - 1);
        } else if(type == 'llz-kzf') {
          // 矿渣粉
          this.llzFormData.phbInfo.kzf = value.substring(0, value.length - 1);
        } else if(type == 'llz-fmh') {
          // 粉煤灰
          this.llzFormData.phbInfo.fmh = value.substring(0, value.length - 1);
        } else if(type == 'llz-sn') {
          // 水泥
          this.llzFormData.phbInfo.sn = value.substring(0, value.length - 1);
        } else if(type == 'llz-cgl') {
          // 粗骨料
          this.llzFormData.phbInfo.cgl = value.substring(0, value.length - 1);
        } else if(type == 'llz-xgl') {
          // 细骨料
          this.llzFormData.phbInfo.xgl = value.substring(0, value.length - 1);
        } else if(type == 'llz-s') {
          // 水
          this.llzFormData.phbInfo.s = value.substring(0, value.length - 1);
        } else if(type == 'llz-wjj') {
          // 外加剂
          this.llzFormData.phbInfo.wjj = value.substring(0, value.length - 1);
        } else if(type == 'llz-ddlyhl') {
          // 滴定滤液量
          this.llzFormData.ddlyhl = value.substring(0, value.length - 1);
        } else if(type == 'llz-ddz1') {
          // 滴滤值1
          this.llzFormData.synInfo.ddz1 = value.substring(0, value.length - 1);
        } else if(type == 'llz-ddz2') {
          // 滴滤值2
          this.llzFormData.synInfo.ddz2 = value.substring(0, value.length - 1);
        } else if(type == 'llz-pjz') {
          // 平均值
          this.llzFormData.xsybzrypjz = value.substring(0, value.length - 1);
        }
        
      }
    },
    handlePreview(file) {
      this.previewUrl = file.url
      this.imgViewerVisible = true;
    },
    onClose() {
      this.imgViewerVisible = false;
    },
    clearData(){
      this.activeName= 'quick';
      this.quickForm= {};//快检数;
      this.quickData= [];
      this.ssjlInfoList= [];
      this.searchForm6={};
      this.qdInfoList7d= [];
      this.qdInfoList28d= [];
      this.searchForm7={};
      this.loading2= false;
      this.projectList=[];
      this.originalDataList= [];
      this.ssjlInfoListColumn=[];
      this.ssjlInfoOriginal=[];
    },
    async getsfccOption(){
      this.$api.getDictValue({
        dictCode: 'SPECIMEN_SIZE'
      }).then(res =>{
        if(res.succ){
          console.log('事件尺寸：',res.data.list)
          this.sjccOption = res.data.list.map(i => {
            return {
              label: i.dictValueName,
              value: i.dictValueCode
            }
          })
          if (!this.searchForm6?.sjcc && this.sjccOption.length > 0) {
            this.$set(this.searchForm6, "sjcc", this.sjccOption[0].value);
            this.changeSJCC(this.sjccOption[0].value);
          }
        }
      })

      let userOpt = await getOpt.getUserAll(this);
      console.log('---------',userOpt)
      userOpt.map(item => {
        if (item.label) {
          this.zbrOptions.push(item.label)
        }
      });
    },
    setSStotal(){
      
      
      // this.ssjlInfoListColumn = [];
      // this.ssjlInfoList = [];
      // //至少默认有一条
      // let onum = this.sampleTotal > 0 ? this.sampleTotal : 1;
      // for(let i =0; i< this.ssjlInfoOriginal.length; i++){
      //   this.ssjlInfoListColumn.push({
      //     label: `是否渗水(${this.ssjlInfoOriginal[i].smokeNo})`,
      //     prop: `sfss${this.ssjlInfoOriginal[i].smokeNo}`,
      //   })
      // }
      // for(let j = 0; j < onum; j++){
      //   let oRow = {
      //     jysj: moment().add(8 * j, 'hours').format('YYYY-MM-DD HH:mm:ss'),
      //     syyl: this.add(this.div(j, 10), 0.1),
      //   }
      //   for(let i =0; i< this.ssjlInfoListColumn.length ; i++){
      //     oRow[this.ssjlInfoListColumn[i].prop] = '否'
      //   }
      //   this.ssjlInfoList.push(oRow)
      // }
      console.log(this.ssjlInfoListColumn, this.ssjlInfoList )
    },
    addRow(index, row) { //增加列
      for(let i=0;i<this.ssjlInfoList.length; i++){
        let total = 0;
        for(let j =0; j < this.sfssListColumn?.length; j++){
          if(this.ssjlInfoList[i][this.sfssListColumn[j]] == '是'){
            total = total + 1;
          }
        }
        if(total >= 3){
          this.$message.error(`第${i + 1}行，已有3个试块渗水，不可以再新增`)
          return false;
        }
      }
    
    
      let osyyl = 0.1;
      let oRow = {};
      if(this.ssjlInfoList.length > 0){
        oRow = JSON.parse(JSON.stringify(this.ssjlInfoList[this.ssjlInfoList.length - 1]))
      }else{
        oRow = {
          jysj: "",
          sfssList: [],
          syyl: "",
          zbr: this.$store.state.loginStore.userInfo.userName
        }
      }
      let oSfssList = JSON.parse(JSON.stringify(this.ssjlInfoListColumn));
      let sfssList = [];
      for (let index = 0; index < oSfssList.length; index++) {
        const item = oSfssList[index];
        if (item.prop === ('sfss' + (index + 1))) {
          sfssList.push({
            [item.prop]: '否'
          })
        }
      }
      oRow = Object.assign(oRow, ...sfssList );
      oRow.sfssList = sfssList;

      oRow.syyl = this.add(oRow.syyl || 0, 0.1)
      if(oRow.jysj){
        oRow.jysj = moment(oRow.jysj).add(8, 'hours').format('YYYY-MM-DD HH:mm:ss')
      }else{
        oRow.jysj = moment().add(8, 'hours').format('YYYY-MM-DD HH:mm:ss')
      }
      this.ssjlInfoList.push(oRow);
      // this.searchForm7.jcrq = moment(oRow.jysj).format('YYYY-MM-DD')
      // this.ksdjJcrqChange(this.searchForm7.jcrq);
    },
    deleteRow(index, row) { //删除个人列
      this.ssjlInfoList.splice(index, 1);
      // if (this.ssjlInfoList.length > 0) {
      //   this.searchForm7.jcrq = moment(this.ssjlInfoList[this.ssjlInfoList.length - 1].jysj).format('YYYY-MM-DD');
      // }else{
      //   this.searchForm7.jcrq = "";
      // }
      // this.ksdjJcrqChange(this.searchForm7.jcrq);
    },

    diffDays(date1, date2) {
      const start = new Date(date1);
      const end = new Date(date2);
      // 计算毫秒差并转换为天数（1 天 = 24 * 60 * 60 * 1000 毫秒）
      const timeDiff = end.getTime() - start.getTime();
      const daysDiff = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
      return daysDiff;
    },
    ksdjJcrqChange(val) {
      if (!val) {
        return;
      }
      // if (this.moldingTime) {
      //   // 龄期=检测时间-成型时间
      //   this.searchForm7.lq = moment(val).diff(moment(moment(this.moldingTime).format('YYYY-MM-DD')), 'day');
      // }
    },
    //图片
    kjHandlePicSuccess(response, file, fileList) {
      console.log(fileList,this.quickForm.img)
      this.quickForm.img = fileList.map(item =>{
        if(item.response){
          return {
            url: item.response.data.filePath,
            uid: item.uid
          }
        }else{
          return item
          // if(item.url.startsWith(this.filePrefix)){
          //   return item.url;
          // }else{
          //   return this.filePrefix + item.url;
          // }
        }
      })
    },
    kjHandlePicRemove(file, fileList) {
      this.quickForm.img = fileList.map(item =>{
        if(item.response){
          return {
            url: item.response.data.filePath,
            uid: item.uid
          }
        }else{
          return item
        }
      })
    },



    //图片
    llzHandlePicSuccess(response, file, fileList) {
      this.llzFormData.img = fileList.map(item =>{
        if(item.response){
          return {
            url: item.response.data.filePath,
            uid: item.uid
          }
        }else{
          return item
        }
      })
    },
    llzHandlePicRemove(file, fileList) {
      this.llzFormData.img = fileList.map(item =>{
        if(item.response){
          return {
            url: item.response.data.filePath,
            uid: item.uid
          }
        }else{
          return item
        }
      })
    },

    //图片
    kzqdHandlePicSuccess(response, file, fileList) {
      this.kzqdFormData.img = fileList.map(item =>{
        if(item.response){
          return {
            url: item.response.data.filePath,
            uid: item.uid
          }
        }else{
          return item
        }
      })
    },
    kzqdHandlePicRemove(file, fileList) {
      this.kzqdFormData.img = fileList.map(item =>{
        if(item.response){
          return {
            url: item.response.data.filePath,
            uid: item.uid
          }
        }else{
          return item
        }
      })
    },
    //图片
    kyqdHandlePicSuccess(response, file, fileList) {
      this.searchForm6.img = fileList.map(item =>{
        if(item.response){
          return {
            url: item.response.data.filePath,
            uid: item.uid
          }
        }else{
          return item
          // if(item.url.startsWith(this.filePrefix)){
          //   return item.url;
          // }else{
          //   return this.filePrefix + item.url;
          // }
        }
      })
    },
    kyqdHandlePicRemove(file, fileList) {
      this.searchForm6.img = fileList.map(item =>{
        if(item.response){
          return {
            url: item.response.data.filePath,
            uid: item.uid
          }
        }else{
          return item
        }
      })
    },
    //图片C
    ksdjHandlePicSuccess(response, file, fileList) {
      this.searchForm7.img = fileList.map(item =>{
        if(item.response){
          return {
            url: item.response.data.filePath,
            uid: item.uid
          }
        }else{
          return item;
          // if(item.url.startsWith(this.filePrefix)){
          //   return item.url;
          // }else{
          //   return this.filePrefix + item.url;
          // }
        }
      })
    },
    ksdjHandlePicRemove(file, fileList) {
      this.searchForm7.img = fileList.map(item =>{
        if(item.response){
          return {
            url: item.response.data.filePath,
            uid: item.uid
          }
        }else{
          return item
        }
      })
    },
    
    //图片
    handlePicSuccess(response, file, fileList) {
      console.log(fileList)
      let oUploadImg = fileList.map(item =>{
        if(item.response){
          return {
            url: item.response.data.filePath,
            uid: item.uid
          }
        }else{
          return item;
          // if(item.url.startsWith(this.filePrefix)){
          //   return item.url;
          // }else{
          //   return this.filePrefix + item.url;
          // }
        }
      })
      
      if(this.activeName === 'quick'){
        this.quickForm.img = oUploadImg;
      }else{
        this.batchForm[this.activeName].img = oUploadImg;
      }
    },
    handlePicRemove(file, fileList) {
      let oUploadImg = fileList.map(item =>{
        if(item.response){
          return {
            url: item.response.data.filePath,
            uid: item.uid
          }
        }else{
          return item
        }
      })
      if(this.activeName === 'quick'){
        this.quickForm.img = oUploadImg;
      }else{
        this.batchForm[this.activeName].img = oUploadImg;
      }
    },
    
    //获取所有快检或批检试验项目名称
    async setExperimentProject(id){
      //获取抗压 抗渗 和其它
      const detailKYQD = await this.$api.getExperimentDetail({
        experimentId: this.activeId,
        //"testProjectCode":"CONCRETE_PARAM_KYQD",
        //"checkType": 1
      }, this)
      if(detailKYQD.succ){
        this.quickData = []
        this.projectList = []//批检
        this.quickForm.img = [];
        
        let batchData = [];//批检
        let batchForm = {};
        detailKYQD.data.list.forEach(item => {
          let oImgArr = [];
          if(item.objImg && item.objImg != 'null'){
            oImgArr = item.objImg.split(',').map(item => {
              if(item.startsWith(this.filePrefix)){
                return {
                  url: item
                }
              }else{
                return {
                  url: this.filePrefix + item
                }
              }
            }) 
          }else{
            oImgArr = [];
          }
          
          if(item.testProjectName === '流动性' || item.testProjectName === '保水性' 
            || item.testProjectName === '粘聚性'|| item.testProjectName === '实测坍落度' 
            || item.testProjectName === '目测砂率' 
          ){
            // if (item.testProjectName === '坍落度') {
            //   if (!item.objJson.tldfs) {
            //     item.objJson.tldfs = "目测";
            //   }
            // }
            if((item.testProjectName === '流动性' || item.testProjectName === '保水性' 
            || item.testProjectName === '粘聚性')){
              // 接口没有val 字段，根据判断转化一个加进去自己用
              item.objJson.val = 'hh'
              if (item.objJson.hh == 1) {
                item.objJson.val = 'hh'
              }else if (item.objJson.yb == 1) {
                item.objJson.val = 'yb'
              }else if (item.objJson.fcc == 1) {
                item.objJson.val = 'fcc'
              }
            }
            this.quickData.push(item)
            this.quickForm[item.testProjectCode] = item;
            this.quickForm.img = this.quickForm.img.concat(oImgArr);
          }else{
            item.objJson.img = oImgArr;
            this.projectList.push(item);
            if(item.testProjectCode === 'CONCRETE_PARAM_KYQD'){
              if(!item.objJson.zsxs7d){
                item.objJson.zsxs7d = '95'
              }
              if(!item.objJson.zsxs28d){
                item.objJson.zsxs28d = '95'
              }
              if(item.objJson.pjz7d) {
                item.objJson.pjz7d = cpEvenRound(item.objJson.pjz7d, 1);
              }
              if(item.objJson.pjz28d) {
                item.objJson.pjz28d = cpEvenRound(item.objJson.pjz28d, 1);
              }
              if(item.objJson.zsbzqd7d) {
                item.objJson.zsbzqd7d = cpEvenRound(item.objJson.zsbzqd7d, 1);
              }
              if(item.objJson.zsbzqd28d) {
                item.objJson.zsbzqd28d = cpEvenRound(item.objJson.zsbzqd28d, 1);
              }
              // 由于从协会同步过来的数据可能存在小数位不正确的情况，所以levin 要求前端自己提前修约保留一位小数
              if (item.objJson.qd7d) {
                for (let element of item.objJson.qd7d) {
                  if (element.hz) {
                    element.hz = cpEvenRound(element.hz, 1);
                  }
                  if (element.qd) {
                    element.qd = cpEvenRound(element.qd, 1);
                  }
                }
              }
              if (item.objJson.qd28d) {
                for (let element of item.objJson.qd28d) {
                  if (element.hz) {
                    element.hz = cpEvenRound(element.hz, 1);
                  }
                  if (element.qd) {
                    element.qd = cpEvenRound(element.qd, 1);
                  }
                }
              }

              this.qdInfoList7d = item.objJson.qd7d;
              this.qdInfoList28d = item.objJson.qd28d;
              this.searchForm6 = item.objJson;
              
              batchForm[item.testProjectCode] = JSON.parse(JSON.stringify(item.objJson));
              if (!this.searchForm6.sjcc && this.sjccOption.length > 0) {
                this.$set(this.searchForm6, "sjcc", this.sjccOption[0].value);
              }
            }else if(item.testProjectCode === 'CONCRETE_PARAM_KSDJ'){
              this.searchForm7 = item.objJson;
              
              let oList = JSON.parse(JSON.stringify(item.objJson.ksInfo))
              
              if(oList){
                for(let i=0;i<oList.length; i++){
                  if(oList[i].jysj == ''){
                    oList[i].jysj = moment().add(i * 8, 'hours').format('YYYY-MM-DD HH:mm:ss')
                  }
                  if(oList[i].syyl == ''){
                    oList[i].syyl = this.mul( i + 1, 0.1)
                  }
                  
                  for(let j =1; j < 7; j++){
                    const sfssobj = 'sfss' + j;
                    if(!oList[i][sfssobj] || oList[i][sfssobj] == 'null'){
                      oList[i][sfssobj] = "否"
                    }
                  }

                  if (oList[i].zbr == undefined || oList[i].zbr == null || oList[i].zbr == '') {
                    oList[i].zbr = this.$store.state.loginStore.userInfo.userName;
                  }
                }
                
                
                this.ssjlInfoList = oList;
                this.ssjlInfoListColumn = [];
                for(let i =0; i< this.sfssListColumn?.length; i++){
                  this.ssjlInfoListColumn.push({
                    label: `是否渗水(${i + 1})`,
                    prop: this.sfssListColumn[i],
                  })
                }

                this.ssjlInfoListColumn.push({
                  label: `值班人`,
                  prop: "zbr",
                })
              }

              if (!this.searchForm7.ksyq) {
                this.searchForm7.ksyq = this.ksdj;
              }

              if (!this.searchForm7.jcrq) {
                // 检测日期不存在则默认当前
                this.searchForm7.jcrq = moment().format('YYYY-MM-DD');
              }

              if (this.moldingTime && oList.length > 0) {
                let jysj = oList[0].jysj;
                if (jysj) {
                  // 龄期=第一个加压时间-成型时间
                  this.searchForm7.lq = moment(jysj).diff(moment(moment(this.moldingTime).format('YYYY-MM-DD')), 'day');
                }
              }

              // if (this.moldingTime) {
              //   // 龄期=检测时间-成型时间
              //   this.searchForm7.lq = moment(this.searchForm7.jcrq).diff(moment(moment(this.moldingTime).format('YYYY-MM-DD')), 'day');
              // }
              
            }else if(item.testProjectCode === 'CONCRETE_PARAM_KZQD'){
              if (item.objJson && item.objJson.kz28d) {
                for (var element of item.objJson.kz28d) {
                  if (!element.kz) {
                    element.kz = 100;
                  }else{
                    element.kz = cpEvenRound(element.kz, 0);
                  }
                  if (!element.gz) {
                    element.gz = 100;
                  }else{
                    element.gz = cpEvenRound(element.gz, 0);
                  }
                  if (!element.zj) {
                    element.zj = 300;
                  }else{
                    element.zj = cpEvenRound(element.zj, 0);
                  }
                }
              }
              this.kzqdFormData = item.objJson;
            }else if(item.testProjectCode === 'CONCRETE_PARAM_XNBG_LLZHL'){
              // 氯离子试验
              this.llzFormData = item.objJson;
              var phbObj = item.objJson.phbInfo || {};
              var synInfo = item.objJson.synInfo || {};
              
              if (phbObj) {
                if(!phbObj.sn){
                  phbObj.sn = this.proportionMaterial.sn.scyl;
                }
                if(!phbObj.kzf){
                  phbObj.kzf = this.proportionMaterial.kzf.scyl;
                }
                if(!phbObj.fmh){
                  phbObj.fmh = this.proportionMaterial.fmh.scyl;
                }
                // 其他
                if(!phbObj.cgl){
                  phbObj.cgl = this.proportionMaterial.cgl.scyl;
                }
                if(!phbObj.xgl){
                  phbObj.xgl = this.proportionMaterial.xgl.scyl;
                }
                if(!phbObj.s){
                  phbObj.s = this.proportionMaterial.water.scyl;
                }
                if(!phbObj.wjj){
                  phbObj.wjj = this.proportionMaterial.wjj1.scyl;
                }

                phbObj.njclzl = Number(phbObj.fmh) + Number(phbObj.kzf) + Number(phbObj.sn);
              }

              if (this.llzFormData.xsybzrynd == '' || this.llzFormData.xsybzrynd == null){
                this.llzFormData.xsybzrynd = '0.0141';
              }
              if (this.llzFormData.ddlyhl == '' || this.llzFormData.ddlyhl == null){
                this.llzFormData.ddlyhl = '20';
              }
              if (this.llzFormData.jcrq == '' || this.llzFormData.jcrq == null){
                this.llzFormData.jcrq = this.entrustTime;
              }
              
              this.llzFormData.phbInfo = phbObj;
            }else{
              if(!item.objJson?.jcrq){
                item.objJson.jcrq = this.defaultDate;
              }
              item.objJson.img = oImgArr;
              batchForm[item.testProjectCode] = JSON.parse(JSON.stringify(item.objJson));
            }
          }
        })
        this.batchForm = batchForm;
        if(this.quickData.length > 0){
          this.activeName = 'quick'
        }else{
          this.activeName = this.projectList[0]?.testProjectCode
        }
        console.log(this.projectList,111)
      }  
    },
    // 抗折切换试件尺寸
    changeKZSJCC(event) {
      const list = event.split('*');
      const numStr = list.pop();
      if (numStr == '400') {
        this.$set(this.kzqdFormData, "zsxs", "0.85");
      }else {
        this.$set(this.kzqdFormData, "zsxs", "1");
      }
      // this.computeKangZhePjz(true);
    },
    changeZJ(index) {
      this.computeKangZhePjz(true);
    },
    // 计算抗折强度
    computeKangZhe(index){
      // const sjcc = this.kzqdFormData.sjcc;
      // const list = sjcc.split('*');
      const list = [this.kzqdFormData.kz28d[index].kz, this.kzqdFormData.kz28d[index].gz, this.kzqdFormData.kz28d[index].zj, ];
      var kzqd = '';
      const num1Str = list[0];
      const num2Str = list.pop();
      if(num1Str && num2Str){
        const value = this.kzqdFormData.kz28d[index].phhz;
        const val1 = (parseFloat(num2Str) * parseFloat(value) * 1000);
        const val2 = (parseFloat(num1Str) * parseFloat(num1Str) * parseFloat(num1Str));
        let arr2 = [
          {
            v: val1,
          },{
            k: '/',
            v: val2,
          }
        ]
        kzqd = calcEquation(arr2, 1);
        this.$set(this.kzqdFormData.kz28d[index], "kzqd", kzqd);
      }


  },
  changePHHZ(index){
    this.computeKangZhe(index);
    this.computeKangZhePjz(false);
  },
    // 计算抗折强度平均值  是否点击试件尺寸
    computeKangZhePjz(isCheckSjcc){
      var total = 0.0
      var canCompute = true;
      this.kzqdFormData.kz28d.forEach((item, index)=>{
        // 点击试件尺寸，全部的 强度需要重新计算
        if(isCheckSjcc){
          this.computeKangZhe(index);
        }
        
        if(!item.kzqd || item.kzqd == '0.0'){
          canCompute = false;
        }
        
        if(item.kzqd && item.kzqd != '0.0'){
          total = total + parseFloat(item.kzqd);
        }
      });
      if(!canCompute) return '';

      let arr2 = [
          {
            v: total,
          },{
            k: '/',
            v: 3,
          }
        ]
      var pjz = calcEquation(arr2, 1);
      var zsbzqd = '';
      if(pjz != '0.0' && this.kzqdFormData.zsxs.length != 0){
          let arr = [
            {
              v: pjz,
            },{
              k: '*',
              v: this.kzqdFormData.zsxs,
            }
          ]
          zsbzqd = calcEquation(arr, 1);
      }
      this.$set(this.kzqdFormData, "pjz", pjz);
      this.$set(this.kzqdFormData, "zsbzqd", zsbzqd);
      if (this.$parent.activeData?.conversionFactor) {
        let conversionFactor = this.$parent.activeData?.conversionFactor.slice(1) || 1;
        let arr5 = [
          {
            v: zsbzqd,
          },{
            k: '/',
            v: conversionFactor,
          },{
            k: '*',
            v: 100,
          }
        ]
        console.log(zsbzqd, conversionFactor)
        let ddsjqd = calcEquation(arr5, 0);
        let dxjl = zsbzqd >= parseFloat(conversionFactor) ? '合格' : '不合格';
        this.$set(this.kzqdFormData, "ddsjqd", ddsjqd);
        this.$set(this.kzqdFormData, "dxjl", dxjl);
      }
    },

    changeSJCC(event) {
      if (event.indexOf("100") > -1) {
        this.$set(this.searchForm6, "zsxs7d", "95");
        this.$set(this.searchForm6, "zsxs28d", "95");
      }else if (event.indexOf("150") > -1) {
        this.$set(this.searchForm6, "zsxs7d", "100");
        this.$set(this.searchForm6, "zsxs28d", "100");
      }else if (event.indexOf("200") > -1) {
        this.$set(this.searchForm6, "zsxs7d", "105");
        this.$set(this.searchForm6, "zsxs28d", "105");
      }
    },
    
    add(a, b) {
    	    var c, d, e;
          var that = this;
    	    try {
    	        c = a.toString().split(".")[1].length;
    	    } catch (f) {
    	        c = 0;
    	    }
    	    try {
    	        d = b.toString().split(".")[1].length;
    	    } catch (f) {
    	        d = 0;
    	    }
    	    return e = Math.pow(10, Math.max(c, d)), (that.mul(a, e) + that.mul(b, e)) / e;
    },
    sub(a, b) {
    	    var c, d, e;
          var that = this;
    	    try {
    	        c = a.toString().split(".")[1].length;
    	    } catch (f) {
    	        c = 0;
    	    }
    	    try {
    	        d = b.toString().split(".")[1].length;
    	    } catch (f) {
    	        d = 0;
    	    }
    	    return e = Math.pow(10, Math.max(c, d)), (that.mul(a, e) - that.mul(b, e)) / e;
    },
    mul(a, b) {
        var c = 0,
            d = a.toString(),
            e = b.toString();
        try {
            c += d.split(".")[1].length;
        } catch (f) {}
        try {
            c += e.split(".")[1].length;
        } catch (f) {}
        return Number(d.replace(".", "")) * Number(e.replace(".", "")) / Math.pow(10, c);
    },
    div(a, b) {
        var that = this;
        var c, d, e = 0,
            f = 0;
        try {
            e = a.toString().split(".")[1].length;
        } catch (g) {}
        try {
            f = b.toString().split(".")[1].length;
        } catch (g) {}
        return c = Number(a.toString().replace(".", "")), d = Number(b.toString().replace(".", "")), that.mul(c / d, Math.pow(10, f - e));
    },
  },
};
</script>

<style scoped lang="scss">
  ::v-deep .el-input-group__append, 
  ::v-deep .el-input-group__prepend{
    padding: 0 5px;
    text-align: center;
    width: 40px;
  }
  ::v-deep .pr0{
    .el-input__inner{
      padding-right: 0;
    }
  }
  ::v-deep .el-input-number.is-controls-right .el-input__inner{
    text-align: left;
  }
  ::v-deep .textspan .el-input__inner{
    background: transparent;
    border: none;
    padding: 0;
    color: #000;
    margin-left: -10px;
    margin-top: -2px;
  }
  
  
  ::v-deep .el-upload-list__item{
    transition: none !important; 
  }
  
  
  // .img-box{
  //   margin-right: 8px;
  //   float: left;
  //   img{
  //     display: block;
  //     background: #E0E8EB;
  //     border-radius: 8px;
  //     width: 140px;
  //     height: 140px;
  //   }
  //   span{
  //     font-size: 14px;
  //     font-family: PingFangSC, PingFang SC;
  //     font-weight: 400;
  //     color: #1F2329;
  //     line-height: 20px;
  //     letter-spacing: 1px;
  //     display: block;
  //     padding: 8px 4px;
  //   }
  // }
</style>