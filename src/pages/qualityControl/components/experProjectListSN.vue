<template>
  <div>
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane v-if="quickData.length > 0" label="快检" name="quick"></el-tab-pane>
      <template v-if="batchData.length > 0">
        <el-tab-pane v-for="(item, index) in batchData" :key="item.testProjectCode" :label="item.testProjectName" :name="item.testProjectCode">
        </el-tab-pane>
      </template>
    </el-tabs>
    <!-- 快检 -->
    <div v-if="activeName === 'CEMENT_PARAM_KJ'">
      <el-form :disabled="loading2 || experimentStatus == 3" class="mt16">
        <el-row>
          <el-col :span="24">
            <el-form-item label="颜色">
              <el-input
                clearable
                placeholder="请输入"
                v-model="batchForm[activeName].ys"
                :style="{'width': 260 + 'px'}"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="检测图片：">
              <div class="cbo">
                <div class="img-box">
                  <el-upload
                    accept=".jpeg,.png,.jpg,.bmp,.gif"
                    :action="baseUrl + '/upload/file'"
                    list-type="picture-card"
                    :on-preview="handlePreview"
                    :file-list="batchForm[activeName].img"
                    :on-success="handlePicSuccess"
                    :on-remove="handlePicRemove">
                    <i class="el-icon-plus"></i>
                  </el-upload>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    
    <!-- 标准稠度 -->
    <div v-if="activeName === 'CEMENT_PARAM_BZCD'" class="custom-form">
      <el-form :disabled="loading2 || experimentStatus == 3" :inline="true">
        <div class="mt16 jcrq-box">
          <el-form-item label="检测日期：" label-width="124px">
            <el-date-picker
              type="date"
              v-model="batchForm[activeName].jcrq"
              placeholder="选择日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
        </div>
        <!-- <div class="flex-box mb16 mt24"> -->
           <!-- :class="{'is-error': batchForm[activeName].xcsd < 5 || batchForm[activeName].xcsd > 7}" -->
          <!-- <el-form-item label="下沉深度：" class="flex-item flex-box">
            <el-input
              v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].xcsd" 
              placeholder="请输入(正常范围：5-7mm)"
              style="width: 220px;"
            >
              <template slot="append">mm</template>
            </el-input>
          </el-form-item> -->
           <!-- :class="{'is-error': batchForm[activeName].xcsd < 5 || batchForm[activeName].xcsd > 7}" -->
          
          <!-- <div class="flex-item flex-box"></div> -->
        <!-- </div> -->
        <div class="flex-box mb16 mt24">
          <el-form-item label="试杆距底板距离："  class="flex-item flex-box">
            <el-input
              v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].sgjdbjl" 
              placeholder="请输入"
              style="width: 175px;"
            >
              <template slot="append">mm</template>
            </el-input>
          </el-form-item>
          
          <el-form-item label="注水时间：" class="flex-item flex-box">
            
            <el-date-picker
              v-model="batchForm[activeName].zssj"
              @input="setJssj"
              type="datetime"
              value-format="yyyy-MM-dd HH:mm"
              format="MM-dd HH:mm"
              :default-value= "new Date()"
              placeholder="任意时间点"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item class="flex-item flex-box" label="用水量：">
            <el-input
              v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].ysl"
              @input="setBZCD"
              style="width: 220px;"
              placeholder="请输入"
            >
              <template slot="append">g</template>
            </el-input>
          </el-form-item>
          <el-form-item label="" class="flex-item">
            <el-form-item class="flex-item flex-box" label="水泥含量：">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @input="setBZCD"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="batchForm[activeName].snhl"
                disabled
              >
                <template slot="append">g</template>
              </el-input>
            </el-form-item>
          </el-form-item>
        </div>
        <div class="line"></div>
        <el-row>
          <el-col :span="10">
            <el-form-item class="flex-box" label="标准稠度用水量：">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                v-model="batchForm[activeName].bzcdysl" 
                disabled
                :style="{'width': 200 + 'px'}"
              >
                <template slot="append">%</template>
              </el-input>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="14">
            <el-form-item class="flex-box" label="单项结论：">
              <el-input
                v-model="batchForm[activeName].dxjl" 
                disabled
                placeholder="请输入"
                :style="{'width': 180 + 'px'}"
              >
              </el-input>
            </el-form-item>
          </el-col> -->
        </el-row>
        
        <el-row>
          <el-col :span="24">
            <el-form-item label="检测图片：" label-width="124px">
              <div class="cbo">
                <div class="img-box">
                  <el-upload
                    accept=".jpeg,.png,.jpg,.bmp,.gif"
                    :action="baseUrl + '/upload/file'"
                    list-type="picture-card"
                    :on-preview="handlePreview"
                    :file-list="batchForm[activeName].img"
                    :on-success="handlePicSuccess"
                    :on-remove="handlePicRemove">
                    <i class="el-icon-plus"></i>
                  </el-upload>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <!-- 凝结时间 -->
    <div v-if="activeName === 'CEMENT_PARAM_NJSJ'">
      <el-form :disabled="loading2 || experimentStatus == 3" class="mt16">
        <div class="mt16 jcrq-box">
          <el-form-item label="检测日期：">
            <el-date-picker
              type="date"
              v-model="batchForm[activeName].jcrq"
              placeholder="选择日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
        </div>
        <p class="info-form-title mt16">凝结时间</p>
        <div class="info-form-piece flex-box">
          <el-form-item label="加水时间：" class="flex-item flex-box">
            
            <!-- batchForm[activeName].njsj.jssj CEMENT_PARAM_BZCD -->
            <el-date-picker
              v-model="batchForm[activeName].njsj.jssj"
              @input="setNJSJ('a')"
              type="datetime"
              :disabled="true"
              value-format="yyyy-MM-dd HH:mm"
              format="MM-dd HH:mm"
              style="width: 145px;"
              :default-value= "new Date()"
              placeholder="任意时间点"
            >
            </el-date-picker>
          </el-form-item>
          
          <el-form-item class="flex-item flex-box" label="下沉深度：">
            <el-input
              v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].njsj.xcsd"
              style="width: 135px;"
              placeholder="请输入"
            >
              <template slot="append">mm</template>
            </el-input>
          </el-form-item>
          
          
          <el-form-item label="到达初凝时间：" class="flex-item flex-box">
            <el-date-picker
              @input="setNJSJ('s')"
              v-model="batchForm[activeName].njsj.ddcnsj"
              type="datetime"
              value-format="yyyy-MM-dd HH:mm"
              format="MM-dd HH:mm"
              :default-value= "new Date()"
              style="width: 135px;"
              placeholder="任意时间点"
            >
              <template slot="append">HH:mm</template>
            </el-date-picker>
          </el-form-item>
          <el-form-item label="到达终凝时间：" class="flex-item flex-box">
            <el-date-picker
              v-model="batchForm[activeName].njsj.ddznsj"
              @input="setNJSJ('e')"
              type="datetime"
              value-format="yyyy-MM-dd HH:mm"
              format="MM-dd HH:mm"
              style="width: 135px;"
              :default-value= "new Date()"
              placeholder="任意时间点"
            >
              <template slot="append">HH:mm</template>
            </el-date-picker>
          </el-form-item>
        </div>
        <p class="info-form-title mt16">结果计算</p>
        <div class="info-form-piece">
          <div class="flex-box">
            <el-form-item label="初凝时间：" class="flex-item flex-box">
              <!-- <el-time-picker
               v-model="batchForm[activeName].cnsj"
               format="HH:mm"
               value-format="HH:mm"
                placeholder="任意时间点">
              </el-time-picker> -->
              <el-input
                type="number" v-decimal="0"
                v-model="batchForm[activeName].jgjs.cnsj" 
                disabled
                :style="{'width': 220 + 'px'}"
              >
                <template slot="append">分钟</template>
              </el-input>
            </el-form-item>
            <el-form-item label="结论：" class="flex-item2 flex-box">
              <el-input
                v-model="batchForm[activeName].jgjs.cnsjjl" 
                disabled
                placeholder="请输入"
                :style="{'width': 500 + 'px'}"
              >
              </el-input>
            </el-form-item>
          </div>
          <div class="flex-box">
            <el-form-item label="终凝时间：" class="flex-item flex-box">
              <el-input
                :style="{'width': 220 + 'px'}"
                type="number" v-decimal="0"
                v-model="batchForm[activeName].jgjs.znsj" 
                disabled
              >
                <template slot="append">分钟</template>
              </el-input>
            </el-form-item>
            <el-form-item label="结论：" class="flex-item2 flex-box">
              <el-input
                v-model="batchForm[activeName].jgjs.znsjjl" 
                disabled
                placeholder="请输入"
                :style="{'width': 500 + 'px'}"
              >
              </el-input>
            </el-form-item>
          </div>
        </div>
        <el-row class="mt16">
          <el-col :span="24">
            <el-form-item label="检测图片：">
              <div class="cbo">
                <div class="img-box">
                  <el-upload
                    accept=".jpeg,.png,.jpg,.bmp,.gif"
                    :action="baseUrl + '/upload/file'"
                    list-type="picture-card"
                    :on-preview="handlePreview"
                    :file-list="batchForm[activeName].img"
                    :on-success="handlePicSuccess"
                    :on-remove="handlePicRemove">
                    <i class="el-icon-plus"></i>
                  </el-upload>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <!-- 安定性 -->
    <div v-if="activeName === 'CEMENT_PARAM_ADX'">
      <el-form :disabled="loading2 || experimentStatus == 3" label-width="120px">
        <div class="mt16 jcrq-box">
          <el-form-item label="检测日期：">
            <el-date-picker
              type="date"
              v-model="batchForm[activeName].jcrq"
              placeholder="选择日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
        </div>
        <p class="info-form-title">雷氏法</p>
        <div class="info-form-piece">
          <el-row>
            <el-col :span="8">
              <el-form-item label="煮前A：">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  v-model="batchForm[activeName].lsf.jza"
                  @input="val => setADXlsf()"
                  placeholder="请输入"
                  :style="{'width': 200 + 'px'}"
                >
                  <template slot="append">mm</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="煮后C：">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  v-model="batchForm[activeName].lsf.jzc"
                  @input="val => setADXlsf()"
                  placeholder="请输入"
                  :style="{'width': 200 + 'px'}"
                >
                  <template slot="append">mm</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="增加距离C-A：">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="batchForm[activeName].lsf.zjjl" 
                  disabled
                  :style="{'width': 200 + 'px'}"
                >
                  <template slot="append">mm</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="煮前A：">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  v-model="batchForm[activeName].lsf.jza1"
                  @input="val => setADXlsf('1')"
                  placeholder="请输入"
                  :style="{'width': 200 + 'px'}"
                >
                  <template slot="append">mm</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="煮后C：">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  v-model="batchForm[activeName].lsf.jzc1"
                  @input="val => setADXlsf('1')"
                  placeholder="请输入"
                  :style="{'width': 200 + 'px'}"
                >
                  <template slot="append">mm</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="增加距离C-A：">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="batchForm[activeName].lsf.zjjl1" 
                  disabled
                  :style="{'width': 200 + 'px'}"
                >
                  <template slot="append">mm</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="C-A平均值：">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="batchForm[activeName].lsf.pjz" 
                  disabled
                  :style="{'width': 200 + 'px'}"
                >
                  <template slot="append">mm</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="16">
              <el-form-item label="结论：">
                <el-input
                  v-model="batchForm[activeName].lsf.jl" 
                  disabled
                  placeholder="请输入"
                  :style="{'width': 180 + 'px'}"
                >
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <p class="info-form-title mt16">试饼法</p>
        <div class="info-form-piece">
          <el-row>
            <el-col :span="8">
              <el-form-item label="有无纹裂：">
                <el-select @input="setADXsbf" v-model="batchForm[activeName].sbf.ywlw" placeholder="请选择">
                  <el-option label="是" value="是">
                  </el-option>
                  <el-option label="否" value="否">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="是否弯曲：">
                <el-select @input="setADXsbf" v-model="batchForm[activeName].sbf.sfwq" placeholder="请选择">
                  <el-option label="是" value="是">
                  </el-option>
                  <el-option label="否" value="否">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="结论：">
                <el-input
                  v-model="batchForm[activeName].sbf.jl" 
                  disabled
                  placeholder="请输入"
                  :style="{'width': 200 + 'px'}"
                >
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <el-row>
          <el-col :span="24">
            <el-form-item label="检测图片：">
              <div class="cbo">
                <div class="img-box">
                  <el-upload
                    accept=".jpeg,.png,.jpg,.bmp,.gif"
                    :action="baseUrl + '/upload/file'"
                    list-type="picture-card"
                    :on-preview="handlePreview"
                    :file-list="batchForm[activeName].img"
                    :on-success="handlePicSuccess"
                    :on-remove="handlePicRemove">
                    <i class="el-icon-plus"></i>
                  </el-upload>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <!-- 流动度 -->
    <div v-if="activeName === 'CEMENT_PARAM_LDX'" class="custom-form">
      <el-form :disabled="loading2 || experimentStatus == 3" :inline="true" label-width="90px">
        <div class="mt16 jcrq-box">
          <el-form-item label="检测日期：">
            <el-date-picker
              type="date"
              v-model="batchForm[activeName].jcrq"
              placeholder="选择日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
        </div>
        <div class="mt16 flex-box">
          <el-form-item label="直径1：" class="flex-item flex-box" >
            <el-input
              v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @input="val => setLDX()"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].zj1" 
              placeholder="请输入"
            >
              <template slot="append">mm</template>
            </el-input>
          </el-form-item>
          <el-form-item class="flex-item flex-box" label="直径2：">
            <el-input
              v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].zj2"
              @input="val => setLDX()"
              placeholder="请输入"
            >
              <template slot="append">mm</template>
            </el-input>
          </el-form-item>
          <el-form-item class="flex-item flex-box">
          </el-form-item>
        </div>
        <div class="mt16 flex-box">
          <el-form-item label="水泥用量：" class="flex-item flex-box" >
            <el-input
              v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].snyl" 
              placeholder="450"
            >
              <template slot="append">g</template>
            </el-input>
          </el-form-item>
          <el-form-item class="flex-item flex-box" label="流动度：">
            <el-input
              v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].ldd"
              disabled
            >
              <template slot="append">mm</template>
            </el-input>
          </el-form-item>
          <el-form-item class="flex-item flex-box">
          </el-form-item>
        </div>
        
        <el-row class="mt16">
          <el-col :span="24">
            <el-form-item label="检测图片：">
              <div class="cbo">
                <div class="img-box">
                  <el-upload
                    accept=".jpeg,.png,.jpg,.bmp,.gif"
                    :action="baseUrl + '/upload/file'"
                    list-type="picture-card"
                    :on-preview="handlePreview"
                    :file-list="batchForm[activeName].img"
                    :on-success="handlePicSuccess"
                    :on-remove="handlePicRemove">
                    <i class="el-icon-plus"></i>
                  </el-upload>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    
    <!-- 密度 -->
    <div v-if="activeName === 'CEMENT_PARAM_MD'" class="custom-form">
      <el-form :disabled="loading2 || experimentStatus == 3" :inline="true">
        <div class="mt16 jcrq-box">
          <el-form-item label="检测日期：">
            <el-date-picker
              type="date"
              v-model="batchForm[activeName].jcrq"
              placeholder="选择日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
        </div>
        <div v-for="(item, index) in batchForm[activeName].dycsy" :key="index">
          <p class="info-form-title mt16">{{index == 0 ? '第一次试验' : '第二次试验'}}</p>
          <div class="info-form-piece flex-box">
            <el-form-item label="试样质量：" class="flex-item flex-box">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @input="val => setYPMD(index)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                v-model="item.syzl" 
                placeholder="请输入"
              >
                <template slot="append">g</template>
              </el-input>
            </el-form-item>
            <el-form-item class="flex-item flex-box" label="第一次读数：">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                @input="val => setYPMD(index)"
                v-model="item.dycds"
                placeholder="请输入"
              >
                <template slot="append">ml</template>
              </el-input>
            </el-form-item>
            <el-form-item label="第二次读数：" class="flex-item flex-box">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                @input="val => setYPMD(index)"
                v-model="item.decds"
                placeholder="请输入"
              >
                <template slot="append">ml</template>
              </el-input>
            </el-form-item>
            <el-form-item label="检测结果：" class="flex-item flex-box">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                disabled
                v-model="item.jcjg"
                placeholder="请输入"
              >
                <template slot="append">g/ml</template>
              </el-input>
            </el-form-item>
          </div>
        </div>
        <div class="line"></div>
        <el-row>
          <el-col :span="10">
            <el-form-item class="flex-box" label="平均值：">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                disabled
                v-model="batchForm[activeName].pjz" 
                :style="{'width': 200 + 'px'}"
              >
                <template slot="append">g/ml</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="14">
            <el-form-item class="flex-box" label="单项结论：">
              <el-input
                :disabled="dxjlDisabled"
                v-model="batchForm[activeName].dxjl" 
                placeholder="请输入"
                :style="{'width': 180 + 'px'}"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row>
          <el-col :span="24">
            <el-form-item label="检测图片：">
              <div class="cbo">
                <div class="img-box">
                  <el-upload
                    accept=".jpeg,.png,.jpg,.bmp,.gif"
                    :action="baseUrl + '/upload/file'"
                    list-type="picture-card"
                    :on-preview="handlePreview"
                    :file-list="batchForm[activeName].img"
                    :on-success="handlePicSuccess"
                    :on-remove="handlePicRemove">
                    <i class="el-icon-plus"></i>
                  </el-upload>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    
    <!-- 细度 -->
    <div v-if="activeName === 'CEMENT_PARAM_XD'" class="custom-form">
      <el-form :disabled="loading2 || experimentStatus == 3" :inline="true" class="mt16">
        <div class="mt16 jcrq-box flex-box">
          <el-form-item label="检测日期：" class="flex-item">
            <el-date-picker
              type="date"
              v-model="batchForm[activeName].jcrq"
              placeholder="选择日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
          
          <el-form-item label="方孔筛规格：" class="flex-item">
            <el-input
              v-manual-update
              @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].fksgg"
              placeholder="请输入"
            >
              <template slot="append">um</template>
            </el-input>
          </el-form-item>
          <div class="flex-item"></div>
        </div>
        <div class="table-box-kzf mt16">
          <div class="tb-title">负压筛析法&nbsp; <!-- 细度取样数量:{{xdNmSampleQuantity}} --></div>
          <div class="tb-content">
            <div class="flex-box tb-item" v-for="(item, index) in batchForm[activeName].fysff" :key="index">
              <div class="tb-left">{{index + 1}}</div>
              <el-form-item label="称量值：" class="flex-item">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="item.clz"
                  @input="val => setXD(index)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  placeholder="请输入"
                  style="width: 180px;"
                >
                  <template slot="append">g</template>
                </el-input>
              </el-form-item>
              <el-form-item label="筛余量：" class="flex-item">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="item.syl"
                  @input="val => setXD(index)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  placeholder="请输入"
                  style="width: 180px;"
                >
                  <template slot="append">g</template>
                </el-input>
              </el-form-item>
              <el-form-item label="筛余率：" class="flex-item">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="item.sylv"
                  disabled
                  placeholder="请输入"
                  style="width: 180px;"
                >
                  <template slot="append">%</template>
                </el-input>
              </el-form-item>
            </div>
          </div>
          <div class="tb-bottom">
            <el-form-item label="平均值：" class="flex-item">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                v-model="batchForm[activeName].pjz"
                disabled
              > 
                <template slot="append">%</template>
              </el-input>
            </el-form-item>
            <el-form-item label="修正系数：" class="flex-item">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                v-model="batchForm[activeName].xzxs"
                @input="val => setXDAll()"
                placeholder="请输入(0-10000)"
                style="width: 180px;"
              >
              </el-input>
            </el-form-item>
          </div>
        </div>
      
        <el-row class="mt16">
          <el-col :span="24">
            <el-form-item label="检测图片：">
              <div class="cbo">
                <div class="img-box">
                  <el-upload
                    accept=".jpeg,.png,.jpg,.bmp,.gif"
                    :action="baseUrl + '/upload/file'"
                    list-type="picture-card"
                    :on-preview="handlePreview"
                    :file-list="batchForm[activeName].img"
                    :on-success="handlePicSuccess"
                    :on-remove="handlePicRemove">
                    <i class="el-icon-plus"></i>
                  </el-upload>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    
    <!-- 强度测定 -->
    <div v-if="activeName === 'CEMENT_PARAM_QDCD'" class="custom-form">
      <div class="mt16">
        <span v-if="batchForm[activeName]?.kyqdsy3d || batchForm[activeName]?.kzqdsy3d" @click="dayNum = '3d'" class="tag-btn" :class="{'tag-btn-primary' : dayNum === '3d'}">抗折、抗压强度试验-3d</span>
        <span v-if="batchForm[activeName]?.kyqdsy7d || batchForm[activeName]?.kzqdsy7d" @click="dayNum = '7d'" class="tag-btn" :class="{'tag-btn-primary' : dayNum === '7d'}">抗折、抗压强度试验-7d</span>
        <span v-if="batchForm[activeName]?.kyqdsy28d || batchForm[activeName]?.kzqdsy28d" @click="dayNum = '28d'" class="tag-btn" :class="{'tag-btn-primary' : dayNum === '28d'}">抗折、抗压强度试验-28d</span>
      </div>
      <el-form :disabled="loading2 || experimentStatus == 3" :inline="true" class="mt16">
        <div class="mt16 jcrq-box">
          <el-form-item label="检测日期：">
            <el-date-picker
              type="date"
              v-model="batchForm[activeName]['jcrq' + dayNum]"
              placeholder="选择日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
        </div>
        
        <div class="table-box-kzf mt16" v-if="batchForm[activeName]['kzqdsy' + dayNum]">
          <div class="tb-title">抗折强度试验-{{dayNum}}</div>
          <div class="tb-content">
            <div class="flex-box tb-item" v-for="(item, index) in batchForm[activeName]['kzqdsy' + dayNum].kzqdsyInfo" :key="index">
              <div class="tb-left">{{index + 1}}</div>
              <el-form-item label="荷载：" class="flex-item">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="item.hz"
                  @input="val => setKZQD(index)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  placeholder="请输入"
                  style="width: 135px;"
                >
                  <template slot="append">KN</template>
                </el-input>
              </el-form-item>
              <el-form-item label="距离：" class="flex-item">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="item.jl"
                  @input="val => setKZQD(index)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  placeholder="请输入"
                  style="width: 135px;"
                >
                  <template slot="append">mm</template>
                </el-input>
              </el-form-item>
              <el-form-item label="试件宽度：" class="flex-item">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="item.sjkd"
                  @input="val => setKZQD(index)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  placeholder="默认100"
                  style="width: 135px;"
                >
                  <template slot="append">mm</template>
                </el-input>
              </el-form-item>
              <el-form-item label="抗折强度：" class="flex-item">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="item.kzqd"
                  disabled
                  placeholder="请输入"
                  style="width: 135px;"
                >
                  <template slot="append">Mpa</template>
                </el-input>
              </el-form-item>
            </div>
          </div>
          <div class="tb-bottom">
            <el-form-item label="平均值：" class="flex-item">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                v-model="batchForm[activeName]['kzqdsy' + dayNum].pjz"
                disabled
              >
                <template slot="append">Mpa</template>
              </el-input>
            </el-form-item>
            <el-form-item label="单项结论：" class="flex-item">
              <el-input
                type="text"
                v-model="batchForm[activeName]['kzqdsy' + dayNum].dxjl"
                :disabled="dxjlDisabled"
                style="width: 280px;"
              >
              </el-input>
            </el-form-item>
          </div>
        </div>
        
        <div class="table-box-kzf mt16" v-if="batchForm[activeName]['kyqdsy' + dayNum]">
          <div class="tb-title">抗压强度试验-{{dayNum}}</div>
          <div class="tb-content">
            <div class="flex-box tb-item" v-for="(item, index) in batchForm[activeName]['kyqdsy' + dayNum].kyqdsyInfo" :key="index">
              <div class="tb-left">{{index + 1}}</div>
              <el-form-item label="荷载：" class="flex-item">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="item.hz"
                  @input="val => setKYQD(index)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  placeholder="请输入"
                  style="width: 180px;"
                >
                  <template slot="append">KN</template>
                </el-input>
              </el-form-item>
              <el-form-item label="受压面积：" class="flex-item">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="item.symj"
                  @input="val => setKYQD(index)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  placeholder="默认1600"
                  style="width: 180px;"
                >
                  <template slot="append">mm²</template>
                </el-input>
              </el-form-item>
              <el-form-item label="抗压强度：" class="flex-item">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="item.kyqd"
                  disabled
                  placeholder="请输入"
                  style="width: 180px;"
                >
                  <template slot="append">Mpa</template>
                </el-input>
              </el-form-item>
            </div>
          </div>
          <div class="tb-bottom">
            <el-form-item label="平均值：" class="flex-item">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                v-model="batchForm[activeName]['kyqdsy' + dayNum].pjz"
                disabled
              >
                <template slot="append">Mpa</template>
              </el-input>
            </el-form-item>
            <el-form-item label="单项结论：" class="flex-item">
              <el-input
                type="text"
                v-model="batchForm[activeName]['kyqdsy' + dayNum].dxjl"
                :disabled="dxjlDisabled"
                style="width: 280px;"
              >
              </el-input>
            </el-form-item>
          </div>
        </div>
      
        <el-row class="mt16">
          <el-col :span="24">
            <el-form-item label="检测图片：">
              <div class="cbo">
                <div class="img-box">
                  <el-upload
                    accept=".jpeg,.png,.jpg,.bmp,.gif"
                    :action="baseUrl + '/upload/file'"
                    list-type="picture-card"
                    :on-preview="handlePreview"
                    :file-list="batchForm[activeName].img"
                    :on-success="handlePicSuccess"
                    :on-remove="handlePicRemove">
                    <i class="el-icon-plus"></i>
                  </el-upload>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    
    <!-- 比表面积 -->
    <!-- <div v-if="activeName === 'CEMENT_PARAM_BBMJ'" class="custom-form">
      <el-form :disabled="loading2 || experimentStatus == 3" :inline="true" class="mt16">
        <div class="mt16 jcrq-box">
          <el-form-item label="检测日期：">
            <el-date-picker
              type="date"
              v-model="batchForm[activeName].jcrq"
              placeholder="选择日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
        </div>
        <div class="mt16 flex-box">
          <el-form-item label="试料层体积：" class="flex-item flex-box" >
            <el-input
              v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @input="setSYZL"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].slctj" 
              placeholder="请输入"
            >
              <template slot="append">cm³</template>
            </el-input>
          </el-form-item>
          <el-form-item class="flex-item flex-box" label="密度：">
            <el-input
              v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @input="setBbmjAll"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].md"
              disabled
            >
              <template slot="append">g/ml</template>
            </el-input>
          </el-form-item>
          <el-form-item label="试料层空隙率：" class="flex-item flex-box" >
            <el-input
              v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              @input="setBbmjAll"
              @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
              v-model="batchForm[activeName].slckxl" 
              placeholder="请输入"
            >
              <template slot="append">%</template>
            </el-input>
          </el-form-item>
        </div>
        <div class="mt16 flex-box">
          <el-form-item label="试样质量：" class="flex-item flex-box" >
            <el-input
              v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
              v-model="batchForm[activeName].syzl" 
              disabled
            >
              <template slot="append">g</template>
            </el-input>
          </el-form-item>
          <div class="flex-item"></div>
          <div class="flex-item"></div>
        </div>
        
        <div class="table-box-kzf mt16" v-for="(item, index) in batchForm[activeName].bbdycsy" :key="index">
          <div class="tb-title">{{index == 0 ? '第一次试验': '第二次试验'}} </div>
          <div class="tb-content">
            <div class="flex-box tb-item">
              <div class="tb-left" style="width: 88px;"></div>
              <el-form-item label="试验温度：" class="flex-item">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="item.bzsywd"
                  @input="val => setBBMJ(index)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  placeholder="请输入"
                  style="width: 135px;"
                >
                  <template slot="append">°C</template>
                </el-input>
              </el-form-item>
              <el-form-item label="标准密度：" class="flex-item">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="item.bzmd"
                  @input="val => setBBMJ(index)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  placeholder="请输入"
                  style="width: 135px;"
                >
                  <template slot="append">g/ml</template>
                </el-input>
              </el-form-item>
              <el-form-item label="标准空隙率：" class="flex-item">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="item.bzkxl"
                  @input="val => setBBMJ(index)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  placeholder="请输入"
                  style="width: 135px;"
                >
                  <template slot="append">%</template>
                </el-input>
              </el-form-item>
            </div>
            <div class="flex-box tb-item">
              <div class="tb-left" style="width: 88px; margin-top: -24px;">标准样品</div>
              <el-form-item label="降落时间：" class="flex-item">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="item.bzjlsj"
                  @input="val => setBBMJ(index)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+','.'].includes(e.key))?false:e.returnValue"
                  placeholder="请输入"
                  style="width: 135px;"
                >
                  <template slot="append">s</template>
                </el-input>
              </el-form-item>
              <el-form-item label="空气粘度：" class="flex-item">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="item.bzkqnd"
                  @input="val => setBBMJ(index)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  placeholder="请输入"
                  style="width: 135px;"
                >
                  <template slot="append">μPa·s</template>
                </el-input>
              </el-form-item>
              <el-form-item label="比表面积：" class="flex-item">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="item.bzbbmj"
                  @input="val => setBBMJ(index)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  disabled
                  style="width: 135px;"
                >
                  <template slot="append">c㎡/g</template>
                </el-input>
              </el-form-item>
            </div>
            <div class="flex-box tb-item">
              <div class="tb-left pt16" style="width: 88px;">被测样品</div>
              <el-form-item label="校准温度：" class="flex-item pt16">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="item.bcjywd"
                  @input="val => setBBMJ(index)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  placeholder="请输入"
                  style="width: 135px;"
                >
                  <template slot="append">°C</template>
                </el-input>
              </el-form-item>
              <el-form-item label="降落时间：" class="flex-item pt16">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="item.bcjlsj"
                  @input="val => setBBMJ(index)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+','.'].includes(e.key))?false:e.returnValue"
                  placeholder="请输入"
                  style="width: 135px;"
                >
                  <template slot="append">s</template>
                </el-input>
              </el-form-item>
              <el-form-item label="空气粘度：" class="flex-item pt16">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="item.bckqnd"
                  @input="val => setBBMJ(index)"
                  @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                  placeholder="请输入"
                  style="width: 135px;"
                >
                  <template slot="append">μPa·s</template>
                </el-input>
              </el-form-item>
              <el-form-item label="比表面积：" class="flex-item pt16">
                <el-input
                  v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                  v-model="item.bcbbmj"
                  disabled
                  style="width: 135px;"
                >
                  <template slot="append">c㎡/g</template>
                </el-input>
              </el-form-item>
            </div>
          </div>
        </div>
        
        <el-row class="mt16">
          <el-col :span="10">
            <el-form-item class="flex-box" label="平均值：">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                disabled
                v-model="batchForm[activeName].pjz" 
                :style="{'width': 200 + 'px'}"
              >
                <template slot="append">c㎡/g</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="14">
            <el-form-item class="flex-box" label="单项结论：">
              <el-input
                :disabled="dxjlDisabled"
                v-model="batchForm[activeName].dxjl" 
                placeholder="请输入"
                :style="{'width': 180 + 'px'}"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row class="mt16">
          <el-col :span="24">
            <el-form-item label="检测图片：">
              <div class="cbo">
                <div class="img-box">
                  <el-upload
                    accept=".jpeg,.png,.jpg,.bmp,.gif"
                    :action="baseUrl + '/upload/file'"
                    list-type="picture-card"
                    :on-preview="handlePreview"
                    :file-list="batchForm[activeName].img"
                    :on-success="handlePicSuccess"
                    :on-remove="handlePicRemove">
                    <i class="el-icon-plus"></i>
                  </el-upload>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div> -->
    
    <div v-if="activeName === 'CEMENT_PARAM_BBMJ'" class="custom-form">
      <el-form :disabled="loading2 || experimentStatus == 3" :inline="true" class="mt16">
        <div class="mt16 jcrq-box">
          <el-form-item label="检测日期：">
            <el-date-picker
              type="date"
              v-model="batchForm[activeName].jcrq"
              placeholder="选择日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="计算方法：">
            <el-select v-model="batchForm[activeName].wa" placeholder="请选择" style="width: 155px;">
              <el-option label="试验时温差小于等于3度" value="试验时温差小于等于3度"></el-option>
            </el-select>
          </el-form-item>
        </div>
        
        <div class="table-box-kzf mt16" v-for="(item, index) in batchForm[activeName].bbmjsy" :key="index">
          <div class="tb-title">{{index == 0 ? '第一次试验': '第二次试验'}} </div>
          <div class="tb-content">
            <div class="flex-box tb-item">
              <div class="tb-left" style="width: 88px;">标准样品</div>
              <el-row>
                <el-form-item label="密度(ρs)：" class="flex-item">
                  <el-input
                    v-manual-update

                  @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                    v-model="item.bzypmd"
                    @input="val => setNewBBMJ(index, 'bz')"
                    @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                    placeholder="请输入"
                    style="width: 115px;"
                  >
                  </el-input>
                </el-form-item>
                <el-form-item label="空隙率(εs)：" class="flex-item">
                  <el-input
                    v-manual-update

                  @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                    v-model="item.bzypkxl"
                    @input="val => setNewBBMJ(index, 'bz')"
                    @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                    placeholder="请输入"
                    style="width: 115px;"
                  >
                  </el-input>
                </el-form-item>
                <el-form-item label="页面降落时间(Ts)：" class="flex-item">
                  <el-input
                    v-manual-update

                  @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                    v-model="item.bzypymjlsj"
                    @input="val => setNewBBMJ(index, 'bz')"
                    @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                    placeholder="请输入"
                    style="width: 115px;"
                  >
                    <template slot="append">s</template>
                  </el-input>
                </el-form-item>
                <el-form-item label="比表面积(Ss)：" class="flex-item">
                  <el-input
                    v-manual-update

                  @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                    v-model="item.bzypbbmj"
                    @input="val => setNewBBMJ(index, 'bz')"
                    @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                    style="width: 115px;"
                  >
                    <template slot="append">c㎡/g</template>
                  </el-input>
                </el-form-item>
              </el-row>
            </div>
            <div style="margin-top: 20px;"></div>
            <div class="flex-box tb-item">
              <div class="tb-left" style="width: 88px;">被测样品</div>
              <el-row>
                <el-form-item label="密度(ρ)：" class="flex-item">
                  <el-input
                    v-manual-update

                  @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                    v-model="item.bcypmd"
                    @input="val => setNewBBMJ(index, 'bc')"
                    @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                    placeholder="请输入"
                    style="width: 115px;"
                  >
                  </el-input>
                </el-form-item>
                <el-form-item label="空隙率(ε)：" class="flex-item">
                  <el-input
                    v-manual-update

                  @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                    v-model="item.bcypkxl"
                    @input="val => setNewBBMJ(index, 'bc')"
                    @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                    placeholder="请输入"
                    style="width: 115px;"
                  >
                  </el-input>
                </el-form-item>
                <el-form-item label="页面降落时间(T)：" class="flex-item">
                  <el-input
                    v-manual-update

                  @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                    v-model="item.bcypymjlsj"
                    @input="val => setNewBBMJ(index, 'bc')"
                    @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                    placeholder="请输入"
                    style="width: 115px;"
                  >
                    <template slot="append">s</template>
                  </el-input>
                </el-form-item>
                <el-form-item label="比表面积(Ss)：" class="flex-item">
                  <el-input
                    v-manual-update

                  @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                    v-model="item.bcypbbmj"
                    @input="val => setNewBBMJ(index, 'bc')"
                    @keydown.native="e=>e.returnValue=(['e','E','-','+'].includes(e.key))?false:e.returnValue"
                    disabled
                    style="width: 115px;"
                  >
                    <template slot="append">c㎡/g</template>
                  </el-input>
                </el-form-item>
              </el-row>
            </div>
          </div>
        </div>
        
        <el-row class="mt16">
          <el-col :span="10">
            <el-form-item class="flex-box" label="平均值：">
              <el-input
                v-manual-update

                @keyup.native="e=> e.target.value = e.target.value * 1 > 10000 ? e.target.value.match(/10000|\d{1,4}/) : e.target.value.match(/\d+\.?\d{0,4}/)"
                disabled
                v-model="batchForm[activeName].pjz" 
                :style="{'width': 200 + 'px'}"
              >
                <template slot="append">c㎡/g</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="14">
            <el-form-item class="flex-box" label="单项结论：">
              <el-input
                :disabled="dxjlDisabled"
                v-model="batchForm[activeName].dxjl" 
                placeholder="请输入"
                :style="{'width': 180 + 'px'}"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row class="mt16">
          <el-col :span="24">
            <el-form-item label="检测图片：">
              <div class="cbo">
                <div class="img-box">
                  <el-upload
                    accept=".jpeg,.png,.jpg,.bmp,.gif"
                    :action="baseUrl + '/upload/file'"
                    list-type="picture-card"
                    :on-preview="handlePreview"
                    :file-list="batchForm[activeName].img"
                    :on-success="handlePicSuccess"
                    :on-remove="handlePicRemove">
                    <i class="el-icon-plus"></i>
                  </el-upload>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    
    <image-viewer
      v-if="imgViewerVisible"
      :urlList="[previewUrl]"
      :on-close="onClose"
    >
    </image-viewer>
  </div>
</template>

<script>
import {conclusion_FMH_XD, conclusion_FMH_SSL, conclusion_FMH_XSLB} from "./conclusion.js"
import {add,  sub,  mul,  div, roundToDecimalPlace, calcEquation} from "@/utils/calculate.js"
import { standard } from "./config.js"
import ImageViewer from "element-ui/packages/image/src/image-viewer";
import moments from 'moment'
import moment from "@/utils/moment.js"
import util from "../../../common/js/util.js";

export default {
  name:'userMgt',
  components: {
    ImageViewer
  },
  props: {
    activeId: {
      type: Number | String,
    },
    experimentStatus: {
      type: Number | String,
    },
    sampleLevel: {
      type: String,
      default: '1'
    },
    entrustTime: {
      type: String,
      default: new Date().toISOString().split('T')[0],
    },
    
  },
  watch: {
    batchForm: {
      handler(newVal, oldVal){
        // if(!newVal.CEMENT_PARAM_FMH_XD.xdInfo[0].jzxs){
        //   this.CEMENT_PARAM_FMH_XD.xdInfo[0].jzxs = 1.10;
        // }
        // if(!newVal.CEMENT_PARAM_FMH_XD.xdInfo[1].jzxs){
        //   this.CEMENT_PARAM_FMH_XD.xdInfo[1].jzxs = 1.10;
        // }
      },
      deep: true,
      //immediate: true
    },
    entrustTime: {
      handler(newValue, oldValue){
        if(newValue){
          this.setDefaultTime(this.batchForm)
        }
      },
      immediate: true
    },
    
  },
  computed: {
    xd(){
      
      //let a = this.batchForm.CEMENT_PARAM_FMH_XD.xdInfo[0] + 
    }
  },
  data() {
    return {
      defaultDate: moment.today(),
      xdNmSampleQuantity: 1,//水泥细度试样的质量，列表中取样数量，
      
      infoForm: '',
      filePrefix: this.$store.state.loginStore.userInfo.filePrefix,
      baseUrl: process.env.NODE_ENV === 'development' ? "/api" : "/mes-platform",
      activeName: 'quick',
      activeInfoName: 'dycsy',
      quickForm: {},//快检数据
      quickData: [],//快检列表
      batchForm: {
        CEMENT_PARAM_XGL_HNL: {
        },
      },//批检数据
      batchData: [],//批检列表
      
      loading2: false,
      
      previewUrl: "",
      imgViewerVisible: false,
      dayNum: '3d',
      
      sampleLevel2: 'PO42.5',
      accordingObj: {},
      dxjlDisabled: false,
    };
  },
  
  created() {
  },
  methods: {
    
        isEmptyValue(value) {
            // 处理用户输入的特殊字符串
            if (value === 'null') return true;
            if (value === 'undefined') return true;
            if (value === '') return true;
            
            // 处理实际的JavaScript值
            if (value === null) return true;
            if (value === undefined) return true;
            if (value === '') return true;
            
            return false;
        },
    
        setDefaultTime(val){

      // 安定性检测时间=委托时间+1天，
      // 3天抗压抗折=委托时间+3天，7天抗压抗折=委托时间+7天，28天抗压抗折=委托时间+28天，其他默认为当天
      // 强度测定 batchForm[activeName]['jcrq' + dayNum]  3d 7d 28d
      if(val.CEMENT_PARAM_QDCD && this.isEmptyValue(val.CEMENT_PARAM_QDCD?.jcrq3d)){
        val.CEMENT_PARAM_QDCD['jcrq3d'] = util.calculateFutureDate(this.entrustTime, 3)
      }
      
      if(val.CEMENT_PARAM_QDCD && this.isEmptyValue(val.CEMENT_PARAM_QDCD?.jcrq7d)){
        val.CEMENT_PARAM_QDCD['jcrq7d'] = util.calculateFutureDate(this.entrustTime, 7)
      }
      
      if(val.CEMENT_PARAM_QDCD && this.isEmptyValue(val.CEMENT_PARAM_QDCD?.jcrq28d)){
        val.CEMENT_PARAM_QDCD['jcrq28d'] = util.calculateFutureDate(this.entrustTime, 28)
      }

      // 安定
      if(val.CEMENT_PARAM_ADX && this.isEmptyValue(val.CEMENT_PARAM_ADX?.jcrq)){
        val.CEMENT_PARAM_ADX['jcrq'] = util.calculateFutureDate(this.entrustTime, 1)
      }
      // 标准稠度
      if(val.CEMENT_PARAM_BZCD && this.isEmptyValue(val.CEMENT_PARAM_BZCD?.jcrq)){
        val.CEMENT_PARAM_BZCD['jcrq'] = util.calculateFutureDate(this.entrustTime, 0)
      }
      
      // 凝结时间
      if(val.CEMENT_PARAM_NJSJ && this.isEmptyValue(val.CEMENT_PARAM_NJSJ?.jcrq)){
        val.CEMENT_PARAM_NJSJ['jcrq'] = util.calculateFutureDate(this.entrustTime, 0)
      }
          // 流动度
      if(val.CEMENT_PARAM_LDX && this.isEmptyValue(val.CEMENT_PARAM_LDX?.jcrq)){
        val.CEMENT_PARAM_LDX['jcrq'] = util.calculateFutureDate(this.entrustTime, 0)
      }
      
      // 密度
      if(val.CEMENT_PARAM_MD && this.isEmptyValue(val.CEMENT_PARAM_MD?.jcrq)){
        val.CEMENT_PARAM_MD['jcrq'] = util.calculateFutureDate(this.entrustTime, 0)
      }
      
      // 细度
      if(val.CEMENT_PARAM_XD && this.isEmptyValue(val.CEMENT_PARAM_XD?.jcrq)){
        val.CEMENT_PARAM_XD['jcrq'] = util.calculateFutureDate(this.entrustTime, 0)
      }
      // 比表面积
      if(val.CEMENT_PARAM_BBMJ && this.isEmptyValue(val.CEMENT_PARAM_BBMJ?.jcrq)){
        val.CEMENT_PARAM_BBMJ['jcrq'] = util.calculateFutureDate(this.entrustTime, 0)
      }

  

        },
    //公用
    //判断结论
    getPJZUtil(data,key,n){//数组，key，保留小数
      let len = 0,num = 0;
      data.forEach(item =>{
        if(item[key] !== ''){
          len++;
          num += item[key] * 1
        }
      })
      //2次结果之和除以2，保留2位小数，四舍五入
      let arr = [
        {
          v: num,
        },{
          k: '/',
          v: len === 0 ? '' : len
        }
      ]
      return calcEquation(arr, n);
    },
    //判断结论
    conclusion(num, key){
      if(num === ''){
        return ''
      }
      const aName = this.activeName;
      if(!key){
        key = aName.match(/[^_]+$/)[0];
        key = key.toLowerCase();
      }

      if (key === 'adx') {
        // 当试验为安定性的时候，取雷氏法的对象
        key = "lsf";
      }
      if(!this.accordingObj[aName][key]){
        return ''
      }
      const min = this.accordingObj[aName][key].min;
      const max = this.accordingObj[aName][key].max;
      
      console.log(num, aName, key, min, max);
      if(min !== null && num < min){
        // this.$parent.testInfoForm.isQualified = 2;
        return '不合格'
      }
      if(max !== null && num > max){
        // this.$parent.testInfoForm.isQualified = 2;
        return '不合格'
      }
      return '合格';
    },
    // 完成试验的时候提示：标准稠度试验异常（后端），非空校验，单位mm 
    //注意3、水泥含量：根据列表中取样数量得来
    // 标准稠度
    setBZCD(){
      const data = this.batchForm[this.activeName];
      //标准稠度用水量=用水量/水泥含量*100%
      let arr = [
        {
          v: data.ysl,
        },{
          k: '/',
          v: data.snhl,
        },{
          k: '*',
          v: 100,
        }
      ]
      let res = calcEquation(arr, 1)
      this.batchForm[this.activeName].bzcdysl = res;
    },
    setJssj(val){
      if(this.batchForm.CEMENT_PARAM_NJSJ?.njsj?.zssj){
        this.batchForm.CEMENT_PARAM_NJSJ.njsj.zssj = val
      }
    },
    getMinutesBetweenDates(date1, date2) {
      return moments(date2).diff(moments(date1), 'minutes');
    },
    // <!-- 凝结时间 -->
    //  
    setNJSJ(type){//a 全部，s初，e终
      const data = this.batchForm[this.activeName];
      let jssj = '';
      let ddcnsj = '';
      let ddznsj = '';
      if(type === 'a'){//注水时间
        if(this.batchForm.CEMENT_PARAM_BZCD?.jssj){
          this.batchForm.CEMENT_PARAM_BZCD.jssj = data.njsj.jssj
        }
      }
      // 结论：
      // 初凝：大于等于45分钟视为合格，否则视为不合格
      // 终凝：大于0，小于等于600分钟视为合格，否则视为不合格
      if(data.njsj.jssj){
        jssj = new Date(data.njsj.jssj);
        if(data.njsj.ddcnsj && (type == 'a' || type == 's')){
          ddcnsj = new Date(data.njsj.ddcnsj);
          let cnsj = this.getMinutesBetweenDates(jssj, ddcnsj)
          this.batchForm[this.activeName].jgjs.cnsj = cnsj;
          if( cnsj >= 45){
            this.batchForm[this.activeName].jgjs.cnsjjl = '合格'// + `(合格范围：大于等于45分钟)`
          }else{
            this.batchForm[this.activeName].jgjs.cnsjjl = '不合格'// + `(合格范围：大于等于45分钟)`
          }
        }
        if(data.njsj.ddznsj && (type == 'a' || type == 'e')){
          ddznsj = new Date(data.njsj.ddznsj);
          let znsj = this.getMinutesBetweenDates(jssj, ddznsj)
          this.batchForm[this.activeName].jgjs.znsj = znsj;
          
          if(znsj > 0 && znsj <= 600){
            this.batchForm[this.activeName].jgjs.znsjjl = '合格'// + `(合格范围：大于0，小于等于600分钟)`
          }else{
            this.batchForm[this.activeName].jgjs.znsjjl = '不合格'// + `(合格范围：大于0，小于等于600分钟)`
          }
        }
        this.$forceUpdate();
      }
      
    },
    
    // <!-- 安定性 -->
    setADXlsf(type){
      const data = this.batchForm[this.activeName].lsf;
      
      
      if(type == '1'){
        let res = sub(data.jzc1, data.jza1)
        this.batchForm[this.activeName].lsf.zjjl1 = res;
      }else{
        let res = sub(data.jzc, data.jza)
        this.batchForm[this.activeName].lsf.zjjl = res;
      }
      let odata = [{
        zj: data.zjjl
      },{
        zj: data.zjjl1
      }]
      
      let res = this.getPJZUtil(odata, 'zj', 1);
      this.batchForm[this.activeName].lsf.pjz = res;
      this.batchForm[this.activeName].lsf.jl = this.conclusion(res);
      
    },
    setADXsbf(){
      // 结论：均为否，视为合格，否则，不合格
      // 雷氏法域试饼法二选一，只需要做一个试验，其中一个试验合格，视为安定性试验合格，如2个都做，以雷氏法为准。
      const data = this.batchForm[this.activeName].sbf;
      if(data.ywlw === '否' && data.sfwq == "否"){
        this.batchForm[this.activeName].sbf.jl = '合格'
      }else if(data.ywlw && data.sfwq){
        this.batchForm[this.activeName].sbf.jl = '不合格'
      }else{
        this.batchForm[this.activeName].sbf.jl = ''
      }
    },
    // <!-- 流动度 -->
    setLDX(){
      const data = this.batchForm[this.activeName];
      let arr = [
        {
          v: data.zj1,
        },{
          k: '+',
          v: data.zj2,
        },{
          k: '/',
          v: 2,
        }
      ]
      let res = calcEquation(arr, 0)
      this.batchForm[this.activeName].ldd = res;
    },
    // 密度
    setYPMD(index){
      const data = this.batchForm[this.activeName][this.activeInfoName][index];
      let arr = [
        {
          v: data.syzl,
        },{
          k: '/',
          v: sub(data.decds, data.dycds),
        }
      ]
      let res = calcEquation(arr, 3)
      this.batchForm[this.activeName][this.activeInfoName][index].jcjg = res;
      this.setPJZ();
    },
    setPJZ(){
      // 平均值：两次检测结果之和的平均值，不可编辑
      // 单项结论：大于等于2.8视为合格，反之不合格
      // PS：如两次结果只差绝对值大于0.02g/cm3，则不允许完成试验  提交注意
      const data = this.batchForm[this.activeName][this.activeInfoName];
      
      let res = this.getPJZUtil(data, 'jcjg', 2);
      this.batchForm[this.activeName].pjz = res;
      this.batchForm[this.activeName].dxjl = this.conclusion(res);
      this.batchForm.CEMENT_PARAM_BBMJ.md = res;
    },
    // <!-- 细度 -->
    setXDAll(){
      this.setXD(0)
      this.setXD(1)
      this.setXD(2)
    },
    setXD(index){
      const data = this.batchForm[this.activeName].fysff[index];
      const obj = this.batchForm[this.activeName];
      if(!data.syl || !data.clz){
        this.batchForm[this.activeName].fysff[index].sylv = '';
      }
      let arr = [
        {
          v: data.syl,
        },{
          k: '/',
          v: data.clz,//this.xdNmSampleQuantity,
        },{
          k: '*',
          v: 100,
        },{
          k: '*',
          v: obj.xzxs || 1.1,
        }
      ]
      let res = calcEquation(arr, 2)
      this.batchForm[this.activeName].fysff[index].sylv = res;
      this.setXDPJZ();
    },
    setXDPJZ(){
      const data = this.batchForm[this.activeName].fysff;
      
      let res = this.getPJZUtil(data, 'sylv', 1);
      
      this.batchForm[this.activeName].pjz = res;
      this.batchForm[this.activeName].dxjl = this.conclusion(res);
      
      // let length = 0;
      // let num = data.fysff.reduce((pre, cur) => {
      //   if(cur.sylv > 0){
      //     length ++;
      //   }
      //   return add(pre, cur.sylv)
      // }, 0);
      // if(data.fysff[0].sylv > 0 && data.fysff[1].sylv > 0){
      //   if(Math.abs(data.fysff[0].sylv - data.fysff[1].sylv) > 0.5 || Math.abs(data.fysff[1].sylv - data.fysff[2].sylv) > 0.5){
      //     this.$message.error('筛余率误差大于0.5%, 细度试验异常！')
      //   }
      // }
      // 注意：平均值：两次筛余率之和除以2*修正系数，如两次试验筛余率误差大于0.5%，则完成提示：细度试验异常！
      // let arr = [
      //   {
      //     v: num,
      //   },{
      //     k: '/',
      //     v: length,
      //   }
      // ]
      // let res = calcEquation(arr, 1)
      // this.batchForm[this.activeName].pjz = res;
      // this.batchForm[this.activeName].syzl = this.xdNmSampleQuantity;
    },
    // 强度测定
    setKZQD(index){
      // 抗折强度R以(MPa)表示
      // 公式： R=1. 5*F*L/b
      // F:折断时施加于棱柱体中部的荷载(N);
      // L:支撑圆柱之间的距离(m );
      // b：棱柱体正方形截面的边长(m ); 
      // 平均值：3次抗折强度之和除以3
      const data = this.batchForm[this.activeName]['kzqdsy' + this.dayNum].kzqdsyInfo[index];
      let arr = [
        {
          v: 1.5,
        },{
          k: '*',
          v: data.hz * 1000,
        },{
          k: '*',
          v: data.jl,
        },{
          k: '/',
          v: data.sjkd * data.sjkd * data.sjkd,
        }
      ]
      let res = calcEquation(arr, 1)
      this.batchForm[this.activeName]['kzqdsy' + this.dayNum].kzqdsyInfo[index].kzqd = res;
      this.setKZQDpjz('kzqdsy');
    },
    setKZQDpjz(type){
      const data = this.batchForm[this.activeName][type + this.dayNum];
      
      let res = this.getPJZUtil(data[type + 'Info'], type.slice(0,4), 1);
      this.batchForm[this.activeName][type + this.dayNum].pjz = res;
      this.batchForm[this.activeName][type + this.dayNum].dxjl = this.conclusion(res);
      
      // let num = data[type + 'Info'].reduce((pre, cur) => {
      //   return add(pre, cur[type.slice(0,4)])
      // }, 0);
      // let arr = [
      //   {
      //     v: num,
      //   },{
      //     k: '/',
      //     v: data[type + 'Info'].length,
      //   }
      // ]
      // let res = calcEquation(arr, 1)
      // this.batchForm[this.activeName][type + this.dayNum].pjz = res;
      
      // if(res >= standard[this.sampleLevel2][type + this.dayNum]){
      //   this.batchForm[this.activeName][type + this.dayNum].dxjl = '合格'
      // }else{
      //   this.batchForm[this.activeName][type + this.dayNum].dxjl = '不合格'
      // }
    },
    setKYQD(index){
      const data = this.batchForm[this.activeName]['kyqdsy' + this.dayNum].kyqdsyInfo[index];
      let arr = [
        {
          v: data.hz * 1000,
        },{
          k: '/',
          v: data.symj,
        }
      ]
      let res = calcEquation(arr, 1)
      this.batchForm[this.activeName]['kyqdsy' + this.dayNum].kyqdsyInfo[index].kyqd = res;
      this.setKZQDpjz('kyqdsy');
    },
    //比表面积
    //试样质量=密度*试料层体积*（1-空隙率）
    setBbmjAll(){
      this.setSYZL()
      this.setBBMJ(0)
      this.setBBMJ(1)
    },
    setSYZL(){
      const data = this.batchForm[this.activeName];
      //密度：只读，数字类型，数据来源于水泥密度试验
      
      let arr = [
        {
          v: data.md || 1,
        },{
          k: '*',
          v: data.slctj,
        },{
          k: '*',
          v: sub(1, data.slckxl),
        }
      ]
      let res = calcEquation(arr, 2)
      this.batchForm[this.activeName].syzl = res;
    },
    setBBMJ(index){
      const data = this.batchForm[this.activeName].bbdycsy[index];
      const obj = this.batchForm[this.activeName];
      // 1、当标准样品的试验温度与被测试样温度之差绝对值小于等于3，且空隙率、密度相同的情况下，计算公式如下
      // 2、当空隙率、密度相同，温度之差绝对值大于3
      // 3、密度相同，被测空隙率与标准空隙率不同，且温度之差小于等于3
      // 4、密度相同，被测空隙率与标准空隙率不同，且温度之差大于3
      // 5、如果密度、空隙率不同，且温度之差小于等于3
      // 6、如果密度、空隙率不同，且温度之差大于3
      let mdEqu = parseFloat(data.bzmd) == parseFloat(obj.md);
      let kxlEqu = parseFloat(data.bzkxl) == parseFloat(obj.slckxl);
      let wdc = Math.abs(data.bzsywd *1 - data.bcjywd *1);
      console.log(mdEqu, kxlEqu, data.bzmd,obj.md, data.bzkxl, obj.slckxl)
      
      let arr = [];
      if(wdc <= 3){
        if(mdEqu && kxlEqu){//1
          arr = [
            {
              v: data.bzbbmj,
            },{
              k: '*',
              v: Math.sqrt(data.bcjlsj),
            },{
              k: '/',
              v: Math.sqrt(data.bzjlsj),
            }
          ]
        }else if(mdEqu && !kxlEqu){//3
          //data.bzbbmj*zdata.bcjlsj*(1-data.bzkxl)*zMath.pow(obj.slckxl, 3)/zdata.bzjlsj/(1-obj.slckxl)/zMath.pow(data.bzkxl, 3)
          arr = [
            {
              v: data.bzbbmj,
            },{
              k: '*',
              v: Math.sqrt(data.bcjlsj),
            },{
              k: '*',
              v: (1-data.bzkxl),
            },{
              k: '*',
              v: Math.sqrt(Math.pow(obj.slckxl, 3)),
            },{
              k: '/',
              v: Math.sqrt(data.bzjlsj),
            },{
              k: '/',
              v: (1-obj.slckxl),
            },{
              k: '/',
              v: Math.sqrt(Math.pow(data.bzkxl, 3)),
            }
          ]
        }else if(!mdEqu && !kxlEqu){//5
          //data.bzbbmj*data.bzmd*zdata.bcjlsj*(1-data.bzkxl)*zMath.pow(obj.slckxl, 3)/obj.md/zdata.bzjlsj/(1-obj.slckxl)/zMath.pow(data.bzkxl, 3)
          arr = [
            {
              v: data.bzbbmj,
            },{
              k: '*',
              v: data.bzmd,
            },{
              k: '*',
              v: Math.sqrt(data.bcjlsj),
            },{
              k: '*',
              v: (1-data.bzkxl),
            },{
              k: '*',
              v: Math.sqrt(Math.pow(obj.slckxl, 3)),
            },{
              k: '/',
              v: obj.md,
            },{
              k: '/',
              v: Math.sqrt(data.bzjlsj),
            },{
              k: '/',
              v: (1-obj.slckxl),
            },{
              k: '/',
              v: Math.sqrt(Math.pow(data.bzkxl, 3)),
            }
          ]
        }else{
          this.$message.warning('参数有误，请确认参数！')
        }
      }else{
        if(mdEqu && kxlEqu){//2
          //data.bzbbmj*zdata.bzkqnd*zdata.bcjlsj/zdata.bckqnd/zdata.bzjlsj
          arr = [
            {
              v: data.bzbbmj,
            },{
              k: '*',
              v: Math.sqrt(data.bzkqnd),
            },{
              k: '*',
              v: Math.sqrt(data.bcjlsj),
            },{
              k: '/',
              v: Math.sqrt(data.bckqnd),
            },{
              k: '/',
              v: Math.sqrt(data.bzjlsj),
            }
          ]
        }else if(mdEqu && !kxlEqu){//4
          //data.bzbbmj*zdata.bzkqnd*zdata.bcjlsj*(1-data.bzkxl)*zMath.pow(obj.slckxl, 3)/zdata.bckqnd/zdata.bzjlsj/(1-obj.slckxl)/zMath.pow(data.bzkxl, 3)
          arr = [
            {
              v: data.bzbbmj,
            },{
              k: '*',
              v: Math.sqrt(data.bzkqnd),
            },{
              k: '*',
              v: Math.sqrt(data.bcjlsj),
            },{
              k: '*',
              v: (1-data.bzkxl),
            },{
              k: '*',
              v: Math.sqrt(Math.pow(obj.slckxl, 3)),
            },{
              k: '/',
              v: Math.sqrt(data.bckqnd),
            },{
              k: '/',
              v: Math.sqrt(data.bzjlsj),
            },{
              k: '/',
              v: (1-obj.slckxl),
            },{
              k: '/',
              v: Math.sqrt(Math.pow(data.bzkxl, 3)),
            }
          ]
        }else if(!mdEqu && !kxlEqu){//6
          //data.bzbbmj*data.bzmd*zdata.bzkqnd*zdata.bcjlsj*(1-data.bzkxl)*zMath.pow(obj.slckxl, 3)/obj.md/zdata.bckqnd/zdata.bzjlsj/(1-obj.slckxl)/zMath.pow(data.bzkxl, 3)
          arr = [
            {
              v: data.bzbbmj,
            },{
              k: '*',
              v: data.bzmd,
            },{
              k: '*',
              v: Math.sqrt(data.bzkqnd),
            },{
              k: '*',
              v: Math.sqrt(data.bcjlsj),
            },{
              k: '*',
              v: (1-data.bzkxl),
            },{
              k: '*',
              v: Math.sqrt(Math.pow(obj.slckxl, 3)),
            },{
              k: '/',
              v: obj.md,
            },{
              k: '/',
              v: Math.sqrt(data.bckqnd),
            },{
              k: '/',
              v: Math.sqrt(data.bzjlsj),
            },{
              k: '/',
              v: (1-obj.slckxl),
            },{
              k: '/',
              v: Math.sqrt(Math.pow(data.bzkxl, 3)),
            }
          ]
        }else{
          this.$message.warning('参数有误，请确认参数！')
        }
      }
       
      let res = calcEquation(arr, 2)
      this.batchForm[this.activeName].bbdycsy[index].bcbbmj = res;
      //注意：：2次相差2%，无法完成试验，提示：水泥比表面积异常！
      this.setBbmjPJZ();
    },
    setBbmjPJZ(){
      const data = this.batchForm[this.activeName].bbdycsy;
      
      let res = this.getPJZUtil(data, 'bcbbmj', 2);
      this.batchForm[this.activeName].pjz = res;
      this.batchForm[this.activeName].dxjl = this.conclusion(res);
    },

    setNewBBMJ(index, type){
      // bzypmd": "标准样品密度",
      // "bzypkxl": "标准样品空隙率",
      // "bzypymjlsj": "标准样品页面降落时间",
      // "bzypbbmj": "标准样品比表面积",
      // "bcypmd": "被测样品密度",
      // "bcypkxl": "被测样品空隙率",
      // "bcypymjlsj": "被测样品页面降落时间",

      // 当以上字段都有值的时候开始计算 bcypbbmj
      const data = this.batchForm[this.activeName].bbmjsy[index];
      if(data.bzypmd && data.bzypkxl && data.bzypymjlsj && data.bzypbbmj && data.bcypmd && data.bcypkxl && data.bcypymjlsj){
        // mol = SsPs\sqrt {Ts}(1-Es)\sqrt {{E}^{3}}
        // deno = P\sqrt {Ts}(1-E)\sqrt {{Es}^{3}}

        // Ss = bzypbbmj
        // Ps = bzypmd
        // Ts = bzypymjlsj
        // Es = bzypkxl
        // E = bcypkxl
        // T = bcypymjlsj
        // P = bcypmd
        let mol = (data.bzypbbmj * data.bzypmd * Math.sqrt(data.bcypymjlsj) * (1 - data.bzypkxl) * Math.sqrt(Math.pow(data.bcypkxl, 3)))
        let deno = (data.bcypmd * Math.sqrt(data.bzypymjlsj) * (1 - data.bcypkxl) * Math.sqrt(Math.pow(data.bzypkxl, 3)))

        let arr = [
          {
            v: mol,
          },{
            k: '/',
            v: deno,
          }
        ]
        let res = calcEquation(arr, 0)
        this.batchForm[this.activeName].bbmjsy[index].bcypbbmj = res;
      }

      // 当第一次试验和第二次试验的 bcypbbmj 都有值的时候则计算平均值
      if(this.batchForm[this.activeName].bbmjsy[0].bcypbbmj && this.batchForm[this.activeName].bbmjsy[1].bcypbbmj){
        let arr = [
          {
            v: this.batchForm[this.activeName].bbmjsy[0].bcypbbmj,
          },{
            k: '+',
            v: this.batchForm[this.activeName].bbmjsy[1].bcypbbmj,
          },{
            k: '/',
            v: 2,
          }
        ]
        let res = calcEquation(arr, 0)
        this.batchForm[this.activeName].pjz = res;
        this.batchForm[this.activeName].dxjl = this.conclusion(res);
      }
    },
    
    
    //图片
    handlePicSuccess(response, file, fileList) {
      console.log(fileList)
      let oUploadImg = fileList.map(item =>{
        if(item.response){
          return {
            url: item.response.data.filePath,
            uid: item.uid
          }
        }else{
          return item;
          // if(item.url.startsWith(this.filePrefix)){
          //   return item.url;
          // }else{
          //   return this.filePrefix + item.url;
          // }
        }
      })
      
      if(this.activeName === 'quick'){
        this.quickForm.img = oUploadImg;
      }else{
        this.batchForm[this.activeName].img = oUploadImg;
      }
    },
    handlePicRemove(file, fileList) {
      let oUploadImg = fileList.map(item =>{
        if(item.response){
          return {
            url: item.response.data.filePath,
            uid: item.uid
          }
        }else{
          return item
        }
      })
      if(this.activeName === 'quick'){
        this.quickForm.img = oUploadImg;
      }else{
        this.batchForm[this.activeName].img = oUploadImg;
      }
    },
    
    
    //获取所有快检或批检试验项目名称
    async setExperimentProject(id){
      //获取抗压 抗渗 和其它
      const resDetail = await this.$api.getExperimentDetail({
        experimentId: this.activeId,
        // "testProjectCode":"FINE_AGGREGATE",
        // "checkType": 2
      }, this)
      if(resDetail.succ){
        let quickData = [];
        let batchData = [];//批检
        let quickForm = {};
        quickForm.img = [];
        let batchForm = {};
        resDetail.data.list.forEach(item => {
          let oImgArr = [];
          if(item.objImg && item.objImg != 'null'){
            oImgArr = item.objImg.split(',').map(item => {
              if(item.startsWith(this.filePrefix)){
                return {
                  url: item
                }
              }else{
                return {
                  url: this.filePrefix + item
                }
              }
            }) 
          }else{
            oImgArr = [];
          }
          
          // if(item.testProjectName === '流动性' || item.testProjectName === '保水性' 
          //   || item.testProjectName === '粘聚性'|| item.testProjectName === '坍落度' 
          //   || item.testProjectName === '目测砂率' 
          // ){
          //   if (item.testProjectName === '坍落度') {
          //     if (!item.objJson.tldfs) {
          //       item.objJson.tldfs = "目测";
          //     }
          //   }
          //   if((item.testProjectName === '流动性' || item.testProjectName === '保水性' 
          //   || item.testProjectName === '粘聚性')){
          //     // 接口没有val 字段，根据判断转化一个加进去自己用
          //     item.objJson.val = 'hh'
          //     if (item.objJson.hh == 1) {
          //       item.objJson.val = 'hh'
          //     }else if (item.objJson.yb == 1) {
          //       item.objJson.val = 'yb'
          //     }else if (item.objJson.fcc == 1) {
          //       item.objJson.val = 'fcc'
          //     }
          //   }
          //   quickData.push(item)
          //   quickForm[item.testProjectCode] = item;
          //   quickForm.img = quickForm.img.concat(oImgArr);
          // }else{
            if(!item.objJson?.jcrq){
              item.objJson.jcrq = this.defaultDate;
            }
            item.objJson.img = oImgArr;
            
            batchForm[item.testProjectCode] = JSON.parse(JSON.stringify(item.objJson));
            item.objJson = undefined;
            batchData.push(item);
          // }
        })
        this.quickData = quickData;
        this.quickForm = quickForm;
        this.batchData = batchData;
        this.batchForm = this.setDefaultVal(batchForm);
        
        console.log(this.batchForm,this.batchData)
        if(this.quickData.length > 0){
          this.activeName = 'quick'
        }else{
          this.activeName = this.batchData[0].testProjectCode;
          this.getAccordingToHand()
        }
        
      }  
    },
    getAccordingToHand(){
      this.$api.getAccordingTo({
        testProjectCode: this.activeName,
        materialAbbreviation : this.$parent.activeData.materialAbbreviation,
        materialsName	 : this.$parent.activeData.materialsName,
        materialsSpec : this.$parent.activeData.materialsSpecs
      }, this).then(res =>{
        if(res.data.list.length > 0){
          let o = res.data.list[0].objJson;
          this.accordingObj[this.activeName] = JSON.parse(o);
        }else{
          this.accordingObj[this.activeName] = {};
        }
      })
    },
    //设置默认值
    setDefaultVal(val){
      if(val.CEMENT_PARAM_QDCD?.kyqdsy3d){
        val.CEMENT_PARAM_QDCD?.kyqdsy3d?.kyqdsyInfo.forEach(item =>{
          if(item.symj == ''){
            item.symj = '1600'
          }
        })
        val.CEMENT_PARAM_QDCD?.kzqdsy3d?.kzqdsyInfo.forEach(item =>{
          if(item.jl == ''){
            item.jl = '40'
          }
          if(item.sjkd == ''){
            item.sjkd = '100'
          }
        })
        if (!val.CEMENT_PARAM_QDCD.jcrq3d) {
          val.CEMENT_PARAM_QDCD.jcrq3d = moment.today();
        }
      }
      if(val.CEMENT_PARAM_QDCD?.kyqdsy7d){
        val.CEMENT_PARAM_QDCD.kyqdsy7d?.kyqdsyInfo.forEach(item =>{
          if(item.symj == ''){
            item.symj = '1600'
          }
        })
        val.CEMENT_PARAM_QDCD.kzqdsy7d?.kzqdsyInfo.forEach(item =>{
          if(item.jl == ''){
            item.jl = '40'
          }
          if(item.sjkd == ''){
            item.sjkd = '100'
          }
        })

        if (!val.CEMENT_PARAM_QDCD.jcrq7d) {
          val.CEMENT_PARAM_QDCD.jcrq7d = moment.today();
        }
      }
      if(val.CEMENT_PARAM_QDCD?.kyqdsy28d){
        val.CEMENT_PARAM_QDCD.kyqdsy28d?.kyqdsyInfo.forEach(item =>{
          if(item.symj == ''){
            item.symj = '1600'
          }
        })
        val.CEMENT_PARAM_QDCD.kzqdsy28d?.kzqdsyInfo.forEach(item =>{
          if(item.jl == ''){
            item.jl = '40'
          }
          if(item.sjkd == ''){
            item.sjkd = '100'
          }
        })

        if (!val.CEMENT_PARAM_QDCD.jcrq28d) {
          val.CEMENT_PARAM_QDCD.jcrq28d = moment.today();
        }
      }
      
      if(val.CEMENT_PARAM_LDX?.snyl == ''){
        val.CEMENT_PARAM_LDX.snyl = '450'
      }
      if(val.CEMENT_PARAM_XD?.xzxs == ''){
        val.CEMENT_PARAM_XD.xzxs = '1.10'
      }
      
      if(val.CEMENT_PARAM_BZCD?.snhl == ''){
        val.CEMENT_PARAM_BZCD.snhl = '500'
      }
      
      if(val.CEMENT_PARAM_BBMJ?.slckxl == ''){
        if(this.sampleLevel == 'PI/PII2'){
          val.CEMENT_PARAM_BBMJ.slckxl = '0.5'
        }else{
          val.CEMENT_PARAM_BBMJ.slckxl = '0.53'
        }
      }
      
      return val;
    },
    
    handleClick(tab, event){
      this.getAccordingToHand()
      if(this.activeName === 'CEMENT_PARAM_XD'){
        const params ={
          pageNum: 1,
          pageSize: 2,
          params: {
            experimentId: this.activeId,
            testProjectCode: 'CEMENT_PARAM_XD'
          }
        }
        //params
        //获取列表
        this.$api.getSampleMoreList(params, this).then(res => {
          this.loading3 = false;
          if(res.succ){
            if(res.data.list && res.data.list.length > 0){
              this.xdNmSampleQuantity = res.data.list[0].sampleQuantity
            }
          }else{
            this.$message.error(res.msg || '查询失败')
          }
        })
      }
      
      switch (this.activeName){
        case 'CEMENT_PARAM_MD':
          this.activeInfoName = 'dycsy'
          break;
        default:
          break;
      }

      console.log(this.activeName);
      if(this.activeName === 'CEMENT_PARAM_NJSJ'){
        this.batchForm.CEMENT_PARAM_NJSJ.njsj.jssj = this.batchForm.CEMENT_PARAM_BZCD.zssj
      }

    },
    clearData(){
      this.activeName= 'quick';
      this.quickForm= {};
      this.quickData= [];
      this.batchForm= {};
      this.batchData= [];
    },
    handlePreview(file) {
      this.previewUrl = file.url
      this.imgViewerVisible = true;
    },
    onClose() {
      this.imgViewerVisible = false;
    },
  },
};
</script>

<style scoped lang="scss">
  .flex-item2{
    flex: 2;
  }
  .line{
    height: 1px;
    width: 100%;
    background: #E8E8E8;
    margin: 24px 0;
  }
  .info-form-title{
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 14px;
    color: #1F2329;
    line-height: 20px;
    text-align: left;
    font-style: normal;
    padding: 10px 0 4px;
    margin-bottom: 4px;
  }
  .info-form-piece{
    background: #F8F8F8;
    border-radius: 8px;
    padding: 16px 16px 8px 16px;
  }
  ::v-deep .el-form .el-form-item{
    margin-bottom: 8px;
    margin-right: 10px;
    .el-form-item__content{
      flex: 1;
    }
  }
  ::v-deep .el-input-group__append, 
  ::v-deep .el-input-group__prepend{
    padding: 0 5px;
    text-align: center;
    width: 40px;
  }
  ::v-deep .pr0{
    .el-input__inner{
      padding-right: 0;
    }
  }
  ::v-deep .el-input-number.is-controls-right .el-input__inner{
    text-align: left;
  }
  ::v-deep .textspan .el-input__inner{
    background: transparent;
    border: none;
    padding: 0;
    color: #000;
    margin-left: -10px;
    margin-top: -2px;
  }
  
  
  ::v-deep .el-upload-list__item{
    transition: none !important; 
  }
  
  
  .el-row{
    margin-bottom: 24px;
    &:last-child{
      margin-bottom: 0;
    }
  }
  .mb16{
    margin-bottom: 16px;
  }
  .mt24{
    margin-top: 24px;
  }
  .el-tabs{
    height: 40px;
  }
  ::v-deep .custom-form{
    .el-form--inline{
      .table-box-kzf{
        .el-form-item{
          margin-right: 5px;
        }
      }
      .el-form-item{
        margin-bottom: 8px;
      }
    }
  }
  .table-box-kzf{
    width: 100%;
    background: #FFFFFF;
    border-radius: 4px;
    border: 1px solid #DDDFE6;
    
    .tb-title{
      height: 52px;
      background: #F8F8F8;
      border-radius: 4px 4px 0px 0px;
      border-bottom: 1px solid #DDDFE6;
      line-height: 52px;
      padding-left: 16px;
      font-weight: 600;
      font-size: 14px;
      color: #1F2329;
    }
    .tb-bottom{
      border-top: 1px solid #DDDFE6;
      padding: 16px 16px 8px;
    }
    .tb-left{
      line-height: 40px;
      border-right: 1px solid #DDDFE6;
      width: 72px;
      text-align: center;
    }
    .tb-content{
      .el-form-item{
        padding-left: 16px;
      }
      .tb-item:first-child{
        .tb-left,.el-form-item{
          padding-top: 24px;
        }
      }
      .tb-item:last-child{
        .tb-left,.el-form-item{
          padding-bottom: 16px;
        }
      }
    }
    .tb-center{
      padding: 24px 0;
      margin: 0 8px;
      &>div:first-child{
        margin-bottom: 8px;
      }
    }
    .tb-right{
      height: 88px;
      margin-top: 24px;
      border-left: 1px solid #DDDFE6;
      line-height: 40px;
      padding: 0 16px;
      &>p:first-child{
        margin-bottom: 8px;
      }
    }
  }
  
  .tag-btn{
    display: inline-block;
    cursor: pointer;
    height: 32px;
    border: 1px solid #F8F8F8;
    line-height: 30px;
    padding: 0 8px;
    background: #F8F8F8;
    border-radius: 4px;
    margin-right: 12px;
    font-weight: 500;
    box-sizing: border-box;
    &.tag-btn-succ{
      background: #BBFADA;
      color: #13D466;
      border: 1px solid #13D466;
    }
    &.tag-btn-warn{
      background: rgba(255,206,80,0.26);
      border: 1px solid #FCCB83;
      color: #FFB803;
    }
    &.tag-btn-error{
      background: rgba(255, 95, 88, 0.26);
      border: 1px solid #FF5F58;
      color: #FF5F58;
    }
    &.tag-btn-primary{
      background: rgba(31, 87, 179, .1);
      color: #1F57B3;
      border: 1px dashed rgba(31, 87, 179, .1);
    }
    &.tag-btn-disabled{
      color: rgba(153,153,153,0.85);
    }
    &.tag-btn-dotted{
      padding: 0 13px;
      border-style: dashed;
    }
    &.tag-btn-mini{
      font-size: 16px;
      height: 28px;
      line-height: 26px;
    }
    &:last-child{
      margin: 0;
    }
  }
  .pt16{
    padding-top: 16px;
  }
</style>