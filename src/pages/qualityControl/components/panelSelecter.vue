<template>
    <el-dialog
      :visible.sync="showVisible"
      width="90%" 
      :before-close="handleClose" 
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div class="content-box">
        <div class="flex-box flex-column content">
          <div class="search-box flex-box">
            <el-form class="flex-item" ref="searchForm" :inline="true" :model="searchForm" :disabled="loading">
              <el-row class="flex-row-start" style="margin-bottom: 14px;">
                <el-form-item label="委托编号：">
                  <el-input v-model="searchForm.noKeyword" clearable
                    placeholder="请输入" 
                    style="width: 180px" 
                  />
                </el-form-item>
                <el-form-item label="报告编号：">
                  <el-input v-model="searchForm.reportNo" clearable
                    placeholder="请输入" 
                    style="width: 180px" 
                  />
                </el-form-item>
                <el-form-item label="样品编号：">
                  <el-input v-model="searchForm.sampleNo" clearable
                    placeholder="请输入" 
                    style="width: 180px" 
                  />
                </el-form-item>
                <el-form-item v-if="materialType == 7" label="配合比编号：">
                  <el-input v-model="searchForm.phb" clearable
                    placeholder="请输入" 
                    style="width: 180px" 
                  />
                </el-form-item>
                <el-form-item v-if="materialType != 7" label="厂家：">
                  <el-input v-model="searchForm.factory" clearable
                    placeholder="请输入" 
                    style="width: 180px" 
                  />
                </el-form-item>

                <el-form-item v-if="materialType == 7 && !hideTestProject" label="试验项目：">
                  <el-select
                    v-model="searchForm.entrustExperimentBlur" 
                    filterable clearable 
                    collapse-tags
                    placeholder="请选择试验项目" 
                    style="width: 180px">
                    
                    <el-option
                      v-for="item in checkConfigOpts"
                      :key="item.value"
                      :label="item.label"
                      :value="item.label">
                    </el-option>
                  </el-select>
                </el-form-item>

                <el-form-item v-if="materialType != 7" label="材料类型：">
                  <el-select
                    v-model="searchForm.experimentTypeList" 
                    filterable clearable multiple 
                    collapse-tags
                    placeholder="请选择材料类型" 
                    style="width: 180px">
                    
                    <el-option
                      v-for="item in materialTypeList"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-row>

              <el-row class="flex-row-start" style="margin-bottom: 14px;">
                <el-form-item label="委托时间：">
                  <!-- 
                    format="yyyy-MM-dd" value-format="yyyy-MM-dd HH:mm:ss"
                    :default-time="['00:00:00', '23:59:59']"
                     -->
                  <el-date-picker type="daterange" 
                    v-model="searchForm.takeEffectDate" 
                    start-placeholder="开始时间" end-placeholder="结束时间" 
                    :clearable="true" style="width: 360px"
                  >
                  </el-date-picker>
                </el-form-item>
                <el-form-item label="检验类型：">
                  <el-radio-group v-model="searchForm.checkType" @change="checkTypeChange">
                    <el-radio :label="1">快检</el-radio>
                    <el-radio :label="2">批检</el-radio>
                  </el-radio-group>
                </el-form-item>

                <el-form-item style="float: right;">
                  <el-button type="primary" :disabled="loading" @click="handleFilter">搜索</el-button>
                  <el-button type="text" icon="el-icon-refresh-right" :disabled="loading" @click="resetForm()">重置</el-button>
                  
                </el-form-item>
              </el-row>
            </el-form>
            
          </div>
          
          <div class="flex-item overHide">
            <div class="scroll-div">
              
              <el-table
                :data="tableData"
                v-loading="loading"
                row-key="id"
                :key="2"
                ref="multipleTable"
                @selection-change="panelSelectionChange"
                >
                <!-- :selectable="checkSelectable" -->
                <el-table-column type="selection" align="center" fixed="left" ></el-table-column>
                
                <template v-for="item in tableColumn" >
                  <el-table-column 
                    v-if="item.prop == 'checkType'" 
                    :key="item.prop" 
                    :label="item.label" 
                    :fixed="item.fixed" 
                    align="center"
                  >
                    <template slot-scope="scope">
                      <span class="type-span" style="border-radius: 3px; padding: 1px 3px" :style="{backgroundColor: scope.row.checkType == '1' ? '#61A480' : '#496BF9', color: '#fff'}">{{ scope.row.checkType == '1' ? '快检' : '批检' }}</span>
                    </template>
                  </el-table-column>
                  <!-- 0-待接收  1-待取样  2-试验中 3-已完成 4-已拒绝 -->
                  <!-- 0-待接收  1-待取样  2-试验中 3-已完成 4-已拒绝  5-已作废 -->
                  <el-table-column v-else-if="item.prop == 'experimentStatus'" :key="item.prop" :label="item.label" :fixed="item.fixed" align="center" >
                    <template slot-scope="scope">
                      <el-row class="cell-state">
                        <label v-if="scope.row.experimentStatus == '0'" class="rda-task-state dqr">待接收</label>
                        <label v-if="scope.row.experimentStatus == '1'" class="rda-task-state ddd">待取样</label>
                        <label v-if="scope.row.experimentStatus == '2'" class="rda-task-state dwc">试验中</label>
                        <label v-if="scope.row.experimentStatus == '3'" class="rda-task-state yqx">已完成</label>
                        <label v-if="scope.row.experimentStatus == '4'" class="rda-task-state yqx">已拒绝</label>
                        <label v-if="scope.row.experimentStatus == '5'" class="rda-task-state yqx">已作废</label>
                      </el-row>
                    </template>
                  </el-table-column>
    
                  <template v-else-if="item.prop == 'batch'">
                    <el-table-column v-if="materialType != 7" :key="item.prop" :label="item.label" :fixed="item.fixed" align="center" >
                      <template slot-scope="scope">
                        <el-button v-if="scope.row.batch" type="text" size="small" @click="setBatch(scope.row)">{{scope.row.batch}}</el-button>
                        <el-button v-else type="text" size="small" @click="setBatch(scope.row)">--</el-button>
                      </template>
                    </el-table-column>
                  </template>

                  <el-table-column v-else-if="item.prop == 'finistStatus'" :key="item.prop" :label="item.label" :fixed="item.fixed" align="center" >
                    <template slot-scope="scope">
                      <el-tooltip v-if="scope.row.reason" class="item" effect="dark" :content="scope.row.reason" placement="top">
                        <el-row class="cell-state" >
                          <label style="cursor: pointer;" v-if="scope.row.finistStatus == '未同步'" class="rda-task-state dqr">{{ scope.row.finistStatus }}</label>
                          <label style="cursor: pointer;" v-if="scope.row.finistStatus == '部分同步'" class="rda-task-state yjj">{{ scope.row.finistStatus }}</label>
                          <label style="cursor: pointer;" v-if="scope.row.finistStatus == '已同步'" class="rda-task-state yqx">{{ scope.row.finistStatus }}</label>
                        </el-row>
                      </el-tooltip>
                      <el-row v-else class="cell-state">
                          <label v-if="scope.row.finistStatus == '未同步'" class="rda-task-state dqr">{{ scope.row.finistStatus }}</label>
                          <label v-if="scope.row.finistStatus == '部分同步'" class="rda-task-state yjj">{{ scope.row.finistStatus }}</label>
                          <label v-if="scope.row.finistStatus == '已同步'" class="rda-task-state yqx">{{ scope.row.finistStatus }}</label>
                      </el-row>
                    </template>
                  </el-table-column>

                  <el-table-column
                    v-else
                    :key="item.prop" 
                    :prop="item.prop" 
                    :label="item.label" 
                    :fixed="item.fixed" 
                    :width="item.width || ''"
                    :formatter="item.prop === 'experimentType' ? (row) => item.formatter(row,materialTypeList) : item.formatter"
                    align="center" 
                    :show-overflow-tooltip="true" 
                  />
                </template>
              </el-table>
            </div>
          </div>
          <div class="mt16 mb4">
            <Pagination
              :total="total" 
              :pageNum="pageObj.pageNum" 
              :pageSize="pageObj.pageSize" 
              @getData="initData" 
            />
          </div>
        </div>
      </div>

      <div class="footer-btn">
          <el-button type="primary" @click="handleClose()"plain>取消</el-button>
          <el-button type="primary" @click="saveSelected()">保存</el-button>
      </div>
    </el-dialog>
  </template>
  
  <script>
  import { panelColumn } from "../config.js"
  import Pagination from "@/components/Pagination/index.vue";
  export default {
    components: {
      Pagination
    },
    props:{
        fphbNo: {
            type: String,
            default: "",
        },
        plantimeStart: {
            type: String,
            default: "",
        },
        materialType: {
            type: String,
            required: false,
        },
        hideTestProject: {
            type: Boolean,
            required: false,
            default: false
        },
        entrustExperimentBlur: {
            type: String,
            required: false,
        },
        selectNoList: {
            type: Array,
            required: false,
            default: () => []
        },
        multipleSelect: {
            type: Boolean,
            required: false,
            default: true
        }
    },
    data() {
      return {
        showVisible: false,
        loading: false,
        searchForm: {
          takeEffectDate: ['', this.plantimeStart],
          entrustReasonCodeList: [],
          entrustExperimentBlur: '',
          phb:this.fphbNo,
        },
        
        pageObj: {
          pageNum: 1, // 页数
          pageSize: 10, // 条数
        },
        total: 1,
        
        tableData: [],
        expands: [],
  
        tableColumn: [
          {
            label: '协会委托编号',
            prop: 'consignId',
            width: 160,
          },
          {
            label: '样品编号',
            prop: 'sampleId',
            width: 160,
          },{
            label: '报告编号',
            prop: 'reportNo',
          },{
            label: '质保书编号',
            prop: 'batch',
          },
          {
            label: '委托原因',
            prop: 'entrustReasonName'
          },
          {
            label: '试验项目',
            prop: 'testProjectNameStr',
            width: 140
          },
          {
            label: '代表数量',
            prop: 'behalfNumber'
          },
          {
            label: '是否合格',
            prop: 'isQualified',
            formatter: (row) => {
              if(row.isQualified == 1){
                return '合格'
              }else if(row.isQualified == 2){
                return '不合格'
              }else{
                return "--"
              }
            },
          },
          {
            label: "材料类型",
            prop: "experimentType",
            formatter: (row,materialsTypeOpts) => {
              for(let i =0;i<materialsTypeOpts.length; i++){
                  if(row.experimentType == materialsTypeOpts[i].value){
                      return materialsTypeOpts[i].label;
                  }
              }
            },
          },
          {
            label: '样品等级',
            prop: 'sampleLevel'
          },
          {
            label: "检验类型",
            prop: "checkType",
          },
          {
            label: '委托时间',
            prop: 'entrustTime',
            width: 140,
            formatter: (row) => {
              if (!row.entrustTime) {
                return '--';
              }
              return moment(row.entrustTime).format('YYYY-MM-DD')
            },
          },
        ],
        
        entrustReasonList: [],
        materialTypeList: [],

        panelSelects: [],

        experimentAuth: [],

        checkConfigOpts: [], // 试验项目
      };
    },
    
    methods: {
      show() {
        this.showVisible = true;
        this.$set(this.searchForm,'phb', this.fphbNo);
        this.$set(this.searchForm,'takeEffectDate', ['', this.plantimeStart]);
        this.createdData();
      },
      createdData() {
        this.$api.getDictValue({
          dictCode: 'ENTRUST_REASON'
        }, this).then(res => {
          if(this.materialType == '7'){
            this.entrustReasonList = res.data.list
          }else{
            this.entrustReasonList = res.data.list.filter(item => {
              return item.dictValueName == '现场反馈' || item.dictValueName == '抽检';
            })
          }
        })

        this.getUserInfo();
        
        if (this.materialType == 7) {
          let tabCol = [].concat(panelColumn);
          // tabCol 第三个位置插入
          tabCol.splice(3, 0, {
            prop: 'phb',
            label: '配合比编号',
          });
          // 去掉第九个
          tabCol.splice(9, 1);
          this.tableColumn = tabCol;
        }else{
          this.tableColumn = [].concat(panelColumn);
        }

        this.getCheckConfigList();
      },
      handleClose() {
          this.showVisible = false;
      },

      checkTypeChange(event) {
        console.log(">>checkTypeChange>>", event);
        this.getCheckConfigList();
      },
      // 获取试验项目下拉数据
      getCheckConfigList() {
        // this.$api.getCheckConfig({
        //   projectCategory: 7,
        //   checkType: this.searchForm.checkType || 3
        // }, this).then(res => {
        //   if(res.succ){
        //     let keys = Object.keys(res.data.list);
        //     this.checkConfigOpts = keys.map(item => {
        //       let value = res.data.list[item];
        //       return {
        //           label: value,
        //           value: item,
        //       }
        //     });
        //   }
        // })
        let parm = {
          checkType: this.searchForm.checkType || 3,
          testType: 'CONCRETE'
        }
        this.checkConfigOpts = [];
        this.$api.getTestProject2(parm, this).then(res => {
            if (res.code == 1) {
              this.checkConfigOpts = res.data.list.map(item => {
                return {
                    label: item.testName,
                    value: item.testName,
                }
              });
            }
        })
      },
      // 查询当前登录用户的试验权限
      getUserInfo() {
        this.experimentAuth = [];
        this.$api.queryUserInfo(`id=${this.$store.state.loginStore.userInfo.userId}`, this).then(userRes => {
          if(userRes.code == 1 && userRes.data.testProjectJson.length > 0){
            for (const userItem of userRes.data.testProjectJson) {
              if (userItem.no != '7') {
                this.experimentAuth.push(`${userItem.no}`);
              }
            }
            this.$api.getDictValue({
              dictCode: 'MASTERIAL_TYPE'
            }, this).then(res => {
              this.materialTypeList = res.data.list
              .filter(i => this.materialType ? i.dictValueCode == this.materialType : (i.dictValueCode != 7 && this.experimentAuth.indexOf(`${i.dictValueCode}`) > -1))
              .map(i => {
                return {
                  label: i.dictValueName,
                  dictCode: i.dictCode,
                  id: i.id,
                  value: `${i.dictValueCode}`
                }
              })
              console.log(">>this.materialTypeList>>", this.materialTypeList)
              if(this.materialType){
                this.searchForm.experimentTypeList = [this.materialType]
              }
              this.initData();
            })
          }else{
            this.$message.error("暂无原材料的权限")
          }
        })
      },
      checkSelectable(row) {
        return row.experimentStatus == '3';
      },
      handleFilter(opageNum, opageSize) {
        this.initData(1);
      },
      isEmpty(val) {
        if (typeof val === "boolean") {
          return false;
        }
        if (typeof val === "number") {
          return false;
        }
        if (val instanceof Array) {
          if (val.length === 0) return true;
        } else if (val instanceof Object) {
          if (JSON.stringify(val) === "{}") return true;
        } else {
          if (
            val === "null" ||
            val == null ||
            val === "undefined" ||
            val === undefined ||
            val === ""
          )
            return true;
          return false;
        }
        return false;
      },
      resetForm(){
        this.searchForm = {
          takeEffectDate: [],
          entrustReasonCodeList: [],
        };
        this.initData(1);
        this.getCheckConfigList();
      },
      initData(opageNum, opageSize){
        this.loading = true;
        if (opageNum) this.pageObj.pageNum = opageNum;
        if (opageSize) this.pageObj.pageSize = opageSize;
        
        let oform = JSON.parse(JSON.stringify(this.searchForm))

        if (this.entrustExperimentBlur) {
          oform.entrustExperimentBlur = this.entrustExperimentBlur;
        }

        if (!this.isEmpty(this.searchForm.takeEffectDate)) {
          oform.beginTime = this.searchForm.takeEffectDate[0]
            ? this.searchForm.takeEffectDate[0]
            : "";
          oform.comTime = this.searchForm.takeEffectDate[1]
            ? this.searchForm.takeEffectDate[1]
            : "";
        }
        oform.takeEffectDate = undefined;
        
        if(!oform.experimentTypeList || oform.experimentTypeList.length === 0){
          if(this.materialType){
            oform.experimentTypeList = ['7']
          }else{
            oform.experimentTypeList = this.experimentAuth; // ["1", "2", "3", "4", "5", "6"];
          }
        }
        oform.experimentStatusList = [2, 3];
        oform.isFinish = 2;
        const params ={
          ...this.pageObj,
          params: oform,
        }
        
        //获取列表
        this.tableData = [];
        this.$api.getExperimentList(params, this).then(res => {
          this.loading = false;
          if(res.succ){
            this.tableData = res.data.list;
            this.total = res.data.total;

            this.$nextTick(() => {
              this.tableData.forEach(item => {
                if (this.selectNoList.indexOf(item.sampleId || item.experimentNo) > -1) {
                  this.$refs.multipleTable.toggleRowSelection(item, true);
                }
              });
            });
          }else{
            this.$message.error(res.msg || '查询失败')
          }
        })
      },
      
      panelSelectionChange(val) {
        if (this.multipleSelect) {
          this.panelSelects = val;
        }else{
          if (val.length > 1) {        
              this.$refs.multipleTable.clearSelection();        
              this.$refs.multipleTable.toggleRowSelection(val.pop());      
          }      
          this.panelSelects = val;      
        }
      },

      saveSelected() {
        if (this.panelSelects.length == 0) {
          this.$message.error("请选择试验项目");
          return;
        }


        // this.otherForm.kztzypbh = selectedInfos[0].ypbh;
        //             this.otherForm.kztzjl = selectedInfos[0].jcjl;
        //             this.otherForm.kzExperimentId = selectedInfos[0].experimentId;
        //             this.otherForm.kztzwtbh = selectedInfos[0].tzwtbh;

        let noList = [];
        let infoList = [];
        this.panelSelects.map(item => {
          noList.push(item.sampleId || item.experimentNo);
          infoList.push({
            sytzbh: item.id,
            ypbh: item.sampleId,
            xhwtbh: item.consignId,
            qdpjz: "",
            
            experimentId: item.id,
            tzwtbh: item.experimentNo,
            jcjl: item.isQualified == 1 ? '合格' : (item.isQualified == 2 ? '不合格' : ''),
          });
        });
        this.$emit("selected", noList, infoList);
        this.showVisible = false;
      }
    },
  };
  </script>
  
  <style scoped lang="scss">
    ::v-deep .el-button{
      padding-left: 13px;
      padding-right: 13px;
    }
    .el-form-item{
      margin-bottom: 8px;
    }
    ::v-deep .el-form--inline{
      .el-form-item{
        margin-right: 24px;
        margin-bottom: 0;
        &:last-child{
          margin: 0;
        }
      }
    }
    ::v-deep .el-table{
      .expanded,.expanded:hover{
        background-color: #FFFBD9;
        
      }
      .expanded + tr{
        background-color: #FFFBD9;
        td{
          background-color: #FFFBD9;
        }
      }
      
      .table-child-box{
        margin: 16px;
        padding: 16px;
        background: #FFFFFF;
      }
    }
    
    .content-box{
      padding: 16px;
      height: 100%;
    }
    .content{
      width: 100%;
      height: 100%;
      padding: 16px;
      background: #FFFFFF;
      border-radius: 16px;
    }
    
    .search-box{
      padding-bottom: 16px;
      line-height: 40px;
    }

    .cell-state {
      .rda-task-state {
          display: inline-block;
          padding-left: 3px;
          padding-right: 3px;
          height: 18px;
          line-height: 18px;
          font-size: 12px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #FFFFFF;
          text-align: center;
          background: #1F7AFF;
          border-radius: 2px;
      }
      .dqr {
          background: #DC3290;
      }
      .ddd {
          background: #3369FF;
      }
      .dwc {
          background: #1FAE66;
      }
      .yqx {
          background: #D6D6D6;
      }
      .yjj {
          background: #ADAA00;
      }
      .ywc {
          background: #515157;
      }
    }

  .footer-btn {
    position: absolute;
    bottom: 10px;
    left: calc(50% - 100px)
  }
  </style>