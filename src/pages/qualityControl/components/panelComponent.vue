<template>
    <div class="content-box">
      <div class="flex-box flex-column content">
        <div class="search-box flex-box">
          <el-form class="flex-item" ref="searchForm" :inline="true" :model="searchForm" :disabled="loading">
            <el-col :span="18">
              <el-form-item label="委托编号：" style="margin-bottom: 16px;">
                <el-input v-model="searchForm.noKeyword" clearable
                  placeholder="请输入" 
                  style="width: 180px" 
                />
              </el-form-item>
              <el-form-item label="报告编号：">
                <el-input v-model="searchForm.reportNo" clearable
                  placeholder="请输入" 
                  style="width: 180px" 
                />
              </el-form-item>
              <el-form-item label="样品编号：">
                <el-input v-model="searchForm.sampleNo" clearable
                  placeholder="请输入" 
                  style="width: 180px" 
                />
              </el-form-item>
              <el-form-item v-if="materialType == 7" label="配合比编号：">
                <el-input v-model="searchForm.phb" clearable
                  placeholder="请输入" 
                  style="width: 180px" 
                />
              </el-form-item>
              <el-form-item v-if="materialType != 7" label="厂家：">
                <el-input v-model="searchForm.factory" clearable
                  placeholder="请输入" 
                  style="width: 180px" 
                />
              </el-form-item>

              <el-form-item v-if="materialType == 7" label="试验项目：">
                <el-select
                  v-model="searchForm.entrustExperimentBlur" 
                  filterable clearable 
                  collapse-tags
                  placeholder="请选择试验项目" 
                  style="width: 180px">
                  
                  <el-option
                    v-for="item in checkConfigOpts"
                    :key="item.value"
                    :label="item.label"
                    :value="item.label">
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item v-if="materialType != 7" label="材料类型：">
                <el-select
                  v-model="searchForm.experimentTypeList" 
                  filterable clearable multiple 
                  collapse-tags
                  placeholder="请选择材料类型" 
                  style="width: 180px">
                  
                  <el-option
                    v-for="item in materialTypeList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item label="委托时间：">
                <el-date-picker type="daterange" 
                  v-model="searchForm.takeEffectDate" 
                  start-placeholder="开始时间" end-placeholder="结束时间" 
                  format="yyyy-MM-dd" value-format="yyyy-MM-dd HH:mm:ss"
                  :default-time="['00:00:00', '23:59:59']"
                  :clearable="true" style="width: 360px"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item label="检验类型：">
                <el-radio-group v-model="searchForm.checkType" @change="checkTypeChange">
                  <el-radio :label="1">快检</el-radio>
                  <el-radio :label="2">批检</el-radio>
                </el-radio-group>
              </el-form-item>

              <el-button type="primary" :disabled="loading" @click="handleFilter">搜索</el-button>
              <el-button type="text" icon="el-icon-refresh-right" :disabled="loading" @click="resetForm()">重置</el-button>
            </el-col>

            <el-col :span="6">
              <el-form-item style="float: right;">
                <el-button v-if="materialType != 7" type="primary" @click="setBatch">修改质保书编号</el-button>
                <el-button type="primary" @click="updateClick">上传至协会</el-button>
                <el-button type="primary" @click="downloadClick">更新至品控</el-button>
                <el-button v-if="materialType != 7" type="primary" @click="printClick">打印</el-button>
                <el-button type="primary" @click="goDetail()">切换视图</el-button>
              </el-form-item>
            </el-col>
            
            
            <!-- <el-form-item label="任务单编号：">
              <el-input v-model="searchForm.FRwdh" clearable
                placeholder="请输入" 
                style="width: 180px" 
              />
            </el-form-item> -->
            
            <!-- <el-form-item label="委托原因：">
              <el-checkbox-group v-model="searchForm.entrustReasonCodeList"
              @change="initData(1)"
              >
                <el-checkbox v-for="item in entrustReasonList" :key="item.dictValueCode" :label="item.dictValueCode">{{item.dictValueName}}</el-checkbox>
              </el-checkbox-group>
            </el-form-item> -->
          </el-form>
          
        </div>
        
        <div class="flex-item overHide">
          <div class="scroll-div">
            
            <el-table
              :data="tableData"
              v-loading="loading"
              @expand-change="getExperimentDetail"
              :expand-row-keys="expands"
              row-key="id"
              :key="2"
              @selection-change="panelSelectionChange"
              >
              <!-- :selectable="checkSelectable" -->
              <el-table-column type="selection" align="center" fixed="left" ></el-table-column>
              <el-table-column type="expand">
                <template slot-scope="scope">
                  <div class="table-child-box">
                    <el-table :data="scope.row.childernData"
                      v-loading="loading2" 
                      :key="scope.row.id"
                      border
                      style="width: 100%;"
                    >
                      <el-table-column 
                        v-for="item in scope.row.childern" 
                        :key="item.testProjectCode + scope.row.id"
                        :prop="item.testProjectCode"
                        :label="item.testProjectName" 
                        align="center" 
                      >
                        <template slot-scope="subScope">
                          <span>
                            <!-- 混凝土试验 -->
                            <span v-if="scope.row.experimentType == 7" class="type-span" :style="{color: hntColor(subScope.row[item.testProjectCode], item.testProjectCode), whiteSpace: 'pre-wrap'}">{{ hntDxjl(subScope.row[item.testProjectCode], item.testProjectCode) }}</span>
                            <!-- 水泥试验 -->
                            <span v-else-if="scope.row.experimentType == 1" class="type-span" style="color: red; white-space:pre-wrap;">{{ snDxjl(subScope.row[item.testProjectCode], item.testProjectCode) }}</span>
                            <!-- 粉煤灰试验 -->
                            <span v-else-if="scope.row.experimentType == 2" class="type-span" style="color: red; white-space:pre-wrap;">{{ fmhDxjl(subScope.row[item.testProjectCode], item.testProjectCode) }}</span>
                            <!-- 矿渣粉试验 -->
                            <span v-else-if="scope.row.experimentType == 3" class="type-span" style="color: red; white-space:pre-wrap;">{{ kzfDxjl(subScope.row[item.testProjectCode], item.testProjectCode) }}</span>
                            <!-- 粗骨料试验 -->
                            <span v-else-if="scope.row.experimentType == 4" class="type-span" style="color: red; white-space:pre-wrap;">{{ cglDxjl(subScope.row[item.testProjectCode], item.testProjectCode) }}</span>
                            <!-- 细骨料试验 -->
                            <span v-else-if="scope.row.experimentType == 5" class="type-span" style="color: red; white-space:pre-wrap;">{{ xglDxjl(subScope.row[item.testProjectCode], item.testProjectCode) }}</span>
                            <!-- 外加剂试验 -->
                            <span v-else-if="scope.row.experimentType == 6" class="type-span" style="color: red; white-space:pre-wrap;">{{ wjjDxjl(subScope.row[item.testProjectCode], item.testProjectCode) }}</span>

                            <span v-else class="type-span" style="color: red;">{{ '--' }}</span>
                          </span>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </template>
              </el-table-column>
              
              <template v-for="item in tableColumn" >
                
                <el-table-column 
                  v-if="item.prop == 'checkType'" 
                  :key="item.prop" 
                  :label="item.label" 
                  :fixed="item.fixed" 
                  align="center"
                 >
                  <template slot-scope="scope">
                    <span class="type-span" style="border-radius: 3px; padding: 1px 3px" :style="{backgroundColor: scope.row.checkType == '1' ? '#61A480' : '#496BF9', color: '#fff'}">{{ scope.row.checkType == '1' ? '快检' : '批检' }}</span>
                  </template>
                </el-table-column>
                <!-- 0-待接收  1-待取样  2-试验中 3-已完成 4-已拒绝 -->
                <!-- 0-待接收  1-待取样  2-试验中 3-已完成 4-已拒绝  5-已作废 -->
                <el-table-column v-else-if="item.prop == 'experimentStatus'" :key="item.prop" :label="item.label" :fixed="item.fixed" align="center" >
                  <template slot-scope="scope">
                    <el-row class="cell-state">
                      <label v-if="scope.row.experimentStatus == '0'" class="rda-task-state dqr">待接收</label>
                      <label v-if="scope.row.experimentStatus == '1'" class="rda-task-state ddd">待取样</label>
                      <label v-if="scope.row.experimentStatus == '2'" class="rda-task-state dwc">试验中</label>
                      <label v-if="scope.row.experimentStatus == '3'" class="rda-task-state yqx">已完成</label>
                      <label v-if="scope.row.experimentStatus == '4'" class="rda-task-state yqx">已拒绝</label>
                      <label v-if="scope.row.experimentStatus == '5'" class="rda-task-state yqx">已作废</label>
                    </el-row>
                  </template>
                </el-table-column>
  
                <template v-else-if="item.prop == 'batch'">
                  <el-table-column v-if="materialType != 7" :key="item.prop" :label="item.label" :fixed="item.fixed" align="center" >
                    <template slot-scope="scope">
                      <el-button v-if="scope.row.batch" type="text" size="small" @click="setBatch(scope.row)">{{scope.row.batch}}</el-button>
                      <el-button v-else type="text" size="small" @click="setBatch(scope.row)">--</el-button>
                    </template>
                  </el-table-column>
                </template>

                <el-table-column v-else-if="item.prop == 'finistStatus'" :key="item.prop" :label="item.label" :fixed="item.fixed" align="center" >
                  <template slot-scope="scope">
                    <el-tooltip v-if="scope.row.reason" class="item" effect="dark" :content="scope.row.reason" placement="top">
                      <el-row class="cell-state" >
                        <label style="cursor: pointer;" v-if="scope.row.finistStatus == '未同步'" class="rda-task-state dqr">{{ scope.row.finistStatus }}</label>
                        <label style="cursor: pointer;" v-if="scope.row.finistStatus == '部分同步'" class="rda-task-state yjj">{{ scope.row.finistStatus }}</label>
                        <label style="cursor: pointer;" v-if="scope.row.finistStatus == '已同步'" class="rda-task-state yqx">{{ scope.row.finistStatus }}</label>
                      </el-row>
                    </el-tooltip>
                    <el-row v-else class="cell-state">
                        <label v-if="scope.row.finistStatus == '未同步'" class="rda-task-state dqr">{{ scope.row.finistStatus }}</label>
                        <label v-if="scope.row.finistStatus == '部分同步'" class="rda-task-state yjj">{{ scope.row.finistStatus }}</label>
                        <label v-if="scope.row.finistStatus == '已同步'" class="rda-task-state yqx">{{ scope.row.finistStatus }}</label>
                    </el-row>
                  </template>
                </el-table-column>

                <el-table-column
                  v-else
                  :key="item.prop" 
                  :prop="item.prop" 
                  :label="item.label" 
                  :fixed="item.fixed" 
                  :width="item.width || ''"
                  :formatter="item.prop === 'experimentType' ? (row) => item.formatter(row,materialTypeList) : item.formatter"
                  align="center" 
                  :show-overflow-tooltip="true" 
                />
              </template>
              
              
              <el-table-column width="220" label="操作" align="center" key="handle" :resizable="false">
                <template slot-scope="scope">
                  <el-button type="text" size="small" @click="goDetail(scope.row)">详情</el-button>
                  <el-button v-if="scope.row.sampleId" type="text" size="small" @click="revokeUploadResp(scope.row)">撤销上传</el-button>
                  <el-button type="text" size="small" style="color: #ff0000;" @click="handleDel(scope.row)">作废</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
        <div class="mt16 mb4">
          <Pagination
            :total="total" 
            :pageNum="pageObj.pageNum" 
            :pageSize="pageObj.pageSize" 
            @getData="initData" 
          />
        </div>
      </div>

      <PrintDrawer :show="printDrawer" @close="printDrawerClose" @sure="getPrintType" />
      
      
      
      <el-dialog 
        width="500px" 
        title="修改质保书编号" 
        :visible.sync="dialogFormVisibleBatch" 
        :close-on-click-modal="false"
        :close-on-press-escape="false"
      >
        <el-form>
          <el-form-item label="质保书编号:">
            <el-input placeholder="请输入质保书编号" v-model="batchTxt" autocomplete="off"></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogFormVisibleBatch = false">取 消</el-button>
          <el-button type="primary" @click="setBatchSub">确 定</el-button>
        </div>
      </el-dialog>
    </div>
  </template>
  
  <script>
  import { panelColumn,
  panelChildColumn } from "../config.js"
  import Pagination from "@/components/Pagination/index.vue";
  import PrintDrawer from "./printDrawer.vue";
import moment from "moment";
  export default {
    components: {
      Pagination,
      PrintDrawer
    },
    props:{
        materialType: {
            type: String,
            required: false,
        },
    },
    data() {
      return {
        loading: false,
        loading2: false,
        // 设置时间选择
        pickerOptions: {
          disabledDate(time) {
            let deadline = Date.now() - 60 * 60 * 1000;
            return time.getTime() > deadline //
          },
        },
        searchForm: {
          takeEffectDate: [],
          entrustReasonCodeList: [],
        },
        
        pageObj: {
          pageNum: 1, // 页数
          pageSize: 10, // 条数
        },
        total: 1,
        
        tableData: [],
        experimentCheckRecord: [], // 根据试验台账id获取所有检测记录信息
        expands: [],
  
        tableColumn: [],
        tableColumn2: panelChildColumn,
        activeName: 'second',
        
        entrustReasonList: [],
        materialTypeList: [],

        printDrawer: false,
        panelSelects: [],
        
        
        batchTxt: '',
        batchList: [],
        dialogFormVisibleBatch: false,

        experimentAuth: [],

        checkConfigOpts: [], // 试验项目
      };
    },
    
    created: function() {
      this.$api.getDictValue({
        dictCode: 'ENTRUST_REASON'
      }, this).then(res => {
        if(this.materialType == '7'){
          this.entrustReasonList = res.data.list
        }else{
          this.entrustReasonList = res.data.list.filter(item => {
            return item.dictValueName == '现场反馈' || item.dictValueName == '抽检';
          })
        }
      })

      this.getUserInfo();
      
      // this.$api.getDictValue({
      //   dictCode: 'MASTERIAL_TYPE'
      // }, this).then(res => {
      //   if(res.succ){
      //     this.materialTypeList = res.data.list
      //     .filter(i => this.materialType ? i.dictValueCode == this.materialType : i.dictValueCode != 7)
      //     .map(i => {
      //       return {
      //         label: i.dictValueName,
      //         dictCode: i.dictCode,
      //         id: i.id,
      //         value: i.dictValueCode
      //       }
      //     })
      //     console.log(">>this.materialTypeList>>", this.materialTypeList)
      //     if(this.materialType){
      //       this.searchForm.experimentTypeList = [this.materialType]
      //     }
      //     this.initData();
      //   }
      // })
      
      if (this.materialType == 7) {
        let tabCol = [].concat(panelColumn);
        // tabCol 第三个位置插入
        tabCol.splice(3, 0, {
          prop: 'phb',
          label: '配合比编号',
        });
        tabCol.splice(8, 0, {
          prop: 'projectNameStr',
          label: '工程名称',
        });
        tabCol.splice(10, 3);
        this.tableColumn = tabCol;
      }else{
        this.tableColumn = [].concat(panelColumn);
      }

      this.getCheckConfigList();
    },
    methods: {

      checkTypeChange(event) {
        console.log(">>checkTypeChange>>", event);
        this.getCheckConfigList();
      },
      // 获取试验项目下拉数据
      getCheckConfigList() {
        // this.$api.getCheckConfig({
        //   projectCategory: 7,
        //   checkType: this.searchForm.checkType || 3
        // }, this).then(res => {
        //   if(res.succ){
        //     let keys = Object.keys(res.data.list);
        //     this.checkConfigOpts = keys.map(item => {
        //       let value = res.data.list[item];
        //       return {
        //           label: value,
        //           value: item,
        //       }
        //     });
        //   }
        // })
        let parm = {
          checkType: this.searchForm.checkType || 3,
          testType: 'CONCRETE'
        }
        this.checkConfigOpts = [];
        this.$api.getTestProject2(parm, this).then(res => {
            if (res.code == 1) {
              this.checkConfigOpts = res.data.list.map(item => {
                return {
                    label: item.testName,
                    value: item.testName,
                }
              });
            }
        })
      },
      // 查询当前登录用户的试验权限
      getUserInfo() {
        this.experimentAuth = [];
        this.$api.queryUserInfo(`id=${this.$store.state.loginStore.userInfo.userId}`, this).then(userRes => {
          if(userRes.code == 1 && userRes.data.testProjectJson.length > 0){
            for (const userItem of userRes.data.testProjectJson) {
              if (userItem.no != '7') {
                this.experimentAuth.push(`${userItem.no}`);
              }
            }
            this.$api.getDictValue({
              dictCode: 'MASTERIAL_TYPE'
            }, this).then(res => {
              this.materialTypeList = res.data.list
              .filter(i => this.materialType ? i.dictValueCode == this.materialType : (i.dictValueCode != 7 && this.experimentAuth.indexOf(`${i.dictValueCode}`) > -1))
              .map(i => {
                return {
                  label: i.dictValueName,
                  dictCode: i.dictCode,
                  id: i.id,
                  value: `${i.dictValueCode}`
                }
              })
              console.log(">>this.materialTypeList>>", this.materialTypeList)
              if(this.materialType){
                this.searchForm.experimentTypeList = [this.materialType]
              }
              this.initData();
            })
          }else{
            this.$message.error("暂无原材料的权限")
          }
        })
      },
      checkSelectable(row) {
        return row.experimentStatus == '3';
      },
      handleFilter(opageNum, opageSize) {
        this.initData(1);
      },
      isEmpty(val) {
        if (typeof val === "boolean") {
          return false;
        }
        if (typeof val === "number") {
          return false;
        }
        if (val instanceof Array) {
          if (val.length === 0) return true;
        } else if (val instanceof Object) {
          if (JSON.stringify(val) === "{}") return true;
        } else {
          if (
            val === "null" ||
            val == null ||
            val === "undefined" ||
            val === undefined ||
            val === ""
          )
            return true;
          return false;
        }
        return false;
      },
      resetForm(){
        this.searchForm = {
          takeEffectDate: [],
          entrustReasonCodeList: [],
        };
        this.initData(1);
        this.getCheckConfigList();
      },
      initData(opageNum, opageSize){
        this.loading = true;
        if (opageNum) this.pageObj.pageNum = opageNum;
        if (opageSize) this.pageObj.pageSize = opageSize;
        
        let oform = JSON.parse(JSON.stringify(this.searchForm))

        if (!this.isEmpty(this.searchForm.takeEffectDate)) {
          oform.beginTime = this.searchForm.takeEffectDate[0]
            ? this.searchForm.takeEffectDate[0]
            : "";
          oform.comTime = this.searchForm.takeEffectDate[1]
            ? this.searchForm.takeEffectDate[1]
            : "";
        }
        oform.takeEffectDate = undefined;
        
        if(!oform.experimentTypeList || oform.experimentTypeList.length === 0){
          if(this.materialType){
            oform.experimentTypeList = ['7']
          }else{
            oform.experimentTypeList = this.experimentAuth; // ["1", "2", "3", "4", "5", "6"];
          }
        }
        oform.isFinish = 2;
        const params ={
          ...this.pageObj,
          params: oform,
        }
        
        //params.params.entrustReasonCode = params.params.code.join()
        //获取列表
        this.tableData = [];
        this.$api.getExperimentList(params, this).then(res => {
          this.loading = false;
          if(res.succ){
            // this.$refs.tablePer.
            this.tableData = res.data.list;
            this.total = res.data.total;
          }else{
            this.$message.error(res.msg || '查询失败')
          }
        })
      },
      
      async getExperimentDetail(row, expanded){
        //this.expands = [];
        if(row.childern && row.childern.length > 0){
          return false;
        }
        let index;
        for(let i = 0; i< this.tableData.length; i++){
          if(this.tableData[i].id == row.id){
            this.tableData[i].childern
            index = i;
          }
        }
        
        if(index >= 0){
          this.loading2 = true;
          
          const resDetail = await this.$api.getExperimentDetail({
            experimentId: row.id,
          }, this)
          this.loading2 = false;
          if(resDetail.succ){
            let batchForm = {};
            // resDetail.data.list.forEach(item => {
            //   batchForm[item.testProjectCode] = JSON.parse(JSON.stringify(item.objJson));
            // })
            this.tableData[index].childern = resDetail.data.list.map(item =>{
              batchForm[item.testProjectCode] = JSON.parse(JSON.stringify(item.objJson));
              delete item.objJson
              return item;
            });
            console.log(batchForm)
            this.tableData[index].childernData = [batchForm];
            this.$forceUpdate();
          }  
          
          
          
          
          //获取列表xiang请
          // this.$api.getExperimentListDetail(`experimentId=${row.id}`, this).then(res => {
          //   this.loading2 = false;
          //   if(res.succ){
          //     console.log(index,res.data.list)
          //     this.tableData[index].childern = res.data.list;
          //     // this.$set(this.tableData[index],)
          //     this.$forceUpdate();
          //     console.log(this.tableData)
          //   }else{
          //     this.$message.error(res.msg || '查询失败')
          //   }
          // })
        }
      },
      
      revokeUploadResp(row){
        this.$confirm("确定要撤销吗？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
          //作废
          this.$api.revokeUpload(`id=${row.id}`,this).then((res) => {
            if (res.succ) {
              this.$message({
                showClose: true,
                message: "撤销成功",
                type: "success",
              });
              
              this.initData();
            }else{
              this.$message.error(res.msg || '查询失败')
            }
          });
        }).catch(() => {
          this.$message({
            type: "info",
            message: "已取消撤销",
          });
        });
      },
      
      handleDel(row){
        this.$confirm("作废后不能恢复，确定要作废吗？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
          //作废
          this.$api.delExperiment({
            id: row.id
          },this).then((res) => {
            if (res.succ) {
              this.$message({
                showClose: true,
                message: "作废成功",
                type: "success",
              });
              
              this.initData();
            }
          });
        }).catch(() => {
          this.$message({
            type: "info",
            message: "已取消作废",
          });
        });
      },
      
      goDetail(row){
        let entrustTime = row ? moment(row.entrustTime).format('YYYY-MM-DD') : '';
        if(this.materialType){
          this.$router.push({
            path: '/qualityControl/experimentBoard',
            query: {
              id: row ? row.id : undefined,
              entrustTime: entrustTime,
            }
          })
        }else{
          this.$router.push({
            path: '/qualityControl/materialBoard',
            query: {
              id: row ? row.id : undefined,
              entrustTime: entrustTime,
              experimentType: row ? row.experimentType : undefined,
            }
          })
        }
      },

      panelSelectionChange(val) {
        this.panelSelects = val;
      },

      getPrintType(val) {
        if (val.length == 0) {
          this.$message({
            showClose: true,
            message: "请选择打印数据表",
            type: "warning",
          });
          return;
        }
        this.printBtnClick(val.join(","))
      },
      printBtnClick(types) {
        if (this.panelSelects.length == 0) {
          // 弹出提示
          this.$message({
            showClose: true,
            message: "请选择试验台账",
            type: "warning",
          });
          return;
        }
        let routeData = this.$router.resolve({
          path: "/printRawContent",
          query: {
            // this.panelSelects 去除id 并逗号拼接
            experimentIds: this.panelSelects.map(i => i.id).join(','),
            printType: types
          }
        });
        window.open(routeData.href, '_blank');
      },

      printClick() {
        if (this.panelSelects.length == 0) {
          // 弹出提示
          this.$message({
            showClose: true,
            message: "请选择试验台账",
            type: "warning",
          });
          return;
        }
        let self = this;
        let routeData = this.$router.resolve({
          path: this.materialType == 7 ? "/printHntContent" : "/printRawContent",
          query: {
            // this.panelSelects 去除id 并逗号拼接
            experimentIds: this.panelSelects.map(i => i.id).join(','),
            // printType: types
            // prints: JSON.stringify(self.printDataHandle()),
          }
        });
        window.open(routeData.href, '_blank');
      },

      printDataHandle() {
        let data = [];
        let self = this;
        this.panelSelects.map(item => {
          data.push({
            experimentId: item.id,
            printType: self.returnPrintType(item.experimentType)
          })
        });
        
        return data;
      },

      returnPrintType(type) {
        if (type == 1) {
          return "b1";
        }else if (type == 2) {
          return "b2";
        }else if (type == 3) {
          return "b3";
        }else if (type == 4) {
          return "b4";
        }else if (type == 5) {
          return "b5";
        }else if (type == 6) {
          return "b6";
        }
      },
      
      printDrawerClose() {
        this.printDrawer = false;
      },  

      // 更新事件
      updateClick(row){
        if (this.panelSelects.length == 0) {
          // 弹出提示
          this.$message({
            showClose: true,
            message: "请选择试验台账",
            type: "warning",
          });
          return;
        }
        this.$api.uploadAssociation({
          experimentId: this.panelSelects.map(i => i.id),
        },this).then((res) => {
          if (res.succ) {
            this.$message({
              showClose: true,
              message: "更新成功",
              type: "success",
            });
            
            this.initData();
          }
        });
      },
      // 下载事件
      downloadClick(row){
        if (this.panelSelects.length == 0) {
          // 弹出提示
          this.$message({
            showClose: true,
            message: "请选择试验台账",
            type: "warning",
          });
          return;
        }
        this.$api.downloadAssociation({
          experimentId: this.panelSelects.map(i => i.id),
        },this).then((res) => {
          if (res.succ) {
            this.$message({
              showClose: true,
              message: "下载成功",
              type: "success",
            });
            
            this.initData();
          }
        });
      },
      //批次
      setBatch(row){
        if(row.id){
          this.batchList = [row]
          this.batchTxt = row.batch;
        }else{
          this.batchList = this.panelSelects
        }
        if(this.batchList.length == 0){
          this.$message.warning("请至少先选择一条")
          return false;
        }
        this.dialogFormVisibleBatch = true;
      },
      setBatchSub(){
        // if(this.batchTxt.length == 0){
        //   this.$message.warning("请填写批次")
        //   return false;
        // }
        this.$api.setBatchApi({
          experimentIds: this.batchList.map(i => i.id),
          batch: this.batchTxt || "",
        },this).then((res) => {
          if (res.succ) {
            this.$message({
              showClose: true,
              message: "修改成功",
              type: "success",
            });
            this.batchTxt = "";
            this.dialogFormVisibleBatch = false;
            this.initData();
          }
        });
      },
      // 混凝土
      hntDxjl(objJson, testCode) {
        if (testCode == 'CONCRETE_PARAM_KYQD') {
          return objJson.pjz28d || '--';
        }else if (testCode == 'CONCRETE_PARAM_KSDJ') {
          return objJson.ssksdj || '--';
        }else if (['CONCRETE_PARAM_HYX', 'CONCRETE_PARAM_BSX', 'CONCRETE_PARAM_ZJX'].indexOf(testCode) > -1) {
          return this.resEnum(objJson);
        }else if (['CONCRETE_PARAM_MCTLD', 'CONCRETE_PARAM_XNBG_TLD'].indexOf(testCode) > -1) {
          return `${objJson.tld1 || '--'} ± ${objJson.tld2 || '--'}`;
        }else if (['CONCRETE_PARAM_XNBG_HQL', 'CONCRETE_PARAM_XNBG_CNSJ',
         'CONCRETE_PARAM_XNBG_ZNSJ', 'CONCRETE_PARAM_XNBG_MSL', 
         'CONCRETE_PARAM_XNBG_KZD'].indexOf(testCode) > -1
        ) {
          return `${objJson.jcz || '--'}(${objJson.dxjl || '--'})`;
        }else if (testCode == 'CONCRETE_PARAM_XNBG_LLZHL') {
          return `${objJson.synInfo.llzhl || '--'}(${objJson.dxjl || '--'})`;
        }
        else if (testCode == 'CONCRETE_PARAM_KZQD') {
          return objJson.pjz || '--';
        }
        return "--";
      },
      hntColor(objJson, testCode) {
        if (testCode == 'CONCRETE_PARAM_KYQD') {
          return '#606266';
        }else if (testCode == 'CONCRETE_PARAM_KSDJ') {
          if (objJson.ssksdj === '不合格') {
            return 'red';
          }
          return '#606266';
        }else if (['CONCRETE_PARAM_HYX', 'CONCRETE_PARAM_BSX', 'CONCRETE_PARAM_ZJX'].indexOf(testCode) > -1) {
          return '#606266';
        }else if (['CONCRETE_PARAM_MCTLD', 'CONCRETE_PARAM_XNBG_TLD'].indexOf(testCode) > -1) {
          return '#606266';
        }else if (['CONCRETE_PARAM_XNBG_HQL', 'CONCRETE_PARAM_XNBG_CNSJ',
         'CONCRETE_PARAM_XNBG_ZNSJ', 'CONCRETE_PARAM_XNBG_LLZHL', 'CONCRETE_PARAM_XNBG_MSL', 
         'CONCRETE_PARAM_XNBG_KZD'].indexOf(testCode) > -1
        ) {
          if (objJson.dxjl === '不合格') {
            return 'red';
          }
          return '#606266';
        }
        return '#606266';
      },
      resEnum(objJson = {}) {
        if (objJson.hh == '1') {
          return "良好";
        }else if (objJson.yb == '1') {
          return "一般";
        }else if (objJson.fcc == '1') {
          return "非常差";
        }
        return '--';
      },
      // 水泥
      snDxjl(objJson, testCode) {
        if (testCode == 'CEMENT_PARAM_BZCD') {
          return objJson.bzcdysl || '--';
        }else if (testCode == 'CEMENT_PARAM_NJSJ') {
          return `${objJson.jgjs.znsjjl || '--'}`;
        }else if (testCode == 'CEMENT_PARAM_ADX') {
          return `${objJson.sbf.jl || '--'}`;
        }else if (testCode == 'CEMENT_PARAM_QDCD') {
          return `抗压：3天：${objJson.kyqdsy3d.pjz || '--'}(${objJson.kyqdsy3d.dxjl || '--'})，7天：${objJson.kyqdsy7d.pjz || '--'}(${objJson.kyqdsy7d.dxjl || '--'})，28天：${objJson.kyqdsy28d.pjz || '--'}(${objJson.kyqdsy28d.dxjl || '--'})
          \n 抗折：3天：${objJson.kzqdsy3d.pjz || '--'}(${objJson.kzqdsy3d.dxjl || '--'})，7天：${objJson.kzqdsy7d.pjz || '--'}(${objJson.kzqdsy7d.dxjl || '--'})，28天：${objJson.kzqdsy28d.pjz || '--'}(${objJson.kzqdsy28d.dxjl || '--'})`;
        }else if (testCode == 'CEMENT_PARAM_LDX') {
          return `${objJson.jgjs.ldd || '--'}`;
        }else if (['CEMENT_PARAM_BBMJ', 'CEMENT_PARAM_MD', 'CEMENT_PARAM_XD'].indexOf(testCode) > -1) {
          return `${objJson.pjz || '--'}(${objJson.dxjl || '--'})`;
        }
        return "--";
      },
      snColor(objJson, testCode) {
        if (testCode == 'CEMENT_PARAM_BZCD') {
          return objJson.bzcdysl || '--';
        }else if (testCode == 'CEMENT_PARAM_NJSJ') {
          if (objJson.jgjs.znsjjl === '不合格') {
            return 'red';
          }
          return '#606266';
        }else if (testCode == 'CEMENT_PARAM_ADX') {
          if (objJson.sbf.jl === '不合格') {
            return 'red';
          }
          return '#606266';
        }else if (testCode == 'CEMENT_PARAM_QDCD') {
          if (objJson.kyqdsy3d.dxjl === '不合格' || objJson.kyqdsy7d.dxjl === '不合格' || objJson.kyqdsy28d.dxjl === '不合格'
            || objJson.kzqdsy3d.dxjl === '不合格' || objJson.kzqdsy7d.dxjl === '不合格' || objJson.kzqdsy28d.dxjl === '不合格'
          ) {
            return 'red';
          }
          return '#606266';
        }else if (testCode == 'CEMENT_PARAM_LDX') {
          return '#606266';
        }else if (['CEMENT_PARAM_BBMJ', 'CEMENT_PARAM_MD', 'CEMENT_PARAM_XD'].indexOf(testCode) > -1) {
          if (objJson.dxjl === '不合格') {
            return 'red';
          }
          return '#606266';
        }
        return '#606266';
      },
      // 粉煤灰
      fmhDxjl(objJson, testCode) {
        if (testCode == 'FLY_ASK_PARAM_FMH_XD') {
          return `${objJson.jzhxddbz || '--'}(${objJson.dxjl || '--'})`;
        }else if (testCode == 'FLY_ASK_PARAM_FMH_SSL') {
          return `${objJson.sslpjz || '--'}(${objJson.dxjl || '--'})`;
        }else if (testCode == 'FLY_ASK_PARAM_FMH_XSLB') {
          return `${objJson.xslb || '--'}(${objJson.dxjl || '--'})`;
        }else if (testCode == 'FLY_ASK_PARAM_FMH_HSL') {
          return `${objJson.hsl || '--'}(${objJson.dxjl || '--'})`;
        }else if (testCode == 'FLY_ASK_PARAM_FMH_ADX') {
          return `${objJson.lsf.pjz || '--'}(${objJson.lsf.jl || '--'})`;
        }else if (testCode == 'FLY_ASK_PARAM_FMH_HXZS') {
          return `7天：${objJson.hxzsInfoList[0].hxzs || '--'}(${objJson.hxzsInfoList[0].dxjl || '--'})
          \n 28天：${objJson.hxzsInfoList[1].hxzs || '--'}(${objJson.hxzsInfoList[1].dxjl || '--'})`;
        }else if (['FLY_ASK_PARAM_FMH_SYHL', 'FLY_ASK_PARAM_FMH_YLYHG'].indexOf(testCode) > -1) {
          return `${objJson.pjz || '--'}(${objJson.dxjl || '--'})`;
        }
        return "--";
      },
      fmhColor(objJson, testCode) {
        if (testCode == 'FLY_ASK_PARAM_FMH_XD') {
          if (objJson.dxjl === '不合格') {
            return 'red';
          }
          return '#606266';
        }else if (testCode == 'FLY_ASK_PARAM_FMH_SSL') {
          if (objJson.dxjl === '不合格') {
            return 'red';
          }
          return '#606266';
        }else if (testCode == 'FLY_ASK_PARAM_FMH_XSLB') {
          if (objJson.dxjl === '不合格') {
            return 'red';
          }
          return '#606266';
        }else if (testCode == 'FLY_ASK_PARAM_FMH_HSL') {
          if (objJson.dxjl === '不合格') {
            return 'red';
          }
          return '#606266';
        }else if (testCode == 'FLY_ASK_PARAM_FMH_ADX') {
          if (objJson.lsf.jl === '不合格') {
            return 'red';
          }
          return '#606266';
        }else if (testCode == 'FLY_ASK_PARAM_FMH_HXZS') {
          if (objJson.hxzsInfoList[0].dxjl === '不合格' || objJson.hxzsInfoList[1].dxjl === '不合格') {
            return 'red';
          }
          return '#606266';
        }else if (['FLY_ASK_PARAM_FMH_SYHL', 'FLY_ASK_PARAM_FMH_YLYHG'].indexOf(testCode) > -1) {
          if (objJson.dxjl === '不合格') {
            return 'red';
          }
          return '#606266';
        }
        return '#606266';
      },
      // 矿渣粉
      kzfDxjl(objJson, testCode) {
        if (testCode == 'SLAG_PARAM_KZF_LDDB') {
          return `${objJson.syjg || '--'}(${objJson.dxjl || '--'})`;
        }else if (testCode == 'SLAG_PARAM_KZF_HSL') {
          return `${objJson.hsl || '--'}(${objJson.dxjl || '--'})`;
        }else if (testCode == 'SLAG_PARAM_KZF_SSL') {
          return `${objJson.sslpjz || '--'}(${objJson.dxjl || '--'})`;
        }else if (testCode == 'SLAG_PARAM_KZF_HXZS') {
          return `7天：${objJson.hxzsInfoList[0].hxzs || '--'}(${objJson.hxzsInfoList[0].dxjl || '--'})
          \n 28天：${objJson.hxzsInfoList[1].hxzs || '--'}(${objJson.hxzsInfoList[1].dxjl || '--'})`;
        }else if (['SLAG_PARAM_KZF_YPMD', 'SLAG_PARAM_KZF_BBMJ', "SLAG_PARAM_KZF_SYHL"].indexOf(testCode) > -1) {
          return `${objJson.pjz || '--'}(${objJson.dxjl || '--'})`;
        }
        return "--";
      },
      kzfColor(objJson, testCode) {
        if (testCode == 'SLAG_PARAM_KZF_LDDB') {
          if (objJson.dxjl === '不合格') {
            return 'red';
          }
          return '#606266';
        }else if (testCode == 'SLAG_PARAM_KZF_HSL') {
          if (objJson.dxjl === '不合格') {
            return 'red';
          }
          return '#606266';
        }else if (testCode == 'SLAG_PARAM_KZF_SSL') {
          if (objJson.dxjl === '不合格') {
            return 'red';
          }
          return '#606266';
        }else if (testCode == 'SLAG_PARAM_KZF_HXZS') {
          if (objJson.hxzsInfoList[0].dxjl === '不合格' || objJson.hxzsInfoList[1].dxjl === '不合格') {
            return 'red';
          }
          return '#606266';
        }else if (['SLAG_PARAM_KZF_YPMD', 'SLAG_PARAM_KZF_BBMJ', "SLAG_PARAM_KZF_SYHL"].indexOf(testCode) > -1) {
          if (objJson.dxjl === '不合格') {
            return 'red';
          }
          return '#606266';
        }
        return '#606266';
      },
      //粗骨料
      cglDxjl(objJson, testCode) {
        if (['COARSE_AGGREGATE_PARAM_CGL_HNL', 'COARSE_AGGREGATE_PARAM_CGL_HNKL'].indexOf(testCode) > -1) {
          return `${objJson.hnlpjz || '--'}(${objJson.dxjl || '--'})`;
        }else if (testCode == 'COARSE_AGGREGATE_PARAM_CGL_ZPZKLHL') {
          return `${objJson.zpzkklhl || '--'}(${objJson.dxjl || '--'})`;
        }else if (testCode == 'COARSE_AGGREGATE_PARAM_CGL_SFX') {
          return `${objJson.dxjl || '--'}`;
        }else if (['COARSE_AGGREGATE_PARAM_CGL_YSZZB', 'COARSE_AGGREGATE_PARAM_CGL_BGMD', "COARSE_AGGREGATE_PARAM_CGL_DJMD", 
          "COARSE_AGGREGATE_PARAM_CGL_JMMD"
        ].indexOf(testCode) > -1) {
          return `${objJson.pjz || '--'}(${objJson.dxjl || '--'})`;
        }
        return "--";
      },
      cglColor(objJson, testCode) {
        if (['COARSE_AGGREGATE_PARAM_CGL_HNL', 'COARSE_AGGREGATE_PARAM_CGL_HNKL'].indexOf(testCode) > -1) {
          if (objJson.dxjl === '不合格') {
            return 'red';
          }
          return '#606266';
        }else if (testCode == 'COARSE_AGGREGATE_PARAM_CGL_ZPZKLHL') {
          if (objJson.dxjl === '不合格') {
            return 'red';
          }
          return '#606266';
        }else if (testCode == 'COARSE_AGGREGATE_PARAM_CGL_SFX') {
          if (objJson.dxjl === '不合格') {
            return 'red';
          }
          return '#606266';
        }else if (['COARSE_AGGREGATE_PARAM_CGL_YSZZB', 'COARSE_AGGREGATE_PARAM_CGL_BGMD', "COARSE_AGGREGATE_PARAM_CGL_DJMD", 
          "COARSE_AGGREGATE_PARAM_CGL_JMMD"
        ].indexOf(testCode) > -1) {
          if (objJson.dxjl === '不合格') {
            return 'red';
          }
          return '#606266';
        }
        return '#606266';
      },
      // 细骨料
      xglDxjl(objJson, testCode) {
        if (['FINE_AGGREGATE_PARAM_XGL_NKHL', 'FINE_AGGREGATE_PARAM_XGL_HNL'].indexOf(testCode) > -1) {
          return `${objJson.hnlpjz || '--'}(${objJson.dxjl || '--'})`;
        }else if (testCode == 'FINE_AGGREGATE_PARAM_XGL_YSZZB') {
          return `${objJson.dxjl || '--'}`;
        }else if (testCode == 'FINE_AGGREGATE_PARAM_XGL_SFX') {
          return `${objJson.dxjl || '--'}`;
        }else if (['FINE_AGGREGATE_PARAM_XGL_HSL', 'FINE_AGGREGATE_PARAM_XGL_YMHL', "FINE_AGGREGATE_PARAM_XGL_DJMD", 
          "FINE_AGGREGATE_PARAM_XGL_LLZHL", "FINE_AGGREGATE_PARAM_XGL_BGMD", "FINE_AGGREGATE_PARAM_XGL_BKHL", "FINE_AGGREGATE_PARAM_XGL_JMMD"
        ].indexOf(testCode) > -1) {
          return `${objJson.pjz || '--'}(${objJson.dxjl || '--'})`;
        }
        return "--";
      },
      xglColor(objJson, testCode) {
        if (['FINE_AGGREGATE_PARAM_XGL_NKHL', 'FINE_AGGREGATE_PARAM_XGL_HNL'].indexOf(testCode) > -1) {
          if (objJson.dxjl === '不合格') {
            return 'red';
          }
          return '#606266';
        }else if (testCode == 'FINE_AGGREGATE_PARAM_XGL_YSZZB') {
          if (objJson.dxjl === '不合格') {
            return 'red';
          }
          return '#606266';
        }else if (testCode == 'FINE_AGGREGATE_PARAM_XGL_SFX') {
          if (objJson.dxjl === '不合格') {
            return 'red';
          }
          return '#606266';
        }else if (['FINE_AGGREGATE_PARAM_XGL_HSL', 'FINE_AGGREGATE_PARAM_XGL_YMHL', "FINE_AGGREGATE_PARAM_XGL_DJMD", 
          "FINE_AGGREGATE_PARAM_XGL_LLZHL", "FINE_AGGREGATE_PARAM_XGL_BGMD", "FINE_AGGREGATE_PARAM_XGL_BKHL", "FINE_AGGREGATE_PARAM_XGL_JMMD"
        ].indexOf(testCode) > -1) {
          if (objJson.dxjl === '不合格') {
            return 'red';
          }
          return '#606266';
        }
        return '#606266';
      },
      // 外加剂
      wjjDxjl(objJson, testCode) {
        if (testCode == 'CONCRETE_ADMIXTURE_PARAM_WJJ_PHZ') {
          return `${objJson.pjph || '--'}`;
        }else if (testCode == 'CONCRETE_ADMIXTURE_PARAM_WJJ_HGL') {
          return `${objJson.pjhgl || '--'}`;
        }else if (testCode == 'CONCRETE_ADMIXTURE_PARAM_WJJ_MD') {
          return `${objJson.jmmdInfo.mdpjz || '--'}`;
        }else if (testCode == 'CONCRETE_ADMIXTURE_PARAM_WJJ_NJSJZC') {
          return `${objJson.znsjzc || '--'}(${objJson.zndxjl || '--'})`;
        }else if (testCode == 'CONCRETE_ADMIXTURE_PARAM_WJJ_QSLB') {
          return `${objJson.qslb || '--'}(${objJson.dxjl || '--'})`;
        }else if (testCode == 'CONCRETE_ADMIXTURE_PARAM_WJJ_QSLB') {
          return `${objJson.sjhntObject.tldpjz || '--'}`;
        }else if (['CONCRETE_ADMIXTURE_PARAM_WJJ_JSL'].indexOf(testCode) > -1) {
          return `${objJson.pjz || '--'}(${objJson.dxjl || '--'})`;
        }
        return "--";
      },
      wjjColor(objJson, testCode) {
        if (testCode == 'CONCRETE_ADMIXTURE_PARAM_WJJ_PHZ') {
          return '#606266';
        }else if (testCode == 'CONCRETE_ADMIXTURE_PARAM_WJJ_HGL') {
          return '#606266';
        }else if (testCode == 'CONCRETE_ADMIXTURE_PARAM_WJJ_MD') {
          return '#606266';
        }else if (testCode == 'CONCRETE_ADMIXTURE_PARAM_WJJ_NJSJZC') {
          if (objJson.zndxjl === '不合格') {
            return 'red';
          }
          return '#606266';
        }else if (testCode == 'CONCRETE_ADMIXTURE_PARAM_WJJ_QSLB') {
          if (objJson.dxjl === '不合格') {
            return 'red';
          }
          return '#606266';
        }else if (testCode == 'CONCRETE_ADMIXTURE_PARAM_WJJ_QSLB') {
          return '#606266';
        }else if (['CONCRETE_ADMIXTURE_PARAM_WJJ_JSL'].indexOf(testCode) > -1) {
          if (objJson.dxjl === '不合格') {
            return 'red';
          }
          return '#606266';
        }
        return '#606266';
      },
    },
  };
  </script>
  
  <style scoped lang="scss">
    ::v-deep .el-button{
      padding-left: 13px;
      padding-right: 13px;
    }
    .el-form-item{
      margin-bottom: 8px;
    }
    ::v-deep .el-form--inline{
      .el-form-item{
        margin-right: 24px;
        margin-bottom: 0;
        &:last-child{
          margin: 0;
        }
      }
    }
    ::v-deep .el-table{
      .expanded,.expanded:hover{
        background-color: #FFFBD9;
        
      }
      .expanded + tr{
        background-color: #FFFBD9;
        td{
          background-color: #FFFBD9;
        }
      }
      
      .table-child-box{
        margin: 16px;
        padding: 16px;
        background: #FFFFFF;
      }
    }
    
    .content-box{
      padding: 16px;
      height: 100%;
    }
    .content{
      width: 100%;
      height: 100%;
      padding: 16px;
      background: #FFFFFF;
      border-radius: 16px;
    }
    
    .search-box{
      padding-bottom: 16px;
      line-height: 40px;
    }

    .cell-state {
      .rda-task-state {
          display: inline-block;
          padding-left: 3px;
          padding-right: 3px;
          height: 18px;
          line-height: 18px;
          font-size: 12px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #FFFFFF;
          text-align: center;
          background: #1F7AFF;
          border-radius: 2px;
      }
      .dqr {
          background: #DC3290;
      }
      .ddd {
          background: #3369FF;
      }
      .dwc {
          background: #1FAE66;
      }
      .yqx {
          background: #D6D6D6;
      }
      .yjj {
          background: #ADAA00;
      }
      .ywc {
          background: #515157;
      }
    }
  </style>