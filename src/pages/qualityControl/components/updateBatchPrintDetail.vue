<!--
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-12-01 11:23:26
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-07-08 23:09:44
 * @FilePath: /quality_center_web/src/pages/qualityControl/components/updateBatchPrintDetail.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div v-if="showDialog">
        <el-dialog 
            :visible.sync="showDialog" 
            class="claim-dialog-box"
            title="" 
            width="80vw" 
            :before-close="handleClose" 
            :close-on-click-modal="false"
            :close-on-press-escape="false">
            <div>
                <el-row style="font-size: 20px; font-weight: bold;">
                    <div>
                        任务单信息
                        <span style="margin-left: 10px; font-size: 17px;">(任务单号：{{ taskNoString }})</span>
                    </div>

                    <div class="footer-btn">
                        <el-button type="primary" @click="saveForm(true)">保存</el-button>
                        <el-button type="primary" @click="saveForm(false)">保存并继续</el-button>
                        <el-button type="primary" @click="handleClose()"plain>取消</el-button>
                    </div>
                </el-row>
                <div class="form-view">
                    <el-form ref="customerForm" :model="customerForm" class="flex-row-start form-box">
                        <el-form-item class="flex-row-start form-item" :label="item.label" :prop="item.prop" v-for="(item, index) in customerFormContent" :key="index">
                            <template v-if="item.type === 'input'">
                                <el-input :style="item.width ? {width: item.width} : {}" v-if="item.inputType === 'number'" v-manual-update :type="item.inputType" v-model="customerForm[item.prop]" :disabled="item.disabled" placeholder="请输入内容" />
                                <el-input :style="item.width ? {width: item.width} : {}" v-else v-model="customerForm[item.prop]" :disabled="item.disabled" placeholder="请输入内容" />
                            </template>
                            
                            <el-select 
                                v-else-if="item.type === 'select'" 
                                :value-key="item.valueKey" 
                                v-model="customerForm[item.prop]" 
                                :disabled="item.disabled"
                                clearable
                                @change="item.handle"
                            >
                                <el-option
                                v-for="(option, index) in item.options" 
                                :key="index" 
                                :label="option.label" 
                                :value="option.value" 
                                />
                            </el-select>
                        </el-form-item>
                        <!-- 供货起止日期 -->
                         <el-row class="el-form-item flex-row-start form-item">
                            <el-form-item class="flex-row-start form-item" label="计划日期" :prop="customerForm.plantimeStart">
                                <el-input style="width: 300px;" v-model="customerForm.plantimeStart" placeholder="请输入计划日期" />
                            </el-form-item>
                            <el-form-item class="flex-row-start form-item" label="供货起止日期" :prop="customerForm.supplyStartTime">
                                <el-date-picker
                                    style="width: 300px;"
                                    type="date"
                                    v-model="customerForm.supplyStartTime"
                                    placeholder="选择日期"
                                    format="yyyy-MM-dd"
                                    value-format="yyyy-MM-dd"
                                />
                            </el-form-item>
                            <span style="margin-left: -20px; margin-right: 10px;">至</span>
                            <el-form-item class="flex-row-start form-item" :prop="customerForm.supplyEndTime">
                                <el-date-picker
                                    style="width: 300px;"
                                    type="date"
                                    v-model="customerForm.supplyEndTime"
                                    placeholder="选择日期"
                                    format="yyyy-MM-dd"
                                    value-format="yyyy-MM-dd"
                                />
                            </el-form-item>
                        </el-row>
                        <el-row class="el-form-item flex-row-start form-item">
                            <el-form-item class="flex-row-start form-item" label="合格证编号" :prop="otherForm.cchgzbh">
                                <el-input style="width: 300px;" v-model="otherForm.cchgzbh" placeholder="请输入合格证编号" />
                            </el-form-item>
                            <el-form-item class="flex-row-start form-item" label="配比报告编号" :prop="otherForm.phbbgbh">
                                <el-input style="width: 300px;" v-model="otherForm.phbbgbh" placeholder="请输入配比报告编号" />
                            </el-form-item>
                            <el-form-item class="flex-row-start form-item" label="开盘鉴定编号" :prop="otherForm.kpjdbh">
                                <el-input style="width: 300px;" v-model="otherForm.kpjdbh" placeholder="请输入开盘鉴定编号" />
                            </el-form-item>
                        </el-row>
                        <el-row class="el-form-item flex-row-start form-item">
                            <el-form-item class="flex-row-start form-item" label="配合比备注" :prop="otherForm.phbbz">
                                <el-input style="width: 400px" v-model="otherForm.phbbz" placeholder="请输入配合比备注" />
                            </el-form-item>
                            <el-form-item class="flex-row-start form-item" label="出厂证明书备注" :prop="otherForm.cczmsbz">
                                <el-input style="width: 400px" v-model="otherForm.cczmsbz" placeholder="请输入出厂证明书备注" />
                            </el-form-item>
                            <el-form-item class="flex-row-start form-item" label="出厂证明签发日期" :prop="otherForm.cczmqfrq">
                                <el-date-picker
                                type="date"
                                v-model="otherForm.cczmqfrq"
                                placeholder="选择日期"
                                format="yyyy-MM-dd"
                                value-format="yyyy-MM-dd"
                                >
                                </el-date-picker>
                            </el-form-item>
                        </el-row>
                    </el-form>
                </div>

                <el-row style="font-size: 20px; font-weight: bold; margin-top: 20px;">配比信息</el-row>
                <div class="row-title flex-row" style="justify-content: space-between;">
                    <!-- <span class="row-title-label">对应试验报告</span> -->
                    <span>
                        <!-- <span>生产：容重：{{ mixProportionInfo.productionRz | isNull }}kg，水胶比：{{ mixProportionInfo.productionSjb | isNull }}，砂率：{{ mixProportionInfo.productionSl | isNull }}%，外加剂掺料：{{ mixProportionInfo.productionCl | isNull}}%</span> -->
                        <span style="margin-left: 30px;">用量：容重：{{ mixProportionInfo.printRz | isNull}}kg，水胶比：{{ mixProportionInfo.printSjb | isNull}}，砂率：{{ mixProportionInfo.printSl | isNull}}%，外加剂掺料：{{ mixProportionInfo.printCl | isNull}}%</span>
                    </span>
                    <div>
                        <span>配合比编号：</span>
                         <el-select 
                            v-model="selectedPhbObj"
                            placeholder="请选择" 
                            style="width: 200px;"
                            value-key="proportionPhb"
                            filterable
                            @change="selectedMixProportion"
                            @visible-change="showMixProportion"
                        >
                            <el-option
                                v-for="item in phbbhOptions"
                                :key="item.proportionPhb"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                        <!-- <el-input disabled v-model="mixProportionInfo.phbbh" style="width: 200px;" />
                        <el-button type="primary" @click="selectedMixProportion">选择</el-button> -->
                        <el-button style="margin-left: 10px;" type="primary" @click="generateReportResp()">生成报告</el-button>
                    </div>
                </div>
                <div>
                    <el-table :data="tableData"
                        class="mt16"
                        border :max-height="600"
                        ref="tableDataDom" 
                        style="width: 100%;"
                        :row-style="{height: '40px'}"
                        :cell-style="{padding: '0'}"
                    >
                        
                        <el-table-column 
                            v-for="item in tableColumn" 
                            :key="item.prop" :prop="item.prop" 
                            :label="item.label" 
                            :fixed="item.fixed" 
                            :width="item.width || ''"
                            :formatter="item.formatter"
                            align="center" 
                            :resizable="false" 
                            :show-overflow-tooltip="true"
                        />
                        <el-table-column  width="300" label="操作" align="center" key="handle" :resizable="false">
                            <template slot-scope="scope">
                        <el-button v-if="(scope.row.key != 'water' && scope.row.key != 'wcl1' && scope.row.key != 'wcl2')" type="text" size="small" @click="handleSetTarget(scope.row, scope.$index)">选择原材料</el-button>
                                <el-button style="margin-left: 30px;" type="text" size="small" @click="setConsumptionTarget(scope.row, scope.$index)">设置用量</el-button>
                                <el-button style="margin-left: 30px;color: #ff0000;" type="text" size="small" @click="resetConsumptionTarget(scope.row, scope.$index)">删除</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>

                <el-row style="font-size: 20px; font-weight: bold; margin-top: 30px;">其他信息</el-row>
                <div class="form-view">
                    <el-form ref="otherForm" :model="otherForm" class="flex-row-start form-box">
                        <el-col :span="24">
                            <el-form-item class="flex-row-start form-item" label="抗压委托台账">
                                <div class="flex-row-start">
                                    <div style="width: 60vw; min-height: 40px; background: #f5f5f5; padding: 4px;">
                                        <el-tag
                                            :key="tag"
                                            v-for="tag in otherForm.kytzList"
                                            closable
                                            :disable-transitions="false"
                                            @close="deleteKytz(tag)">
                                            {{tag}}
                                        </el-tag>
                                    </div>
                                    <el-button style="margin-left: 30px;" type="primary" @click="handleSelectKytz()" plain>选择</el-button>
                                </div>
                            </el-form-item>
                        </el-col>
                        <el-form-item class="flex-row-start form-item" :label="item.label" :prop="item.prop" v-for="(item, index) in otherFormContent" :key="index">
                            <el-row class="flex-row-start">
                                <template v-if="item.type === 'input'">
                                    <el-input v-if="item.prop === 'kstzypbh'" :value="otherForm.kstzypbh || otherForm.kstzwtbh" :disabled="item.disabled" placeholder="请输入内容" />
                                    <el-input v-if="item.prop === 'kztzypbh'" :value="otherForm.kztzypbh || otherForm.kztzwtbh" :disabled="item.disabled" placeholder="请输入内容" />
                                    <el-input v-if="item.prop === 'kllztzypbh'" :value="otherForm.kllztzypbh || otherForm.kllztzwtbh" :disabled="item.disabled" placeholder="请输入内容" />
                                </template>
                            
                                <el-input
                                    v-else-if="item.type === 'number'"
                                    :type="item.type"
                                    :disabled="item.disabled"
                                    v-model="otherForm[item.prop]" 
                                    clearable
                                    :placeholder="item.placeholder || `请输入${item.label}`"
                                    v-manual-update
                                >
                                </el-input>

                                <el-button v-if="item.haveSelect" style="margin-left: 30px;" type="primary" @click="handleOtherPanelSelect(item.prop)" plain>选择</el-button>
                            </el-row>
                        </el-form-item>
                    </el-form>
                </div>
            </div>
        </el-dialog>
        <!-- 抗压选择 -->
        <PanelSelecter 
            ref="panelSelecter" 
            materialType="7" 
            :fphbNo="selectedPhbObj.proportionPhb"
            :plantimeStart="customerForm.plantimeStart"
            :hideTestProject="true" 
            :multipleSelect="true"
            :entrustExperimentBlur="'抗压强度'" 
            :selectNoList="otherForm.kytzList"
            @selected="panelSelectionChange" 
        />
        <!-- 抗渗，抗折，抗氯离子 -->
        <PanelSelecter 
            ref="panelOtherSelecter" 
            materialType="7" 
            :fphbNo="selectedPhbObj.proportionPhb"
            :plantimeStart="customerForm.plantimeStart"
            :hideTestProject="true" 
            :multipleSelect="false"
            :entrustExperimentBlur="otherEntrustExperimentBlur" 
            :selectNoList="otherPanelSelecterList"
            @selected="otherPanelSelectionChange" 
        />

        <el-dialog
            title="选择原材料报告"
            :visible.sync="validationVisible"
            class="claim-dialog-box"
            :before-close="handleValidationVisibleClose"
            width="90%"
            top="20px"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
        >
            <div>
                <panelComponent ref="panel" @setSelectInfo="setSelectInfo">
                </panelComponent>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="handleValidationVisibleClose" size="small">关闭</el-button>
            </span>
        </el-dialog>

        <el-drawer
            title="设置用量"
            :visible.sync="showConsumption"
            direction="rtl"
            :before-close="handleConsumptionClose"
        >
            <div class="flex-box flex-column h100p drawer-box">
                <div class="flex-item ofy-auto">
                    <el-form label-width="160px" 
                        :model="editForm"
                    >
                        <el-form-item
                            v-for="(el,index) in consumptionForm"
                            :key="index"
                            :label="`${el.label}：`"
                            :required="el.required || false"
                        >
                            <el-input 
                                v-if="el.type === 'input'"
                                v-model="editForm[el.prop]" 
                                :disabled="el.disabled"
                                clearable
                                :placeholder="el.placeholder || `请输入${el.label}`"
                                style="width: 350px"
                                oninput ="value=value.replace(/[^0-9.]/g,'')"
                                @change="handleConsumptionChange($event, el.prop)"
                            />
                        </el-form-item>

                        <template v-if="editForm.prop === 'water'">
                            <el-form-item label="材料名称：" prop="clmc">
                                <el-input 
                                    v-model="editForm.clmc" 
                                    clearable
                                    :placeholder="`请输入材料名称`"
                                    style="width: 350px"
                                    @change="handleConsumptionChange($event, 'clmc')"
                                />
                            </el-form-item>
                            <el-form-item label="材料规格：" prop="clgg">
                                <el-input 
                                    v-model="editForm.clgg" 
                                    clearable
                                    :placeholder="`请输入材料规格`"
                                    style="width: 350px"
                                    @change="handleConsumptionChange($event, 'clgg')"
                                />
                            </el-form-item>
                        </template>

                        <template  v-else-if="editForm.prop === 'wcl1' || editForm.prop === 'wcl2'">
                            <el-form-item label="材料名称：" prop="wlmc">
                            <el-input 
                                v-model="editForm.wlmc" 
                                clearable
                                :placeholder="`请输入材料名称`"
                                style="width: 350px"
                                :disabled="true"
                                @change="handleConsumptionChange($event, 'wlmc')"
                            />
                            </el-form-item>
                            <el-form-item label="材料规格：" prop="wlgg">
                            <el-select
                                v-model="editForm.wlObj"
                                @change="changeMaterialSpec"
                                filterable clearable 
                                :placeholder="'请选择'"
                                value-key="wlgg"
                                style="width: 350px"
                            >
                                <el-option
                                    v-for="item in materialSpecOpts"
                                    :key="item.label"
                                    :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                            </el-form-item>

                            <el-form-item  label="供应商：" prop="gys">
                            <el-select
                                v-model="editForm.gys"
                                @change="changeGys"
                                filterable clearable 
                                value-key="supplierName"
                                :placeholder="'请选择'"
                                style="width: 350px"
                            >
                                <el-option
                                    v-for="item in supplierCompanyOpts"
                                    :key="item.label"
                                    :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                            </el-form-item>

                            <el-form-item  label="厂家：" prop="cj">
                            <el-select
                                v-model="editForm.cj"
                                @change="changeCj"
                                filterable clearable 
                                value-key="manufacturers"
                                :placeholder="'请选择'"
                                style="width: 350px"
                            >
                                <el-option
                                    v-for="item in cjOpts"
                                    :key="item.label"
                                    :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                            </el-form-item>
                        </template>
                    
                    </el-form>
                </div>
                <div class="drawer-footer">
                    <el-button type="primary" @click="showConsumption = false" plain>取消</el-button>
                    <el-button type="primary" @click="saveConsumptionTarget">保存</el-button>
                </div>
            </div>
        </el-drawer>
    </div>
</template>

<script>
import PanelSelecter from './panelSelecter.vue'
import panelComponent from '../../mixProportion/panelComponent.vue'
  import {calcEquation, cpEvenRound} from "@/utils/calculate.js"

export default {
    components: {
        PanelSelecter,
        panelComponent
    },
    props: {
        taskSelects: {
            type: Array,
            default: () => []
        },
    },
    watch:{
        taskSelects:{
        handler(newValue, oldValue) {
            console.log('监听数据：', newValue);
        },
        deep: true,
        immediate: true
      }
    },
    data() {
        return {
            showDialog: false,
            validationVisible: false,
            showConsumption: false,
            selectIndex: 0,

            customerForm: {
                fphbNo:"",
                projectName: "",
                gcdz: "",
                bjbh: "",
                buildName: "",
                jsdwName: "",
                erpProjectId: "",
                planQuantity: "",
                FWcsl: "",
                tld: "",
                pouringPosition: "",

                ftpz: "",
                plantimeStart: "",
                supplyStartTime: "",
                supplyEndTime: "",
                ksdj: "",
            },
            customerFormContent: [
                {
                    // type: 'select',
                    type: 'input',
                    label: '客户名称',
                    prop: 'customerName',
                    width: '270px'
                    // valueKey: 'id',
                    // options: this.customerList,
                    // handle: (val) => this.changeCustomerSelect(val)
                },
                {
                    // type: 'select',
                    type: 'input',
                    label: '工程名称',
                    prop: 'projectName',
                    width: '470px'
                    // valueKey: 'id',
                    // options: this.engineeringList,
                    // handle: (val) => this.changeEngineeringSelect(val)
                },
                //以下四个根据工程名称获取
                // {
                //     type: 'input',
                //     label: '工程地址',
                //     prop: 'gcdz',
                //     disabled: true
                // },
                // {
                //     type: 'input',
                //     label: '报建编号',
                //     prop: 'bjbh',
                //     disabled: true
                // },
                // {
                //     type: 'input',
                //     label: '施工单位',
                //     prop: 'buildName',
                //     disabled: true
                // },
                // {
                //     type: 'input',
                //     label: '建设单位',
                //     prop: 'jsdwName',
                //     disabled: true
                // },
                {
                    type: 'input',
                    label: '计划方量',
                    prop: 'planQuantity',
                    inputType: 'number',
                    width: '100px'
                },
                {
                    type: 'input',
                    label: '完成方量',
                    prop: 'FWcsl',
                    inputType: 'number',
                    width: '100px'
                },
                {
                    type: 'input',
                    label: '坍落度',
                    prop: 'tld',
                    width: '100px'
                },
                {
                    type: 'input',
                    label: '施工部位',
                    prop: 'pouringPosition',
                    width: '470px'
                },
                {
                    type: 'input',
                    label: '砼品种',
                    prop: 'ftpz',
                },
                {
                    type: 'select',
                    label: '抗渗等级',
                    prop: 'ksdj',
                    options: [{
                        label: "无抗渗要求",
                        value: ""
                    },{
                        label: "P6",
                        value: "P6"
                    },{
                        label: "P8",
                        value: "P8"
                    },{
                        label: "P10",
                        value: "P10"
                    },{
                        label: "P12",
                        value: "P12"
                    }],
                    handle: (val) => this.changeKsdjSelect(val)
                },
                // {
                //     type: 'input',
                //     label: '计划日期',
                //     prop: 'plantimeStart',
                // },
            ],

            otherForm: {
                kytzList: [],
                kyInfoList: [],
                kyExperimentId: "",
                kstzypbh: "",
                kstzjl: "",
                kztzypbh: "",
                kztzjl: "",
                kllztzypbh: "",
                kllztzjl: "",
                llzhl: "",
                phbbz: "",
                cchgzbh: "",
                phbbgbh: "",
                kpjdbh: "",
                cczmsbz: "",
                cczmqfrq: "",
                ksExperimentId: "",
                kstzwtbh: "",
                kzExperimentId: "",
                kztzwtbh: "",
                kllExperimentId: "",
                kllztzwtbh: "",
            },
            otherFormContent: [
                {
                    type: 'input',
                    label: '抗渗台账编号',
                    prop: 'kstzypbh',
                    disabled: true,
                    haveSelect: true,
                },
                // {
                //     type: 'input',
                //     label: '抗渗台账结论',
                //     prop: 'kstzjl',
                // },
                {
                    type: 'input',
                    label: '抗折台账编号',
                    prop: 'kztzypbh',
                    disabled: true,
                    haveSelect: true,
                },
                // {
                //     type: 'input',
                //     label: '抗折台账结论',
                //     prop: 'kztzjl',
                // },
                {
                    type: 'input',
                    label: '抗氯离子台账编号',
                    prop: 'kllztzypbh',
                    disabled: true,
                    haveSelect: true,
                },
                // {
                //     type: 'input',
                //     label: '抗氯离子台账结论',
                //     prop: 'kllztzjl',
                // },
                {
                    type: 'number',
                    label: '氯离子含量',
                    prop: 'llzhl',
                },
            ],

            tableColumn: [
                {
                    prop: "name",
                    label: "原材料名称",
                },
                {
                    prop: "json.xhbgbh",
                    label: "协会报告编号",
                },
                {
                    prop: "json.batch",
                    label: "批号",
                },
                {
                    prop: "json.certificateNo",
                    label: "备案证号",
                },
                {
                    prop: "json.clmc",
                    label: "材料名称",
                    formatter: function(row, column) {
                    return (row.json.clmc || '--') + `${row.json.wlmc ? '(' + row.json.wlmc + ')' : ''}`
                    }
                },
                {
                    prop: "json.clgg",
                    label: "材料规格",
                    formatter: function(row, column) {
                    return (row.json.clgg || '--') + `${row.json.wlgg ? '(' + row.json.wlgg + ')' : ''}`
                    }
                },
                {
                    prop: "json.cj",
                    label: "厂家",
                },
                // {
                //     prop: "json.scyl",
                //     label: "生产用量",
                // },
                // {
                //     prop: "json.scylb",
                //     label: "生产用量比例",
                // },
                {
                    prop: "json.dyyl",
                    label: "用量",
                },
                {
                    prop: "json.dyylb",
                    label: "用量比例",
                },
            ],
            tableData: [
                {
                    name: '水泥',
                    key: 'sn',
                    type: 1,
                    json: {
                        "batch": "",
                        "certificateNo": "",
                        "pktzid": "",
                        "xhbgbh": "",
                        // "ypmc": "",
                        // "ypdj": "",
                        // "ypgg": "",
                        "clmc": "",
                        "cllx": "",
                        "clgg": "",
                        "wlmc": "",
                        "wllx": "",
                        "wlgg": "",
                        "cj": "",
                        "cjjc": "",
                        "gys": "",
                        "gysjc": "",
                        "scyl":"",
                        "scylb":"1.00",
                        "dyyl":"",
                        "dyylb":"1.00",
                    }
                },
                {
                    name: "水",
                    key: 'water',
                    type: -1,
                    json: {
                        "batch": "",
                        "certificateNo": "",
                        "pktzid": "",
                        "xhbgbh": "",
                        // "ypmc": "",
                        // "ypdj": "",
                        // "ypgg": "",
                        "clmc": "",
                        "cllx": "",
                        "clgg": "",
                        "wlmc": "",
                        "wllx": "",
                        "wlgg": "",
                        "cj": "",
                        "cjjc": "",
                        "gys": "",
                        "gysjc": "",
                        "scyl":"",
                        "scylb":"",
                        "dyyl":"",
                        "dyylb":""
                    }
                },
                {
                    name: '粉煤灰',
                    key: 'fmh',
                    type: 2,
                    json: {
                        "batch": "",
                        "certificateNo": "",
                        "pktzid": "",
                        "xhbgbh": "",
                        // "ypmc": "",
                        // "ypdj": "",
                        // "ypgg": "",
                        "clmc": "",
                        "cllx": "",
                        "clgg": "",
                        "wlmc": "",
                        "wllx": "",
                        "wlgg": "",
                        "cj": "",
                        "cjjc": "",
                        "gys": "",
                        "gysjc": "",
                        "scyl":"",
                        "scylb":"",
                        "dyyl":"",
                        "dyylb":""
                    }
                },
                {
                    name: '矿渣粉',
                    key: 'kzf',
                    type: 3,
                    json: {
                        "batch": "",
                        "certificateNo": "",
                        "pktzid": "",
                        "xhbgbh": "",
                        // "ypmc": "",
                        // "ypdj": "",
                        // "ypgg": "",
                        "clmc": "",
                        "cllx": "",
                        "clgg": "",
                        "wlmc": "",
                        "wllx": "",
                        "wlgg": "",
                        "cj": "",
                        "cjjc": "",
                        "gys": "",
                        "gysjc": "",
                        "scyl":"",
                        "scylb":"",
                        "dyyl":"",
                        "dyylb":""
                    }
                },
                {
                    name: '粗骨料',
                    key: 'cgl',
                    type: 4,
                    json: {
                        "batch": "",
                        "certificateNo": "",
                        "pktzid": "",
                        "xhbgbh": "",
                        // "ypmc": "",
                        // "ypdj": "",
                        // "ypgg": "",
                        "clmc": "",
                        "cllx": "",
                        "clgg": "",
                        "wlmc": "",
                        "wllx": "",
                        "wlgg": "",
                        "cj": "",
                        "cjjc": "",
                        "gys": "",
                        "gysjc": "",
                        "scyl":"",
                        "scylb":"",
                        "dyyl":"",
                        "dyylb":""
                    }
                },
                {
                    name: '细骨料',
                    key: 'xgl',
                    type: 5,
                    json: {
                        "batch": "",
                        "certificateNo": "",
                        "pktzid": "",
                        "xhbgbh": "",
                        // "ypmc": "",
                        // "ypdj": "",
                        // "ypgg": "",
                        "clmc": "",
                        "cllx": "",
                        "clgg": "",
                        "wlmc": "",
                        "wllx": "",
                        "wlgg": "",
                        "cj": "",
                        "cjjc": "",
                        "gys": "",
                        "gysjc": "",
                        "scyl":"",
                        "scylb":"",
                        "dyyl":"",
                        "dyylb":""
                    }
                },
                {
                    name: '外加剂1',
                    key: 'wjj1',
                    type: 6,
                    json: {
                        "batch": "",
                        "certificateNo": "",
                        "pktzid": "",
                        "xhbgbh": "",
                        // "ypmc": "",
                        // "ypdj": "",
                        // "ypgg": "",
                        "clmc": "",
                        "cllx": "",
                        "clgg": "",
                        "wlmc": "",
                        "wllx": "",
                        "wlgg": "",
                        "cj": "",
                        "cjjc": "",
                        "gys": "",
                        "gysjc": "",
                        "scyl":"",
                        "scylb":"",
                        "dyyl":"",
                        "dyylb":""
                    }
                },
                {
                    name: '外加剂2',
                    key: 'wjj2',
                    type: 6,
                    json: {
                        "batch": "",
                        "certificateNo": "",
                        "pktzid": "",
                        "xhbgbh": "",
                        // "ypmc": "",
                        // "ypdj": "",
                        // "ypgg": "",
                        "clmc": "",
                        "cllx": "",
                        "clgg": "",
                        "wlmc": "",
                        "wllx": "",
                        "wlgg": "",
                        "cj": "",
                        "cjjc": "",
                        "gys": "",
                        "gysjc": "",
                        "scyl":"",
                        "scylb":"",
                        "dyyl":"",
                        "dyylb":""
                    }
                },
                {
                    name: '外掺料1',
                    key: 'wcl1',
                    type: 2,
                    json: {
                        "batch": "",
                        "certificateNo": "",
                        "pktzid": "",
                        "xhbgbh": "",
                        // "ypmc": "",
                        // "ypdj": "",
                        // "ypgg": "",
                        "clmc": "",
                        "cllx": "6",
                        "clgg": "",
                        "wlmc": "膨胀剂",
                        "wllx": "",
                        "wlgg": "",
                        "cj": "",
                        "cjjc": "",
                        "gys": "",
                        "gysjc": "",
                        "scyl":"",
                        "scylb":"",
                        "dyyl":"",
                        "dyylb":""
                    }
                },
                {
                    name: '外掺料2',
                    key: 'wcl2',
                    type: 3,
                    json: {
                        "batch": "",
                        "certificateNo": "",
                        "pktzid": "",
                        "xhbgbh": "",
                        // "ypmc": "",
                        // "ypdj": "",
                        // "ypgg": "",
                        "clmc": "",
                        "cllx": "6",
                        "clgg": "",
                        "wlmc": "纤维",
                        "wllx": "",
                        "wlgg": "",
                        "cj": "",
                        "cjjc": "",
                        "gys": "",
                        "gysjc": "",
                        "scyl":"",
                        "scylb":"",
                        "dyyl":"",
                        "dyylb":""
                    }
                },
            ],
            tableDataOrg: [
                {
                    name: '水泥',
                    key: 'sn',
                    type: 1,
                    json: {
                        "batch": "",
                        "certificateNo": "",
                        "pktzid": "",
                        "xhbgbh": "",
                        // "ypmc": "",
                        // "ypdj": "",
                        // "ypgg": "",
                        "clmc": "",
                        "cllx": "",
                        "clgg": "",
                        "wlmc": "",
                        "wllx": "",
                        "wlgg": "",
                        "cj": "",
                        "cjjc": "",
                        "gys": "",
                        "gysjc": "",
                        "scyl":"",
                        "scylb":"1.00",
                        "dyyl":"",
                        "dyylb":"1.00",
                    }
                },
                {
                    name: "水",
                    key: 'water',
                    type: -1,
                    json: {
                        "batch": "",
                        "certificateNo": "",
                        "pktzid": "",
                        "xhbgbh": "",
                        // "ypmc": "",
                        // "ypdj": "",
                        // "ypgg": "",
                        "clmc": "",
                        "cllx": "",
                        "clgg": "",
                        "wlmc": "",
                        "wllx": "",
                        "wlgg": "",
                        "cj": "",
                        "cjjc": "",
                        "gys": "",
                        "gysjc": "",
                        "scyl":"",
                        "scylb":"",
                        "dyyl":"",
                        "dyylb":""
                    }
                },
                {
                    name: '粉煤灰',
                    key: 'fmh',
                    type: 2,
                    json: {
                        "batch": "",
                        "certificateNo": "",
                        "pktzid": "",
                        "xhbgbh": "",
                        // "ypmc": "",
                        // "ypdj": "",
                        // "ypgg": "",
                        "clmc": "",
                        "cllx": "",
                        "clgg": "",
                        "wlmc": "",
                        "wllx": "",
                        "wlgg": "",
                        "cj": "",
                        "cjjc": "",
                        "gys": "",
                        "gysjc": "",
                        "scyl":"",
                        "scylb":"",
                        "dyyl":"",
                        "dyylb":""
                    }
                },
                {
                    name: '矿渣粉',
                    key: 'kzf',
                    type: 3,
                    json: {
                        "batch": "",
                        "certificateNo": "",
                        "pktzid": "",
                        "xhbgbh": "",
                        // "ypmc": "",
                        // "ypdj": "",
                        // "ypgg": "",
                        "clmc": "",
                        "cllx": "",
                        "clgg": "",
                        "wlmc": "",
                        "wllx": "",
                        "wlgg": "",
                        "cj": "",
                        "cjjc": "",
                        "gys": "",
                        "gysjc": "",
                        "scyl":"",
                        "scylb":"",
                        "dyyl":"",
                        "dyylb":""
                    }
                },
                {
                    name: '粗骨料',
                    key: 'cgl',
                    type: 4,
                    json: {
                        "batch": "",
                        "certificateNo": "",
                        "pktzid": "",
                        "xhbgbh": "",
                        // "ypmc": "",
                        // "ypdj": "",
                        // "ypgg": "",
                        "clmc": "",
                        "cllx": "",
                        "clgg": "",
                        "wlmc": "",
                        "wllx": "",
                        "wlgg": "",
                        "cj": "",
                        "cjjc": "",
                        "gys": "",
                        "gysjc": "",
                        "scyl":"",
                        "scylb":"",
                        "dyyl":"",
                        "dyylb":""
                    }
                },
                {
                    name: '细骨料',
                    key: 'xgl',
                    type: 5,
                    json: {
                        "batch": "",
                        "certificateNo": "",
                        "pktzid": "",
                        "xhbgbh": "",
                        // "ypmc": "",
                        // "ypdj": "",
                        // "ypgg": "",
                        "clmc": "",
                        "cllx": "",
                        "clgg": "",
                        "wlmc": "",
                        "wllx": "",
                        "wlgg": "",
                        "cj": "",
                        "cjjc": "",
                        "gys": "",
                        "gysjc": "",
                        "scyl":"",
                        "scylb":"",
                        "dyyl":"",
                        "dyylb":""
                    }
                },
                {
                    name: '外加剂1',
                    key: 'wjj1',
                    type: 6,
                    json: {
                        "batch": "",
                        "certificateNo": "",
                        "pktzid": "",
                        "xhbgbh": "",
                        // "ypmc": "",
                        // "ypdj": "",
                        // "ypgg": "",
                        "clmc": "",
                        "cllx": "",
                        "clgg": "",
                        "wlmc": "",
                        "wllx": "",
                        "wlgg": "",
                        "cj": "",
                        "cjjc": "",
                        "gys": "",
                        "gysjc": "",
                        "scyl":"",
                        "scylb":"",
                        "dyyl":"",
                        "dyylb":""
                    }
                },
                {
                    name: '外加剂2',
                    key: 'wjj2',
                    type: 6,
                    json: {
                        "batch": "",
                        "certificateNo": "",
                        "pktzid": "",
                        "xhbgbh": "",
                        // "ypmc": "",
                        // "ypdj": "",
                        // "ypgg": "",
                        "clmc": "",
                        "cllx": "",
                        "clgg": "",
                        "wlmc": "",
                        "wllx": "",
                        "wlgg": "",
                        "cj": "",
                        "cjjc": "",
                        "gys": "",
                        "gysjc": "",
                        "scyl":"",
                        "scylb":"",
                        "dyyl":"",
                        "dyylb":""
                    }
                },
                {
                    name: '外掺料1',
                    key: 'wcl1',
                    type: 2,
                    json: {
                        "batch": "",
                        "certificateNo": "",
                        "pktzid": "",
                        "xhbgbh": "",
                        // "ypmc": "",
                        // "ypdj": "",
                        // "ypgg": "",
                        "clmc": "",
                        "cllx": "6",
                        "clgg": "",
                        "wlmc": "膨胀剂",
                        "wllx": "",
                        "wlgg": "",
                        "cj": "",
                        "cjjc": "",
                        "gys": "",
                        "gysjc": "",
                        "scyl":"",
                        "scylb":"",
                        "dyyl":"",
                        "dyylb":""
                    }
                },
                {
                    name: '外掺料2',
                    key: 'wcl2',
                    type: 3,
                    json: {
                        "batch": "",
                        "certificateNo": "",
                        "pktzid": "",
                        "xhbgbh": "",
                        // "ypmc": "",
                        // "ypdj": "",
                        // "ypgg": "",
                        "clmc": "",
                        "cllx": "6",
                        "clgg": "",
                        "wlmc": "纤维",
                        "wllx": "",
                        "wlgg": "",
                        "cj": "",
                        "cjjc": "",
                        "gys": "",
                        "gysjc": "",
                        "scyl":"",
                        "scylb":"",
                        "dyyl":"",
                        "dyylb":""
                    }
                },
            ],
            editForm: {
                "prop": "",
                "index": -1,
                "scyl":"",
                "scylb":"",
                "dyyl":"",
                "dyylb":"",

                "clmc": "",
                "cllx": "",
                "clgg": "",
                "wlmc": "",
                "wlgg": "",
                "cj": "",
                "cjjc": "",
                "gys": "",
                "gysjc": "",
            },
            consumptionForm: [
                // {
                //     type: 'input',
                //     label: '生产用量',
                //     prop: 'scyl',
                //     value: "",
                //     placeholder: "生产用量"
                // },
                // {
                //     type: 'input',
                //     label: '生产用量比例',
                //     prop: 'scylb',
                //     disabled: true,
                //     value: "",
                //     placeholder: "生产用量比例"
                // },
                {
                    type: 'input',
                    label: '用量',
                    prop: 'dyyl',
                    value: "",
                    placeholder: "用量"
                },
                {
                    type: 'input',
                    label: '用量比例',
                    prop: 'dyylb',
                    disabled: true,
                    value: "",
                    placeholder: "用量比例"
                },
            ],
            mixProportionInfo: {},
            
            cjOpts: [],
            supplierCompanyOpts: [],
            materialSpecOpts: [],
            // 批量修改用到的筛选信息
            customerList: [],
            engineeringList: [],

            otherEntrustExperimentBlur: '',
            otherPanelSelecterList: [],
            
            selectedPhbObj: {},
            phbbhOptions: [],
        }
    },

    computed: {
        taskNoString() {
            return this.taskSelects.map(item => item.rwdextraInfo.frwno).join(',');
        }
    },

    filters: {
        isNull: function (value) {
            if (value == undefined || value == null || value === '' || isNaN(value)) return '---'
            
            return value;
        },
    },

    methods: {
        show() {
            this.showDialog = true;
            this.queryEditDetail();
        },
        handleClose() {
            this.showDialog = false;
            this.$emit('close');
        },

      isEmpty(val) {
            if (typeof val === "boolean") {
                return false;
            }
            if (typeof val === "number") {
                return false;
            }
            if (val instanceof Array) {
                if (val.length === 0) return true;
            } else if (val instanceof Object) {
                if (JSON.stringify(val) === "{}" || val == null ) return true;
            } else {
                if (
                val === "null" ||
                val == null ||
                val === "undefined" ||
                val === undefined ||
                val === ""
                )
                return true;
                return false;
            }
            return false;
        },
      getYongLiangInt(str, index){
            if(this.isEmpty(str)) return ''
            let res = cpEvenRound(str, index);
            if(res == 0.00 && index == 2) return '';
            return res;
        },

        generateReportResp() {
            this.$confirm('系统将自动根据完成方量、配比编号及计划时间匹配对应的报告，是否确认？').then(() => {
                console.log(this.customerForm);
                console.log(this.mixProportionForm);
                console.log(this.otherForm);

                let proportionMaterial = {};
                this.tableData.map(item => {
                    proportionMaterial[item.key] = item.json;
                });
                let mixProportionForm = {
                    ...this.mixProportionInfo,
                    proportionMaterial: proportionMaterial
                }
                
                this.$api.generateReport({
                    frwdhList: [this.taskSelects[0].frwdh],
                    printJson: {
                        customer: this.customerForm,
                        mixProportion: mixProportionForm,
                        ...this.otherForm,
                        planQuantity: this.customerForm.planQuantity || "",
                        FWcsl: this.customerForm.FWcsl || "",
                        pouringPosition: this.customerForm.pouringPosition || "",
                        ftpz: this.customerForm.ftpz || "",
                        plantimeStart: this.customerForm.plantimeStart || "",
                        supplyStartTime: this.customerForm.supplyStartTime || "",
                        supplyEndTime: this.customerForm.supplyEndTime || "",
                        ksdj: this.customerForm.ksdj || "",
                    }
                }).then(res => {
                    if (res.succ) {
                        this.$message.success('生成成功');
                        // this.$emit("saveSuccessed");
                        this.handlePrintJson(res.data.printJson, res.data);
                    } else {
                        this.$message.error(res.msg || '生成失败，请重试')
                    }
                })
            });
        },

        saveForm(closePage) {
            console.log(this.customerForm);
            console.log(this.mixProportionForm);
            console.log(this.otherForm);

            let proportionMaterial = {};
            this.tableData.map(item => {
                proportionMaterial[item.key] = item.json;
            });
            let mixProportionForm = {
                ...this.mixProportionInfo,
                proportionMaterial: proportionMaterial
            }

            this.$api.updateBatchPrintDetail({
                frwdhList: [this.taskSelects[0].frwdh],
                printJson: {
                    customer: this.customerForm,
                    mixProportion: mixProportionForm,
                    ...this.otherForm,
                    planQuantity: this.customerForm.planQuantity || "",
                    FWcsl: this.customerForm.FWcsl || "",
                    pouringPosition: this.customerForm.pouringPosition || "",
                    ftpz: this.customerForm.ftpz || "",
                    plantimeStart: this.customerForm.plantimeStart || "",
                    supplyStartTime: this.customerForm.supplyStartTime || "",
                    supplyEndTime: this.customerForm.supplyEndTime || "",
                    ksdj: this.customerForm.ksdj || "",
                }
            }).then(res => {
                if (res.succ) {
                    this.$message.success('保存成功');
                    this.$emit("saveSuccessed");
                    if (closePage) {
                        this.handleClose();
                    }
                } else {
                    this.$message.error(res.msg || '保存失败，请重试')
                }
            })
        },

        // 获取所有客户信息
        getErpCustomerAllResp() {
            this.$api.getErpCustomerAll({}, this).then(res => {
                if(res.succ){
                    this.customerList = res.data.list.map(i => {
                        if (this.customerForm.erpCustomerId == i.id) {
                            this.$set(this.customerForm, "customerName", i.customerName);
                        }
                        return {
                            label: i.customerName,
                            value: i,
                        }
                    });
                    this.$set(this.customerFormContent[0], "options", this.customerList);
                }else{
                    this.$message.error(res.msg || '查询失败')
                }
            })
        },

        // 获取所有任务信息
        getEngineeringListAllResp(erpCustomerId) {
            this.$api.getEngineeringListAll({
                erpCustomerId: erpCustomerId
            }, this).then(res => {
                if(res.succ){
                    this.engineeringList = res.data.list.map(i => {
                        return {
                            label: i.projectName,
                            value: i,
                        }
                    });
                    this.$set(this.customerFormContent[1], "options", this.engineeringList);
                }else{
                    this.$message.error(res.msg || '查询失败')
                }
            })
        },

         // 选择回调
         changeCustomerSelect(item) {
            this.$set(this.customerForm, 'erpCustomerId', item.id)
            this.$set(this.customerForm,'projectName', '');
            this.$set(this.customerForm,'erpProjectId', '');
            this.$set(this.customerForm,'gcdz', '');
            this.$set(this.customerForm,'jsdwName', '');
            this.$set(this.customerForm,'buildName', '');
            this.$set(this.customerForm,'bjbh', '')
            
            this.getEngineeringListAllResp(item.id);
        },
        changeEngineeringSelect(item) {
            this.$set(this.customerForm,'projectName', item.projectName);
            this.$set(this.customerForm,'erpProjectId', item.id);
            this.$set(this.customerForm,'gcdz', item.projectAddress);
            this.$set(this.customerForm,'jsdwName', item.jsdw);
            this.$set(this.customerForm,'buildName', item.sgdw);
            this.$set(this.customerForm,'bjbh', item.bjbh)
        },
        changeKsdjSelect(val) {
            this.$set(this.customerForm, 'ksdj', val);
        },

        handleSelectKytz() {
            this.$refs.panelSelecter.show();
        },

        handleOtherPanelSelect(type) {
            if (type === 'kstzypbh') {
                this.otherEntrustExperimentBlur = "抗渗等级";
            }else if (type === 'kztzypbh') {
                this.otherEntrustExperimentBlur = "抗折强度";
            }else if (type === 'kllztzypbh') {
                this.otherEntrustExperimentBlur = "氯离子含量";
            }
            this.$refs.panelOtherSelecter.show();
        },

        panelSelectionChange(noList, selectedInfos) {
            // this.updateKytzList(noList);
            this.otherForm.kytzList = noList;
            this.otherForm.kyInfoList = selectedInfos;
        },

        otherPanelSelectionChange(noList, selectedInfos) {
            if (selectedInfos.length > 0) {
                if (this.otherEntrustExperimentBlur === '抗折强度') {
                    this.otherForm.kztzypbh = selectedInfos[0].ypbh;
                    this.otherForm.kztzjl = selectedInfos[0].jcjl;
                    this.otherForm.kzExperimentId = selectedInfos[0].experimentId;
                    this.otherForm.kztzwtbh = selectedInfos[0].tzwtbh;
                }else if (this.otherEntrustExperimentBlur === '抗渗等级') {
                    this.otherForm.kstzypbh = selectedInfos[0].ypbh; // 协会委托编号
                    this.otherForm.kstzjl = selectedInfos[0].jcjl;
                    this.otherForm.ksExperimentId = selectedInfos[0].experimentId;
                    this.otherForm.kstzwtbh = selectedInfos[0].tzwtbh; // 台账委托编号
                }else if (this.otherEntrustExperimentBlur === '氯离子含量') {
                    this.otherForm.kllztzypbh = selectedInfos[0].ypbh;
                    this.otherForm.kllztzjl = selectedInfos[0].jcjl;
                    this.otherForm.kllExperimentId = selectedInfos[0].experimentId;
                    this.otherForm.kllztzwtbh = selectedInfos[0].tzwtbh;
                }
            }
        },

        updateKytzList(noList) {
            let kytzArr = [].concat(this.otherForm.kytzList);
            noList.forEach(el => {
                if (kytzArr.indexOf(el) < 0) {
                    kytzArr.push(el);
                }
            });
            this.otherForm.kytzList = kytzArr;
        },
        deleteKytz(item) {
            if (this.otherForm.kytzList.indexOf(item) > -1) {
                this.otherForm.kytzList.splice(this.otherForm.kytzList.indexOf(item), 1);
            }
        },

        queryEditDetail() {
            let frwdh = this.taskSelects[0].frwdh;
            this.$api.queryPrintByFrwdh(`frwdh=${frwdh}`).then(res => {
                if (res.succ && res.data) {
                    this.handlePrintJson(res.data.printJson, res.data);
                } else {
                    this.$message.error(res.msg || '查询失败')
                }
                // this.getErpCustomerAllResp();
            })
        },

        handlePrintJson(printJson, resData) {
            let rwdextra = resData.rwdextra;
            // 客户名称 rwdextra.rwdextraInfo.fhtdw
            // 工程名称 rwdextra.rwdextraInfo.fgcmc
            // 计划方量 rwdextra.rwdextraInfo.fjhsl
            // 完成方量 rwdextra.rwdextraInfo.fwcsl
            // 施工部位 rwdextra.rwdextraInfo.fjzbw
            this.customerForm = {
                fphbNo: rwdextra?.rwdextraInfo?.fphbNo || "",
                projectName: rwdextra?.rwdextraInfo?.fgcmc || "",
                customerName: rwdextra?.rwdextraInfo?.fhtdw || "",
                erpProjectId: printJson?.customer?.erpProjectId || "",
                erpCustomerId: printJson?.customer?.erpCustomerId || "",
                gcdz: printJson?.customer?.gcdz || "",
                jsdwName: printJson?.customer?.jsdwName || "",
                buildName: printJson?.customer?.buildName || "",
                bjbh: printJson?.customer?.bjbh || "",
                planQuantity: rwdextra?.rwdextraInfo?.fjhsl || "",
                FWcsl: rwdextra?.rwdextraInfo?.fwcsl || "",
                tld: rwdextra?.rwdextraInfo?.ftld || "",
                pouringPosition: rwdextra?.rwdextraInfo?.fjzbw || "",
                ftpz: rwdextra?.rwdextraInfo?.ftpz || "",
                plantimeStart: rwdextra?.plantime || "",
                supplyStartTime: resData?.supplyStartTime || "",
                supplyEndTime: resData?.supplyEndTime || "",
                ksdj: rwdextra?.rwdextraInfo?.ksdj || "",
            };
            this.otherForm = {
                kytzList: printJson?.kytzList || [],
                kyInfoList: printJson?.kyInfoList || [],
                kyExperimentId: printJson?.kyExperimentId || "",
                kstzypbh: printJson?.kstzypbh || "",
                kstzjl: printJson?.kstzjl || "",
                kztzypbh: printJson?.kztzypbh || "",
                kztzjl: printJson?.kztzjl || "",
                kllztzypbh: printJson?.kllztzypbh || "",
                kllztzjl: printJson?.kllztzjl || "",
                llzhl: printJson?.llzhl || "",
                phbbz: printJson?.phbbz || "",
                cchgzbh: resData?.qualityCertificateNo || "",
                phbbgbh: rwdextra?.rwdextraInfo?.frwno || "",
                kpjdbh: resData?.openAppraisalNo || "",
                cczmsbz: printJson?.cczmsbz || "",
                cczmqfrq: printJson?.cczmqfrq || "",

                ksExperimentId: printJson?.ksExperimentId || "",
                kstzwtbh: printJson?.kstzwtbh || "",
                kzExperimentId: printJson?.kzExperimentId || "",
                kztzwtbh: printJson?.kztzwtbh || "",
                kllExperimentId: printJson?.kllExperimentId || "",
                kllztzwtbh: printJson?.kllztzwtbh || "",
            };
            this.mixProportionInfo = {
                ...printJson?.mixProportionInfo,
                productionSjb: printJson?.mixProportionInfo?.productionSjb || "",
                productionSl: printJson?.mixProportionInfo?.productionSl || "",
                productionRz: printJson?.mixProportionInfo?.productionRz || "",
                productionCl: printJson?.mixProportionInfo?.productionCl || "",
                printSjb: printJson?.mixProportionInfo?.printSjb || "",
                printSl: printJson?.mixProportionInfo?.printSl || "",
                printRz: printJson?.mixProportionInfo?.printRz || "",
                printCl: printJson?.mixProportionInfo?.printCl || "",
                proportionPhb: printJson?.mixProportionInfo?.proportionPhb || "",
            }
            if (printJson?.mixProportionInfo?.proportionMaterial) {
                for (let index = 0; index < this.tableData.length; index++) {
                    let element = this.tableData[index];
                    const key = element["key"];
                    if (printJson?.mixProportionInfo?.proportionMaterial[key]) {
                        element.json = {
                            ...printJson.mixProportionInfo.proportionMaterial[key]
                        };

                        if (element.key == 'wjj1' || element.key == 'wjj2') {
                            element.json.scyl = this.getYongLiangInt(element.json.scyl, 2);
                            element.json.scylb = this.getYongLiangInt(element.json.scylb, 2);
                            element.json.dyyl = this.getYongLiangInt(element.json.dyyl, 2);
                            element.json.dyylb = this.getYongLiangInt(element.json.dyylb, 2);
                        }else{
                            element.json.scyl = this.getYongLiangInt(element.json.scyl, 0);
                            element.json.scylb = this.getYongLiangInt(element.json.scylb, 2);
                            element.json.dyyl = this.getYongLiangInt(element.json.dyyl, 0);
                            element.json.dyylb = this.getYongLiangInt(element.json.dyylb, 2);
                        }
                    }
                }
            }
            this.requestMixProportion();
        },

        handleValidationVisibleClose() {
            this.validationVisible = false;
        },
        setSelectInfo(row) {
            console.log(row,this.selectIndex);
            let org = this.tableData[this.selectIndex];
            let cllxName = this.$util.experimentTypeToName(row.experimentType);
            this.$set(this.tableData,this.selectIndex,{
                ...org,
                json: {
                    ...org.json,
                    "pktzid": row.id || "",
                    "xhbgbh": row.reportId || "",
                    "batch": row.batch || "",
                    "certificateNo": row.certificateNo || "",
                    "clmc": row.materialsName || "",
                    "cllx": row.experimentType || "",
                    "cllxName": cllxName,
                    "clgg": row.materialsSpecs || "",
                    "wlmc": row.matterName || "",
                    "wllx": row.experimentType || "",
                    "wllxName": cllxName,
                    "wlgg": row.matterSpecs || "",
                    "cj": row.factory || "",
                    "cjjc": row.factoryCalled || "",
                    "cj": row.factory || "",
                    "cjjc": row.factoryCalled || "",
                    "gys": row.supplyCompanyName || "",
                    "gysjc": row.supplyCompanyCalled || "",
                }
            });
            this.validationVisible = false;
        },

        handleSetTarget(row,index){
            this.selectIndex = index;
            this.validationVisible = true;
            this.$nextTick(()=>{
                this.$refs.panel.initData(1,10,row.type);
            },200)
        },

        setConsumptionTarget(row,index){
            if (row.key != 'sn') {
            let snObj = this.tableData[0].json;
            if ((snObj.scyl == '' || snObj.scyl == null || snObj.scyl == undefined)
                || (snObj.dyyl == '' || snObj.dyyl == null || snObj.dyyl == undefined)) {
                this.$message({
                    showClose: true,
                    message: "请先设置水泥的用量",
                    type: "warning",
                });
                return;
            }
            }

            if (row.key != 'water' && row.key != 'wcl1' && row.key != 'wcl2') {
                if (!row.json.xhbgbh) {
                    this.$message({
                        showClose: true,
                        message: "请先选择原材料",
                        type: "warning",
                    });
                    return;
                }
            }
            this.editForm = {
                "prop": row.key,
                "index": index,
                "dyyl": row.json.dyyl,
                "dyylb": row.json.dyylb,

                "clmc": row.json.clmc,
                "clgg": row.json.clgg,
                "wlmc": row.json.wlmc,
                "wlgg": row.json.wlgg,
                
                "cj": row.json.cj,
                "cjjc": row.json.cjjc,
                "gys": row.json.gys,
                "gysjc": row.json.gysjc,
                "wlObj": {
                    "clmc": row.json.clmc,
                    "clgg": row.json.clgg,
                    "wlmc": row.json.wlmc,
                    "wlgg": row.json.wlgg,
                },
            }
            if (this.editForm.prop === 'water') {
                this.editForm.clmc = row.json.clmc || '水';
                this.editForm.clgg = row.json.clgg || '清水';
            }else if (this.editForm.prop === 'wcl1') {
                this.editForm.wlmc = row.json.wlmc || '膨胀剂';
                this.editForm.wlgg = row.json.wlgg || '';
                this.editForm.clmc = row.json.clmc || '';
                this.editForm.clgg = row.json.clgg || '';
                this.queryMaterialsSpecListResp();
            }else if (this.editForm.prop === 'wcl2') {
                this.editForm.wlmc = row.json.wlmc || '纤维';
                this.editForm.wlgg = row.json.wlgg || '';
                this.editForm.clmc = row.json.clmc || '';
                this.editForm.clgg = row.json.clgg || '';
                this.queryMaterialsSpecListResp();
            }

            this.showConsumption = true;
        },

        // 查询材料规格
        queryMaterialsSpecListResp() {
            this.$api.queryMaterialsSpecList(`materialsType=6&materialsName=${this.editForm.wlmc}`, this).then(res => {
            if (res.code == 1) {
                this.materialSpecOpts = res.data.list.map(item => {
                    return {
                        label: `${item.wlgg}【${item.clgg}】`,
                        value: item,
                    }
                })
            }
            });
        },
        // 外掺料厂家
        changeMaterialSpec(item) {
            this.editForm.wlmc = item.wlmc;
            this.editForm.wlgg = item.wlgg;
            this.editForm.clgg = item.clgg;
            this.editForm.clmc = item.clmc;

            this.changeSupplierCompnayMaterialSpec();

            this.$api.queryMaterialsFactoryList(`materialsType=6&materialsName=${this.editForm.wlmc}&materialsSpec=${this.editForm.wlgg}`, this).then(res => {
            if (res.code == 1) {
                this.cjOpts = res.data.list.map(item => {
                    return {
                        label: item.manufacturers,
                        value: item,
                    }
                })
            }
            });
        },
        // 外掺料供应商
        changeSupplierCompnayMaterialSpec(item) {
            this.$api.queryMaterialSupplierCompanyList(`materialsType=6&materialsName=${this.editForm.wlmc}&materialsSpec=${this.editForm.wlgg}`, this).then(res => {
                if (res.code == 1) {
                    this.supplierCompanyOpts = res.data.list.map(item => {
                    return {
                        label: item.supplierName,
                        value: item,
                    }
                    });
                }
            })
        },
        // 厂家
        changeCj(item) {
            this.editForm.cj = item.manufacturers;
            this.editForm.cjjc = item.manufacturersCalled;
        },
        changeGys(item) {
            this.editForm.gys = item.supplierName;
            this.editForm.gysjc = item.supplierAbbreviation;
        },
        handleConsumptionChange(val, prop) {
            if (this.editForm.prop != 'sn') {
                let snObj = this.tableData[0].json;
                let sn_scyl = snObj.scyl;
                let sn_dyyl = snObj.dyyl;
                if (prop == 'scyl') {
                    // let res = calcEquation([
                    // {
                    //     v: val,
                    // },
                    // {
                    //     k: '/',
                    //     v: sn_scyl,
                    // },
                    // ], 2)
                    // this.editForm.scylb = res;
                }else if (prop == 'dyyl') {
                    let res = calcEquation([
                    {
                        v: val,
                    },
                    {
                        k: '/',
                        v: sn_dyyl,
                    },
                    ], 2)
                    this.editForm.dyylb = res;
                }else{
                    this.editForm[prop] = val;
                }
            }
        },

        resetConsumptionTarget(row,index) {
            this.editForm = {
                "prop": row.key,
                "index": index,
                "scyl": '',
                "scylb": '',
                "dyyl": '',
                "dyylb": '',
                "cj": '',
                "cjjc": '',
                "gys": "",
                "gysjc": "",
                "wlObj": {},
            }
            
            row.json = {
                ...this.tableDataOrg[index].json,
            }
            this.editForm.scyl = row.json.scyl;
            this.editForm.scylb = row.json.scylb;
            this.editForm.dyyl = row.json.dyyl;
            this.editForm.dyylb = row.json.dyylb;

            this.saveConsumptionTarget();
        },

        saveConsumptionTarget() {
            let orgJson = this.tableData[this.editForm.index].json;
            let ylJson = {
                // "scyl": this.editForm.scyl,
                // "scylb": this.editForm.scylb,
                "dyyl": this.editForm.dyyl,
                "dyylb": this.editForm.dyylb
            }
            if (this.editForm.prop === 'water' || this.editForm.prop === 'wcl1' || this.editForm.prop === 'wcl2') {
                ylJson["clmc"] = this.editForm.clmc;
                ylJson["clgg"] = this.editForm.clgg;
                ylJson["wlmc"] = this.editForm.wlmc;
                ylJson["wlgg"] = this.editForm.wlgg;
                ylJson["cj"] = this.editForm.cj;
                ylJson["cjjc"] = this.editForm.cjjc;
                ylJson["gys"] = this.editForm.gys;
                ylJson["gysjc"] = this.editForm.gysjc;
            }
            this.$set(this.tableData,this.editForm.index,{
                ...this.tableData[this.editForm.index],
                json: {
                    ...orgJson,
                    ...ylJson,
                }
            });

            this.showConsumption = false;

            // 计算生产 和 打印的四个参数
            //  productionSjb: '', //生产水胶比
            //   productionSl: '',
            //   productionRz: '',
            //   productionCl: '',

            //   printSjb: '', //打印水胶比
            //   printSl: '',
            //   printRz: '',
            //   printCl: '', 
            let productionSjb = 0;
            let productionSl = 0;
            let productionRz = 0;
            let productionCl = 0;
            let printSjb = 0;
            let printSl = 0;
            let printRz = 0;
            let printCl = 0;

            // 水的生产用量
            let productionWater = 0;
            // 水泥+矿粉+粉煤灰 生产用量和
            let productionSjbSum = 0;

            // 水的打印用量
            let printWater = 0;
            // 水泥+矿粉+粉煤灰 打印用量和
            let printSjbSum = 0;

            // 外加剂掺量生产
            let productionWjj = 0;
            let productionWjjclSum = 0;
            // 外加剂掺量打印
            let printWjj = 0;
            let printWjjclSum = 0;

            // 细骨料用量
            let productionXgl = 0;
            let printXgl = 0;
            // 粗骨料用量
            let productionCgl = 0;
            let printCgl = 0;


            // for (let item of this.tableData) {
                for (let i=0; i < this.tableData.length; i++) {
                    let item = this.tableData[i];
                let itemJson = item.json;

                let snObj = this.tableData[0].json;
                let sn_dyyl = snObj.dyyl;
                let res = calcEquation([
                    {
                        v: itemJson.dyyl,
                    },
                    {
                        k: '/',
                        v: sn_dyyl,
                    },
                    ], 2)
                let ylJsonInfo = {
                "dyylb": res
            }
            this.$set(this.tableData, i,{
                ...this.tableData[i],
                json: {
                    ...itemJson,
                    ...ylJsonInfo,
                }
            });
                // 生产容重等于json 中的生产用量之和
                // productionRz += itemJson.scyl ? parseFloat(itemJson.scyl) : 0;
                // 打印容重等于json 中的打印用量之和
                printRz += itemJson.dyyl ? parseFloat(itemJson.dyyl) : 0;
                // 水胶比公式：(水 / （水泥+矿粉+粉煤灰）生产用量)*100)/1000
                if (item.key == 'water') {
                    // productionWater = itemJson.scyl ? parseFloat(itemJson.scyl) : 0;
                    printWater = itemJson.dyyl ? parseFloat(itemJson.dyyl) : 0;
                }
                if (item.key == 'sn' || item.key == 'fmh' || item.key == 'kzf' || item.key == 'wcl1') {
                    // productionSjbSum += itemJson.scyl ? parseFloat(itemJson.scyl) : 0;
                    printSjbSum += itemJson.dyyl ? parseFloat(itemJson.dyyl) : 0;
                }

                // 外加剂掺量：外加剂/（水泥+矿粉+掺和料）
                if (item.key == 'wjj1') {
                    // productionWjj = itemJson.scyl ? parseFloat(itemJson.scyl) : 0;
                    printWjj = itemJson.dyyl ? parseFloat(itemJson.dyyl) : 0;
                }
                
                if (item.key == 'sn' || item.key == 'kzf' || item.key == 'wcl2') {
                    // productionWjjclSum += itemJson.scyl ? parseFloat(itemJson.scyl) : 0;
                    printWjjclSum += itemJson.dyyl ? parseFloat(itemJson.dyyl) : 0;
                }

                // 砂率： 细骨料用量/(细骨料生产用量+粗骨料生产用量)*100
                if (item.key == 'xgl') {
                    // productionXgl = itemJson.scyl ? parseFloat(itemJson.scyl) : 0;
                    printXgl = itemJson.dyyl ? parseFloat(itemJson.dyyl) : 0;
                }
                if (item.key == 'cgl') {
                    // productionCgl = itemJson.scyl ? parseFloat(itemJson.scyl) : 0;
                    printCgl = itemJson.dyyl ? parseFloat(itemJson.dyyl) : 0;
                }
            }

            // 水胶比公式：(水 / （水泥+矿粉+粉煤灰）生产用量)
            // productionSjb = calcEquation([
            //     {
            //         v: productionWater,
            //     },{
            //         k: '/',
            //         v: productionSjbSum,
            //     }
            //     ], 2);
            printSjb = calcEquation([
                {
                    v: printWater,
                },{
                    k: '/',
                    v: printSjbSum,
                }
                ], 2);
            // 外加剂掺量：外加剂/（水泥+矿粉+掺和料）
            // productionCl = calcEquation([
            //     {
            //         v: productionWjj,
            //     },{
            //         k: '/',
            //         v: productionWjjclSum,
            //     }
            //     ], 2);
            // productionCl = productionWjj / productionWjjclSum;
            printCl = calcEquation([
                {
                    v: printWjj,
                },{
                    k: '/',
                    v: printWjjclSum,
                }
                ], 2);
            // printCl = printWjj / printWjjclSum;

            // 砂率： 细骨料用量/(细骨料生产用量+粗骨料生产用量)*100
            // productionSl = calcEquation([
            //     {
            //         v: productionXgl,
            //     },{
            //         k: '/',
            //         v: productionXgl + productionCgl,
            //     },{
            //         k: '*',
            //         v: 100,
            //     }
            //     ], 0);
            // productionSl = (productionXgl / (productionXgl + productionCgl)) * 100;
            printSl = calcEquation([
                {
                    v: printXgl,
                },{
                    k: '/',
                    v: printXgl + printCgl,
                },{
                    k: '*',
                    v: 100,
                }
                ], 0);
            // printSl = (printXgl / (printXgl + printCgl)) * 100;

            // 容重改为整数
            // productionRz = calcEquation([
            //     {
            //         v: productionRz,
            //     },{
            //         k: '*',
            //         v: 1,
            //     }
            //     ], 0);
            
            printRz = calcEquation([
                {
                    v: printRz,
                },{
                    k: '*',
                    v: 1,
                }
                ], 0);


            // this.mixProportionInfo.productionSjb = productionSjb;
            // this.mixProportionInfo.productionSl = productionSl;
            // this.mixProportionInfo.productionRz = productionRz;
            // this.mixProportionInfo.productionCl = productionCl;
            this.mixProportionInfo.printSjb = printSjb;
            this.mixProportionInfo.printSl = printSl;
            this.mixProportionInfo.printRz = printRz;
            this.mixProportionInfo.printCl = printCl;
        },
        handleConsumptionClose(done) {
            this.$confirm('确认关闭？')
            .then(_ => {
                if(done){
                    done();
                }
                this.showConsumption = false;
            })
            .catch(_ => {});
        },

        selectedMixProportion(item) {
            this.selectedPhbObj = {
                ...item
            };
            
            delete this.selectedPhbObj.proportionMaterial;

            let proportionMaterial = item.proportionMaterial;
            this.mixProportionInfo = {
                ...this.selectedPhbObj,
            }
            
            for (let tabItem of this.tableData) {
                tabItem.json = {
                    ...tabItem.json,
                    ...proportionMaterial[tabItem.key]
                };
            }
        },
        showMixProportion(val) {
            if (val) {
                this.requestMixProportion();
            }
        },

        requestMixProportion() {
          if (this.phbbhOptions.length == 0) {
            this.$api.queryMixProportionAll({}, this).then(res => {
                if (res.code == 1) {
                    this.phbbhOptions = res.data.list.map(item => {
                        if (item.proportionPhb === this.mixProportionInfo.proportionPhb) {
                            this.selectedPhbObj = item;
                        }
                        return {
                            label: item.proportionPhb + '【' + item.proportionRemarks + '】',
                            value: item
                        }
                    });
                }else{
                    this.$message.error(res.msg || '查询失败')
                }
            }).catch(err => {
                this.$message.error('查询失败');
            })
          }
        }
    },
}
</script>

<style lang="scss" scoped>
::v-deep .claim-dialog-box{
    // height: calc(100% - 40px);
    // overflow: hidden;
    .el-dialog__body{
        padding: 30px;
    }
}
.form-view {
    margin-top: 30px;
    .form-box {
        flex-wrap: wrap;
        .form-item {
            margin-right: 30px;
        }
    }
}
.row-cell {
    text-align: center;
    font-size: 17px;
    font-weight: 500;
    margin-right: 10px;
}
.footer-btn {
    position: absolute;
    bottom: 5px;
    right: 80px;
}
.row-title {
    border-bottom: #DDDFE6 1px solid;
    padding-bottom: 8px;
    margin-top: 20px;

    .row-title-label {
        font-family: PingFangSC, PingFang SC;
        font-weight: 600;
        font-size: 16px;
        color: #1F2329;
    }
}
</style>