<template>
  <div class="content-box">
    <div class="content flex-box flex-column ">
      <div class="g-card">
        <p class="gc-main pb16">采购任务编号：{{purchaseData.supplyTaskCode}}<span class="red">{{ status[purchaseData.state]}}</span></p>
        <!-- <p class="gc-main pb16">{{purchaseData.ftbj}}</p> -->
        <div class="fl">
          <p>原材料名称规格：{{purchaseData.projectName+ '-' + purchaseData.projectSpecs}}</p>
          <p>供应商：{{purchaseData.supplyCompanyCompany}}</p>
        </div>
        <div class="fl">
          <p>采购数量：{{purchaseData.supplySignQuantity}}</p>
          <p>厂家：{{purchaseData.factory}}</p>
        </div>
        <div class="fl">
          <p>采购时间：{{purchaseData.taskStartTime}}</p>
          <p>供应时间：{{purchaseData.supplyPlanTime}}</p>
        </div>
        <div class="fl">
          <p>收货方：{{purchaseData.mixingPlantCompany}}</p>
        </div>
        <div class="cb"></div>
      </div>
      <div class="h40"></div>
      <!-- <div class="region-con">
        <p class="title">报告列表</p>
        <p class="rc-item">
          <span>水泥报告</span>
          <span>BG00001</span>
          <el-button type="text" size="small" @click="tableOperated()">打印</el-button>
        </p>
      </div> -->

      
      <div class="h40"></div>
      <p class="title">运单列表</p>
      <div class="region-con">
        <el-row :gutter="32" style="height: 100%;">
          <el-table
            :data="receiptData"
            empty-text="暂无数据"
            height="100%"
            width="100%">
            <template v-for="item in receiptColumn">
              <af-table-column
                :key="item.prop"
                :prop="item.prop" 
                :label="item.label" 
                :formatter="item.formatter"
                :fixed="item.fixed" 
                :width="item.width || ''"
                align="center" 
              >
                
              </af-table-column>
            </template>
            <af-table-column width="150" label="操作" fixed="right" align="center" key="handle" :resizable="false">
              <template slot-scope="scope">
                <el-button type="text" size="small" style="color: #ff0000;" @click="deleteReceipt(scope.row)">删除</el-button>
              </template>
            </af-table-column>
          </el-table>
          <!-- <el-col :span="12">
            <div class="receipt-list">
              <div class="receipt-item" v-for="(item,index) in receiptData">
                <p>{{item.waybillCode}}</p>
                <p><span>{{item.factory}}</span>发货<span>{{item.transportQuantity}}</span></p>
                <p><img src="@/assets/images/icon_car_blue.png" alt="" />
                  <span>{{item.vehicleNum}}，{{item.vehiclePlate}}</span>{{item.driverName}}</p>
              </div>
            </div>
          </el-col> -->
          <!-- <el-col :span="12"> -->
            <!-- <div>
              <p class="title-public">生产视频：</p>
              <div class="video-box clearfloat">
                <div class="video-item">
                  <video class="art-video"
                    controls
                    preload="auto" crossorigin="anonymous" autoplay="" 
                    src="https://upload.wikimedia.org/wikipedia/commons/8/87/Schlossbergbahn.webm">
                  </video>
                  <p>dsaf</p>
                </div>
                <div class="cb"></div>
              </div>
              <p class="title-public">装车视频：</p>
              <div class="video-box clearfloat">
                <div class="video-item">
                  <video class="art-video"
                    controls
                    preload="auto" crossorigin="anonymous" autoplay="" 
                    src="https://upload.wikimedia.org/wikipedia/commons/8/87/Schlossbergbahn.webm">
                  </video>
                  <p>dsaf</p>
                </div>
                <div class="cb"></div>
              </div>
            </div> -->
          <!-- </el-col> -->
        </el-row>
      </div>
    </div>
  </div>
</template>

<script>
import { engDetailColumn } from '../../engineeringService/config.js'
export default {
  components: {},
  data() {
    return {
      purchaseData: {},
      loading: false,
      receiptData: [],
      receiptColumn: [
        {
          label: '运单编号',
          prop: 'waybillCode',
        },
        {
          label: '车号',
          prop: 'vehicleNum',
        },
        {
          label: '司机名称',
          prop: 'driverName',
        },
        {
          label: '车牌号',
          prop: 'vehiclePlate',
        },
        {
          label: '发货重量',
          prop: 'transportQuantity',
        },
        {
          label: '供应商名称',
          prop: 'supplyCompanyCompany',
        },
        {
          label: '厂家名称',
          prop: 'factory',
        },
        {
          label: '进场时间',
          prop: 'entryTime',
        },
      ],
      engDetailColumn: engDetailColumn,
      
      status:{
        0: "已拒绝",
        1: "待确认",
        2: "待派车",
        3: "已发货",
        4: "已完成"
      }
    };
  },
  
  created: function() {
    this.getTaskInfo(this.$route.query.experimentId);
  },
  methods: {
    initData(row){
      
    },
    //获取任务信息
    getTaskInfo(id){
      this.$api.getTcWaybill(`experimentId=${id}`, this).then(res => {
        if(res.succ){
          this.purchaseData = res.data.filter(item => item.id == this.$route.query.taskId)[0]
          this.getReceiptInfo(this.$route.query.experimentId);
        }else{
          this.$message.error(res.msg || '查询失败')
        }
      })
    },
    //获取小票信息
    getReceiptInfo(id){
      let parma = `experimentId=${id}&supplyTaskId=${this.$route.query.taskId}`;
      if (this.experimentType == '7') {
        parma = parma + `&frwdh=${this.purchaseData.frwdh}`;
      }
      this.$api.getTaskInfoMaterial(parma, this).then(res => {
        this.loading = false;
        if(res.succ){
          this.receiptData = res.data;
        }else{
          this.$message.error(res.msg || '查询失败')
        }
      })
    },

    deleteReceipt(row){
      this.$confirm("确定要删除吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        //删除
        this.$api.deleteByExperimentIdAndWaybillId({
          experimentId: this.$route.query.experimentId,
          waybillId: row.waybillId
        }, this).then((res) => {
          if (res.succ) {
            this.$message({
              showClose: true,
              message: "删除成功",
              type: "success",
            });
            this.getReceiptInfo(this.$route.query.experimentId);
          }
        });
      });
    },
  },
};
</script>

<style scoped lang="scss">
  
  .content-box{
    padding: 16px;
  }
  .content{
    width: 100%;
    height: 100%;
    padding: 16px;
    background: #FFFFFF;
    border-radius: 16px;
    .title{
      font-size: 16px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      color: #1F2329;
      line-height: 22px;
      letter-spacing: 1px;
      padding-bottom: 8px;
      border-bottom: 1px solid #E8E8E8;
      margin-bottom: 8px;
    }
    .region-con{
      height: 380px;
      .el-row,.el-col{
        height: 100%;
      }
      
      .rc-item{
        padding-top: 8px;
        height: 28px;
        span{
          color: $color-txt;
          line-height: 20px;
          letter-spacing: 1px;
          padding-right: 16px;
        }
        .el-button{
          height: 20px;
          padding: 0;
        }
      }
    }
  }
  .h40{
    height: 40px;
    width: 100%;
  }
  .cc-info{
    margin: 0 0px 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #E8E8E8;
    &:last-child{
      margin-bottom: 0;
      border: none;
      padding: 0;
    }
  }
  .pb16{
    padding-bottom: 16px !important;
  }
  
  .g-card{
    width: 100%;
    padding: 16px 0 0;
    margin-bottom: 0;
    & > div{
      margin-right: 62px;
    }
    p{
      color: $color-txt;
      line-height: 20px;
      letter-spacing: 1px;
      padding-bottom: 8px;
      &:last-child{
        padding: 0;
      }
    }
    .gc-main{
      font-size: 16px;
      font-weight: 600;
      color: #1F2329;
      line-height: 22px;
      letter-spacing: 1px;
      span{
        line-height: 20px;
        height: 20px;
        background: #FFE9D1;
        border-radius: 10px;
        font-size: 12px;
        font-weight: 600;
        display: inline-block;
        vertical-align: middle;
        margin-top: -2px;
        color: #FF7B2F;
        margin-left: 16px;
        padding: 0 7px;
        &.succ{
          color: $color-success;
          background: #DDEFEA;
        }
        &.red{
          color: #FF2F2F;
          background: #FFE9E9;
        }
      }
    }
  }
  
  .receipt-list{
    width: 100%;
    height: 100%;
    background: #EFF1F2;
    border-radius: 8px;
    padding: 16px;
    overflow-y: auto;
    display: flex;
    flex-wrap: wrap;
    gap: 16px 8px;
    align-content: flex-start;
    .receipt-item{
      width: calc(50% - 4px);
      height: 106px;
      background: #FFFFFF;
      border-radius: 8px;
      padding: 8px;
      p{
        &:nth-child(1){
          height: 22px;
          // font-size: 16px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 600;
          color: #1F2329;
          line-height: 22px;
          letter-spacing: 1px;
        }
        &:nth-child(2){
          padding: 8px 0 7px;
          color: #1F2329;
          line-height: 25px;
          height: 48px;
          letter-spacing: 1px;
          span{
            // font-size: 16px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 600;
            padding: 0 16px 0 4px;
          }
        }
        &:nth-child(3){
          height: 20px;
          font-size: 14px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #6A727D;
          line-height: 20px;
          letter-spacing: 1px;
          span{
            padding: 0 16px 0 4px;
          }
          img{
            width: 18px;
            height: 18px;
            display: inline-block;
            vertical-align: middle;
            margin-top: -2px;
          }
        }
      }
    }
  }
  
  
  .video-box{
    width: 100%;
    overflow-x: auto;
    padding-top: 8px;
    border-bottom: 1px solid #E8E8E8;
    margin-bottom: 24px;
    height: 185px;
    &:last-child{
      border: none;
    }
    .video-item{
      width: 220px;
      height: 140px;
      margin-right: 8px;
      float: left;
      .art-video{
        width: 100%;
      }
      p{
        height: 20px;
        font-size: 14px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #1F2329;
        line-height: 20px;
        letter-spacing: 1px;
        text-align: center;
        margin-top: 8px;
        margin-bottom: 24px;
      }
    }
  }

  .cell-state {
    .rda-task-state {
        display: inline-block;
        width: 43px;
        height: 18px;
        line-height: 18px;
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #FFFFFF;
        text-align: center;
        background: #1F7AFF;
        border-radius: 2px;
    }
    .dqr {
        background: #DC3290;
    }
    .ddd {
        background: #3369FF;
    }
    .dwc {
        background: #1FAE66;
    }
    .yqx {
        background: #D6D6D6;
    }
    .yjj {
        background: #ADAA00;
    }
    .ywc {
        background: #515157;
    }
  }
</style>