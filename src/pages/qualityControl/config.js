import { h } from 'vue'
import moment from 'moment'
export const rangePrecondTableProp = [
  {
    label: '样品编号',
    prop: 'smokeNo',
    // formatter: (row) => {
    //   return row.createTime.split(' ')[0];
    // },
  },
  {
    label: '样品用途',
    prop: 'sampleUse',
    formatter: (row) => {
      if(row.freqNumber || row.freqUnit){
        return `${row.sampleUse}(${row.freqNumber}${row.freqUnit})` 
      }else{
        return row.sampleUse
      }
    },
  },
  {
    label: '抽样人',
    prop: 'samplePersonName'
  },
  {
    label: '抽样时间',
    prop: 'sampleTime',
    formatter: (row) => {
      if (!row.sampleTime) {
        return '--';
      }
      return moment(row.sampleTime).format('YYYY-MM-DD')
    },
  },
  {
    label: '抽样数量',
    prop: 'sampleQuantity'
  },
  {
    label: '抽样照片',
    prop: 'sampleImg',
    formatter: (row) => {
      console.log(h)
      if(row.sampleImg){
        const oimgList = row.sampleImg.split(',')
        let ohtml = [];
        
        // <el-image 
        //     style="width: 100px; height: 100px"
        //     :src="url" 
        //     :preview-src-list="srcList">
        //   </el-image>
          
          
        for(let i = 0; i<oimgList.length; i++){
          ohtml.push(h('el-image', {
            style:{
              width: '60px',
              height: '60px',
              cursor: 'pointer'
            },
            attrs: {
              src: row.filePrefix + oimgList[i],
              'preview-src-list': oimgList
            }
          }, ''))
        }
        return h('div', null, ohtml) 
      }else{
        return '--'
      }
    },
    
  },
  
  {
    label: '处置人',
    prop: 'disposerPersonName'
  },
  {
    label: '处置时间',
    prop: 'updateTime',
    formatter: (row) => {
      if (!row.updateTime) {
        return '--';
      }
      return moment(row.updateTime).format('YYYY-MM-DD')
    },
  },
]

export const panelColumn = [
  // {
  //   label: '委托编号',
  //   prop: 'experimentNo',
  // },
  {
    label: '协会委托编号',
    prop: 'consignId',
    width: 160,
  },
  {
    label: '样品编号',
    prop: 'sampleId',
    width: 160,
  },{
    label: '报告编号',
    prop: 'reportNo',
  },{
    label: '质保书编号',
    prop: 'batch',
  },
  // { 
  //   label: '工程名称',
  //   prop: 'projectName',
  // },
  // {
  //   label: '客户名称',
  //   prop: 'customerName',
  // },
  // {
  //   label: '委托原因',
  //   prop: 'entrustReasonName'
  // },
  {
    label: '试验项目',
    prop: 'testProjectNameStr',
    width: 140
  },
  {
    label: '代表数量',
    prop: 'behalfNumber'
  },
  {
    label: '是否合格',
    prop: 'isQualified',
    formatter: (row) => {
      if(row.isQualified == 1){
        return '合格'
      }else if(row.isQualified == 2){
        return '不合格'
      }else{
        return "--"
      }
    },
  },
  // {
  //   label: "材料类型",
  //   prop: "experimentType",
  //   formatter: (row,materialsTypeOpts) => {
  //     for(let i =0;i<materialsTypeOpts.length; i++){
  //         if(row.experimentType == materialsTypeOpts[i].value){
  //             return materialsTypeOpts[i].label;
  //         }
  //     }
  //   },
  // },
  {
    label: '样品等级',
    prop: 'sampleLevel'
  },
  {
    label: "检验类型",
    prop: "checkType",
    // formatter: (row) => {
    //   if(row.checkType == 1){
    //     return "快检"
    //   }else if(row.checkType == 2){
    //     return "批检"
    //   }else{
    //     return "--"
    //   }
    // },
  },
  // {
  //   label: '委托人',
  //   prop: 'entrustPersonName'
  // },
  
  {
    label: '厂家',
    prop: 'factoryCalled'
  },
  {
    label: '供应商',
    prop: 'supplyCompanyCalled'
  },
  {
    label: '委托时间',
    prop: 'entrustTime',
    width: 140,
    formatter: (row) => {
      if (!row.entrustTime) {
        return '--';
      }
      return moment(row.entrustTime).format('YYYY-MM-DD')
    },
  },
  // {
  //   label: '试验人',
  //   prop: 'experimentPersonName'
  // },
  {
    label: '状态',
    prop: 'experimentStatus',
    width: 80
  },
  {
    label: '试验时间',
    prop: 'experimentTime',
    width: 140,
    formatter: (row) => {
      if (!row.experimentTime) {
        return '--';
      }
      return moment(row.experimentTime).format('YYYY-MM-DD')
    },
  },
  
  {
    label: '上传状态',
    prop: 'finistStatus',
  },
  // {
  //   label: '上传失败原因',
  //   prop: 'reason',
  //   width: 180
  // }
  // {
  //   label: '校核人',
  //   prop: 'checkPersonName'
  // },
  // {
  //   label: '校核时间',
  //   prop: 'checkTime'
  // },
  // {
  //   label: '批准人',
  //   prop: 'approvePersonName'
  // },
  // {
  //   label: '批准时间',
  //   prop: 'approveTime'
  // }
]

export const panelChildColumn = [
  {
    label: '检测结果',
    prop: 'resultValue',
  },
  {
    label: '是否检测',
    prop: 'isQualified',
    formatter: (row) => {
      if(row.isQualified == 0){
        return '未检测'
      }else if(row.isQualified == 1){
        return '合格'
      }else if(row.isQualified == 2){
        return '不合格'
      }else{
        return "--"
      }
    },
  },
  {
    label: '国标值	',
    prop: 'nationalStandardValue'
  },
  {
    label: '试验项目',
    prop: 'testProjectName'
  },
  {
    label: '合格指标',
    prop: 'qualifiedCheckText'
  },
  {
    label: '让步指标',
    prop: 'compromiseCheckText'
  },
  {
    label: '不合格指标',
    prop: 'unQualifiedCheckText'
  }
]


export const moistureColumn = [
  {
    label: '日期',
    prop: 'createTime',
  },
  {
    label: '细骨料',
    prop: '',
    children: [
      {
        label: '烘前试样重(g)',
        prop: 'fineAggregateBefore',
        align: 'right'
      },
      {
        label: '烘后试样重(g)',
        prop: 'fineAggregateAfter',
        align: 'right'
      },
      {
        label: '含水率(%)',
        prop: 'fineAggregateWater',
        align: 'right'
      }
    ]
  },
  {
    label: '粗骨料',
    prop: '',
    children: [
      {
        label: '烘前试样重(g)',
        prop: 'coarseAggregateBefore',
        align: 'right'
      },
      {
        label: '烘后试样重(g)',
        prop: 'coarseAggregateAfter',
        align: 'right'
      },
      {
        label: '含水率(%)',
        prop: 'coarseAggregateWater',
        align: 'right'
      }
    ]
  },
  {
    label: '试验人',
    prop: 'testPerson'
  },
  {
    label: '备注',
    prop: ''
  }
]


export const receiptTableColumn = [
  {
    label: '任务单号',
    prop: 'frwno',
  },
  {
    label: '小票编号',
    prop: 'itemorderno',
  },
  {
    label: '车号',
    prop: 'carnumber',
  },
  {
    label: '车牌号',
    prop: 'carLicenseNo',
  },
  {
    label: '司机',
    prop: 'drivername',
  },
  {
    label: '司机联系方式',
    prop: 'tel',
  },
  {
    label: '拌台',
    prop: 'mixtable',
  },
  {
    label: '发货方量',
    prop: 'fhquantity',
  },
  {
    label: '发货员',
    prop: 'fhy',
  },
  {
    label: '操作人',
    prop: 'czy'
  },
  
]
export const taskTableColumn = [
  {
    label: '运单编号	',
    prop: 'waybillCode',
  },
  {
    label: '车号',
    prop: 'vehicleNum',
  },
  {
    label: '车牌号',
    prop: 'vehiclePlate',
  },
  {
    label: '司机',
    prop: 'driverName',
  },
  {
    label: '司机联系方式',
    prop: 'driverTel',
  },
  {
    label: '发货方量',
    prop: 'transportQuantity',
  },
  {
    label: '生产厂家',
    prop: 'factory',
  },
  {
    label: '材料名称',
    prop: 'projectName'
  },
  {
    label: '材料规格	',
    prop: 'projectSpecs'
  }
]
