<template>
    <div class="content-box">
        <div class="flex-box flex-column content">
            <div class="search-box flex-box">
                <el-form class="flex-item" ref="searchForm" :inline="true" :model="searchForm">
                    <el-form-item label="任务单号：">
                        <el-input v-model="searchForm.frwno" clearable
                        placeholder="请输入" 
                        style="width: 180px" 
                        />
                    </el-form-item>
                    <el-form-item label="配合比：">
                        <el-input v-model="searchForm.fphbNo" clearable
                        placeholder="请输入" 
                        style="width: 180px" 
                        />
                    </el-form-item>
                    <!-- <el-form-item label="工程名称：">
                        <el-input v-model="searchForm.projectNameNew" clearable
                        placeholder="请输入" 
                        style="width: 180px" 
                        />
                    </el-form-item>
                    <el-form-item label="施工单位：">
                        <el-input v-model="searchForm.buildNameNew" clearable
                        placeholder="请输入" 
                        style="width: 180px" 
                        />
                    </el-form-item>
                    <el-form-item label="浇筑部位：">
                        <el-input v-model="searchForm.jzbw" clearable
                        placeholder="请输入" 
                        style="width: 180px" 
                        />
                    </el-form-item> -->
                    <!-- <el-form-item label="强度等级：">
                    <el-select
                        v-model="searchForm.qddjList" 
                        filterable clearable 
                        multiple
                        placeholder="请选择等级强度" 
                        style="width: 300px">
                        
                        <el-option
                        v-for="item in ddList"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                        </el-option>
                    </el-select>
                    </el-form-item> -->
                    <!-- <el-form-item label="骨料粒径：">
                        <el-input v-model="searchForm.cglSpec" clearable
                        placeholder="请输入" 
                        style="width: 180px" 
                        />
                    </el-form-item> -->
                    <!-- <el-form-item label="合同编号：">
                        <el-input v-model="searchForm.htbh" clearable
                        placeholder="请输入" 
                        style="width: 180px" 
                        />
                    </el-form-item>
                    <el-form-item label="计划方量最小值：">
                        <el-input v-model="searchForm.planQuantityMix" clearable
                        placeholder="请输入" 
                        style="width: 180px" 
                        />
                    </el-form-item>
                    <el-form-item label="计划方量最大值：">
                        <el-input v-model="searchForm.planQuantityMax" clearable
                        placeholder="请输入" 
                        style="width: 180px" 
                        />
                    </el-form-item>
                    <el-form-item label="生产方量最小值：">
                        <el-input v-model="searchForm.scQuantityMix" clearable
                        placeholder="请输入" 
                        style="width: 180px" 
                        />
                    </el-form-item>
                    <el-form-item label="生产方量最大值：">
                        <el-input v-model="searchForm.scQuantityMax" clearable
                        placeholder="请输入" 
                        style="width: 180px" 
                        />
                    </el-form-item> -->
                    <el-form-item label="关键字：">
                        <el-input v-model="searchForm.searchkey" clearable
                        placeholder="请输入工程名称、施工地点、砼品种、合同单位" 
                        style="width: 380px" 
                        />
                    </el-form-item>
                    <el-form-item label="计划日期" prop="plantimeDate">
            
                        <el-date-picker type="daterange" 
                        v-model="searchForm.plantimeDate" 
                        start-placeholder="开始日期" end-placeholder="结束日期" 
                        format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                        :clearable="true" style="width: 360px"
                        >
                        </el-date-picker>
                    </el-form-item>
                    <!-- <el-form-item label="合同名称：">
                        <el-input v-model="searchForm.contractNameNew" clearable
                        placeholder="请输入" 
                        style="width: 180px" 
                        />
                    </el-form-item>
                    <el-form-item label="施工单位：">
                        <el-input v-model="searchForm.buildNameNew" clearable
                        placeholder="请输入" 
                        style="width: 180px" 
                        />
                    </el-form-item> -->

                    <el-form-item label="状态：">
                        <el-checkbox-group v-model="searchForm.fztList">
                            <el-checkbox v-for="item in fztStatusOpts" :key="item" :label="item"></el-checkbox>
                        </el-checkbox-group>
                    </el-form-item>
                    
                    <el-form-item>
                        <el-button type="primary" @click="handleFilter">搜索</el-button>
                        <el-button type="text" icon="el-icon-refresh-right" @click="resetForm()">重置</el-button>
                    </el-form-item>
                </el-form>
                <!-- <el-button type="primary" @click="batchEditClick">修改任务单</el-button> -->
                <el-button type="primary" @click="newBatchEditClick">批量修改</el-button>
                <el-button type="primary" @click="batchCreatedInformation">批量生成生产资料</el-button>
                <el-button type="primary" @click="batchUploadClick">批量上传</el-button>
                <el-button type="primary" @click="batchPrintClick">批量打印</el-button>
                <el-button type="primary" @click="exportPrintClick">导出</el-button>
            </div>
        
            <div class="flex-item overHide">
                <div class="scroll-div">
                <el-table
                    :data="tableData"                
                    :key="1"
                    @selection-change="taskSelectionChange"
                    :row-style="cellStyle"
                    >
                    <el-table-column type="selection" align="center" :selectable="checkSelectable"></el-table-column>
                    <template v-for="(item, index) in tableColumn">
                        <el-table-column v-if="item.prop === 'rwdextraInfo.fphbNo' && !$store.state.loginStore.tieBiaoFlag" 
                        :key="index"
                        :prop="item.prop" 
                        :label="item.label" 
                        :fixed="item.fixed" 
                        :width="item.width || ''"
                        :formatter="item.formatter"
                        :show-overflow-tooltip="true"
                        align="center" 
                        >
                            <template slot-scope="scope">
                                <span v-if="scope.row.rwdextraInfo?.fphbNo && !$store.state.loginStore.tieBiaoFlag" style="cursor:pointer; color: #3369FF;" @click="gotoPHBDetail(scope.row)">{{ scope.row.rwdextraInfo?.fphbNo }}</span>
                                <!-- <span v-else style="cursor:pointer; color: #3369FF;" @click="setPHB(scope.row)">设置配比</span> -->
                                 <span v-else>--</span>
                            </template>
                        </el-table-column>
                        <el-table-column v-else-if="item.prop === 'phbshow' && $store.state.loginStore.tieBiaoFlag" 
                        :key="index"
                        :prop="item.prop" 
                        :label="item.label" 
                        :fixed="item.fixed" 
                        :width="item.width || ''"
                        :formatter="item.formatter"
                        :show-overflow-tooltip="true"
                        align="center" 
                        >
                            <template slot-scope="scope">
                                <span v-if="scope.row.phbshow && $store.state.loginStore.tieBiaoFlag" style="cursor:pointer; color: #3369FF;" @click="gotoPHBDetail(scope.row)">{{ scope.row.phbshow }}</span>
                                <!-- <span v-else style="cursor:pointer; color: #3369FF;" @click="setPHB(scope.row)">设置配比</span> -->
                                 <span v-else>--</span>
                            </template>
                        </el-table-column>
                        <el-table-column v-else-if="item.prop === 'consignId'" 
                            :key="index"
                            :prop="item.prop" 
                            :label="item.label" 
                            :fixed="item.fixed" 
                            :width="item.width || ''"
                            :formatter="item.formatter"
                            :show-overflow-tooltip="true"
                            align="center" 
                        >
                            <template slot-scope="scope">
                                <span v-if="parseFloat(scope.row.scquantity || '0') > 0" style="cursor:pointer; color: #3369FF;" @click="selectExperiment(scope.row)">查看</span>
                            </template>
                        </el-table-column>
                        <el-table-column
                            v-else-if="item.prop === 'fzt'"
                            :key="index"
                            :prop="item.prop" 
                            :label="item.label" 
                            :fixed="item.fixed" 
                            :width="item.width || ''"
                            :formatter="item.formatter"
                            :show-overflow-tooltip="true"
                            align="center" 
                        >
                            <template slot-scope="scope">
                                <el-row class="cell-state">
                                    <label v-if="scope.row.fzt == '新任务单' || scope.row.fzt == '等待配比'" class="rda-task-state dqr">{{ scope.row.fzt }}</label>
                                    <label v-if="scope.row.fzt == '等待生产' || scope.row.fzt == '正在生产'" class="rda-task-state ddd">{{ scope.row.fzt }}</label>
                                    <label v-if="scope.row.fzt == '生产完成' || scope.row.fzt == '取消生产'" class="rda-task-state yqx">{{ scope.row.fzt }}</label>
                                    <label v-if="scope.row.fzt == '暂停生产'" class="rda-task-state yqx">{{ scope.row.fzt }}</label>
                                    <label v-if="scope.row.fzt == '作废'" class="rda-task-state yqx">{{ scope.row.fzt }}</label>
                                    <!-- <label v-else class="rda-task-state yqx">{{ scope.row.fzt }}</label> -->
                                </el-row>
                            </template>
                        </el-table-column>
                        <el-table-column
                            v-else
                            :key="index"
                            :prop="item.prop" 
                            :label="item.label" 
                            :fixed="item.fixed" 
                            :width="item.width || ''"
                            :formatter="item.formatter"
                            :show-overflow-tooltip="true"
                            align="center" 
                        />
                    </template>
                    
                    
                    <el-table-column fixed="right" width="280" label="操作" align="center" key="handle" :resizable="false">
                        <template slot-scope="scope">
                            <el-button type="text" size="small" @click="taskEditClick(scope.row)">增补资料</el-button>
                            <el-button v-if="$store.state.loginStore.tieBiaoFlag" type="text" size="small" @click="bindMaterClick(scope.row)">绑定原材料</el-button>
                            <el-button type="text" size="small" @click="createdInformation(scope.row)">生成资料</el-button>
                            <el-button v-if="!(scope.row.fzt == '作废' || scope.row.fzt == '取消生产')" type="text" size="small" @click="printSigle(scope.row)">打印</el-button>
                            <el-button type="text" size="small" @click="gotoDetail(scope.row)">详情</el-button>
                        </template>
                    </el-table-column>
                </el-table>
                </div>
            </div>
            <div class="mt16 mb4">
                <Pagination
                :total="total" 
                :pageNum="pageObj.pageNum" 
                :pageSize="pageObj.pageSize" 
                @getData="initData" 
                />
            </div>
        </div>

        <!-- <DialogForm
            :formContent="formContent"
            ref="dialogForm"
            :hiddenImg="true"
            dialogTitle="批量修改"
            @saveTarget="saveTargetD"
        >
        </DialogForm> -->

        <div v-if="newBatchShowDialog">
            <el-dialog 
                width="600px" 
                title="批量修改" 
                :visible.sync="newBatchShowDialog" 
                @closed="newBatchShowDialog = false"
            >
                <div style="padding: 0px 16px;">
                    <el-form :model="newBatchForm" label-width="100px">
                        <!-- 工程名称和施工单位输入框 -->
                        <el-form-item label="工程名称" prop="projectName">
                            <el-input v-model="newBatchForm.projectName" placeholder="请输入工程名称" clearable></el-input>
                        </el-form-item>
                        <el-form-item label="施工单位" prop="customerName">
                            <el-input v-model="newBatchForm.customerName" placeholder="请输入施工单位" clearable></el-input>
                        </el-form-item>
                    </el-form>

                    <div class="flex-row-start" style="justify-content: flex-end; margin-top: 30px;">
                        <el-button type="primary" @click="newBatchFormSave">保存</el-button>
                        <el-button @click="newBatchShowDialog = false">取消</el-button>
                    </div>
                </div>
            </el-dialog>
        </div>

        <UpdateBatchPrintDetail 
            v-if="showUpdateBatchDialog" 
            ref="updateBatchPrintDetail" 
            :taskSelects="taskSelects" 
            @close="updateBatchPrintDetailClose"
            @saveSuccessed="updateBatchSaveSuccessed"
        />

        <PrintDrawer ref="printDrawer" :show="printDrawer" @close="printDrawerClose"  @sure="getPrintType" @change="printTypeChange" />

        <div v-if="experimentNoListDialog">
            <el-dialog 
                width="800px" 
                title="" 
                :visible.sync="experimentNoListDialog" 
                @closed="experimentNoListDialog = false"
            >
                <div class="flex-row" style="font-size: 16px; font-weight: bold;">原材料台账</div>
                <div style="padding: 0px 16px;">
                    <template>
                        <el-row class="flex-row">
                            <div style="flex: 1">
                                <div class="flex-row exp-dialog-title">水泥</div>
                                <div v-if="experimentNoList.snInfoList &&  experimentNoList.snInfoList.length > 0" class="flex-row exp-dialog-cell">
                                    <div class="flex-row exp-dialog-item" v-for="(item, index) in experimentNoList.snInfoList" :key="index + '_sn'">
                                        <span>{{ item.ypbh || item.sytzno }}</span>
                                        <el-button type="text" size="mini" @click="gotoMaterialBoard(item.sytzbh, item.sytzno)">查看</el-button>
                                    </div>
                                </div>
                            </div>
                            <div style="flex: 1">
                                <div class="flex-row exp-dialog-title">粗骨料</div>
                                <div v-if="experimentNoList.cglInfoList &&  experimentNoList.cglInfoList.length > 0" class="flex-row exp-dialog-cell">
                                    <div class="flex-row exp-dialog-item" v-for="(item, index) in experimentNoList.cglInfoList" :key="index + '_cgl'">
                                        <span>{{ item.ypbh || item.sytzno }}</span>
                                        <el-button type="text" size="mini" @click="gotoMaterialBoard(item.sytzbh, item.sytzno)">查看</el-button>
                                    </div>
                                </div>
                            </div>
                        </el-row>
                        <el-row class="flex-row">
                            <div style="flex: 1">
                                <div class="flex-row exp-dialog-title">细骨料</div>
                                <div v-if="experimentNoList.xglInfoList &&  experimentNoList.xglInfoList.length > 0" class="flex-row exp-dialog-cell">
                                    <div class="flex-row exp-dialog-item" v-for="(item, index) in experimentNoList.xglInfoList" :key="index + '_xgl'">
                                        <span>{{ item.ypbh || item.sytzno }}</span>
                                        <el-button type="text" size="mini" @click="gotoMaterialBoard(item.sytzbh, item.sytzno)">查看</el-button>
                                    </div>
                                </div>
                            </div>
                            <div style="flex: 1">
                                <div class="flex-row exp-dialog-title">粉煤灰</div>
                                <div v-if="experimentNoList.fmhInfoList &&  experimentNoList.fmhInfoList.length > 0" class="flex-row exp-dialog-cell">
                                    <div class="flex-row exp-dialog-item" v-for="(item, index) in experimentNoList.fmhInfoList" :key="index + '_fmh'">
                                        <span>{{ item.ypbh || item.sytzno }}</span>
                                        <el-button type="text" size="mini" @click="gotoMaterialBoard(item.sytzbh, item.sytzno)">查看</el-button>
                                    </div>
                                </div>
                            </div>
                        </el-row>
                        <el-row class="flex-row">
                            <div style="flex: 1">
                                <div class="flex-row exp-dialog-title">矿渣粉</div>
                                <div v-if="experimentNoList.kzfInfoList &&  experimentNoList.kzfInfoList.length > 0" class="flex-row exp-dialog-cell">
                                    <div class="flex-row exp-dialog-item" v-for="(item, index) in experimentNoList.kzfInfoList" :key="index + '_kzf'">
                                        <span>{{ item.ypbh || item.sytzno }}</span>
                                        <el-button type="text" size="mini" @click="gotoMaterialBoard(item.sytzbh, item.sytzno)">查看</el-button>
                                    </div>
                                </div>
                            </div>
                            <div style="flex: 1">
                                <div class="flex-row exp-dialog-title">外加剂</div>
                                <div v-if="experimentNoList.wjjInfoList &&  experimentNoList.wjjInfoList.length > 0" class="flex-row exp-dialog-cell">
                                    <div class="flex-row exp-dialog-item" v-for="(item, index) in experimentNoList.wjjInfoList" :key="index + '_wjj'">
                                        <span>{{ item.ypbh || item.sytzno }}</span>
                                        <el-button type="text" size="mini" @click="gotoMaterialBoard(item.sytzbh, item.sytzno)">查看</el-button>
                                    </div>
                                </div>
                            </div>
                        </el-row>
                    </template>
                </div>
                <div class="flex-row" style="margin-top: 24px; font-size: 16px; font-weight: bold;">混凝土台账</div>
                <div style="padding: 0px 16px;">
                    <template>
                        <div class="flex-row exp-dialog-title" style="">抗压台账</div>
                        <div v-if="experimentNoList.kyInfoList &&  experimentNoList.kyInfoList.length > 0" class="flex-row exp-dialog-cell">
                            <div class="flex-row exp-dialog-item" v-for="(kyItem, index) in experimentNoList.kyInfoList" :key="index + '_ky'">
                                <div style="width: 180px;">
                                    <span>{{ kyItem.ypbh || kyItem.sytzno }}</span>
                                    <el-button type="text" size="mini" @click="gotoExperimentBoard(kyItem.sytzbh, kyItem.sytzno)">查看</el-button>
                                </div>
                            </div>
                        </div>
                    </template>
                    <template>
                        <div class="flex-row exp-dialog-title">抗渗台账</div>
                        <div v-if="experimentNoList.ksInfoList &&  experimentNoList.ksInfoList.length > 0" class="flex-row exp-dialog-cell">
                            <div class="flex-row exp-dialog-item"  v-for="(ksItem, index) in experimentNoList.ksInfoList" :key="index + '_ks'">
                                <span>{{ ksItem.ypbh || ksItem.sytzno }}</span>
                                <el-button type="text" size="mini" @click="gotoExperimentBoard(ksItem.sytzbh, ksItem.sytzno)">查看</el-button>
                            </div>
                        </div>
                    </template>
                    <template>
                        <div class="flex-row exp-dialog-title">抗折台账</div>
                        <div v-if="experimentNoList.kzInfoList &&  experimentNoList.kzInfoList.length > 0" class="flex-row exp-dialog-cell">
                            <div class="flex-row exp-dialog-item" v-for="(kzItem, index) in experimentNoList.kzInfoList" :key="index + '_kz'">
                                <span>{{ kzItem.ypbh || kzItem.sytzno }}</span>
                                <el-button type="text" size="mini" @click="gotoExperimentBoard(kzItem.sytzbh, kzItem.sytzno)">查看</el-button>
                            </div>
                        </div>
                    </template>
                    <template>
                        <div class="flex-row exp-dialog-title">抗氯离子台账</div>
                        <div v-if="experimentNoList.kllzInfoList &&  experimentNoList.kllzInfoList.length > 0" class="flex-row exp-dialog-cell">
                            <div class="flex-row exp-dialog-item" v-for="(kllzItem, index) in experimentNoList.kllzInfoList" :key="index + '_kllz'">
                                <span>{{ kllzItem.ypbh || kllzItem.sytzno }}</span>
                                <el-button type="text" size="mini" @click="gotoExperimentBoard(kllzItem.sytzbh, kllzItem.sytzno)">查看</el-button>
                            </div>
                        </div>
                    </template>
                </div>
            </el-dialog>
        </div>
        
        <div v-if="bindVisible">
            <el-dialog
                width="1780px" 
                title="绑定原材料"
                :before-close="handleMaterClose"
                :visible.sync="bindVisible">
                <el-table :data="gridData" :height="430" class="table-class">
                    <el-table-column property="cllx" label="材料类型" width="80">
                    </el-table-column>
                    <el-table-column property="clmc" label="材料名称" width="200">

                        <template slot-scope="scope">
                            <el-select v-model="scope.row.clmc" placeholder="请选择" @change="changeClmcSelect(scope, scope.$index+1)">
                            <el-option
                            v-for="item in allOptionsList[scope.$index].clmcOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value">
                            </el-option>
                        </el-select>
                        </template>
                    </el-table-column>
                    <el-table-column property="clgg" label="材料规格" width="200">

                        <template slot-scope="scope">
                            <el-select v-model="scope.row.clgg" placeholder="请选择" @change="changeClggSelect(scope)">
                            <el-option
                            v-for="item in allOptionsList[scope.$index].clggOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value">
                            </el-option>
                        </el-select>
                        </template>
                    </el-table-column>
                    <el-table-column property="bgbh" label="报告编号" width="200">

                        <template slot-scope="scope">
                            <el-input v-model="scope.row.bgbh" placeholder="请输入"></el-input>
                        </template>
                    </el-table-column>

                    <el-table-column property="gys" label="供应商" width="200">

                        
                        <template slot-scope="scope">
                            <el-select v-model="scope.row.gys" placeholder="请选择"  @change="changGysSelect(scope)">
                            <el-option
                            v-for="item in allOptionsList[scope.$index].gysOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value">
                            </el-option>
                        </el-select>
                        </template>
                    </el-table-column>
                    <el-table-column property="gysjc" label="供应商简称" width="200">
                        <template slot-scope="scope">
                            
                            <el-input :disabled="true" v-model="scope.row.gysjc" placeholder="请输入"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column property="clcj" label="材料厂家" width="200">

                        <template slot-scope="scope">
                            <el-select v-model="scope.row.clcj" placeholder="请选择" @change="changClcjSelect(scope)">
                            <el-option
                            v-for="item in allOptionsList[scope.$index].clcjOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value">
                            </el-option>
                        </el-select>
                        </template>
                    </el-table-column>
                    <el-table-column property="cjjc" label="厂家简称" width="200">

                        <template slot-scope="scope">
                            <el-input :disabled="true" v-model="scope.row.cjjc" placeholder="请输入"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column property="zbsbh" label="质保书编号" width="200">
                        <template slot-scope="scope">
                            <el-input v-model="scope.row.zbsbh" placeholder="请输入"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width="60">
                        <template slot-scope="scope">
                        <el-button @click="handleClick(scope.$index)" style="color:red" type="text">清空</el-button>
                    </template>
                    </el-table-column>
                </el-table>
                <!-- 悬浮在底部的div -->
                <div class="drawer-footer">
                    <!-- 这里放底部内容，例如按钮 -->
                    <el-button style="width: 80px;" @click="closeDialog">取消</el-button>
                    <el-button style="width: 80px; margin-left: 30px;" type="primary" @click="submitBind">提交</el-button>
                </div>
            </el-dialog>
        </div>
    </div>
</template>  

<script>
import Pagination from "@/components/Pagination/index.vue";
import DialogForm from "@/components/dialogForm.vue";
import PrintDrawer from "./components/printDrawer.vue";
import moment from "moment";
import UpdateBatchPrintDetail from "./components/updateBatchPrintDetail.vue";

export default {
    components: {
        Pagination,
        DialogForm,
        PrintDrawer,
        UpdateBatchPrintDetail,
    },
    data() {
        return {
            selectedId:'',
            bindVisible:false,
            // 所有的选项列表
            allOptionsList: [
                // 水泥选项
                {
                    clmcOptions:[],
                    clggOptions:[],
                    clcjOptions:[],
                    gysOptions:[],
                },
                // 粉煤灰选项
                {
                    clmcOptions:[],
                    clggOptions:[],
                    clcjOptions:[],
                    gysOptions:[],
                },
                // 矿渣粉选项
                {
                    clmcOptions:[],
                    clggOptions:[],
                    clcjOptions:[],
                    gysOptions:[],
                },
                // 粗骨料选项
                {
                    clmcOptions:[],
                    clggOptions:[],
                    clcjOptions:[],
                    gysOptions:[],
                },
                // 细骨料选项
                {
                    clmcOptions:[],
                    clggOptions:[],
                    clcjOptions:[],
                    gysOptions:[],
                },
                // 外加剂选项
                {
                    clmcOptions:[],
                    clggOptions:[],
                    clcjOptions:[],
                    gysOptions:[],
                },
            ],
             gridData: [{
            // 材料类型
            cllx:'水泥',
                // 材料名称
                clmc:'',
                // 材料规格
                clgg:'',
                // 报告编号
                bgbh:'',
                // 材料厂家
                clcj:'',
                // 厂家简称
                cjjc:'',
                // 质保书编号
                zbsbh:'',
                // 供应商
                gys:'',
                // 供应商简称
                gysjc:'',
            }, {
            // 材料类型
            cllx:'粉煤灰',
                // 材料名称
                clmc:'',
                // 材料规格
                clgg:'',
                // 报告编号
                bgbh:'',
                // 材料厂家
                clcj:'',
                // 厂家简称
                cjjc:'',
                // 质保书编号
                zbsbh:'',
                // 供应商
                gys:'',
                // 供应商简称
                gysjc:'',
            }, {
            // 材料类型
            cllx:'矿渣粉',
                // 材料名称
                clmc:'',
                // 材料规格
                clgg:'',
                // 报告编号
                bgbh:'',
                // 材料厂家
                clcj:'',
                // 厂家简称
                cjjc:'',
                // 质保书编号
                zbsbh:'',
                // 供应商
                gys:'',
                // 供应商简称
                gysjc:'',
            }, {
            // 材料类型
            cllx:'粗骨料',
                // 材料名称
                clmc:'',
                // 材料规格
                clgg:'',
                // 报告编号
                bgbh:'',
                // 材料厂家
                clcj:'',
                // 厂家简称
                cjjc:'',
                // 质保书编号
                zbsbh:'',
                // 供应商
                gys:'',
                // 供应商简称
                gysjc:'',
            },{
            // 材料类型
            cllx:'细骨料',
                // 材料名称
                clmc:'',
                // 材料规格
                clgg:'',
                // 报告编号
                bgbh:'',
                // 材料厂家
                clcj:'',
                // 厂家简称
                cjjc:'',
                // 质保书编号
                zbsbh:'',
                // 供应商
                gys:'',
                // 供应商简称
                gysjc:'',
            },{
            // 材料类型
            cllx:'外加剂',
                // 材料名称
                clmc:'',
                // 材料规格
                clgg:'',
                // 报告编号
                bgbh:'',
                // 材料厂家
                clcj:'',
                // 厂家简称
                cjjc:'',
                // 质保书编号
                zbsbh:'',
                // 供应商
                gys:'',
                // 供应商简称
                gysjc:'',
            }],
            // 上面是铁标数据

            ddList: [{
                label: "C10",
                value: "C10"
            },{
                label: "C15",
                value: "C15"
            },{
                label: "C20",
                value: "C20"
            },{
                label: "C25",
                value: "C25"
            },{
                label: "C30",
                value: "C30"
            },{
                label: "C35",
                value: "C35"
            },{
                label: "C40",
                value: "C40"
            },{
                label: "C45",
                value: "C45"
            },{
                label: "C50",
                value: "C50"
            },{
                label: "C55",
                value: "C55"
            }],
            searchForm: {
                fztList: [],
                plantimeDate: [],
                frwno: '',
                fphbNo:'',
                searchkey:'',
                // projectNameNew:'',
                // buildNameNew:'',
                // jzbw:'',
                // qddjList: [],
                // cglSpec:'',
                // htbh:'',
                // planQuantityMix:'',
                // planQuantityMax:'',
                // scQuantityMix:'',
                // scQuantityMax:''

            },
            pageObj: {
                pageNum: 1, // 页数
                pageSize: 10, // 条数
            },
            total: 1,
            tableData: [],
            taskSelects: [],
            tableColumn: !this.$store.state.loginStore.tieBiaoFlag ? [
                {
                    label: '任务单号',
                    prop: 'rwdextraInfo.frwno',
                    width: '120'
                },
                {
                    label: '配比编号',
                    prop: 'rwdextraInfo.fphbNo',
                    width: '120'
                },
                {
                    label: '协会编号',
                    prop: 'produceIndexId',
                    width: '120'
                },
                {
                    label: '委托编号',
                    prop: 'consignId',
                    width: '120'
                },
                {
                    label: '工程名称',
                    prop: 'rwdextraInfo.fgcmc',
                    width: '240'
                },
                {
                    label: '施工部位',
                    prop: 'rwdextraInfo.fjzbw',
                },
                {
                    label: '浇筑方式',
                    prop: 'rwdextraInfo.fjzfs',
                },
                {
                    label: '坍落度',
                    prop: 'rwdextraInfo.ftld',
                },
                {
                    label: '合同编号',
                    prop: 'rwdextraInfo.fhtbh',
                },
                {
                    label: '砼品种',
                    prop: 'rwdextraInfo.ftpz'
                },
                {
                    label: '方量(方)',
                    prop: 'rwdextraInfo.fjhsl',
                    formatter: function(row) {
                        return (row.fhquantity || '0') + '/' + (row.rwdextraInfo?.fjhsl || '0')
                    }
                },
                {
                    label: '发货车数',
                    prop: 'rwdextraInfo.fljcs',
                },
                {
                    label: '计划日期',
                    prop: 'rwdextraInfo.fjhrq',
                    width: '150',
                    formatter: function(row) {
                        if (row.rwdextraInfo?.fjhrq) {
                            return moment(row.rwdextraInfo?.fjhrq).format('YYYY-MM-DD HH:mm');
                        }
                        return '--';
                    }
                },
                {
                    label: '生产状态',
                    prop: 'fzt',
                    width: '80'
                },
                {
                    label: '打印状态',
                    prop: 'printStatus',
                    formatter(row, column) {
                        return row.printStatus == 1 ? '已打印' : '未打印';
                    },
                    width: '100'
                }
            ] : [
                {
                    label: '任务单号',
                    prop: 'rwdextraInfo.frwno',
                    width: '120'
                },
                {
                    label: '配比编号',
                    prop: 'phbshow',
                    width: '120'
                },
                {
                    label: '协会编号',
                    prop: 'produceIndexId',
                    width: '120'
                },
                {
                    label: '委托编号',
                    prop: 'consignId',
                    width: '120'
                },
                {
                    label: '工程名称',
                    prop: 'rwdextraInfo.fgcmc',
                    width: '240'
                },
                {
                    label: '施工部位',
                    prop: 'rwdextraInfo.fjzbw',
                },
                {
                    label: '浇筑方式',
                    prop: 'rwdextraInfo.fjzfs',
                },
                {
                    label: '坍落度',
                    prop: 'rwdextraInfo.ftld',
                },
                {
                    label: '合同编号',
                    prop: 'rwdextraInfo.fhtbh',
                },
                {
                    label: '砼品种',
                    prop: 'rwdextraInfo.ftpz'
                },
                {
                    label: '方量(方)',
                    prop: 'rwdextraInfo.fjhsl',
                    formatter: function(row) {
                        return (row.fhquantity || '0') + '/' + (row.rwdextraInfo?.fjhsl || '0')
                    }
                },
                {
                    label: '发货车数',
                    prop: 'rwdextraInfo.fljcs',
                },
                {
                    label: '计划日期',
                    prop: 'rwdextraInfo.fjhrq',
                    width: '150',
                    formatter: function(row) {
                        if (row.rwdextraInfo?.fjhrq) {
                            return moment(row.rwdextraInfo?.fjhrq).format('YYYY-MM-DD HH:mm');
                        }
                        return '--';
                    }
                },
                {
                    label: '生产状态',
                    prop: 'fzt',
                    width: '80'
                },
                {
                    label: '打印状态',
                    prop: 'printStatus',
                    formatter(row, column) {
                        return row.printStatus == 1 ? '已打印' : '未打印';
                    },
                    width: '100'
                }
            ],
            fztStatusOpts: [
                '新任务单', '等待配比', '等待生产', '正在生产', '暂停生产', '生产完成', '取消生产', '作废'
            ],
            printDrawer: false,

            // // 批量修改用到的筛选信息
            // customerList: [],
            // engineeringList: [],

            showUpdateBatchDialog: false,
            experimentNoListDialog: false,
            experimentNoList: {
                kyInfoList: [],
                ksInfoList: [],
                kzInfoList: []
            },

            newBatchShowDialog: false,
            newBatchForm: {
                projectName: "",
                customerName: ""
            }
        }
    },

    mounted() {
        this.initData();
        this.queryMaterialsSpecConfigAllResp();
        this.changeExperimentType(1)
        this.changeExperimentType(2)
        this.changeExperimentType(3)
        this.changeExperimentType(4)
        this.changeExperimentType(5)
        this.changeExperimentType(6)

    },

    methods: {
        closeDialog(){
            this.bindVisible = false;
            console.log('点击取消');
            this.setDefaultValue();

        },

        getFirstStr(str){
                return str;

        },

        // 绑定原材料
        submitBind(){
            const _that = this;
            const snInfo = {
                // 材料名称
                materialName:this.gridData[0].clmc,
                // 材料规格
                materialSpec:this.getFirstStr(this.gridData[0].clgg, 0),
                // 厂家
                factory:this.gridData[0].clcj,
                // 厂家简称
                factoryCalled:this.gridData[0].cjjc,
                // 供应商
                supplyCompanyName:this.getFirstStr(this.gridData[0].gys, 1),
                // 供应商简称
                supplyCompanyCalled:this.gridData[0].gysjc,
                // 报告编号
                reportNo:this.gridData[0].bgbh,
                // 质保书编号
                warrantyNo:this.gridData[0].zbsbh,

            }
            const fmhInfo = {
                // 材料名称
                materialName:this.gridData[1].clmc,
                // 材料规格
                materialSpec:this.getFirstStr(this.gridData[1].clgg, 0),
                // 厂家
                factory:this.gridData[1].clcj,
                // 厂家简称
                factoryCalled:this.gridData[1].cjjc,
                // 供应商
                supplyCompanyName:this.getFirstStr(this.gridData[1].gys, 1),
                // 供应商简称
                supplyCompanyCalled:this.gridData[1].gysjc,
                // 报告编号
                reportNo:this.gridData[1].bgbh,
                // 质保书编号
                warrantyNo:this.gridData[1].zbsbh,

            }
            const kzfInfo = {
                // 材料名称
                materialName:this.gridData[2].clmc,
                // 材料规格
                materialSpec:this.getFirstStr(this.gridData[2].clgg, 0),
                // 厂家
                factory:this.gridData[2].clcj,
                // 厂家简称
                factoryCalled:this.gridData[2].cjjc,
                // 供应商
                supplyCompanyName:this.getFirstStr(this.gridData[2].gys, 1),
                // 供应商简称
                supplyCompanyCalled:this.gridData[2].gysjc,
                // 报告编号
                reportNo:this.gridData[2].bgbh,
                // 质保书编号
                warrantyNo:this.gridData[2].zbsbh,

            }
            const cglInfo = {
                // 材料名称
                materialName:this.gridData[3].clmc,
                // 材料规格
                materialSpec:this.getFirstStr(this.gridData[3].clgg, 0),
                // 厂家
                factory:this.gridData[3].clcj,
                // 厂家简称
                factoryCalled:this.gridData[3].cjjc,
                // 供应商
                supplyCompanyName:this.getFirstStr(this.gridData[3].gys, 1),
                // 供应商简称
                supplyCompanyCalled:this.gridData[3].gysjc,
                // 报告编号
                reportNo:this.gridData[3].bgbh,
                // 质保书编号
                warrantyNo:this.gridData[3].zbsbh,

            }
            const xglInfo = {
                // 材料名称
                materialName:this.gridData[4].clmc,
                // 材料规格
                materialSpec:this.getFirstStr(this.gridData[4].clgg, 0),
                // 厂家
                factory:this.gridData[4].clcj,
                // 厂家简称
                factoryCalled:this.gridData[4].cjjc,
                // 供应商
                supplyCompanyName:this.getFirstStr(this.gridData[4].gys, 1),
                // 供应商简称
                supplyCompanyCalled:this.gridData[4].gysjc,
                // 报告编号
                reportNo:this.gridData[4].bgbh,
                // 质保书编号
                warrantyNo:this.gridData[4].zbsbh,

            }
            const wjjInfo = {
                // 材料名称
                materialName:this.gridData[5].clmc,
                // 材料规格
                materialSpec:this.getFirstStr(this.gridData[5].clgg, 0),
                // 厂家
                factory:this.gridData[5].clcj,
                // 厂家简称
                factoryCalled:this.gridData[5].cjjc,
                // 供应商
                supplyCompanyName:this.getFirstStr(this.gridData[5].gys, 1),
                // 供应商简称
                supplyCompanyCalled:this.gridData[5].gysjc,
                // 报告编号
                reportNo:this.gridData[5].bgbh,
                // 质保书编号
                warrantyNo:this.gridData[5].zbsbh,

            }

            const params = {snInfo:snInfo, fmhInfo:fmhInfo,kzfInfo:kzfInfo,cglInfo:cglInfo,xglInfo:xglInfo,wjjInfo:wjjInfo,frwdh:this.selectedId};
            console.log('提交给后端的数据：', params);
            // if(this.hasEmptyValueInReport(params)){
            //     this.$message('请先完善绑定材料信息');
            //     return 
            // }
      this.$api.setMaterialsInfo(params, this).then(res => {
        console.log('绑定原材料数据：', res);
          if (res.code == 1) {
            _that.bindVisible = false;
            _that.handleFilter();
            _that.setDefaultValue();
            
          }
      })

        },

    // 根据材料类型  1、水泥 2、粉煤灰 3、矿渣粉 4、粗骨料 5、细骨料 6.外加剂  查询材料名称
    changeExperimentType(type) {
      this.$api.querySelectByMaterialsType(`materialsType=${type}`, this).then(res => {
          if (res.code == 1) {
              const specConfigOpts = res.data.list.map(item => {
                // const optionVaule = item.materialsName + '$$$' + (item.sampleJudge ? item.sampleJudge : '' )+ '$$$' + item.materialsType;
                // console.log('optionVaule：', optionVaule);
                  return {
                      label: item.materialsName + (item.sampleJudge ? `【${item.sampleJudge}】` : ''),
                      value: item.materialsName + (item.sampleJudge ? `【${item.sampleJudge}】` : ''),
                  }
              })
              this.$set(this.allOptionsList[type-1], "clmcOptions", specConfigOpts);
              this.$set(this.allOptionsList[type-1], "clggOptions", []);
              this.$set(this.allOptionsList[type-1], "clcjOptions", []);
              this.$set(this.allOptionsList[type-1], "gysOptions", []);
              item.clgg = '';
              item.clcj = '';
              item.cjjc = '';
              item.gys = '';
              item.gysjc = '';
          }
      })
    },

    // 根据材料名称   查询材料规格
    changeClmcSelect(scope, type) {
        let index = scope.$index;
        let item = scope.row;
        let clmcList =  item.clmc.split(/【|】/);
        console.log('选择的材料名称：', clmcList);
        if(clmcList.length >= 3){
            let materialsName = clmcList[0];
            let materialsType = type;
            let sampleJudge = clmcList[1];
            this.$api.queryMaterialsSpecConfigAll({
                materialsType: materialsType,
                materialsName: materialsName,
                sampleJudge: sampleJudge || ""
            }, this).then(res => {
                if (res.code == 1) {
                    const specConfig_2Opts = res.data.list.map(item => {
                        return {
                            label: item.materialsSpec || item.materialsName,
                            value: item.materialsSpec || item.materialsName,
                        }
                    })
              this.$set(this.allOptionsList[index], "clggOptions", specConfig_2Opts);
              this.$set(this.allOptionsList[index], "gysOptions", []);
              this.$set(this.allOptionsList[index], "clcjOptions", []);
              item.clgg = '';
              item.clcj = '';
              item.cjjc = '';
              item.gys = '';
              item.gysjc = '';
                }
            })
        }
      
      
    },
    hasEmptyValueInReport(obj) {
  // 遍历对象的所有值
  for (const key in obj) {
    const value = obj[key];
    
    // 检查基础类型空值
    if (value === null || value === undefined) return true;
    if (typeof value === 'string' && value.trim() === '') return true;
    
    // 如果是嵌套对象（如 snInfo, fmhInfo 等）
    if (typeof value === 'object' && !Array.isArray(value)) {
      // 递归检查嵌套对象
      for (const nestedKey in value) {
        const nestedValue = value[nestedKey];
        
        // 检查嵌套对象的空值
        if (
          nestedValue === null || 
          nestedValue === undefined || 
          (typeof nestedValue === 'string' && nestedValue.trim() === '')
        ) {
          return true;
        }
      }
    }
  }
  
  return false;
},
    // 根据材料规格去查询供应商
    changeClggSelect(scope){
              // 获取供应商信息

        let index = scope.$index;
        let item = scope.row;
        let clmcList =  item.clmc.split(/【|】/);
        console.log('选择的材料名称：', clmcList);
        let clgg =  item.clgg;
        console.log('选择的材料规格：', clgg);
        if(clmcList.length >= 1){
        this.$api.querySupplierCompnayMaterialsList(`materialsName=${clmcList[0]}&materialsSpec=${clgg}`, this).then(res => {
                if (res.code == 1) {
                    const supplierCompanyOpts = res.data.list.map(item => {
                        return {
                        label: item.supplierName,
                        value: item.supplierName + '【' + item.id + '】' + '【' + item.supplierAbbreviation + '】',
                        }
                    })
                    this.$set(this.allOptionsList[index], "gysOptions", supplierCompanyOpts);
                    this.$set(this.allOptionsList[index], "clcjOptions", []); 
              item.gys = '';
              item.gysjc = '';
              item.clcj = '';
              item.cjjc = '';
                }
            })
        }
      
    },
    handleClick(index){
        const obj = this.gridData[index];
        var replaceobj = Object.assign(obj, {
                // 材料名称
                clmc:'',
                // 材料规格
                clgg:'',
                // 报告编号
                bgbh:'',
                // 材料厂家
                clcj:'',
                // 厂家简称
                cjjc:'',
                // 质保书编号
                zbsbh:'',
                // 供应商
                gys:'',
                // 供应商简称
                gysjc:'',
            });
            
            this.$set(this.gridData, index, replaceobj);
        

    },
    // 根据供应商差厂家
    changGysSelect(scope){
              // 获取厂家信息

        let index = scope.$index;
        let item = scope.row;
        let clmcList =  item.clmc.split(/【|】/);
        console.log('选择的材料名称：', clmcList);
        let clgg =  item.clgg;
        console.log('选择的材料规格：', clgg);
        let gysList =  item.gys.split(/【|】/);
        console.log('选择的供应商：', gysList);
        console.log('选择的供应商bool：', gysList.length >= 3);
        console.log('选择的原材料bool：', clmcList.length >= 1);
        console.log('bool：', (clmcList.length >= 1 && gysList.length >= 4));
        
        if(clmcList.length >= 1 && gysList.length >= 4){
            item.gysjc = gysList[3];
            this.$api.querySupplierCompanyMaterialsFactoryList(`supplierCompanyId=${gysList[1]}&materialsName=${clmcList[0]}&materialsSpec=${clgg}`, this).then(res => {
          if (res.code == 1) {
              const factoryOpts = res.data.list.map(item => {
                  return {
                      label: item.manufacturers,
                      value: item.manufacturers + '【' + item.manufacturersCalled + '】',
                  }
              })
            this.$set(this.allOptionsList[index], "clcjOptions", factoryOpts);
              item.clcj = '';
              item.cjjc = '';

          }
      })
        }
      
    },

        // 选择厂家
    changClcjSelect(scope){
        // 获取厂家信息
        let index = scope.$index;
        let item = scope.row;
        let gysList =  item.clcj.split(/【|】/);
        console.log('选择的厂家：', gysList);
        if(gysList.length >= 2){
            item.cjjc = gysList[1];
    
        }
      
    },
    setDefaultValue(){
        this.gridData = [{
            // 材料类型
            cllx:'水泥',
                // 材料名称
                clmc:'',
                // 材料规格
                clgg:'',
                // 报告编号
                bgbh:'',
                // 材料厂家
                clcj:'',
                // 厂家简称
                cjjc:'',
                // 质保书编号
                zbsbh:'',
                // 供应商
                gys:'',
                // 供应商简称
                gysjc:'',
            }, {
            // 材料类型
            cllx:'粉煤灰',
                // 材料名称
                clmc:'',
                // 材料规格
                clgg:'',
                // 报告编号
                bgbh:'',
                // 材料厂家
                clcj:'',
                // 厂家简称
                cjjc:'',
                // 质保书编号
                zbsbh:'',
                // 供应商
                gys:'',
                // 供应商简称
                gysjc:'',
            }, {
            // 材料类型
            cllx:'矿渣粉',
                // 材料名称
                clmc:'',
                // 材料规格
                clgg:'',
                // 报告编号
                bgbh:'',
                // 材料厂家
                clcj:'',
                // 厂家简称
                cjjc:'',
                // 质保书编号
                zbsbh:'',
                // 供应商
                gys:'',
                // 供应商简称
                gysjc:'',
            }, {
            // 材料类型
            cllx:'粗骨料',
                // 材料名称
                clmc:'',
                // 材料规格
                clgg:'',
                // 报告编号
                bgbh:'',
                // 材料厂家
                clcj:'',
                // 厂家简称
                cjjc:'',
                // 质保书编号
                zbsbh:'',
                // 供应商
                gys:'',
                // 供应商简称
                gysjc:'',
            },{
            // 材料类型
            cllx:'细骨料',
                // 材料名称
                clmc:'',
                // 材料规格
                clgg:'',
                // 报告编号
                bgbh:'',
                // 材料厂家
                clcj:'',
                // 厂家简称
                cjjc:'',
                // 质保书编号
                zbsbh:'',
                // 供应商
                gys:'',
                // 供应商简称
                gysjc:'',
            },{
            // 材料类型
            cllx:'外加剂',
                // 材料名称
                clmc:'',
                // 材料规格
                clgg:'',
                // 报告编号
                bgbh:'',
                // 材料厂家
                clcj:'',
                // 厂家简称
                cjjc:'',
                // 质保书编号
                zbsbh:'',
                // 供应商
                gys:'',
                // 供应商简称
                gysjc:'',
            }]

    },

        handleMaterClose(done) {
            const _that = this;
        this.$confirm('确认关闭？')
          .then(_ => {
            done();
            _that.setDefaultValue()
          })
          .catch(_ => {});
      },
        /**
         * 绑定原材料
         * @param row 
         */
        bindMaterClick(row){
            let self = this;
            self.selectedId = row.frwdh;
            let params = {
               frwdh:  row.frwdh
            };
            this.$api.getMaterialsInfo(params, self)
            .then(res => {
                this.bindVisible = true;
                if (res.code == '1') {
                    console.log('铁标绑定原材料接口返回数据：', res.data);
                    this.gridData = [{
            // 材料类型
            cllx:'水泥',
                // 材料名称
                clmc:res.data?.snInfo?.materialName || '',
                // 材料规格
                clgg:res.data?.snInfo?.materialSpec || '',
                // 报告编号
                bgbh:res.data?.snInfo?.reportNo || '',
                // 材料厂家
                clcj:res.data?.snInfo?.factory || '',
                // 厂家简称
                cjjc:res.data?.snInfo?.factoryCalled || '',
                // 质保书编号
                zbsbh:res.data?.snInfo?.warrantyNo || '',
                // 供应商
                gys:res.data?.snInfo?.supplyCompanyName || '',
                // 供应商简称
                gysjc:res.data?.snInfo?.supplyCompanyCalled || '',
            }, {
            // 材料类型
            cllx:'粉煤灰',
                // 材料名称
                clmc:res.data?.fmhInfo?.materialName || '',
                // 材料规格
                clgg:res.data?.fmhInfo?.materialSpec || '',
                // 报告编号
                bgbh:res.data?.fmhInfo?.reportNo || '',
                // 材料厂家
                clcj:res.data?.fmhInfo?.factory || '',
                // 厂家简称
                cjjc:res.data?.fmhInfo?.factoryCalled || '',
                // 质保书编号
                zbsbh:res.data?.fmhInfo?.warrantyNo || '',
                // 供应商
                gys:res.data?.fmhInfo?.supplyCompanyName || '',
                // 供应商简称
                gysjc:res.data?.fmhInfo?.supplyCompanyCalled || '',
            }, {
            // 材料类型
            cllx:'矿渣粉',
                // 材料名称
                clmc:res.data?.kzfInfo?.materialName || '',
                // 材料规格
                clgg:res.data?.kzfInfo?.materialSpec || '',
                // 报告编号
                bgbh:res.data?.kzfInfo?.reportNo || '',
                // 材料厂家
                clcj:res.data?.kzfInfo?.factory || '',
                // 厂家简称
                cjjc:res.data?.kzfInfo?.factoryCalled || '',
                // 质保书编号
                zbsbh:res.data?.kzfInfo?.warrantyNo || '',
                // 供应商
                gys:res.data?.kzfInfo?.supplyCompanyName || '',
                // 供应商简称
                gysjc:res.data?.kzfInfo?.supplyCompanyCalled || '',
            }, {
            // 材料类型
            cllx:'粗骨料',
                // 材料名称
                clmc:res.data?.cglInfo?.materialName || '',
                // 材料规格
                clgg:res.data?.cglInfo?.materialSpec || '',
                // 报告编号
                bgbh:res.data?.cglInfo?.reportNo || '',
                // 材料厂家
                clcj:res.data?.cglInfo?.factory || '',
                // 厂家简称
                cjjc:res.data?.cglInfo?.factoryCalled || '',
                // 质保书编号
                zbsbh:res.data?.cglInfo?.warrantyNo || '',
                // 供应商
                gys:res.data?.cglInfo?.supplyCompanyName || '',
                // 供应商简称
                gysjc:res.data?.cglInfo?.supplyCompanyCalled || '',
            },{
            // 材料类型
            cllx:'细骨料',
                // 材料名称
                clmc:res.data?.xglInfo?.materialName || '',
                // 材料规格
                clgg:res.data?.xglInfo?.materialSpec || '',
                // 报告编号
                bgbh:res.data?.xglInfo?.reportNo || '',
                // 材料厂家
                clcj:res.data?.xglInfo?.factory || '',
                // 厂家简称
                cjjc:res.data?.xglInfo?.factoryCalled || '',
                // 质保书编号
                zbsbh:res.data?.xglInfo?.warrantyNo || '',
                // 供应商
                gys:res.data?.xglInfo?.supplyCompanyName || '',
                // 供应商简称
                gysjc:res.data?.xglInfo?.supplyCompanyCalled || '',
            },{
            // 材料类型
            cllx:'外加剂',
                // 材料名称
                clmc:res.data?.wjjInfo?.materialName || '',
                // 材料规格
                clgg:res.data?.wjjInfo?.materialSpec || '',
                // 报告编号
                bgbh:res.data?.wjjInfo?.reportNo || '',
                // 材料厂家
                clcj:res.data?.wjjInfo?.factory || '',
                // 厂家简称
                cjjc:res.data?.wjjInfo?.factoryCalled || '',
                // 质保书编号
                zbsbh:res.data?.wjjInfo?.warrantyNo || '',
                // 供应商
                gys:res.data?.wjjInfo?.supplyCompanyName || '',
                // 供应商简称
                gysjc:res.data?.wjjInfo?.supplyCompanyCalled || '',
            }];
                }
            }).catch(err => {
                this.bindVisible = true;
            })
            
            // console.log('铁标项目绑定原材料');
        },
        cellStyle({ row, column, rowIndex, columnIndex }) {
            if (row.isAddInformation == 1) {
                return { color: '#FF4500' }
            } else {
                return { color: '#333' }
            }
        },
        // 导出
        exportPrintClick(){

            let params = {
                ...this.searchForm
            };

            if (!this.isEmpty(this.searchForm.plantimeDate)) {
                params.plantimeStart = this.searchForm.plantimeDate[0]
                ? this.searchForm.plantimeDate[0] + " 00:00:00"
                : "";
                params.plantimeEnd = this.searchForm.plantimeDate[1]
                ? this.searchForm.plantimeDate[1] + " 23:59:59"
                : "";
            } else {
                params.plantimeStart = '';
                params.plantimeEnd = '';
            }
            delete params.plantimeDate

            this.$api.exportRwdextraPageList(params, this).then(res => {
                if(res.succ){
                    const url = res.data.list || '';
                    window.open(url, '_blank');
                }else{
                    this.$message.error(res.msg || '导出失败')
                }
            });

        },

        queryMaterialsSpecConfigAllResp() {
          this.$api.queryMaterialsSpecConfigAll({"materialsType":7,"materialsName":"混凝土"}, this).then(res => {
              if (res.code == 1) {
                this.ddList = [];
                  this.ddList = res.data.list.map(item => {
                      return {
                          label: item.materialsSpec || item.materialsName,
                          value: item.materialsSpec || item.materialsName,
                      }
                  })
              }
          })
        },
        checkSelectable(row) {
            // if (parseFloat(row.scquantity || '0') > 0) {
            //     return true;
            // }
            if (!(row.fzt == '作废' || row.fzt == '取消生产')) {
                return true;
            }
            return false;
        },
        handleFilter() {
            console.log(this.searchForm)
            this.initData(1);
        },
        resetForm(){
            this.searchForm = {
                fztList: [],
                plantimeDate: []
            };
            this.initData();
        },
        initData(opageNum, opageSize) {
            if (opageNum) this.pageObj.pageNum = opageNum;
            if (opageSize) this.pageObj.pageSize = opageSize;

            let params = {
                ...this.searchForm
            };

            if (!this.isEmpty(this.searchForm.plantimeDate)) {
                params.plantimeStart = this.searchForm.plantimeDate[0]
                ? this.searchForm.plantimeDate[0] + " 00:00:00"
                : "";
                params.plantimeEnd = this.searchForm.plantimeDate[1]
                ? this.searchForm.plantimeDate[1] + " 23:59:59"
                : "";
            }
            delete params.plantimeDate

            this.$api.getErpRwdextraPageList({
                ...this.pageObj,
                params: params,
            }, this).then(res => {
                if(res.succ){
                    this.tableData = res.data.list;
                    this.total = res.data.total;
                }else{
                    this.$message.error(res.msg || '查询失败')
                }
            });
        },

        taskSelectionChange(val) {
            this.taskSelects = val;
        },

        printDrawerClose() {
            this.printDrawer = false;
        },

        batchEditClick() {
            if (this.taskSelects.length != 1) {
                // 弹出提示
                this.$message({
                    showClose: true,
                    message: "请选择一个需要编辑的任务单",
                    type: "warning",
                });
                return;
            }
            // this.getErpCustomerAllResp();

            // this.$refs.dialogForm.initData();
            this.showUpdateBatchDialog = true;
            this.$nextTick(() => {
                this.$refs.updateBatchPrintDetail.show();
            });
        },
        newBatchEditClick() {
            if (this.taskSelects.length == 0) {
                // 弹出提示
                this.$message({
                    showClose: true,
                    message: "请选择需要编辑的任务单",
                    type: "warning",
                });
                return;
            }
            this.newBatchShowDialog = true;
        },
        taskEditClick(row) {
            this.taskSelects = [row];
            this.showUpdateBatchDialog = true;
            this.$nextTick(() => {
                this.$refs.updateBatchPrintDetail.show();
            });
        },
        updateBatchPrintDetailClose() {
            this.showUpdateBatchDialog = false;
        },

        batchUploadClick() {
            if (this.taskSelects.length == 0) {
                // 弹出提示
                this.$message({
                    showClose: true,
                    message: "请选择任务单",
                    type: "warning",
                });
                return;
            }
            let trwdIds = [];
            trwdIds = this.taskSelects.map(item => item.frwdh);
            this.$api.uploadBatchErpRwdextra({
                frwdhs: trwdIds
            }, this).then((res) => {
                if (res.succ) {
                    this.$message({
                        showClose: true,
                        message: "操作成功",
                        type: "success",
                    });
                    this.initData();
                }
            });
        },

        batchPrintClick() {
            if (this.taskSelects.length == 0) {
                // 弹出提示
                this.$message({
                    showClose: true,
                    message: "请选择任务单",
                    type: "warning",
                });
                return;
            }
            this.printDrawer = true;
        },

        saveTargetD(formData){
            let trwdIds = [];
            trwdIds = this.taskSelects.map(item => item.frwdh);
            formData.frwdhList = trwdIds;
            this.$api.updateBatchErpRwdextraDetail(formData, this).then((res) => {
                if (res.succ) {
                    this.$message({
                        showClose: true,
                        message: "修改成功",
                        type: "success",
                    });
                    this.initData();
                    this.$refs.dialogForm.handleClose();
                }
            });
        },

        printSigle(row) {
            this.taskSelects = [row];
            this.printDrawer = true;
        },

        getPrintType(val) {
            if (val.length == 0) {
            this.$message({
                showClose: true,
                message: "请选择打印数据表",
                type: "warning",
            });
            return;
            }
            this.printBtnClick(val.join(","))
        },

        printTypeChange(types) {
            // ["a2", "a1", "a9"] 开盘鉴定、出厂合格证和调整记录是生产方量大于0，才可以打印。
            // a8 配合比是有配比编号了，就可以打印了
            for (const element of this.taskSelects) {
                let tempTypes = [].concat(types);
                tempTypes.map(item => {
                    if ((item === 'a1') && !(parseFloat(element.fhquantity || '0') > 0)) {
                        this.removeCheckItem("a1", "当前存在任务单暂未生产，暂不支持打印")
                    } else if ((item === 'a1N') && !(parseFloat(element.fhquantity || '0') > 0)) {
                        this.removeCheckItem("a1N", "当前存在任务单暂未生产，暂不支持打印")
                    }
                    else if (item === 'a9' && !(parseFloat(element.fhquantity || '0') > 0)) {
                        this.removeCheckItem("a9", "当前存在任务单暂未生产，暂不支持打印")
                    }
                    else if (item === 'a2' && !(parseFloat(element.fhquantity || '0') > 0)) {
                        this.removeCheckItem("a2", "当前存在任务单暂未生产，暂不支持打印")
                    }
                    else if (item === 'a8' && !element.rwdextraInfo?.fphbNo && !this.$store.state.loginStore.tieBiaoFlag) {
                        this.removeCheckItem("a8", "无配比信息请先下配比")
                    }
                    else if (item === 'a8' && !element.phbshow && this.$store.state.loginStore.tieBiaoFlag) {
                        this.removeCheckItem("a8", "无配比信息请先下配比")
                    }
                });
            }
        },

        removeCheckItem(item, mesg) {
            this.$message({
                showClose: true,
                message: mesg,
                type: "warning",
            });
            this.$refs.printDrawer.removeCheckItem(item);
        },

        printBtnClick(types) {
            if (this.taskSelects.length == 0) {
                // 弹出提示
                this.$message({
                    showClose: true,
                    message: "请选择任务单",
                    type: "warning",
                });
                return;
            }
            
            let routeData = this.$router.resolve({
                path: "/printTaskContent",
                query: {
                    taskIds: this.taskSelects.map(i => i.frwdh).join(','),
                    printType: types
                }
            });
            window.open(routeData.href, '_blank');
        },

        gotoDetail(row) {
            this.$router.push({
                path: '/qualityControl/taskDetail',
                query: {
                    frwdh: row.frwdh
                }
            })
        },

        createdInformation(row, trwdIds) {
            this.$confirm('确认生成生产资料吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                let url = trwdIds ? `frwdhs=${trwdIds.join(',')}` : `frwdh=${row.frwdh}`;
                this.$api.genPrintInfo(url, this).then((res) => {
                    if (res.succ) {
                        this.$message({
                            showClose: true,
                            message: "操作成功",
                        })
                    }else{
                        this.$message.error(res.msg || '查询失败')
                    }
                })
            });
        },

        batchCreatedInformation() {
            if (this.taskSelects.length == 0) {
                // 弹出提示
                this.$message({
                    showClose: true,
                    message: "请选择任务单",
                    type: "warning",
                });
                return;
            }
            let trwdIds = [];
            trwdIds = this.taskSelects.map(item => item.frwdh);
            this.createdInformation(null, trwdIds);
        },

        selectExperiment(row) {
            this.$api.getExperimentByFrwdh(`frwdh=${row.frwdh}`, this).then(res => {
                if (res.succ) {
                    this.experimentNoList = res.data;
                    this.experimentNoListDialog = true;
                }else{
                    this.$message.error(res.msg || '查询失败')
                }
            });
        },
        gotoExperimentBoard(sytzbh, sytzno) {
            this.experimentNoListDialog = false;
            window.location.href = `/mes-web/qualityControl/experimentBoard?id=${sytzbh}&experimentNo=${sytzno}`
            // this.$router.push({
            //     path: '/qualityControl/experimentBoard',
            //     query: {
            //         id: sytzbh,
            //         experimentNo: sytzno
            //     }
            // });
        },

        gotoMaterialBoard(sytzbh, sytzno) {
            this.experimentNoListDialog = false;
            window.location.href = `/mes-web/qualityControl/materialBoard?id=${sytzbh}&experimentNo=${sytzno}`
        },

        // // 选择回调
        // changeCustomerSelect(item) {
        //     this.$refs.dialogForm.setEditFormValue('erpCustomerId', item.id)
        //     this.$refs.dialogForm.setEditFormValue('projectName', '');
        //     this.$refs.dialogForm.setEditFormValue('erpProjectId', '');
        //     this.$refs.dialogForm.setEditFormValue('gcdz', '');
        //     this.$refs.dialogForm.setEditFormValue('jsdwName', '');
        //     this.$refs.dialogForm.setEditFormValue('buildName', '');
        //     this.$refs.dialogForm.setEditFormValue('bjbh', '')
            
        //     this.getEngineeringListAllResp(item.id);
        // },
        // changeEngineeringSelect(item) {
        //     this.$refs.dialogForm.setEditFormValue('projectName', item.projectName);
        //     this.$refs.dialogForm.setEditFormValue('erpProjectId', item.id);
        //     this.$refs.dialogForm.setEditFormValue('gcdz', item.projectAddress);
        //     this.$refs.dialogForm.setEditFormValue('jsdwName', item.jsdw);
        //     this.$refs.dialogForm.setEditFormValue('buildName', item.sgdw);
        //     this.$refs.dialogForm.setEditFormValue('bjbh', item.bjbh)

        // },
        // // 获取所有客户信息
        // getErpCustomerAllResp() {
        //     this.$api.getErpCustomerAll({}, this).then(res => {
        //         if(res.succ){
        //             this.customerList = res.data.list.map(i => {
        //                 return {
        //                     label: i.customerName,
        //                     value: i,
        //                 }
        //             });
        //             this.$set(this.formContent[0], "options", this.customerList);
        //         }else{
        //             this.$message.error(res.msg || '查询失败')
        //         }
        //     })
        // },
        // // 获取所有任务信息
        // getEngineeringListAllResp(erpCustomerId) {
        //     this.$api.getEngineeringListAll({
        //         erpCustomerId: erpCustomerId
        //     }, this).then(res => {
        //         if(res.succ){
        //             this.engineeringList = res.data.list.map(i => {
        //                 return {
        //                     label: i.projectName,
        //                     value: i,
        //                 }
        //             });
        //             this.$set(this.formContent[1], "options", this.engineeringList);
        //         }else{
        //             this.$message.error(res.msg || '查询失败')
        //         }
        //     })
        // },
        
        // 跳转配合比编辑页面
        gotoPHBDetail(row) {
            this.$router.push({
                name: 'addMixProportion',
                query: {
                    fphbNo: this.$store.state.loginStore.tieBiaoFlag ? row.phbshow : row.rwdextraInfo?.fphbNo,
                    timestemp: Date.now()
                }
            })
        },


        isEmpty(val) {
            if (typeof val === "boolean") {
                return false;
            }
            if (typeof val === "number") {
                return false;
            }
            if (val instanceof Array) {
                if (val.length === 0) return true;
            } else if (val instanceof Object) {
                if (JSON.stringify(val) === "{}") return true;
            } else {
                if (
                val === "null" ||
                val == null ||
                val === "undefined" ||
                val === undefined ||
                val === ""
                )
                return true;
                return false;
            }
            return false;
        },

        updateBatchSaveSuccessed() {
            this.initData();
        },

        newBatchFormSave() {
            if (this.isEmpty(this.newBatchForm.projectName) && this.isEmpty(this.newBatchForm.customerName)) {
                this.$message.error('请输入内容');
                return;
            }
            let params = {};
            if (!this.isEmpty(this.newBatchForm.projectName)) {
                params.projectName = this.newBatchForm.projectName;
            }
            if (!this.isEmpty(this.newBatchForm.customerName)) {
                params.customerName = this.newBatchForm.customerName;
            }
            let trwdIds = [];
            trwdIds = this.taskSelects.map(item => item.frwdh);
            this.$api.updateBatchPrintDetailRwdextra({
                frwdhList: trwdIds,
                ...params,
            }, this).then(res => {
                if(res.succ){
                    this.$message.success('保存成功');
                    this.newBatchShowDialog = false;
                    this.initData();
                }else{
                    this.$message.error(res.msg || '保存失败')
                }
            });
        },

        setPHB(row) {
            
        }
    },
}
</script>

<style lang="scss" scoped>
 ::v-deep .el-button{
    padding-left: 13px;
    padding-right: 13px;
  }
  .el-form-item{
    margin-bottom: 8px;
  }
  ::v-deep .el-form--inline{
    .el-form-item{
      margin-right: 24px;
      margin-bottom: 0;
      &:last-child{
        margin: 0;
      }
    }
  }
  ::v-deep .el-table{
    .expanded,.expanded:hover{
      background-color: #FFFBD9;
      
    }
    .expanded + tr{
      background-color: #FFFBD9;
      td{
        background-color: #FFFBD9;
      }
    }
    
    .table-child-box{
      margin: 16px;
      padding: 16px;
      background: #FFFFFF;
    }
  }
  
  .content-box{
    padding: 16px;
  }
  .content{
    width: 100%;
    height: 100%;
    padding: 16px;
    background: #FFFFFF;
    border-radius: 16px;
  }
  
  .search-box{
    padding-bottom: 16px;
    line-height: 40px;
  }
  .cell-state {
    .rda-task-state {
        display: inline-block;
        padding-left: 10px;
        padding-right: 10px;
        height: 18px;
        line-height: 18px;
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #FFFFFF;
        text-align: center;
        background: #1F7AFF;
        border-radius: 2px;
    }
    .dqr {
        background: #DC3290;
    }
    .ddd {
        background: #3369FF;
    }
    .dwc {
        background: #1FAE66;
    }
    .yqx {
        background: #D6D6D6;
    }
    .yjj {
        background: #ADAA00;
    }
    .ywc {
        background: #515157;
    }
  }
  .exp-dialog-title {
    background: #F2F4F5; 
    color: #6A727D; 
    font-size: 14px; 
    font-weight: 600; 
    padding: 10px 8px;
    margin-top: 20px;
  }
  .exp-dialog-item {
    height: 41px;
    justify-content: flex-start;
    word-wrap: break-word;
  }
  .exp-dialog-cell {
    justify-content: flex-start;
    flex-wrap:wrap;
  }
  ::v-deep .el-form-item {
    margin-top: 5px;
  }
  ::v-deep .el-drawer .el-drawer__header {
    text-align: left;
  }
  ::v-deep .el-drawer {
    width: 760px !important;
  }

/* 固定在底部的div */
.drawer-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 16px;
  border-top: 1px solid #ebeef5;
  z-index: 10;
  display: flex;
  justify-content: center;
  box-shadow: 0 -2px 12px rgba(0, 0, 0, 0.1);
}
.table-class{

 ::v-deep .el-button{
    padding-left: 2px;
    padding-right: 2px;
  }
}
</style>