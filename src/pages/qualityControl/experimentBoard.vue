<template>
  <div class="flex-box flex-column">
    <div class="search-box flex-box">
      <el-col :span="20">
        <el-form class="flex-item" ref="searchForm" :inline="true" :model="searchForm" :disabled="loading">
          <el-form-item label="委托编号">
            <el-input v-model="searchForm.noKeyword" clearable placeholder="委托编号"
              style="width: 270px; margin-bottom: 16px;" />
          </el-form-item>
          <el-form-item label="报告编号">
            <el-input v-model="searchForm.reportNo" clearable placeholder="报告编号" style="width: 270px" />
          </el-form-item>
          <el-form-item label="样品编号">
            <el-input v-model="searchForm.sampleNo" clearable placeholder="样品编号" style="width: 270px" />
          </el-form-item>

          <el-form-item label="发生日期" prop="takeEffectDate">

            <el-date-picker type="daterange" v-model="searchForm.takeEffectDate" :picker-options="pickerOptions"
              start-placeholder="开始日期" end-placeholder="结束日期" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
              :clearable="true" :disabled="loading" style="width: 360px">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="配合比编号">
            <el-input v-model="searchForm.phb" clearable placeholder="请输入配合比编号" style="width: 270px" />
          </el-form-item>
          <el-form-item label="检验类型：">
            <el-radio-group v-model="searchForm.checkType" @change="checkTypeSearchFormChange">
              <el-radio :label="1">快检</el-radio>
              <el-radio :label="2">批检</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="试验项目：">
            <el-select v-model="searchForm.entrustExperimentBlur" filterable clearable collapse-tags
              placeholder="请选择试验项目" style="width: 180px">

              <el-option v-for="item in testProjectSearchFormOpts" :key="item.value" :label="item.label"
                :value="item.label">
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="是否需要检测：">
            <el-radio-group v-model="searchForm.entrustState">
              <el-radio :label="0">是</el-radio>
              <el-radio label="">否</el-radio>
            </el-radio-group>
          </el-form-item>

          <!-- <el-form-item label="状态：">
            <el-checkbox-group v-model="searchForm.experimentStatusList">
              <el-checkbox v-for="item in experimentStatusOpts" :key="item.dictValueCode" :label="item.dictValueCode">{{item.dictValueName}}</el-checkbox>
            </el-checkbox-group>
          </el-form-item> -->

          <el-form-item>
            <el-button type="primary" :disabled="loading" @click="handleFilter">搜索</el-button>
            <el-button type="text" icon="el-icon-refresh-right" :disabled="loading" @click="resetForm()">重置</el-button>
          </el-form-item>
        </el-form>
      </el-col>

      <el-col :span="4" class="flex-row" style="justify-content: flex-end; align-items: start;">
        <el-button type="primary" @click="goDetail()">切换视图</el-button>
        <el-button type="primary" @click="showEntrustDialog">新建委托</el-button>
      </el-col>
    </div>
    <div class="flex-item content">
      <div class="flex-box">

        <!-- 混凝土看板左侧列表 -->
        <div>
          <div class="flex-row status-view">
            <span>试验状态：</span>
            <el-checkbox-group v-model="experimentStatusLeft" :min="1" @change="handleExperimentStatusLeftFilter">
              <el-checkbox :label="0">待接收</el-checkbox>
              <el-checkbox :label="1">待取样</el-checkbox>
              <el-checkbox :label="2">试验中</el-checkbox>
            </el-checkbox-group>
          </div>
          <div class="con-lef">
            <div v-if="diffExperimentCount > 0" @click="resetForm()"
              style="margin-top: 8px; cursor: pointer; text-align: center; line-height: 30px; width: 100%; background-color: #D9EAFF; margin-bottom: 8px; border-radius: 4px; color: #4F499B;">
              有<span style="color: #1D67BD; font-weight: bold;">{{ diffExperimentCount }}</span>条新的委托，点击刷新
            </div>
            <ul v-infinite-scroll="loadLeftListMore" :infinite-scroll-disabled="infiniteDisabled">
              <div 
                @click="showDetail(item, index)" 
                :class="{ 'active': activeId == item.id, 'consign-null': (!item.consignId || !item.sampleId) }"
                class="g-card" v-for="(item, index) in dataListUnderway" :key="item.id" style="position: relative;">
                <p class="gc-main">{{ item.experimentNo }} <span class="type-span" style="border-radius: 3px;"
                    :style="{ backgroundColor: item.checkType == '1' ? '#61A480' : '#496BF9', color: '#fff' }">{{
                      item.checkType == '1' ? '快检' : '批检' }}</span> <span class="fr">{{
                      experimentStatusObj[item.experimentStatus] }}</span></p>
                <p class="span-label pb16">
                  <!-- {{experimentTypeNameList[item.experimentType]}}&nbsp;&nbsp;&nbsp; -->
                  <!-- {{item.sampleLevel}}&nbsp;&nbsp;&nbsp; -->
                  <!-- {{ item.matterAbbreviation || item.matterName }} -->
                  <span style="margin-right: 10px;">{{ item.matterSpecs }}</span>
                  <!-- {{ item.materialAbbreviation || item.materialsName }} -->
                  <span style="margin-right: 10px;">{{ item.materialsSpecs }}</span>

                  <span>{{ item.phb }}</span>
                </p>
                <!-- <p>供货商：{{item.entrustPersonName}}</p> -->
                <p>委托时间：{{ item.entrustTime }}</p>
                <p>委托：{{ item.consignId || '--' }} 样品：{{ item.sampleId || '--' }}</p>
                <!-- <p>委托编号：{{ item.consignId || '--'}}</p>
              <p>样品编号：{{ item.sampleId || '--'}}</p> -->
                <p>代表数量：{{ item.behalfNumber }}</p>

                <span v-if="item.entrustState == 0"
                  style="color: red; position: absolute; right: 20px; bottom: 10px; font-weight: bold; font-size: 12px;font-style:italic;">等待检测</span>
                <div v-if="item.isNewTask == 1" style="position: absolute; padding: 4px 6px; right: 8px; bottom: 10px; color: #ff0000; font-weight: bold; border-radius: 4px; ">有更新</div>
              </div>
            </ul>
            <p v-if="dataListUnderway.length == 0" style="text-align: center;">暂无数据</p>
          </div>
        </div>

        <!-- 混凝土看板中间 -->
        <div class="flex-item con-center" style="width: 1272px !important;">
          <div class="center-content">
            <div class="cc-box">
              <h4>委托信息</h4>
              <div class="cc-info">
                <div class="flex-box">
                  <div class="flex-item">
                    <p class="pb16 cci-main">
                      {{ activeData.experimentNo }}&nbsp;&nbsp;&nbsp;&nbsp;
                      {{ activeData.consignId ? `协会委托编号: ${activeData.consignId}` : '' }}
                      <span class="type-span"
                        style="border-radius: 3px; padding: 0px 7px; font-size: 12px; font-weight: 600;"
                        :style="{ backgroundColor: activeData.checkType == '1' ? '#61A480' : '#496BF9', color: '#fff' }">{{
                          activeData.checkType == '1' ? '快检' : '批检' }}</span>
                    </p>
                    <p>
                      <span style="display: inline-block; min-width: 150px;">委托原因：{{ activeData.entrustReasonName
                      }}</span>
                      <span>配合比编号：{{ activeData.phb || '--' }}</span>
                      <span>
                        样品编号：{{ activeData.sampleId || '--' }}
                        <el-button
                          v-if="!([3, 4].indexOf(activeData.experimentStatus) > -1) && (!this.activeData.sampleId || !this.activeData.consignId)"
                          type="text" @click="showEditSampleIdDialogClick">修改编号</el-button>
                      </span>
                      <span>备案证号：{{ activeData.certificateNo || '--' }}</span>
                    </p>
                  </div>
                  <div style="margin-top: -10px;" v-if="activeData.experimentStatus == 0">
                    <el-button type="primary" :disabled="loading"
                      @click="handleSetState('setExperimentAccept')">接受</el-button>
                    <el-button type="primary" plain :disabled="loading"
                      @click="handleSetState('setExperimentRefuse')">拒绝</el-button>
                  </div>
                </div>
                <p>
                  <!-- <span style="display: inline-block; min-width: 150px;">样品等级：{{activeData.sampleLevel}}</span> -->
                  <span style="display: inline-block; min-width: 150px;">
                    样品等级：
                    <!-- {{ activeData.matterAbbreviation || activeData.matterName }} -->
                    {{ activeData.matterSpecs }}
                    <!-- {{ activeData.materialAbbreviation || activeData.materialsName }} -->
                    ({{ activeData.materialsSpecs }})
                  </span>
                  <span>委托时间：{{ activeData.entrustTime }}</span>
                  <span>代表数量：{{ activeData.behalfNumber }}</span>
                  <span>委托人：{{ activeData.entrustPersonName }}</span>
                </p>
                <p>委托试验：{{ activeData.entrustExperiment }}</p>
              </div>
              <div class="cc-info ">
                <p class="pb16 cci-main flex-row" style="justify-content: space-between;">
                  <span>任务信息</span>
                  <!-- activeData.createType == 2 代表是手动新建的委托 -->
                  <!-- <el-button v-if="activeData.createType == 2" style="margin: 0; padding-right: 0; " type="text" size="mini" @click="selectInvoice">绑定小票</el-button> -->
                  <el-button style="margin: 0; padding-right: 0; " type="text" size="mini"
                    @click="selectInvoice">绑定小票</el-button>
                </p>
                <el-table :data="taskData" border style="width: 100%;" :cell-style="getCellStyle">
                  <el-table-column align="center" prop="rwdextraInfo.frwno" label="编号">
                  </el-table-column>
                  <el-table-column align="center" show-overflow-tooltip prop="rwdextraInfo.fgcmc" label="工程名称">
                  </el-table-column>
                  <el-table-column align="center" show-overflow-tooltip prop="rwdextraInfo.ftpz" label="砼品种">
                  </el-table-column>
                  <el-table-column align="center" show-overflow-tooltip prop="rwdextraInfo.fhtdw" label="施工单位">
                  </el-table-column>
                  <el-table-column align="center" show-overflow-tooltip prop="rwdextraInfo.fjzbw" label="浇筑部位">
                  </el-table-column>
                  <!-- <el-table-column
                  align="center"
                  prop="fhquantity"
                  label="实际发货数">
                </el-table-column> -->
                  <el-table-column align="center" prop="rwdextraInfo.ftld" label="坍落度">
                  </el-table-column>
                  <el-table-column align="center" prop="countItemorderBehalfNumber" label="代表数量">
                  </el-table-column>
                  <el-table-column width="220" label="操作" align="center" key="handle" :resizable="false">
                    <template slot-scope="scope">
                      <el-button v-if="scope.row?.rwdextraInfo?.isexcep == 1" style="color: #FF7B2F; cursor: pointer;"
                        type="text" size="small" @click="showWorkOrders(scope.row.rwdextraInfo.frwdh)">
                        <img style="width: 12px; height: 13px; margin-right: 2px;"
                          src="@/assets/images/order_abnormal_icon.png" />
                        <span>工单异常</span>
                      </el-button>
                      <el-button type="text" size="small" @click="goTaskDetail(scope.row)">任务单详情</el-button>
                      <el-button type="text" style="color: #ff0000;" size="small"
                        @click="deleteTask(scope.row)">删除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
                <Pagination :total="tasktotal" :pageNum="pageObj.pageNum" :pageSize="pageObj.pageSize"
                  @getData="getTaskInfo" />
                <!-- <p v-for="item in taskData" :key="item.id">
                <span class="fr" style="margin: 0; padding-right: 0; height: 20px;overflow: hidden;">
                  <el-button style="margin: 0; padding-right: 0; " type="text" size="mini" @click="goTaskDetail(item)">任务单详情</el-button>
                </span>
                <span>{{item.rwdextraInfo.frwno}}</span>
                <span style="display: inline-block; width: 240px;" v-if="!item.rwdextraInfo.fgcmc || item.rwdextraInfo.fgcmc?.length < 16">{{item.rwdextraInfo.fgcmc}}</span>
                <el-tooltip v-else class="item" effect="dark" :content="item.rwdextraInfo.fgcmc" placement="top">
                  <span style="display: inline-block; width: 240px;">{{item.rwdextraInfo.fgcmc?.slice(0,15)}}...</span>
                </el-tooltip>
                
                <span v-if="item.rwdextraInfo.fjzbw && item.rwdextraInfo.fjzbw.length < 16" class="txtEllipsis" style="display: inline-block; width: 240px; vertical-align: middle;">{{item.rwdextraInfo.fjzbw}}</span>
                <el-tooltip v-else class="item" effect="dark" :content="item.rwdextraInfo.fjzbw" placement="top">
                  <span class="txtEllipsis" style="display: inline-block; width: 240px; vertical-align: middle;">{{item.rwdextraInfo.fjzbw}}</span>
                </el-tooltip>
                <span style="display: inline-block; width: 60px;">{{item.fhquantity}}</span>
                <span v-if="item.rwdextraInfo.isexcep == 1" class="flex-row" style="font-size: 12px; color: #FF7B2F; cursor: pointer;" @click="showWorkOrders(item.rwdextraInfo.frwdh)">
                  <img style="width: 12px; height: 13px; margin-right: 2px;" src="@/assets/images/order_abnormal_icon.png" />
                  <span>工单异常</span>
                </span>
              </p> -->
              </div>

              <WorkOrderTable @selectedData="gotoWorkDetail" ref="workTableDialog" :tableColumn="engDetailColumn">
              </WorkOrderTable>
              <!-- <div class="cc-info">
            <p class="cci-main">CGRWD0000001</p>
            <p><span>样品等级：PO42.5</span><span>委托时间：2023-12-23 00:23</span></p>
            <p>委托试验：和易性检测、保水性检测、抗压强度检测</p>
          </div> -->
            </div>
            <div
              v-if="activeData.experimentStatus == 1 || activeData.experimentStatus == 2 || activeData.experimentStatus == 3"
              style="padding-bottom: 80px;">

              <!-- 样品管理！！！！！！！！！！！！！！！！！！！！！！ -->
              <SampleMgt ref="sampleMgt" @setSampleTotal="setSampleTotal" :id="activeId"
                :checkType="activeData.checkType" :experimentStatus="activeData.experimentStatus"></SampleMgt>
              <!-- 只有试验中才有试验信息 -->
              <div v-if="activeData.experimentStatus == 2 || activeData.experimentStatus == 3" class="cc-box">
                <h4>试验信息</h4>
                <div class="cc-info">
                  <el-form label-width="84px" ref="testInfoForm" :model="testInfoForm"
                    :disabled="loading || activeData.experimentStatus === 3">
                    <el-row :gutter="40">
                      <el-col :span="12">
                        <el-form-item label="检测方法：">
                          <el-input v-model="testInfoForm.experimentGist" clearable />
                        </el-form-item>
                      </el-col>
                      <el-col :span="12">
                        <el-form-item label="判定依据：">
                          <el-input v-model="testInfoForm.judgeGist" clearable />
                        </el-form-item>
                      </el-col>
                      <el-col :span="24">
                        <el-form-item label="仪器设备：">
                          <div class="flex-box">
                            <el-input class="flex-item" style="width: 500px;" v-model="testInfoForm.equipment" disabled
                              clearable />
                            <!-- <el-button type="text" @click="selectEquipment">选择仪器设备</el-button> -->
                          </div>
                          <!-- <el-select
                        v-model="testInfoForm.equipment" 
                        filterable clearable 
                        multiple
                        placeholder="请选择仪器设备" 
                        style="width: 100%">
                        <el-option
                          v-for="item in equipmentOption"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value">
                        </el-option>
                      </el-select> -->
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item label="入场时间：">
                          <el-date-picker type="date" v-model="testInfoForm.admissionTime" placeholder="选择时间"
                            format="yyyy-MM-dd" style="width: 200px;" value-format="yyyy-MM-dd HH:mm:ss">
                          </el-date-picker>


                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item label="委托时间：">
                          <el-date-picker type="date" v-model="testInfoForm.entrustTime" placeholder="选择时间"
                            format="yyyy-MM-dd" style="width: 200px;" value-format="yyyy-MM-dd HH:mm:ss">
                          </el-date-picker>


                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item label="报告时间：">
                          <el-date-picker type="date" v-model="testInfoForm.reportDate" placeholder="选择时间"
                            format="yyyy-MM-dd" style="width: 200px;" value-format="yyyy-MM-dd HH:mm:ss">
                          </el-date-picker>


                        </el-form-item>
                      </el-col>

                      <el-col :span="8">
                        <el-form-item label="成型日期：">
                          <!-- datetime  HH:mm:ss-->
                          <el-date-picker type="date" v-model="testInfoForm.moldingTime" placeholder="选择日期"
                            format="yyyy-MM-dd" style="width: 200px;" value-format="yyyy-MM-dd"
                            @change="moldingTimeChange">
                          </el-date-picker>


                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item label="环境温度：">
                          <el-input v-model="testInfoForm.temperature" clearable placeholder="请输入"
                            style="width: 200px;">
                            <template slot="append">℃</template>
                          </el-input>
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item label="环境湿度：">
                          <el-input v-model="testInfoForm.humidness" clearable placeholder="请输入" style="width: 200px;">
                            <template slot="append">%</template>
                          </el-input>
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item label="养护方式：">
                          <el-input v-model="testInfoForm.curingMode" clearable placeholder="请输入" style="width: 200px;">
                          </el-input>
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item label="报告编号：">
                          <el-input v-model="testInfoForm.reportNo" clearable placeholder="请输入" style="width: 200px;">
                          </el-input>
                        </el-form-item>
                      </el-col>
                    </el-row>
                  </el-form>
                  <!-- 试验信息- 切换内容！！！！！！！！！！！！！！！！！！！！！！ -->
                  <ExperProjectList ref="formTab" :sampleLevel='activeData.sampleLevel'
                    :conversionFactor='activeData.conversionFactor' :isXnbg='activeData.isXnbg'
                    :proportionMaterial="proportionMaterial" :proportionQllz="proportionQllz"
                    :experimentStatus="activeData.experimentStatus" :sampleTotal="sampleTotal" :activeId="activeId"
                    :ksdj="activeData.ksdj" :moldingTime="testInfoForm.moldingTime"
                    :entrustTime="activeData.entrustTime">
                  </ExperProjectList>
                </div>

              </div>


              <div style="margin-bottom: 20px;"
                v-if="activeData.experimentStatus == 2 || activeData.experimentStatus == 3" class="cc-box">
                <h4>试验结论</h4>
                <div class="cc-info">
                  <el-form label-width="100px" ref="conclusionForm" :model="testInfoForm"
                    :disabled="loading || activeData.experimentStatus === 3">
                    <el-form-item label="是否合格：">
                      <el-radio-group v-model="testInfoForm.isQualified">
                        <el-radio :label="1">合格</el-radio>
                        <el-radio :label="2">不合格</el-radio>
                      </el-radio-group>
                    </el-form-item>

                    <el-form-item label="结论：">
                      <el-input v-model="testInfoForm.conclusion" clearable placeholder="请输入" />
                    </el-form-item>
                    <el-form-item label="备注：">
                      <el-input v-model="testInfoForm.remark" clearable placeholder="请输入" />
                    </el-form-item>
                  </el-form>
                </div>
              </div>
            </div>
          </div>

          <div v-if="activeData.experimentStatus == 2 || activeData.experimentStatus == 3" class="center-footer">
            <template v-if="activeData.experimentStatus == 2">
              <el-button type="primary" :disabled="loading" @click="saveExperimentInfo">保存</el-button>
              <el-button type="primary" v-if="isLLZProject" :disabled="loading"
                @click="saveExperimentInfo('setExperimentFinish')">完成</el-button>
            </template>
            <template v-if="activeData.experimentStatus == 2 || activeData.experimentStatus == 3">
              <el-button type="primary" :disabled="loading" @click="uploadAssociationResp">上传至协会</el-button>
              <el-button type="primary" :disabled="loading" @click="downloadAssociationResp">更新至品控</el-button>
            </template>
          </div>
        </div>

        <!-- 混凝土看板右侧列表 -->
        <div>
          <div class="flex-row status-view">
            <span>试验状态：</span>
            <el-checkbox-group v-model="experimentStatusRight" :min="1" @change="handleExperimentStatusRightFilter">
              <el-checkbox :label="3">已完成</el-checkbox>
              <el-checkbox :label="4">已拒绝</el-checkbox>
            </el-checkbox-group>
          </div>

          <div class="con-right">
            <ul v-infinite-scroll="loadRightListMore" :infinite-scroll-disabled="infiniteDisabled">
              <div @click="showDetail(item)"
                :class="{ 'active': activeId == item.id, 'consign-null': (!item.consignId || !item.sampleId) }"
                class="g-card" v-for="(item, index) in dataListFinished" :key="item.id">
                <p class="gc-main">{{ item.experimentNo }}
                  <span class="type-span" style="border-radius: 3px"
                    :style="{ backgroundColor: item.checkType == '1' ? '#61A480' : '#496BF9', color: '#fff' }">{{
                      item.checkType
                        == '1' ? '快检' : '批检' }}</span>

                  <span class="fr succ" style="background-color: #D6D6D6; color: white;">{{
                    experimentStatusObj[item.experimentStatus] }}</span>
                </p>
                <!-- <p class="gc-main pb16">
                {{experimentTypeNameList[item.experimentType]}}&nbsp;&nbsp;&nbsp;
                {{item.sampleLevel}}&nbsp;&nbsp;&nbsp;
                {{item.phb}}
              </p> -->
                <p class="span-label pb16">
                  <!-- {{experimentTypeNameList[item.experimentType]}}&nbsp;&nbsp;&nbsp; -->
                  <!-- {{item.sampleLevel}}&nbsp;&nbsp;&nbsp; -->
                  <!-- {{ item.matterAbbreviation || item.matterName }} -->
                  <span style="margin-right: 10px;">{{ item.matterSpecs }}</span>
                  <!-- {{ item.materialAbbreviation || item.materialsName }} -->
                  <span style="margin-right: 10px;">{{ item.materialsSpecs }}</span>

                  <span>{{ item.phb }}</span>
                </p>
                <!-- <p>供货商：{{item.entrustPersonName}}</p> -->
                <p>委托时间：{{ item.entrustTime }}</p>
                <p>委托：{{ item.consignId || '--' }} 样品：{{ item.sampleId || '--' }}</p>

                <!-- <p>委托编号：{{ item.consignId || '--'}}</p>
              <p>样品编号：{{ item.sampleId || '--'}}</p> -->
                <p>代表数量：{{ item.behalfNumber }}</p>
              </div>
            </ul>
            <p v-if="dataListFinished.length == 0" style="text-align: center;">暂无数据</p>
          </div>
        </div>

      </div>
    </div>



    <!-- 仪器设备选择 -->
    <el-dialog title="选择仪器设备" width="700px" :visible.sync="equipmentTableShow" class="claim-dialog-box"
      :close-on-click-modal="false" :close-on-press-escape="false">
      <div class="flex-box flex-column h100p drawer-box">
        <div class="flex-item">
          <el-table ref="multipleTable" :data="equipmentOption" tooltip-effect="dark" style="width: 100%"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55">
            </el-table-column>
            <el-table-column prop="equipmentNo" label="设备编号" width="120">
            </el-table-column>
            <el-table-column prop="equipmentName" label="设备名称" width="120">
            </el-table-column>
            <el-table-column prop="equipmentType" label="设备类型" width="120">
            </el-table-column>
            <el-table-column prop="technicalParameter" label="技术参数" width="120">
            </el-table-column>
            <el-table-column prop="equipmentStatus" label="设备状态" width="120">
            </el-table-column>
            <el-table-column prop="custodyUserName" label="保管人" show-overflow-tooltip>
            </el-table-column>
          </el-table>
        </div>
        <div class="drawer-footer">
          <el-button type="primary" @click="equipmentTableShow = false" plain>取消</el-button>
          <el-button type="primary" @click="saveEquipmentTable()">保存</el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 新建委托 -->
    <DrawerForm :formContent="formContent" ref="entrustDrawerForm" @saveTarget="saveEntrustTarget"
      @drawerClose="drawerCloseTarget">
      <template #customLastForm>
        <el-form-item :label="`选择试验项目：`">
          <el-checkbox-group v-model="selectedCheckConfigList">
            <template v-for="(item, index) in checkConfigOpts">
              <el-checkbox :label="item.value" :key="index">{{ item.label }}</el-checkbox>
            </template>
          </el-checkbox-group>
        </el-form-item>
      </template>
    </DrawerForm>

    <TableDialog ref="tableDialog" :tableColumn="dialogTableColumn"></TableDialog>

    <div v-if="showEditSampleIdDialog">
      <el-dialog width="600px" title="修改编号" :visible.sync="showEditSampleIdDialog"
        @closed="showEditSampleIdDialog = false">
        <div style="padding: 0px 16px;">
          <el-form :model="editSampleIdForm" label-width="100px">
            <el-form-item label="协会委托编号" prop="consignId">
              <el-input v-model="editSampleIdForm.consignId" placeholder="请输入协会委托编号" clearable></el-input>
            </el-form-item>
            <el-form-item label="协会样品编号" prop="sampleId">
              <el-input v-model="editSampleIdForm.sampleId" placeholder="请输入协会样品编号" clearable></el-input>
            </el-form-item>
          </el-form>

          <div class="flex-row-start" style="justify-content: flex-end; margin-top: 30px;">
            <el-button type="primary" @click="editSampleIdSave">保存</el-button>
            <el-button @click="showEditSampleIdDialog = false">取消</el-button>
          </div>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import moment from "@/utils/moment";
import momentJS from 'moment'
import SampleMgt from "./components/sampleMgt.vue";
import ExperProjectList from "./components/experProjectList.vue";
import getOpt from "@/common/js/getListData.js"
import WorkOrderTable from "./components/WorkOrderTable.vue";
import { engDetailColumn } from "../engineeringService/config.js"
import { receiptTableColumn } from "./config.js"
import DrawerForm from "@/components/drawerForm.vue";
import { Notification } from 'element-ui';
import Pagination from "@/components/Pagination/index.vue";

import TableDialog from "@/components/tableDialog2.vue";
// getExperimentList
export default {
  components: {
    SampleMgt,
    ExperProjectList,
    WorkOrderTable,
    DrawerForm,
    TableDialog,
    Pagination
  },
  data() {
    return {
      isLLZProject: false,
      sfssListColumn: ["sfss1", "sfss2", "sfss3", "sfss4", "sfss5", "sfss6"],
      loading: false,
      filePrefix: this.$store.state.loginStore.userInfo.filePrefix,
      // 设置时间选择
      pickerOptions: {
        disabledDate(time) {
          let deadline = Date.now() - 60 * 60 * 1000;
          return time.getTime() > deadline //
        },
      },
      searchForm: {
        // moment.severalDaysAgo(60)
        noKeyword: '',
        takeEffectDate: [moment.today(), moment.today()],
        entrustState: "",
        experimentStatusList: []
      },
      dataListFinished: [],//已完成
      dataListUnderway: [],//进行中
      experimentStatusLeft: [0, 1, 2],
      experimentStatusRight: [3, 4],
      dataListFinishedByIds: [],//已完成所有数据的id
      dataListUnderwayByIds: [],//进行中所有数据的id
      experimentStatusObj: {
        0: '待接收',
        1: '待取样',
        2: '试验中',
        3: '已完成',
        4: '已拒绝',
      },
      experimentTypeList: [],
      experimentTypeNameList: {},
      activeId: '',//当前选中的id
      activeData: {},//当前选中的id
      taskData: [],//任务数据
      tasktotal: 1,
      pageObj: {
        pageNum: 1, // 页数
        pageSize: 5, // 条数
      },

      //conclusionForm:{},//结论表单
      testInfoForm: {},//信息表单

      sampleTotal: 0,
      equipmentOption: [],
      diffExperimentCount: 0, // 有几条未更新

      engDetailColumn: engDetailColumn,
      lastId: '', // 创建时间最新一条未完成的试验台账ID
      lastMesgId: '', // 最新消息的id
      notifications: [],
      notifyAlerts: {},

      selectEquipmentList: [],
      equipmentTableShow: false,

      pageLeftNum: 1,
      totalLeftCount: 0,
      pageRightNum: 1,
      totalRightCount: 0,

      infiniteDisabled: true,
      experimentStatusOpts: [
        {
          dictValueCode: 0,
          dictValueName: "待接收"
        },
        {
          dictValueCode: 1,
          dictValueName: "待取样"
        },
        {
          dictValueCode: 2,
          dictValueName: "试验中"
        },
        {
          dictValueCode: 3,
          dictValueName: "已完成"
        },
        {
          dictValueCode: 4,
          dictValueName: "已拒绝"
        },
      ],

      entrustDialog: false,
      addFormData: [],
      // checkConfigSearchFormOpts: [],
      testProjectSearchFormOpts: [],
      checkConfigOpts: [],
      selectedCheckConfigList: [],
      formContent: [
        // {
        //   type: 'input',
        //   label: '委托编号',
        //   prop: 'experimentNo',
        //   required: true
        // },
        {
          type: 'select',
          label: '材料类型',
          placeholder: "混凝土",
          disabled: true
        },
        {
          type: 'select',
          label: '材料名称',
          prop: 'materialsName',
          options: this.specConfigOpts,
          valueKey: "materialsName",
          placeholder: "材料名称",
          required: true,
          handle: "changeSelect"
        },
        {
          type: 'select',
          label: '材料规格',
          prop: 'materialsSpec',
          options: this.specConfig_2Opts,
          valueKey: "materialsSpec",
          placeholder: "材料规格",
          required: true,
          handle: "changeSelectSpec"
        },
        {
          type: 'select',
          label: '检验类型',
          prop: 'checkType',
          required: true,
          options: [
            {
              label: "快检",
              value: 1
            },
            {
              label: "批检",
              value: 2
            },
          ],
          placeholder: "检验类型",
          handle: "changeSelectCheckType"
        },
        {
          type: 'input',
          label: '委托单位',
          prop: 'experimentCompany',
          required: true
        },
        {
          type: 'input',
          label: '委托部门',
          prop: 'experimentDept',
          required: true,
        },
        {
          type: 'input',
          label: '委托人',
          prop: 'entrustPersonName',
          required: true
        },
        {
          type: 'datetime',
          label: '委托时间',
          prop: 'entrustTime',
          format: 'yyyy-MM-dd HH:mm:ss',
          valueFormat: 'yyyy-MM-dd HH:mm:ss',
          required: true
        },
        {
          type: 'select',
          label: '选择配比',
          prop: 'phb',
          required: true,
          options: [],
          handle: "changePhb"
        },
        // {
        //   type: 'input',
        //   label: '联系电话',
        //   prop: 'phone',
        //   required: true
        // },
        // {
        //   type: 'input',
        //   label: '样品等级',
        //   prop: 'sampleLevel',
        //   required: true
        // },
        // {
        //   type: 'number',
        //   label: '代表数量',
        //   slot: 'append',
        //   slotVal: 'm³',
        //   prop: 'behalfNumber',
        //   required: true
        // },
        // {
        //   type: 'input',
        //   label: '备注',
        //   prop: 'remark',
        // },
      ],
      dialogTableColumn: receiptTableColumn,
      isNew: true,
      // 混凝土配合比信息
      proportionMaterial: {},
      // 氯离子
      proportionQllz: '',

      showEditSampleIdDialog: false,
      editSampleIdForm: {
        consignId: '',
        sampleId: '',
      }
    };
  },
  deactivated() {
    if (this.pollTimer) {
      clearInterval(this.pollTimer);
      this.pollTimer = null;
    }

    document.querySelectorAll('.experiment-notification').forEach((el) => {
      if (el) {
        el.classList.add('hidden-notification');
        el.classList.remove('show-notification');
      }
    });
  },

  activated() {
    let lastFullPath = this.$store.state.tagsView.experimentBoardFullpath;
    let currFullPath = this.$route.fullPath;
    let isNew = this.isNew;

    if (!isNew && lastFullPath !== currFullPath) {
      this.$router.replace({ path: lastFullPath });
    } else if (isNew) {
      this.$store.dispatch("tagsView/saveExperimentBoardFullpath", currFullPath);
    }

    this.isNew = false;

    document.querySelectorAll('.experiment-notification').forEach((el) => {
      if (el) {
        el.classList.add('show-notification');
        el.classList.remove('hidden-notification');
      }
    });

    // 
    if (this.$route.query.entrustTime || this.$route.query.experimentNo) {
      if (this.$route.query.entrustTime) {
        this.searchForm.takeEffectDate = [this.$route.query.entrustTime, this.$route.query.entrustTime];
      }
      if (this.$route.query.experimentNo) {
        this.searchForm.noKeyword = this.$route.query.experimentNo;
        this.searchForm.takeEffectDate = [];
      }

      this.handleFilter();
    }
  },

  created: function () {
    if (this.$route.query.entrustTime || this.$route.query.experimentNo) {
      if (this.$route.query.entrustTime) {
        this.searchForm.takeEffectDate = [this.$route.query.entrustTime, this.$route.query.entrustTime];
      }
      if (this.$route.query.experimentNo) {
        this.searchForm.noKeyword = this.$route.query.experimentNo;
        this.searchForm.takeEffectDate = [];
      }
    }

    this.initData();
    this.$api.getDictValue({
      dictCode: 'MASTERIAL_TYPE'
    }, this).then(res => {
      this.experimentTypeList = res.data.list
      this.experimentTypeNameList = {};
      res.data.list.forEach(item => {
        this.experimentTypeNameList[item.dictValueCode] = item.dictValueName
      })
    })
    this.testProjectOptsSearchFormResp();
  },
  methods: {
    getCellStyle({row, column, rowIndex, columnIndex}) {
      let param = {padding: '0'}
      let readTime = row.readTime; // YYYY-MM-DD HH:mm:ss
      // 当前时间 - readTime <= 5分钟 显示红色
      if (readTime) {
        let read = new Date(readTime).getTime();
        let now = new Date().getTime();
        let diff = (now - read) / 1000 / 60;
        if (diff <= 5) {
          param.color = '#ff0000';
        }
      }
      return param;
    },
    moldingTimeChange(val) {
      // let lq = momentJS(momentJS(this.$refs.formTab.searchForm7.jcrq).format('YYYY-MM-DD')).diff(momentJS(val), 'day')
      // this.$refs.formTab.$set(this.$refs.formTab.searchForm7, "lq", lq);
      this.$refs.formTab.ksdjJcrqChange(this.$refs.formTab.searchForm7.jcrq)
    },
    saveEntrustTarget(formData) {
      this.$refs.entrustDrawerForm.$refs.editForm.validate((valid) => {
        if (!valid) {
          return;
        }
        formData.testProjectCodeList = this.selectedCheckConfigList;
        formData["specId"] = this.addFormData["specId"];

        let parma = {
          ...formData,
          experimentType: 7,
          // 下面俩字段后端不需要，自己删掉
          materialsName: undefined,
          materialsSpec: undefined
        }

        this.$api.addExperiment(parma, this).then((res) => {
          if (res.succ) {
            this.$message({
              showClose: true,
              message: "操作成功",
              type: "success",
            });
            this.$refs.entrustDrawerForm.handleClose();
            this.initData();
          } else {
            this.$message({
              showClose: true,
              message: res.msg,
              type: "error",
            });
          }
        });
      });
    },
    drawerCloseTarget() {
      this.selectedCheckConfigList = [];
      this.addFormData = {};
    },
    changeExperimentType() {
      this.$api.querySelectByMaterialsType(`materialsType=7`, this).then(res => {
        if (res.code == 1) {
          this.specConfigOpts = res.data.list.map(item => {
            return {
              label: item.materialsName + (item.sampleJudge ? `【${item.sampleJudge}】` : ''),
              value: item,
            }
          })
          this.$set(this.formContent[1], "options", this.specConfigOpts);

          this.$set(this.addFormData, "materialType", 7);
          this.$set(this.addFormData, "materialsName", "");
          this.$set(this.addFormData, "materialsSpec", "");
          this.$set(this.$refs.entrustDrawerForm.editForm, "materialsName", "");
          this.$set(this.$refs.entrustDrawerForm.editForm, "materialsSpec", "");
        }
      })
      // this.$api.getMaterialsNameByType(`materialType=7`, this).then(res => {
      //     if (res.code == 1) {
      //         this.specConfigOpts = res.data.list.map(item => {
      //             return {
      //                 label: item.materialsName,
      //                 value: item,
      //             }
      //         })
      //         this.$set(this.formContent[1], "options", this.specConfigOpts);

      //         this.$set(this.addFormData, "materialType", 7);
      //         this.$set(this.addFormData, "materialsName", "");
      //         this.$set(this.addFormData, "materialsSpec", "");
      //         this.$set(this.$refs.entrustDrawerForm.editForm, "materialsName", "");
      //         this.$set(this.$refs.entrustDrawerForm.editForm, "materialsSpec", "");
      //     }
      // })
    },

    changeSelect(item) {
      let materialsName = item.materialsName + (item.sampleJudge ? `【${item.sampleJudge}】` : '');
      this.$set(this.$refs.entrustDrawerForm.editForm, "materialsName", materialsName);
      this.$set(this.addFormData, "materialsName", item.materialsName);
      this.$set(this.addFormData, "materialsSpec", "");

      this.$api.queryMaterialsSpecConfigAll({
        materialsType: this.addFormData.materialType,
        materialsName: item.materialsName
      }, this).then(res => {
        if (res.code == 1) {
          this.specConfig_2Opts = res.data.list.map(item => {
            return {
              label: item.materialsSpec || item.materialsName,
              value: item,
            }
          })
          this.$set(this.formContent[2], "options", this.specConfig_2Opts);
        }
      })
      // this.$api.queryMaterialsAll({
      //     materialType: this.addFormData["materialType"],
      //     materialName: item,
      //     "isConfig": 2 //固定传2 已设置
      // }, this).then(res => {
      //     if (res.code == 1) {
      //         this.specConfig_2Opts = res.data.map(item => {
      //             return {
      //                 label: item.materialSpecs || item.materialName,
      //                 value: item,
      //             }
      //         })
      //         this.$set(this.formContent[2], "options", this.specConfig_2Opts);
      //     }
      // })
    },
    changeSelectSpec(item) {
      this.$set(this.addFormData, "specId", item.id);
    },

    changeSelectCheckType(val) {
      this.checkConfigOptsResp();
    },

    checkConfigOptsResp() {
      let parm = {
        checkType: this.$refs.entrustDrawerForm.editForm.checkType,
        projectCategory: 7
      }
      if (parm.checkType == undefined || parm.projectCategory == undefined) return;
      this.checkConfigOpts = [];
      this.selectedCheckConfigList = [];
      this.$api.getCheckConfig(parm, this).then(res => {
        if (res.code == 1) {
          let keys = Object.keys(res.data.list);
          this.checkConfigOpts = keys.map(item => {
            let value = res.data.list[item];
            return {
              label: value,
              value: item,
            }
          });
        }
      })
    },
    checkTypeSearchFormChange(item) {
      this.testProjectOptsSearchFormResp();
    },
    // // 搜索筛选试验项目
    // checkConfigOptsSearchFormResp() {
    //   let parm = {
    //     checkType: this.searchForm.checkType,
    //     projectCategory: 7
    //   }
    //   this.checkConfigSearchFormOpts = [];
    //   this.$api.getCheckConfig(parm, this).then(res => {
    //       if (res.code == 1) {
    //         let keys = Object.keys(res.data.list);
    //         this.checkConfigSearchFormOpts = keys.map(item => {
    //           let value = res.data.list[item];
    //           return {
    //               label: value,
    //               value: item,
    //           }
    //         });
    //       }
    //   })
    // },
    // 搜索筛选试验项目
    testProjectOptsSearchFormResp() {
      let parm = {
        checkType: this.searchForm.checkType,
        testType: 'CONCRETE'
      }
      this.testProjectSearchFormOpts = [];
      this.$api.getTestProject2(parm, this).then(res => {
        if (res.code == 1) {
          this.testProjectSearchFormOpts = res.data.list.map(item => {
            return {
              label: item.testName,
              value: item.testName,
            }
          });
        }
      })
    },

    showEntrustDialog() {
      this.$refs.entrustDrawerForm.initData({
        experimentCompany: this.$store.state.loginStore.userInfo.experimentCompany
      });
      this.changeExperimentType();
      // 获取phb 数据
      this.queryMixProportionAllResp();
    },
    queryMixProportionByIdResp(phb) {
      this.$api.queryMixProportionAll({ proportionPhb: phb }, this).then(res => {
        if (res.code == 1 && res.data.list) {
          var list = res.data.list;
          if (list && list.length > 0) {
            console.log('获取到的配合比信息：', list[0].proportionMaterial);
            this.$set(this, "proportionMaterial", list[0].proportionMaterial);
            this.$set(this, "proportionQllz", list[0].proportionQllz);
          }
        }
      });
    },
    queryMixProportionAllResp() {
      this.$api.queryMixProportionAll({}, this).then(res => {
        if (res.code == 1) {
          let mixProportionOpts = res.data.list.map(item => {
            return {
              label: item.proportionPhb,
              value: item.proportionPhb,
            }
          })
          this.$set(this.formContent[8], "options", mixProportionOpts);
        }
      });
    },
    changePhb(item) {
      this.$set(this.addFormData, "phb", item);
    },
    //仪器设备
    selectEquipment() {
      const oselectEquipmentList = this.testInfoForm.equipment.split('、');
      this.equipmentTableShow = true;
      this.$nextTick(() => {
        for (let i = 0; i < this.equipmentOption.length; i++) {
          for (let j = 0; j < oselectEquipmentList.length; j++) {
            if (this.equipmentOption[i].equipmentName === oselectEquipmentList[j]) {
              this.$refs.multipleTable.toggleRowSelection(this.equipmentOption[i], true);
            }
          }
        }
      })
    },
    handleSelectionChange(val) {
      this.selectEquipmentList = val.map(item => item.equipmentName);
    },
    saveEquipmentTable() {
      this.testInfoForm.equipment = this.selectEquipmentList.join('、');
      this.equipmentTableShow = false;
    },
    async getequipmentOption() {
      this.equipmentOption = await getOpt.getEquipmentAll(this)
    },
    //抽样数量
    setSampleTotal(val) {
      this.sampleTotal = val;
    },
    //保存
    saveExperimentInfo(isFinish) {
      if (!this.testInfoForm.moldingTime || this.testInfoForm.moldingTime.length < 10) {
        this.$message.error('请输入成型日期！')
        return false;
      }
      const odata = [...this.$refs.formTab.quickData, ...this.$refs.formTab.projectList];
      let oQuickForm = this.$refs.formTab.quickForm;
      let oBatchForm = this.$refs.formTab.batchForm;

      let oSsjList = this.$refs.formTab.ssjlInfoList;
      let oSsjListIndex = -1;
      // let ossksdj;
      if (oSsjList && oSsjList.length > 0 && this.$refs.formTab.projectList.length > 0) {
        for (let i = 0; i < oSsjList.length; i++) {
          if (!oSsjList[i].syyl || oSsjList[i].syyl <= 0) {
            this.$message.error(`第${i + 1}行，试验压力需是大于0的数字`);
            return false;
          }
          if (!oSsjList[i].jysj) {
            this.$message.error(`第${i + 1}行，请填写加压时间`);
            return false;
          }

          let total = 0;
          for (let j = 0; j < this.sfssListColumn.length; j++) {
            if (oSsjList[i][this.sfssListColumn[j]] == '是') {
              total = total + 1;
            }
          }
          // if(total >= 3){
          //   oSsjListIndex = i;
          //   ossksdj = oSsjList[i].syyl * 10 - 1;
          //   break;
          // }
        }

        if (oSsjListIndex > -1) {
          //剩余不要
          oSsjList = oSsjList.slice(0, oSsjListIndex * 1 + 1)
        }
      }
      for (let i = 0; i < odata.length; i++) {
        let objJson = {}
        let objImg = ""
        if (odata[i].testProjectName === '流动性' || odata[i].testProjectName === '保水性'
          || odata[i].testProjectName === '粘聚性' || odata[i].testProjectName === '实测坍落度'
          || odata[i].testProjectName === '目测砂率') {
          objJson = oQuickForm[odata[i].testProjectCode].objJson;
          if (odata[i].testProjectName === '流动性' || odata[i].testProjectName === '保水性' || odata[i].testProjectName === '粘聚性') {
            const oVal = objJson.val;
            for (let item in objJson) {
              if (item != 'val') {
                objJson[item] = 0;
                if (item == oVal) {
                  objJson[item] = 1;
                }
              }

            }
          }
          if (this.$refs.formTab.quickForm.img) {
            objImg = this.$refs.formTab.quickForm.img.map(item => {
              if (item.url) {
                return item.url.replace(this.filePrefix, '')
              } else {
                return item.replace(this.filePrefix, '');
              }
            })
            objImg = objImg.join(',');
          }

          this.saveTabData(odata[i], objJson, i == 0 ? objImg : '')
        } else if (odata[i].testProjectCode === 'CONCRETE_PARAM_KYQD') {
          objJson = this.$refs.formTab.searchForm6;
          console.log('保存的 数据： ', objJson);
          if (this.$refs.formTab.searchForm6.img) {
            objImg = this.$refs.formTab.searchForm6.img.map(item => {
              if (item.url) {
                return item.url.replace(this.filePrefix, '')
              } else {
                return item.replace(this.filePrefix, '');
              }
            })
            objImg = objImg.join(',');
          }

          this.saveTabData(odata[i], objJson, objImg)
        } else if (odata[i].testProjectCode === 'CONCRETE_PARAM_KSDJ') {
          objJson = this.$refs.formTab.searchForm7;
          //处理赋值表格数据
          objJson.ksInfo = oSsjList;
          //处理图片
          if (this.$refs.formTab.searchForm7.img) {
            objImg = this.$refs.formTab.searchForm7.img.map(item => {
              if (item.url) {
                return item.url.replace(this.filePrefix, '');
              } else {
                return item.replace(this.filePrefix, '');
              }
            })
            objImg = objImg.join(',');
          }
          this.saveTabData(odata[i], objJson, objImg)
        } else if (odata[i].testProjectCode === 'CONCRETE_PARAM_KZQD') {
          objJson = this.$refs.formTab.kzqdFormData;
          //处理赋值表格数据
          objJson.ksInfo = oSsjList;
          //处理图片
          if (this.$refs.formTab.kzqdFormData.img) {
            objImg = this.$refs.formTab.kzqdFormData.img.map(item => {
              if (item.url) {
                return item.url.replace(this.filePrefix, '');
              } else {
                return item.replace(this.filePrefix, '');
              }
            })
            objImg = objImg.join(',');
          }
          this.saveTabData(odata[i], objJson, objImg)
        } else if (odata[i].testProjectCode === 'CONCRETE_PARAM_XNBG_LLZHL') {
          objJson = this.$refs.formTab.llzFormData;
          console.log('拿到的数据：', this.$refs.formTab.llzFormData)
          console.log('拿到的数据1：', this.$refs.formTab.llzFormData)
          //处理图片
          if (this.$refs.formTab.llzFormData.img) {
            objImg = this.$refs.formTab.llzFormData.img.map(item => {
              if (item.url) {
                return item.url.replace(this.filePrefix, '');
              } else {
                return item.replace(this.filePrefix, '');
              }
            })
            objImg = objImg.join(',');
          }
          this.saveTabData(odata[i], objJson, objImg)
        } else {
          objJson = oBatchForm[odata[i].testProjectCode] || {};

          if (objJson.img) {
            objImg = objJson.img.map(item => {
              if (item.url) {
                return item.url.replace(this.filePrefix, '')
              } else {
                return item.replace(this.filePrefix, '');
              }
            })
            objImg = objImg.join(',');
          }
          delete objJson.img;

          this.saveTabData(odata[i], objJson, objImg)
        }
      }

      //保存实现信息和结论
      let oTestInfoForm = JSON.parse(JSON.stringify(this.testInfoForm))
      if (oTestInfoForm.moldingTime && oTestInfoForm.moldingTime.length === 10) {
        oTestInfoForm.moldingTime = oTestInfoForm.moldingTime + ' 00:00:01'
      }

      this.$api.setExperiment(oTestInfoForm).then(res => {
        if (res.succ) {
          if (isFinish === 'setExperimentFinish') {
            this.handleSetState('setExperimentFinish')
          } else {
            this.$message.success("保存成功")
          }

          // 重新请求页面数据
          // this.getExperimentById(this.activeId);
        }
      })
    },

    // 上传协会
    uploadAssociationResp() {
      this.$api.uploadAssociation({
        experimentId: [this.activeId],
      }, this).then((res) => {
        if (res.succ) {
          this.$message({
            showClose: true,
            message: "更新成功",
            type: "success",
          });
          // 重新请求页面数据
          // this.getExperimentById(this.activeId);
        }
      });
    },
    // 更新品控
    downloadAssociationResp() {
      this.$api.downloadAssociation({
        experimentId: [this.activeId],
      }, this).then((res) => {
        if (res.succ) {
          this.$message({
            showClose: true,
            message: "下载成功",
            type: "success",
          });
          // 重新请求页面数据
          this.getExperimentById(this.activeId);
        }
      });
    },

    saveTabData(odata, objJson, objImg) {
      this.$api.setExperimentDetail({
        id: odata.id,
        experimentId: this.activeId,
        checkType: odata.checkType,//1-快检 2-批检 == 1 ? 1 : 2
        testProjectCode: odata.testProjectCode,
        testProjectName: odata.testProjectName,
        objJson,
        objImg
      }, this).then(res => {
        if (res.succ) {
          //this.$message.success("保存成功")
        } else {
          this.$message.error(res.msg || '保存失败')
        }
      })
    },
    //试验信息

    //试验结论


    handleFilter() {
      console.log(this.searchForm)
      this.activeId = "";
      this.initData();
    },
    resetForm() {
      if (this.$route.query.entrustTime) {
        const baseUrl = window.location.pathname;
        window.location.replace(`${baseUrl}`);
      } else {
        this.searchForm = {
          // moment.severalDaysAgo(60)
          takeEffectDate: [moment.today(), moment.today()],
        };
        this.activeId = "";
        this.initData();
      }
    },
    async initData() {//更新列表，update

      this.pageLeftNum = 1;
      this.pageRightNum = 1;

      try {
        await this.queryLeftList();
        await this.queryRightList();
      } catch (error) {
        console.log(">>>>>", error)
      }

      if (this.$route.query.id) {
        this.showDetail({
          id: this.$route.query.id
        })
      } else if (this.activeId) {
        this.showDetail({
          id: this.activeId
        })
      } else if (this.dataListUnderway.length > 0) {
        this.showDetail(this.dataListUnderway[0], 0)
      } else if (this.dataListFinished.length > 0) {
        this.showDetail(this.dataListFinished[0])
      }
    },

    loadLeftListMore() {
      if (this.totalLeftCount != 0 && this.dataListUnderway.length == this.totalLeftCount) return;

      this.queryLeftList()
    },

    async handleExperimentStatusLeftFilter(event) {
      if (event.length == 0) {
        this.experimentStatusLeft = [0, 1, 2];
      }
      this.pageLeftNum = 1;
      try {
        await this.queryLeftList();
      } catch (error) {
        console.log(">>>>>", error)
      }

      if (this.dataListUnderway.length > 0) {
        this.showDetail(this.dataListUnderway[0], 0)
      } else if (this.dataListFinished.length > 0) {
        this.showDetail(this.dataListFinished[0])
      }
    },

    // 左侧列表数据分页请求
    async queryLeftList() {
      this.loading = true;
      this.infiniteDisabled = true;
      let params = JSON.parse(JSON.stringify(this.searchForm))
      if (!this.isEmpty(this.searchForm.takeEffectDate)) {
        params.beginTime = this.searchForm.takeEffectDate[0]
          ? this.searchForm.takeEffectDate[0]
          : "";
        params.comTime = this.searchForm.takeEffectDate[1]
          ? this.searchForm.takeEffectDate[1]
          : "";
      }
      delete params.takeEffectDate

      if (!(params.experimentStatusList && params.experimentStatusList.length > 0)) {
        params.experimentStatusList = this.experimentStatusLeft;
      }

      try {
        let res = await this.$api.getExperimentList({
          pageNum: this.pageLeftNum,
          pageSize: 10,
          params: {
            experimentTypeList: [7],
            isFinish: 0,
            ...params,
          }
        }, this);

        this.loading = false;
        this.infiniteDisabled = false;

        if (res.succ) {
          if (this.pageLeftNum == 1) {
            this.dataListUnderway = res.data.list;
            this.dataListUnderwayByIds = [];
          } else {
            this.dataListUnderway = this.dataListUnderway.concat(res.data.list);
          }
          // 存储所有的数据id，用来查找下标
          this.dataListUnderwayByIds = [];
          for (const item of this.dataListUnderway) {
            this.dataListUnderwayByIds.push(item.id)
          }

          this.totalLeftCount = res.data.total;
          this.pageLeftNum = res.data.nextPage == 0 ? 1 : res.data.nextPage;
        } else {
          this.$message.error(res.msg || '查询失败')
        }

        this.lastId = this.dataListUnderway.length > 0 ? this.dataListUnderway[0].id : '';
        this.pollingExperimentAllCount()
      } catch (error) {
        this.loading = false;
        this.infiniteDisabled = false;
        console.log(">>>>>>>", error);
      }
    },

    loadRightListMore() {
      if (this.totalRightCount != 0 && this.dataListFinished.length == this.totalRightCount) return;

      this.queryRightList()
    },

    async handleExperimentStatusRightFilter(event) {
      if (event.length == 0) {
        this.experimentStatusRight = [3, 4];
      }
      this.pageRightNum = 1;
      try {
        await this.queryRightList();
      } catch (error) {
        console.log(">>>>>", error)
      }

      if (this.dataListFinished.length > 0) {
        this.showDetail(this.dataListFinished[0])
      } else if (this.dataListUnderway.length > 0) {
        this.showDetail(this.dataListUnderway[0])
      }
    },

    // 右侧列表数据分页请求
    async queryRightList() {
      this.loading = true;
      this.infiniteDisabled = true;

      let params = JSON.parse(JSON.stringify(this.searchForm))
      if (!this.isEmpty(this.searchForm.takeEffectDate)) {
        params.beginTime = this.searchForm.takeEffectDate[0]
          ? this.searchForm.takeEffectDate[0]
          : "";
        params.comTime = this.searchForm.takeEffectDate[1]
          ? this.searchForm.takeEffectDate[1]
          : "";
      }
      delete params.takeEffectDate

      if (!(params.experimentStatusList && params.experimentStatusList.length > 0)) {
        params.experimentStatusList = this.experimentStatusRight;
      }

      try {
        let res = await this.$api.getExperimentList({
          pageNum: this.pageRightNum,
          pageSize: 10,
          params: {
            experimentTypeList: [7],
            isFinish: 1,
            ...params,
          }
        }, this);

        this.loading = false;
        this.infiniteDisabled = false;

        if (res.succ) {
          if (this.pageRightNum == 1) {
            this.dataListFinished = res.data.list;
          } else {
            this.dataListFinished = this.dataListFinished.concat(res.data.list);
          }
          // 存储所有的数据id，用来查找下标
          this.dataListFinishedByIds = [];
          for (const item of this.dataListFinished) {
            this.dataListFinishedByIds.push(item.id)
          }

          this.totalRightCount = res.data.total;
          this.pageRightNum = res.data.nextPage == 0 ? 1 : res.data.nextPage;
        } else {
          this.$message.error(res.msg || '查询失败')
        }
      } catch (error) {
        this.loading = false;
        this.infiniteDisabled = false;
        console.log(">>>>>>>", error);
      }
    },

    pollingExperimentAllCount() {
      if (!this.pollTimer) {
        this.pollTimer = setInterval(() => {
          this.getExperimentAllCountResp()
          this.queryExperimentMessageResp();
        }, 60000);

        this.getExperimentAllCountResp()
        this.queryExperimentMessageResp();
      }
    },
    // 查询总数
    getExperimentAllCountResp() {
      this.$api.getExperimentAllCount({
        id: this.lastId,
        experimentStatusList: [0, 1, 2],
        experimentType: 7,
      }, this).then(res => {
        if (res.succ) {
          this.diffExperimentCount = res.data.totalCount;
        }
      })
    },

    queryExperimentMessageResp() {
      this.$api.queryExperimentMessage({
        id: this.lastMesgId,
        // experimentStatusList: [0,1,2],
        state: 1,
        experimentTypeList: [7],
      }, this).then(res => {
        if (res.succ && res.data.list.length > 0) {
          const newMessages = res.data.list.filter(message =>
            !this.notifications.find(n => n.id === message.id)
          );
          newMessages.forEach(this.showNotification);
          this.notifications = [...this.notifications, ...newMessages];

          this.lastMesgId = res.data.list[0].id;
        }
      })
    },

    showNotification(message) {
      setTimeout(() => {
        if (this.notifyAlerts[message.experimentNo]) return;
        let notify = new Notification({
          title: '新消息',
          position: 'bottom-right',
          duration: 0, // 不自动消失
          message: `您有一条${message.experimentTypeName}委托（${message.experimentNo}）!`,
          customClass: "experiment-notification",
          onClick: () => this.markAsRead(message),
          onClose: () => this.markAsRead(message),
        });
        this.notifyAlerts[message.experimentNo] = notify;
      }, 500);
    },

    markAsRead(message) {
      this.showDetail({ id: message.experimentId });

      this.$api.updateExperimentMessage({
        id: message.id
      }).then(res => {
        if (res.succ) {
          let index = this.notifications.findIndex(n => n.id === message.id);
          // notifications 删除该item
          this.notifications.splice(index, 1);
          // notifyAlerts 删除该item
          this.notifyAlerts[message.experimentNo].close();
          this.notifyAlerts[message.experimentNo] = undefined;
        }
      })
    },

    showDetail(row, index) {
      console.log('打开详情:', row);
      const str = row.testProjectNameStr || '';
      this.isLLZProject = str.includes('氯离子含量'); // 返回布尔值
      this.activeId = row.id;
      this.getExperimentById(row.id);

      this.experimentViewNewTaskResp(row);
    },
    experimentViewNewTaskResp(item) {
      this.$api.experimentViewNewTask({
        id: item.id
      }, this).then(res => {
        this.getTaskInfo(1);
        
        if (res.succ) {
          let index = -1 
          for (let i = 0; i < this.dataListUnderway.length; i++) {
            if (this.dataListUnderway[i].id == item.id) {
              index = i;
              break;
            }
          }
          if (index > -1) {
            this.$set(this.dataListUnderway[index], 'isNewTask', 0)
            return;
          }
          if (index == -1) {
            for (let i = 0; i < this.dataListFinished.length; i++) {
              if (this.dataListFinished[i].id == item.id) {
                index = i;
                break;
              }
            }
          }
          if (index > -1) {
            this.$set(this.dataListFinished[index], 'isNewTask', 0)
          }
        }else{
          this.$message.error(res.msg || '更新失败')
        }
      })
    },
    //获取详情
    getExperimentById(id = this.activeId) {
      this.$api.getExperimentById(`id=${id}`, this).then(res => {
        this.loading = false;
        if (res.succ) {
          this.activeData = res.data;
          this.$set(this.testInfoForm, 'entrustTime', this.activeData.entrustTime);
          this.$set(this.testInfoForm, 'reportDate', this.activeData.reportDate);
          this.$set(this.testInfoForm, 'admissionTime', this.activeData.admissionTime);

          let uIndex = this.dataListUnderwayByIds.indexOf(id);
          let fIndex = this.dataListFinishedByIds.indexOf(id);
          if (uIndex > -1) {
            //0-待接收  1-待取样  2-试验中;  3-已完成 4-已拒绝

            // 代表该数据应该去 dataListFinishedByIds 数组中，但是当前页面列表未刷新，所以手动改下两边列表数据
            if (this.activeData.experimentStatus == 3 || this.activeData.experimentStatus == 4) {
              this.dataListUnderwayByIds.splice(uIndex, 1);
              this.dataListUnderway.splice(uIndex, 1);

              this.dataListFinished.unshift(this.activeData);
              this.dataListFinishedByIds.unshift(this.activeData.id);

              this.$forceUpdate();
            } else {
              this.$set(this.dataListUnderway, uIndex, this.activeData);
            }
          } else if (fIndex > -1) {
            this.$set(this.dataListFinished, fIndex, this.activeData);
          }

          this.testInfoForm = JSON.parse(JSON.stringify(res.data));
          // 获取对应配合信息
          this.queryMixProportionByIdResp(res.data['phb']);
          if (!this.testInfoForm.curingMode) {
            this.testInfoForm.curingMode = '标准养护'
          }
          if (!this.testInfoForm.isQualified) {
            this.testInfoForm.isQualified = 1;
          }
          //if(!){
          //获取仪器设备
          this.getEquipmentData()
          //}
          //获取最新温湿度
          if (!res.data.temperature || !res.data.humidness) {
            this.getHumitureData();
          }
          if (res.data.experimentStatus == 1 || res.data.experimentStatus == 2 || res.data.experimentStatus == 3) {
            this.$nextTick(() => {
              this.$refs.sampleMgt.initData();
            })
          }
          if (res.data.experimentStatus == 2 || res.data.experimentStatus == 3) {
            this.$nextTick(() => {
              this.$refs.formTab.clearData();
              this.$refs.formTab.setExperimentProject()
            })
          }
        } else {
          this.$message.error(res.msg || '查询失败')
        }
      })
    },
    //获取最新温湿度
    getHumitureData() {
      console.log(this.testInfoForm.dictValueCode)
      this.$api.getHumitureData({}, this).then(res => {
        if (res.succ) {
          if (!this.testInfoForm.temperature) {
            this.testInfoForm.temperature = res.data.temperature
          }
          if (!this.testInfoForm.humidness) {
            this.testInfoForm.humidness = res.data.humidness
          }
        } else {
          this.$message.error(res.msg || '查询失败')
        }
      })
    },
    //获取仪器设备
    getEquipmentData() {
      console.log(this.testInfoForm.dictValueCode)
      this.$api.getEquipmentInfoByExperimentType(`experimentType=7`, this).then(res => {
        if (res.succ) {
          if (!this.testInfoForm.equipment) {
            let oName = res.data.list.map(item => item.equipmentName)
            this.testInfoForm.equipment = oName.join('、');
          }
          this.equipmentOption = res.data.list;
        } else {
          this.$message.error(res.msg || '查询失败')
        }
      })
    },

    //获取任务信息
    getTaskInfo(opageNum, opageSize) {
      // this.$api.getTaskInfo(`experimentId=${id}`, this).then(res => {
      //   if(res.succ){
      //     this.taskData = res.data
      //   }else{
      //     this.$message.error(res.msg || '查询失败')
      //   }
      // })
      if (opageNum) this.pageObj.pageNum = opageNum;
      if (opageSize) this.pageObj.pageSize = opageSize;

      const params = {
        ...this.pageObj,
        params: {
          experimentId: this.activeId
        }
      }
      this.$api.getTaskInfo3(params, this).then(res => {
        if (res.succ) {
          this.taskData = res.data.list.list
          this.tasktotal = res.data.list.total;
        } else {
          this.$message.error(res.msg || '查询失败')
        }
      })
    },

    showEditSampleIdDialogClick() {
      this.showEditSampleIdDialog = true;
      this.editSampleIdForm = {
        experimentId: this.activeData.id,
        sampleId: this.activeData.sampleId,
        consignId: this.activeData.consignId,
      }
    },
    editSampleIdSave() {
      this.$api.submitShxhSynchronizedata(this.editSampleIdForm, this).then(res => {
        if (res.code == 1) {
          this.$message({
            showClose: true,
            message: "修改成功",
            type: "success",
          });
          this.showEditSampleIdDialog = false;
          this.showDetail({
            id: this.activeData.id
          })
        }
      })
    },

    // 根据任务单展示异常工单
    showWorkOrders(trwdId) {
      this.$refs.workTableDialog.initData('getWorkOrderAllList', { trwdId, isexcep: 1 })
    },
    // 前往工单详情
    gotoWorkDetail(event) {
      this.$router.push({
        path: '/engineeringService/workOrderDetial',
        query: {
          id: event.id
        }
      })
    },

    isEmpty(val) {
      if (typeof val === "boolean") {
        return false;
      }
      if (typeof val === "number") {
        return false;
      }
      if (val instanceof Array) {
        if (val.length === 0) return true;
      } else if (val instanceof Object) {
        if (JSON.stringify(val) === "{}") return true;
      } else {
        if (
          val === "null" ||
          val == null ||
          val === "undefined" ||
          val === undefined ||
          val === ""
        )
          return true;
        return false;
      }
      return false;
    },

    //接受，完成 拒绝
    handleSetState(type) {
      let tip = ''
      if (type == 'setExperimentAccept') {
        tip = '确定接收： ‘' + this.activeData.experimentNo + '’ 吗？'
      } else if (type == 'setExperimentFinish') {
        tip = '您确认做完所检试验项目并完成此试验委托吗？'
      } else if (type == 'setExperimentRefuse') {
        tip = '确定拒绝： ‘' + this.activeData.experimentNo + '’ 吗？'
      }

      this.$confirm(tip, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.$api[type]({
          id: this.activeId
        }, this).then((res) => {
          if (res.succ) {
            this.$message({
              showClose: true,
              message: "操作成功",
              type: "success",
            });

            this.showDetail({
              id: this.activeId
            })
          } else {
            this.$message({
              showClose: true,
              message: res.msg,
              type: "error",
            });
          }
        });
      }).catch((rej) => {
        console.log(rej)
        this.$message({
          type: "info",
          message: "已取消",
        });
      });
    },
    handleClick(tab, event) {
      console.log(tab, event, this.activeName)

    },
    goDetail() {
      this.$router.push({
        path: '/qualityControl/concretePanelList'
      })
    },

    goTaskDetail(row) {
      this.$router.push({
        path: '/qualityControl/panelListDetail',
        query: {
          taskId: row.id,
          experimentId: this.activeId,
          frwdh: row.frwdh,
          experimentType: 7,
        }
      })
    },

    deleteTask(row) {
      this.$confirm("确定要删除吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        //删除
        this.$api.deleteByExperimentIdAndTaskId({
          experimentId: this.activeId,
          taskId: row.frwdh
        }, this).then((res) => {
          if (res.succ) {
            this.$message({
              showClose: true,
              message: "删除成功",
              type: "success",
            });
            this.getTaskInfo();
          }
        });
      });
    },

    //绑定运单
    selectInvoice() {
      this.$refs.tableDialog.initData('getBindPageXP', {
        stateList: [2, 3]
      }, this.activeData.id, '7')
    },
    add(a, b) {
      var c, d, e;
      var that = this;
      try {
        c = a.toString().split(".")[1].length;
      } catch (f) {
        c = 0;
      }
      try {
        d = b.toString().split(".")[1].length;
      } catch (f) {
        d = 0;
      }
      return e = Math.pow(10, Math.max(c, d)), (that.mul(a, e) + that.mul(b, e)) / e;
    },
    sub(a, b) {
      var c, d, e;
      var that = this;
      try {
        c = a.toString().split(".")[1].length;
      } catch (f) {
        c = 0;
      }
      try {
        d = b.toString().split(".")[1].length;
      } catch (f) {
        d = 0;
      }
      return e = Math.pow(10, Math.max(c, d)), (that.mul(a, e) - that.mul(b, e)) / e;
    },
    mul(a, b) {
      var c = 0,
        d = a.toString(),
        e = b.toString();
      try {
        c += d.split(".")[1].length;
      } catch (f) { }
      try {
        c += e.split(".")[1].length;
      } catch (f) { }
      return Number(d.replace(".", "")) * Number(e.replace(".", "")) / Math.pow(10, c);
    },
    div(a, b) {
      var that = this;
      var c, d, e = 0,
        f = 0;
      try {
        e = a.toString().split(".")[1].length;
      } catch (g) { }
      try {
        f = b.toString().split(".")[1].length;
      } catch (g) { }
      return c = Number(a.toString().replace(".", "")), d = Number(b.toString().replace(".", "")), that.mul(c / d, Math.pow(10, f - e));
    },
  },
};
</script>


<style scoped lang="scss" src='./components/board.scss'></style>
<style scoped lang="scss">
::v-deep .el-upload-list__item {
  transition: none !important;

  .el-upload-list__item-status-label {
    display: none;
  }
}

.el-form-item {
  margin-bottom: 8px;
}

::v-deep .el-form--inline {
  .el-form-item {
    margin-right: 24px;
    margin-bottom: 0;

    &:last-child {
      margin: 0;
    }
  }
}

.el-input {
  height: 40px;
}

.consign-null {
  background-color: #F3E2DF;
}
</style>