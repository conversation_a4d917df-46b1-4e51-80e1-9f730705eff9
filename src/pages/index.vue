<template>
  <div class="flex-box flex-column h100p">
    <div class="header">
      <Header></Header>
      <Navigation ref="navigate"></Navigation>
    </div>
    <router-view v-if="isRouterShow" class="flex-item container-height"></router-view>
  </div>
</template>

<script>
import Header from '@/components/head/Header.vue'
import Navigation from '@/components/head/Navigation.vue'
export default {
  components: {
    Header,
    Navigation
  },
  provide () {
    return {
      reload: this.reload  //把方法传递给下级组件
    }
  },
  data() {
    return {
      isRouterShow: true,
    }
  },
  methods: {
    async reload () {  //触发显示隐藏
      this.isRouterShow = false
      await this.$nextTick()
      this.isRouterShow = true
    },
  },
  beforeRouteLeave(to, from, next) {
    if (to.path === '/login') {
      this.$store.dispatch('tagsView/delAllVisitedViews')
    }
    next()
  },
  created: function () {}
}
</script>

<style scoped lang="scss">
.header {
  width: 100%;
  background: linear-gradient(90deg, #4c4077 0%, #385870 100%);
}
.container-height {
  height: calc(100% - 98px);
}
</style>