<!--
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-05-29 23:45:07
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-08-10 22:41:26
 * @FilePath: /quality_center_web/src/pages/mixProportion/addMinProportion.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="content-box">
    <div class="flex-box flex-column content">
      <el-col :span="24">
        <el-row class="row-title">
          <el-button size="small" type="primary" class="fr" @click="addProportion">保存</el-button>
          <span class="row-title-label">试配记录</span>
        </el-row>
        <el-row style="margin-top: 24px; width: 100%;">
          <el-form :model="params" style="width: 100%;" label-width="100px">
            <el-row class="flex-row-start">
              <el-form-item label="设计依据：" prop="proportionSjyj">
                <el-input v-model="params.proportionSjyj" placeholder="请输入设计依据"></el-input>
              </el-form-item>
              <el-form-item label="配比ID：" prop="erpPhbId">
                <el-input 
                  v-model="params.erpPhbId"
                  :disabled="params.id && $route.query.actType !== 'copy'"
                  placeholder="请输入配比ID">
                </el-input>
              </el-form-item>
              <el-form-item style="margin-left: 10px;" label="配合比编号：" prop="proportionPhb">
                <el-input v-model="params.proportionPhb" placeholder="配合比编号"></el-input>
              </el-form-item>
              <el-form-item class="form-inline" label="报告日期：">
                <el-row class="flex-row">
                  <el-date-picker type="date" 
                    v-model="params.reportDate" 
                    format="yyyy-MM-dd" value-format="yyyy-MM-dd HH:mm:ss"
                    :clearable="true" style="width: 240px"
                  ></el-date-picker>
                </el-row>
              </el-form-item>
              <el-form-item label="龄期：" prop="proportionLq">
                <div class="flex-row">
                  <el-input type="number" v-model="params.proportionLq" placeholder="龄期"></el-input>
                  <span style="padding-left: 10px; padding-right: 10px">天</span>
                </div>
              </el-form-item>
            </el-row>
            
            <el-row class="flex-row" style="width: 100%; justify-content: flex-start">
              <template v-for="(item, index) in Object.keys(designFrom)">
                <el-form-item v-if="index > 1 && index < 4" :key="item" class="form-inline" style="margin-right: 30px;"
                  :label="designFrom[`${item}`].label">
                  
                  <el-input v-if="designFrom[`${item}`].label === '抗折强度'" v-model="params[`${item}`]" placeholder="请输入"></el-input>
                  <el-select v-else v-model="params[`${item}`]" filterable clearable placeholder="请选择">
                    <el-option v-for="op in designFrom[item].opts" :key="item + op.label" :label="op.label"
                      :value="op.value">
                    </el-option>
                  </el-select>
                </el-form-item>
              </template>

              <el-form-item class="form-inline" label="强氯离子：" style="margin-left: 30px;">
                <el-row class="flex-row">
                  <el-input v-model="params.proportionQllz" placeholder="请输入"></el-input>
                  <span style="padding-left: 10px;">%</span>
                </el-row>
              </el-form-item>

              <el-form-item class="form-inline" label="搅拌时间：" style="margin-left: 30px;">
                <el-row class="flex-row">
                  <el-input v-model="params.proportionJbsj" placeholder="请输入"></el-input>
                  <span style="padding-left: 10px;">秒</span>
                </el-row>
              </el-form-item>
            </el-row>
            
            <el-row class="flex-row-start">
              <el-form-item class="form-inline" label="设计坍落度：">
                <el-row class="flex-row">
                  <el-input v-model="params.proportionTld.qz" placeholder="请输入" style="width: 120px;"></el-input>
                  <span style="padding-left: 10px; padding-right: 10px">±</span>
                  <el-input v-model="params.proportionTld.hz" placeholder="请输入" style="width: 120px;"></el-input>
                </el-row>
              </el-form-item>

              <el-form-item class="form-inline" style="margin-right: 30px;"
                :label="designFrom.proportionZjx.label">
                <el-select v-model="params.proportionZjx" filterable clearable placeholder="请选择">
                  <el-option v-for="op in designFrom.proportionZjx.opts" :key="op.label" :label="op.label"
                    :value="op.value">
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item class="form-inline" style="margin-right: 30px;"
                :label="designFrom.proportionBsx.label">
                <el-select v-model="params.proportionBsx" filterable clearable placeholder="请选择">
                  <el-option v-for="op in designFrom.proportionBsx.opts" :key="op.label" :label="op.label"
                    :value="op.value">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-row>

            <el-row class="flex-row" style="width: 100%; justify-content: flex-start">
              <el-form-item class="form-inline" label="抗渗等级：">
                <el-select v-model="params.proportionKsdj" @change="changeProportionKsdj" filterable clearable placeholder="请选择">
                  <el-option v-for="op in proportionKsdj.opts" :key="op.label" :label="op.label"
                    :value="op.value">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item style="margin-left: 30px;" label-width="0">
                <el-checkbox :true-label="1" :false-label="0" :disabled="!params.proportionKsdj || !params.proportionPhb" v-model="params.backwardCompatible" @change="changeNewRatio">是否向下兼容抗渗等级</el-checkbox>
              </el-form-item>
              <el-form-item style="margin-left: 10px;" label-width="0">
                <el-input :readonly="true" style="width: 600px;" :value="params.newRatioString"  />
              </el-form-item>

              <el-form-item label="备注：" prop="proportionRemarks">
                <el-input v-model="params.proportionRemarks" placeholder="请输入备注"
                  style="width: 400px"></el-input>
              </el-form-item>
            </el-row>
          </el-form>
        </el-row>
      </el-col>
      


      
      <div class="row-title flex-row" style="justify-content: space-between;">
        <span class="row-title-label">对应试验报告</span>
        <span>
          <span>生产：容重：{{ params.productionRz | isNull }}kg，水胶比：{{ params.productionSjb | isNull }}，砂率：{{ params.productionSl | isNull }}%，外加剂掺料：{{ params.productionCl | isNull}}%</span>
          <span style="margin-left: 30px;">打印：容重：{{ params.printRz | isNull}}kg，水胶比：{{ params.printSjb | isNull}}，砂率：{{ params.printSl | isNull}}%，外加剂掺料：{{ params.printCl | isNull}}%</span>
        </span>
      </div>
      <div>
        <el-table :data="tableData"
          class="mt16"
          border :max-height="700"
          ref="tableDataDom" 
          style="width: 100%;"
        >
        	
        	<el-table-column 
            v-for="item in tableColumn" 
            :key="item.prop" :prop="item.prop" 
            :label="item.label" 
            :fixed="item.fixed" 
            :width="item.width || ''"
            :formatter="item.formatter"
            align="center" 
            :resizable="false" 
            :show-overflow-tooltip="true"
          />
        	<el-table-column  width="300" label="操作" align="center" key="handle" :resizable="false">
        		<template slot-scope="scope">
              <el-button v-if="(scope.row.key != 'water' && scope.row.key != 'wcl1' && scope.row.key != 'wcl2')" type="text" size="small" @click="handleSetTarget(scope.row, scope.$index)">选择原材料</el-button>
        			<el-button style="margin-left: 30px;" type="text" size="small" @click="setConsumptionTarget(scope.row, scope.$index)">设置用量</el-button>
              <el-button style="margin-left: 30px;color: #ff0000;" type="text" size="small" @click="resetConsumptionTarget(scope.row, scope.$index)">删除</el-button>
        		</template>
        	</el-table-column>
        </el-table>
      </div>
    </div>
    
    
    
    
    <el-dialog
      title="选择原材料报告"
      :visible.sync="validationVisible"
      class="claim-dialog-box"
      :before-close="handleClose"
      width="90%"
      top="20px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div>
        <panelComponent ref="panel" @setSelectInfo="setSelectInfo">
        </panelComponent>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose" size="small">关闭</el-button>
      </span>
    </el-dialog>

    <el-drawer
        title="设置用量"
        :visible.sync="showConsumption"
        direction="rtl"
        :before-close="handleConsumptionClose"
    >
    <div class="flex-box flex-column h100p drawer-box">
          <div class="flex-item ofy-auto">
              <el-form label-width="160px" 
                  :model="editForm"
              >
              <el-form-item
                  v-for="(el,index) in consumptionForm"
                  :key="index"
                  :label="`${el.label}：`"
                  :required="el.required || false"
              >
                <el-input 
                      v-if="el.type === 'input'"
                      v-model="editForm[el.prop]" 
                      :disabled="el.disabled"
                      clearable
                      :placeholder="el.placeholder || `请输入${el.label}`"
                      style="width: 350px"
                      oninput ="value=value.replace(/[^0-9.]/g,'')"
                      @change="handleConsumptionChange($event, el.prop)"
                  />
              </el-form-item>

              <template v-if="editForm.prop === 'water'">
                  <el-form-item label="材料名称：" prop="clmc">
                    <el-input 
                        v-model="editForm.clmc" 
                        clearable
                        :placeholder="`请输入材料名称`"
                        style="width: 350px"
                        @change="handleConsumptionChange($event, 'clmc')"
                    />
                  </el-form-item>
                  <el-form-item label="材料规格：" prop="clgg">
                    <el-input 
                        v-model="editForm.clgg" 
                        clearable
                        :placeholder="`请输入材料规格`"
                        style="width: 350px"
                        @change="handleConsumptionChange($event, 'clgg')"
                    />
                  </el-form-item>
              </template>

              <template  v-else-if="editForm.prop === 'wcl1' || editForm.prop === 'wcl2'">
                <el-form-item label="材料名称：" prop="wlmc">
                  <el-input 
                      v-model="editForm.wlmc" 
                      clearable
                      :placeholder="`请输入材料名称`"
                      style="width: 350px"
                      :disabled="true"
                      @change="handleConsumptionChange($event, 'wlmc')"
                  />
                </el-form-item>
                <el-form-item label="材料规格：" prop="wlgg">
                  <el-select
                      v-model="editForm.wlObj"
                      @change="changeMaterialSpec"
                      filterable clearable 
                      :placeholder="'请选择'"
                      value-key="wlgg"
                      style="width: 350px"
                  >
                      <el-option
                          v-for="item in materialSpecOpts"
                          :key="item.label"
                          :label="item.label"
                          :value="item.value">
                      </el-option>
                  </el-select>
                </el-form-item>

                <el-form-item  label="供应商：" prop="gys">
                  <el-select
                      v-model="editForm.gys"
                      @change="changeGys"
                      filterable clearable 
                      value-key="supplierName"
                      :placeholder="'请选择'"
                      style="width: 350px"
                  >
                      <el-option
                          v-for="item in supplierCompanyOpts"
                          :key="item.label"
                          :label="item.label"
                          :value="item.value">
                      </el-option>
                  </el-select>
                </el-form-item>

                <el-form-item  label="厂家：" prop="cj">
                  <el-select
                      v-model="editForm.cj"
                      @change="changeCj"
                      filterable clearable 
                      value-key="manufacturers"
                      :placeholder="'请选择'"
                      style="width: 350px"
                  >
                      <el-option
                          v-for="item in cjOpts"
                          :key="item.label"
                          :label="item.label"
                          :value="item.value">
                      </el-option>
                  </el-select>
                </el-form-item>
              </template>
              
            </el-form>
          </div>
          <div class="drawer-footer">
              <el-button type="primary" @click="showConsumption = false" plain>取消</el-button>
              <el-button type="primary" @click="saveConsumptionTarget">保存</el-button>
          </div>
      </div>
    </el-drawer>

    <el-dialog
      :visible.sync="showNewRatio"
      width="30%"
      :before-close="() => showNewRatio = false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div class="flex-col" style="align-items: flex-start; padding-left: 30px;">
        <el-row class="flex-row" style="margin-bottom: 10px;">
          <div style="width: 100px;">当前配合比：</div>
          <el-input v-model="params.proportionPhb" :readonly="true" style="width: 120px;"/>
          <span style="margin-left: 10px;">{{ params.proportionKsdj }}</span>
        </el-row>
        
        <el-row class="flex-row" v-for="(item, index) in params.newRatio" :key="index" style="margin-bottom: 10px;">
          <div style="width: 100px;">配合比编号{{ index+1 }}：</div>
          <el-input v-model="item.xpebbh" style="width: 120px;"/>
          <el-select v-model="item.xksdj" filterable clearable placeholder="请选择">
            <el-option v-for="op in ksdjOpts" :key="op.label" :label="op.label"
              :value="op.value">
            </el-option>
          </el-select>
        </el-row>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="() => showNewRatio = false">取 消</el-button>
        <el-button type="primary" @click="sureNewRatio">确 定</el-button>
      </span>
    </el-dialog>

  </div>
</template>

<script>
  import {
    addMixed
  } from './addMixed.js'
  import {calcEquation, cpEvenRound} from "@/utils/calculate.js"
  import panelComponent from './panelComponent'
  import moment from 'moment';
  export default {
    mixins: [addMixed],
    inject: ['reload'],
    components: {
      panelComponent
    },
    data() {
      return {
        materialType: '',
        selectIndex: 0,
        validationVisible: false,
        showNewRatio: false,
        
        tableColumn: [
          {
            prop: "name",
            label: "原材料名称",
          },
          {
            prop: "json.xhbgbh",
            label: "协会报告编号",
          },
          {
            prop: "json.batch",
            label: "批号",
          },
          {
            prop: "json.certificateNo",
            label: "备案证号",
          },
          // {
          //   prop: "json.ypmc",
          //   label: "样品名称",
          // },
          // {
          //   prop: "json.ypdj",
          //   label: "等级",
          // },
          // {
          //   prop: "json.ypgg",
          //   label: "规格",
          // },
          // {
          //   prop: "json.cllxName",
          //   label: "材料类型",
          //   formatter: function(row, column) {
          //     return row.json.cllxName || '--'
          //   }
          // },
          {
            prop: "json.clmc",
            label: "材料名称",
            formatter: function(row, column) {
              return (row.json.clmc || '--') + `${row.json.wlmc ? '(' + row.json.wlmc + ')' : ''}`
            }
          },
          {
            prop: "json.clgg",
            label: "材料规格",
            formatter: function(row, column) {
              return (row.json.clgg || '--') + `${row.json.wlgg ? '(' + row.json.wlgg + ')' : ''}`
            }
          },
          {
            prop: "json.gys",
            label: "供应商",
          },
          {
            prop: "json.cj",
            label: "厂家",
          },
          {
            prop: "json.scyl",
            label: "生产用量",
          },
          {
            prop: "json.scylb",
            label: "生产用量比例",
          },
          {
            prop: "json.dyyl",
            label: "打印用量",
          },
          {
            prop: "json.dyylb",
            label: "打印用量比例",
          },
        ],
        tableData: [
          {
            name: '水泥',
            key: 'sn',
            type: 1,
            json: {
              "batch": "",
              "certificateNo": "",
              "pktzid": "",
              "xhbgbh": "",
              // "ypmc": "",
              // "ypdj": "",
              // "ypgg": "",
              "clmc": "",
              "cllx": "",
              "clgg": "",
              "wlmc": "",
              "wllx": "",
              "wlgg": "",
              "cj": "",
              "cjjc": "",
              "gys": "",
              "gysjc": "",
              "scyl":"",
              "scylb":"1.00",
              "dyyl":"",
              "dyylb":"1.00",
            }
          },
          {
            name: "水",
            key: 'water',
            type: -1,
            json: {
              "batch": "",
              "certificateNo": "",
              "pktzid": "",
              "xhbgbh": "",
              // "ypmc": "",
              // "ypdj": "",
              // "ypgg": "",
              "clmc": "",
              "cllx": "",
              "clgg": "",
              "wlmc": "",
              "wllx": "",
              "wlgg": "",
              "cj": "",
              "cjjc": "",
              "gys": "",
              "gysjc": "",
              "scyl":"",
              "scylb":"",
              "dyyl":"",
              "dyylb":""
            }
          },
          {
            name: '粉煤灰',
            key: 'fmh',
            type: 2,
            json: {
              "batch": "",
              "certificateNo": "",
              "pktzid": "",
              "xhbgbh": "",
              // "ypmc": "",
              // "ypdj": "",
              // "ypgg": "",
              "clmc": "",
              "cllx": "",
              "clgg": "",
              "wlmc": "",
              "wllx": "",
              "wlgg": "",
              "cj": "",
              "cjjc": "",
              "gys": "",
              "gysjc": "",
              "scyl":"",
              "scylb":"",
              "dyyl":"",
              "dyylb":""
            }
          },
          {
            name: '矿渣粉',
            key: 'kzf',
            type: 3,
            json: {
              "batch": "",
              "certificateNo": "",
              "pktzid": "",
              "xhbgbh": "",
              // "ypmc": "",
              // "ypdj": "",
              // "ypgg": "",
              "clmc": "",
              "cllx": "",
              "clgg": "",
              "wlmc": "",
              "wllx": "",
              "wlgg": "",
              "cj": "",
              "cjjc": "",
              "gys": "",
              "gysjc": "",
              "scyl":"",
              "scylb":"",
              "dyyl":"",
              "dyylb":""
            }
          },
          {
            name: '粗骨料',
            key: 'cgl',
            type: 4,
            json: {
              "batch": "",
              "certificateNo": "",
              "pktzid": "",
              "xhbgbh": "",
              // "ypmc": "",
              // "ypdj": "",
              // "ypgg": "",
              "clmc": "",
              "cllx": "",
              "clgg": "",
              "wlmc": "",
              "wllx": "",
              "wlgg": "",
              "cj": "",
              "cjjc": "",
              "gys": "",
              "gysjc": "",
              "scyl":"",
              "scylb":"",
              "dyyl":"",
              "dyylb":""
            }
          },
          {
            name: '细骨料',
            key: 'xgl',
            type: 5,
            json: {
              "batch": "",
              "certificateNo": "",
              "pktzid": "",
              "xhbgbh": "",
              // "ypmc": "",
              // "ypdj": "",
              // "ypgg": "",
              "clmc": "",
              "cllx": "",
              "clgg": "",
              "wlmc": "",
              "wllx": "",
              "wlgg": "",
              "cj": "",
              "cjjc": "",
              "gys": "",
              "gysjc": "",
              "scyl":"",
              "scylb":"",
              "dyyl":"",
              "dyylb":""
            }
          },
          {
            name: '外加剂1',
            key: 'wjj1',
            type: 6,
            json: {
              "batch": "",
              "certificateNo": "",
              "pktzid": "",
              "xhbgbh": "",
              // "ypmc": "",
              // "ypdj": "",
              // "ypgg": "",
              "clmc": "",
              "cllx": "",
              "clgg": "",
              "wlmc": "",
              "wllx": "",
              "wlgg": "",
              "cj": "",
              "cjjc": "",
              "gys": "",
              "gysjc": "",
              "scyl":"",
              "scylb":"",
              "dyyl":"",
              "dyylb":""
            }
          },
          {
            name: '外加剂2',
            key: 'wjj2',
            type: 6,
            json: {
              "batch": "",
              "certificateNo": "",
              "pktzid": "",
              "xhbgbh": "",
              // "ypmc": "",
              // "ypdj": "",
              // "ypgg": "",
              "clmc": "",
              "cllx": "",
              "clgg": "",
              "wlmc": "",
              "wllx": "",
              "wlgg": "",
              "cj": "",
              "cjjc": "",
              "gys": "",
              "gysjc": "",
              "scyl":"",
              "scylb":"",
              "dyyl":"",
              "dyylb":""
            }
          },
          {
            name: '外掺料1',
            key: 'wcl1',
            type: 2,
            json: {
              "batch": "",
              "certificateNo": "",
              "pktzid": "",
              "xhbgbh": "",
              // "ypmc": "",
              // "ypdj": "",
              // "ypgg": "",
              "clmc": "",
              "cllx": "6",
              "clgg": "",
              "wlmc": "膨胀剂",
              "wllx": "",
              "wlgg": "",
              "cj": "",
              "cjjc": "",
              "gys": "",
              "gysjc": "",
              "scyl":"",
              "scylb":"",
              "dyyl":"",
              "dyylb":""
            }
          },
          {
            name: '外掺料2',
            key: 'wcl2',
            type: 3,
            json: {
              "batch": "",
              "certificateNo": "",
              "pktzid": "",
              "xhbgbh": "",
              // "ypmc": "",
              // "ypdj": "",
              // "ypgg": "",
              "clmc": "",
              "cllx": "6",
              "clgg": "",
              "wlmc": "纤维",
              "wllx": "",
              "wlgg": "",
              "cj": "",
              "cjjc": "",
              "gys": "",
              "gysjc": "",
              "scyl":"",
              "scylb":"",
              "dyyl":"",
              "dyylb":""
            }
          },
        ],
        tableDataOrg: [
          {
            name: '水泥',
            key: 'sn',
            type: 1,
            json: {
              "batch": "",
              "certificateNo": "",
              "pktzid": "",
              "xhbgbh": "",
              // "ypmc": "",
              // "ypdj": "",
              // "ypgg": "",
              "clmc": "",
              "cllx": "",
              "clgg": "",
              "wlmc": "",
              "wllx": "",
              "wlgg": "",
              "cj": "",
              "cjjc": "",
              "gys": "",
              "gysjc": "",
              "scyl":"",
              "scylb":"1.00",
              "dyyl":"",
              "dyylb":"1.00",
            }
          },
          {
            name: "水",
            key: 'water',
            type: -1,
            json: {
              "batch": "",
              "certificateNo": "",
              "pktzid": "",
              "xhbgbh": "",
              // "ypmc": "",
              // "ypdj": "",
              // "ypgg": "",
              "clmc": "",
              "cllx": "",
              "clgg": "",
              "wlmc": "",
              "wllx": "",
              "wlgg": "",
              "cj": "",
              "cjjc": "",
              "gys": "",
              "gysjc": "",
              "scyl":"",
              "scylb":"",
              "dyyl":"",
              "dyylb":""
            }
          },
          {
            name: '粉煤灰',
            key: 'fmh',
            type: 2,
            json: {
              "batch": "",
              "certificateNo": "",
              "pktzid": "",
              "xhbgbh": "",
              // "ypmc": "",
              // "ypdj": "",
              // "ypgg": "",
              "clmc": "",
              "cllx": "",
              "clgg": "",
              "wlmc": "",
              "wllx": "",
              "wlgg": "",
              "cj": "",
              "cjjc": "",
              "gys": "",
              "gysjc": "",
              "scyl":"",
              "scylb":"",
              "dyyl":"",
              "dyylb":""
            }
          },
          {
            name: '矿渣粉',
            key: 'kzf',
            type: 3,
            json: {
              "batch": "",
              "certificateNo": "",
              "pktzid": "",
              "xhbgbh": "",
              // "ypmc": "",
              // "ypdj": "",
              // "ypgg": "",
              "clmc": "",
              "cllx": "",
              "clgg": "",
              "wlmc": "",
              "wllx": "",
              "wlgg": "",
              "cj": "",
              "cjjc": "",
              "gys": "",
              "gysjc": "",
              "scyl":"",
              "scylb":"",
              "dyyl":"",
              "dyylb":""
            }
          },
          {
            name: '粗骨料',
            key: 'cgl',
            type: 4,
            json: {
              "batch": "",
              "certificateNo": "",
              "pktzid": "",
              "xhbgbh": "",
              // "ypmc": "",
              // "ypdj": "",
              // "ypgg": "",
              "clmc": "",
              "cllx": "",
              "clgg": "",
              "wlmc": "",
              "wllx": "",
              "wlgg": "",
              "cj": "",
              "cjjc": "",
              "gys": "",
              "gysjc": "",
              "scyl":"",
              "scylb":"",
              "dyyl":"",
              "dyylb":""
            }
          },
          {
            name: '细骨料',
            key: 'xgl',
            type: 5,
            json: {
              "batch": "",
              "certificateNo": "",
              "pktzid": "",
              "xhbgbh": "",
              // "ypmc": "",
              // "ypdj": "",
              // "ypgg": "",
              "clmc": "",
              "cllx": "",
              "clgg": "",
              "wlmc": "",
              "wllx": "",
              "wlgg": "",
              "cj": "",
              "cjjc": "",
              "gys": "",
              "gysjc": "",
              "scyl":"",
              "scylb":"",
              "dyyl":"",
              "dyylb":""
            }
          },
          {
            name: '外加剂1',
            key: 'wjj1',
            type: 6,
            json: {
              "batch": "",
              "certificateNo": "",
              "pktzid": "",
              "xhbgbh": "",
              // "ypmc": "",
              // "ypdj": "",
              // "ypgg": "",
              "clmc": "",
              "cllx": "",
              "clgg": "",
              "wlmc": "",
              "wllx": "",
              "wlgg": "",
              "cj": "",
              "cjjc": "",
              "gys": "",
              "gysjc": "",
              "scyl":"",
              "scylb":"",
              "dyyl":"",
              "dyylb":""
            }
          },
          {
            name: '外加剂2',
            key: 'wjj2',
            type: 6,
            json: {
              "batch": "",
              "certificateNo": "",
              "pktzid": "",
              "xhbgbh": "",
              // "ypmc": "",
              // "ypdj": "",
              // "ypgg": "",
              "clmc": "",
              "cllx": "",
              "clgg": "",
              "wlmc": "",
              "wllx": "",
              "wlgg": "",
              "cj": "",
              "cjjc": "",
              "gys": "",
              "gysjc": "",
              "scyl":"",
              "scylb":"",
              "dyyl":"",
              "dyylb":""
            }
          },
          {
            name: '外掺料1',
            key: 'wcl1',
            type: 2,
            json: {
              "batch": "",
              "certificateNo": "",
              "pktzid": "",
              "xhbgbh": "",
              // "ypmc": "",
              // "ypdj": "",
              // "ypgg": "",
              "clmc": "",
              "cllx": "6",
              "clgg": "",
              "wlmc": "膨胀剂",
              "wllx": "",
              "wlgg": "",
              "cj": "",
              "cjjc": "",
              "gys": "",
              "gysjc": "",
              "scyl":"",
              "scylb":"",
              "dyyl":"",
              "dyylb":""
            }
          },
          {
            name: '外掺料2',
            key: 'wcl2',
            type: 3,
            json: {
              "batch": "",
              "certificateNo": "",
              "pktzid": "",
              "xhbgbh": "",
              // "ypmc": "",
              // "ypdj": "",
              // "ypgg": "",
              "clmc": "",
              "cllx": "6",
              "clgg": "",
              "wlmc": "纤维",
              "wllx": "",
              "wlgg": "",
              "cj": "",
              "cjjc": "",
              "gys": "",
              "gysjc": "",
              "scyl":"",
              "scylb":"",
              "dyyl":"",
              "dyylb":""
            }
          },
        ],
        editForm: {
          "prop": "",
          "index": -1,
          "scyl":"",
          "scylb":"",
          "dyyl":"",
          "dyylb":"",

          "clmc": "",
          "cllx": "",
          "clgg": "",
          "wlmc": "",
          "wlgg": "",
          "cj": "",
          "cjjc": "",
          "gys": "",
          "gysjc": "",
        },
        
        newRatioP12: [
          {
            "xpebbh": "",
            "xksdj": "P6"
          }, {
            "xpebbh": "",
            "xksdj": "P8"
          }, {
            "xpebbh": "",
            "xksdj": "P10"
          }, {
            "xpebbh": "",
            "xksdj": ""
          }
        ],
        newRatioP10: [
          {
            "xpebbh": "",
            "xksdj": "P6"
          }, {
            "xpebbh": "",
            "xksdj": "P8"
          }, {
            "xpebbh": "",
            "xksdj": ""
          }
        ],
        newRatioP8: [
          {
            "xpebbh": "",
            "xksdj": "P6"
          }, {
            "xpebbh": "",
            "xksdj": ""
          }
        ],
        newRatioP6: [
          {
            "xpebbh": "",
            "xksdj": ""
          }
        ],
        params: {
          proportionPhb: '', //配合比编码
          erpPhbId: '', // 添加配比ID字段
          proportionSjyj: 'JGJ55-2011', //设计依据	
          proportionQddj: '', //强度等级	
          proportionKzqd: '', //抗折强度	
          proportionKsdj: '', //抗渗等级	
          proportionRemarks: '', //备注	
          proportionQllz: '', //强氯离子
          proportionJbsj: '', // 搅拌时间
          proportionLq: '28', // 龄期 
          proportionZjx: '良好', // 黏聚性
          proportionBsx: '良好', // 保水性
          proportionTld: { //设计坍落度json
            qz: "",
            hz: "",
          },
          // proportionSjb: '', //水胶比
          // proportionSl: '',
          // proportionRz: '',
          reportDate: moment(new Date()).format('YYYY-MM-DD') + " 00:00:00", // 报告日期

          productionSjb: '', //生产水胶比
          productionSl: '',
          productionRz: '',
          productionCl: '',

          printSjb: '', //打印水胶比
          printSl: '',
          printRz: '',
          printCl: '',

          backwardCompatible: 0, // 是否向下兼容抗渗等级
          newRatioString: '', 
          newRatio: [
            
          ],

          proportionMaterial: { //原材料json	
            
          },
        },
        consumptionForm: [
          {
              type: 'input',
              label: '生产用量',
              prop: 'scyl',
              value: "",
              placeholder: "生产用量"
          },
          {
              type: 'input',
              label: '生产用量比例',
              prop: 'scylb',
              disabled: true,
              value: "",
              placeholder: "生产用量比例"
          },
          {
              type: 'input',
              label: '打印用量',
              prop: 'dyyl',
              value: "",
              placeholder: "打印用量"
          },
          {
              type: 'input',
              label: '打印用量比例',
              prop: 'dyylb',
              disabled: true,
              value: "",
              placeholder: "打印用量比例"
          },
        ],
        showConsumption: false,
        
        materialSpecOpts: [],
        cjOpts: [],
        supplierCompanyOpts: [],

        isNew: true
      }
    },

    filters: {
        isNull: function (value) {
            if (value == undefined || value == null || value === '' || isNaN(value)) return '---'
            
            return value;
        },
    },

    activated() {
      let lastFullPath = this.$store.state.tagsView.addMixProportionFullpath;
      let currFullPath = this.$route.fullPath;
      let isNew = this.isNew;

      if (!isNew && lastFullPath !== currFullPath) {
        this.$router.replace({ path: lastFullPath });
      } else if (isNew) {
        this.$store.dispatch("tagsView/saveAddMixProportionFullpath", currFullPath);
      }

      this.isNew = false;
    },

    mounted() {
      if (this.$route.query.id || this.$route.query.fphbNo) {

        this.$route.meta.title = "编辑配合比";
        if (this.$route.query.id) {
          this.queryMixProportionDetailResp();
        }else{
          this.queryMixProportionDetailByFphbNoResp();
        }
      }
      
      this.queryMaterialsSpecConfigAllResp();  
    },
    methods: {
      queryMaterialsSpecConfigAllResp() {
        this.$api.queryMaterialsSpecConfigAll({"materialsType":7,"materialsName":"混凝土"}, this).then(res => {
            if (res.code == 1) {
                this.designFrom.proportionQddj.opts = res.data.list.map(item => {
                    return {
                        label: item.materialsSpec || item.materialsName,
                        value: item.materialsSpec || item.materialsName,
                    }
                })
            }
        })
      },
      setRatio(type,prop){
        let snb = this.params[type].sn * 1;
        if(snb){
          if(prop === 'sn'){
            for (let item in this.params[type]) {
              if(item.includes('ylbl')){
                let op = item.slice(0,-4)
                if(this.params[type][op]){
                  this.params[type][item] = Math.round(this.params[type][op] * 1 / snb *100)/100;
                }
              }
            }
          }else if(this.params[type][prop]){
            this.params[type][prop + 'ylbl'] = Math.round(this.params[type][prop] * 1 / snb *100)/100;
          }
          this.$forceUpdate()
        }
      },

      isEmpty(val) {
            if (typeof val === "boolean") {
                return false;
            }
            if (typeof val === "number") {
                return false;
            }
            if (val instanceof Array) {
                if (val.length === 0) return true;
            } else if (val instanceof Object) {
                if (JSON.stringify(val) === "{}" || val == null ) return true;
            } else {
                if (
                val === "null" ||
                val == null ||
                val === "undefined" ||
                val === undefined ||
                val === ""
                )
                return true;
                return false;
            }
            return false;
        },
      getYongLiangInt(str, index){
            // if(this.isEmpty(str)) return '---'
            // return cpEvenRound(str, index);
            if(this.isEmpty(str)) return ''
            let res = cpEvenRound(str, index);
            if(res == 0.00 && index == 2) return '';
            return res;
        },
      setSelectInfo(row){
        console.log(row,this.selectIndex);
        let org = this.tableData[this.selectIndex];
        let cllxName = this.$util.experimentTypeToName(row.experimentType);
        this.$set(this.tableData,this.selectIndex,{
          ...org,
          json: {
            ...org.json,
            "pktzid": row.id || "",
            "xhbgbh": row.reportId || "",
            "batch": row.batch || "",
            "certificateNo": row.certificateNo || "",
            // "ypmc": row.materialsName || "",
            // "ypdj": row.sampleLevel || "",
            // "ypgg": row.materialsSpecs || "",
            "clmc": row.materialsName || "",
            "cllx": row.experimentType || "",
            "cllxName": cllxName,
            "clgg": row.materialsSpecs || "",
            "wlmc": row.matterName || "",
            "wllx": row.experimentType || "",
            "wllxName": cllxName,
            "wlgg": row.matterSpecs || "",
            "cj": row.factory || "",
            "cjjc": row.factoryCalled || "",
            "gys": row.supplyCompanyName || "",
            "gysjc": row.supplyCompanyCalled || "",
          }
        });
        this.validationVisible = false;
      },
      
      handleConsumptionChange(val, prop) {
        if (this.editForm.prop != 'sn') {
          let snObj = this.tableData[0].json;
          let sn_scyl = snObj.scyl;
          let sn_dyyl = snObj.dyyl;
          //  "scyl":"生产用量",
          //  "scylb":"生产用量比例",
          //  "dyyl":"打印用量",
          //  "dyylb":"打印用量比例"
          if (prop == 'scyl') {
            let res = calcEquation([
              {
                v: val,
              },
              {
                k: '/',
                v: sn_scyl,
              },
            ], 2)
            this.editForm.scylb = res;
          }else if (prop == 'dyyl') {
            let res = calcEquation([
              {
                v: val,
              },
              {
                k: '/',
                v: sn_dyyl,
              },
            ], 2)
            this.editForm.dyylb = res;
          }else{
            this.editForm[prop] = val;
          }
        }
      },
      handleClose() {
        //关闭弹簧
        this.validationVisible = false;
      },
      handleConsumptionClose(done) {
        this.$confirm('确认关闭？')
          .then(_ => {
            if(done){
              done();
            }
            this.showConsumption = false;
          })
          .catch(_ => {});
      },
      handleSetTarget(row,index){
        console.log(index)
        this.selectIndex = index;
        this.validationVisible = true;
        this.$nextTick(()=>{
          this.$refs.panel.initData(1,10,row.type);
        },200)
      },

      setConsumptionTarget(row,index){
        if (row.key != 'sn') {
          let snObj = this.tableData[0].json;
          if ((snObj.scyl == '' || snObj.scyl == null || snObj.scyl == undefined)
            || (snObj.dyyl == '' || snObj.dyyl == null || snObj.dyyl == undefined)) {
              this.$message({
                showClose: true,
                message: "请先设置水泥的用量",
                type: "warning",
              });
              return;
          }
        }

        if (row.key != 'water' && row.key != 'wcl1' && row.key != 'wcl2') {
          if (!row.json.xhbgbh) {
            this.$message({
              showClose: true,
              message: "请先选择原材料",
              type: "warning",
            });
            return;
          }
        }
        
        this.editForm = {
          "prop": row.key,
          "index": index,
          "scyl": row.json.scyl,
          "scylb": row.json.scylb,
          "dyyl": row.json.dyyl,
          "dyylb": row.json.dyylb,
          "clmc": row.json.clmc,
          "clgg": row.json.clgg,
          "wlmc": row.json.wlmc,
          "wlgg": row.json.wlgg,
          
          "cj": row.json.cj,
          "cjjc": row.json.cjjc,
          "gys": row.json.gys,
          "gysjc": row.json.gysjc,
          "wlObj": {
            "clmc": row.json.clmc,
            "clgg": row.json.clgg,
            "wlmc": row.json.wlmc,
            "wlgg": row.json.wlgg,
          },
        }
        if (this.editForm.prop === 'water') {
            this.editForm.clmc = row.json.clmc || '水';
            this.editForm.clgg = row.json.clgg || '清水';
        }else if (this.editForm.prop === 'wcl1') {
            this.editForm.wlmc = row.json.wlmc || '膨胀剂';
            this.editForm.wlgg = row.json.wlgg || '';
            this.editForm.clmc = row.json.clmc || '';
            this.editForm.clgg = row.json.clgg || '';
            this.queryMaterialsSpecListResp();
            if (this.editForm.clgg) {
              this.changeMaterialSpec();
            }
        }else if (this.editForm.prop === 'wcl2') {
            this.editForm.wlmc = row.json.wlmc || '纤维';
            this.editForm.wlgg = row.json.wlgg || '';
            this.editForm.clmc = row.json.clmc || '';
            this.editForm.clgg = row.json.clgg || '';
            this.queryMaterialsSpecListResp();
            if (this.editForm.clgg) {
              this.changeMaterialSpec();
            }
        }

        this.showConsumption = true;
      },

      resetConsumptionTarget(row,index) {
        this.editForm = {
          "prop": row.key,
          "index": index,
          "scyl": '',
          "scylb": '',
          "dyyl": '',
          "dyylb": '',
          "cj": '',
          "cjjc": '',
          "gys": '',
          "gysjc": '',
          "wlObj": {},
        }
        
        row.json = {
          ...this.tableDataOrg[index].json,
        }
        this.editForm.scyl = row.json.scyl;
        this.editForm.scylb = row.json.scylb;
        this.editForm.dyyl = row.json.dyyl;
        this.editForm.dyylb = row.json.dyylb;

        this.saveConsumptionTarget();
      },

      // countTarget(){

      //   let productionSjb = 0;
      //   let productionSl = 0;
      //   let productionRz = 0;
      //   let productionCl = 0;
      //   let printSjb = 0;
      //   let printSl = 0;
      //   let printRz = 0;
      //   let printCl = 0;

      //   // 水的生产用量
      //   let productionWater = 0;
      //   // 水泥+矿粉+粉煤灰 生产用量和
      //   let productionSjbSum = 0;

      //   // 水的打印用量
      //   let printWater = 0;
      //   // 水泥+矿粉+粉煤灰 打印用量和
      //   let printSjbSum = 0;

      //   // 外加剂掺量生产
      //   let productionWjj = 0;
      //   let productionWjjclSum = 0;
      //   // 外加剂掺量打印
      //   let printWjj = 0;
      //   let printWjjclSum = 0;

      //   // 细骨料用量
      //   let productionXgl = 0;
      //   let printXgl = 0;
      //   // 粗骨料用量
      //   let productionCgl = 0;
      //   let printCgl = 0;

      //   console.log('tableData:', this.tableData);
      //   // for (let item of this.tableData) {
      //         for (let i=0; i < this.tableData.length; i++) {
      //               let item = this.tableData[i];
      //           let itemJson = item.json;

      //           let snObj = this.tableData[0].json;
      //           // 打印用量
      //           let sn_dyyl = snObj.dyyl;
      //           let res = calcEquation([
      //               {
      //                   v: itemJson.dyyl,
      //               },
      //               {
      //                   k: '/',
      //                   v: sn_dyyl,
      //               },
      //               ], 2)


      //               // 生产用量

      //               let sn_scyl = snObj.scyl;
      //       let resSc = calcEquation([
      //         {
      //           v: itemJson.scyl,
      //         },
      //         {
      //           k: '/',
      //           v: sn_scyl,
      //         },
      //       ], 2)
      //           let ylJsonInfo = {
      //           "dyylb": res,
      //           "scylb": resSc
      //       }
      //       this.$set(this.tableData, i,{
      //           ...this.tableData[i],
      //           json: {
      //               ...itemJson,
      //               ...ylJsonInfo,
      //           }
      //       });
      //     // 生产容重等于json 中的生产用量之和
      //     productionRz += (itemJson.scyl !== '---' || itemJson.scyl !== '') ? parseFloat(itemJson.scyl) : 0;
      //     // 打印容重等于json 中的打印用量之和
      //     printRz += (itemJson.dyyl !== '---' || itemJson.dyyl !== '') ? parseFloat(itemJson.dyyl) : 0;
      //     // 水胶比公式：(水 / （水泥+矿粉+粉煤灰）生产用量)*100)/1000
      //     if (item.key == 'water') {
      //       productionWater = (itemJson.scyl !== '---' || itemJson.scyl !== '') ? parseFloat(itemJson.scyl) : 0;
      //       printWater = (itemJson.dyyl !== '---' || itemJson.dyyl !== '') ? parseFloat(itemJson.dyyl) : 0;
      //     }
      //     if (item.key == 'sn' || item.key == 'fmh' || item.key == 'kzf' || item.key == 'wcl1') {
      //       productionSjbSum += (itemJson.scyl !== '---' || itemJson.scyl !== '') ? parseFloat(itemJson.scyl) : 0;
      //       printSjbSum += (itemJson.dyyl !== '---' || itemJson.dyyl !== '') ? parseFloat(itemJson.dyyl) : 0;
      //     }

      //     // 外加剂掺量：外加剂/（水泥+矿粉+掺和料）
      //     if (item.key == 'wjj1') {
      //       productionWjj = (itemJson.scyl !== '---' || itemJson.scyl !== '') ? parseFloat(itemJson.scyl) : 0;
      //       printWjj = (itemJson.dyyl !== '---' || itemJson.dyyl !== '') ? parseFloat(itemJson.dyyl) : 0;
      //     }
          
      //     if (item.key == 'sn' || item.key == 'kzf' || item.key == 'wcl2') {
      //       productionWjjclSum += (itemJson.scyl !== '---' || itemJson.scyl !== '') ? parseFloat(itemJson.scyl) : 0;
      //       printWjjclSum += (itemJson.dyyl !== '---' || itemJson.dyyl !== '') ? parseFloat(itemJson.dyyl) : 0;
      //     }

      //     // 砂率： 细骨料用量/(细骨料生产用量+粗骨料生产用量)*100
      //     if (item.key == 'xgl') {
      //       productionXgl = (itemJson.scyl !== '---' || itemJson.scyl !== '') ? parseFloat(itemJson.scyl) : 0;
      //       printXgl = (itemJson.dyyl !== '---' || itemJson.dyyl !== '') ? parseFloat(itemJson.dyyl) : 0;
      //     }
      //     if (item.key == 'cgl') {
      //       productionCgl = (itemJson.scyl !== '---' || itemJson.scyl !== '') ? parseFloat(itemJson.scyl) : 0;
      //       printCgl = (itemJson.dyyl !== '---' || itemJson.dyyl !== '') ? parseFloat(itemJson.dyyl) : 0;
      //     }
      //   }

      //   // 水胶比公式：(水 / （水泥+矿粉+粉煤灰）生产用量)
      //   productionSjb = calcEquation([
      //     {
      //       v: productionWater,
      //     },{
      //       k: '/',
      //       v: productionSjbSum,
      //     }
      //   ], 2);
      //   printSjb = calcEquation([
      //     {
      //       v: printWater,
      //     },{
      //       k: '/',
      //       v: printSjbSum,
      //     }
      //   ], 2);
      //   // 外加剂掺量：外加剂/（水泥+矿粉+掺和料）
      //   productionCl = calcEquation([
      //     {
      //       v: productionWjj,
      //     },{
      //       k: '/',
      //       v: productionWjjclSum,
      //     }
      //   ], 2);
      //   // productionCl = productionWjj / productionWjjclSum;
      //   printCl = calcEquation([
      //     {
      //       v: printWjj,
      //     },{
      //       k: '/',
      //       v: printWjjclSum,
      //     }
      //   ], 2);
      //   // printCl = printWjj / printWjjclSum;

      //   // 砂率： 细骨料用量/(细骨料生产用量+粗骨料生产用量)*100
      //   productionSl = calcEquation([
      //     {
      //       v: productionXgl,
      //     },{
      //       k: '/',
      //       v: productionXgl + productionCgl,
      //     },{
      //       k: '*',
      //       v: 100,
      //     }
      //   ], 0);
      //   // productionSl = (productionXgl / (productionXgl + productionCgl)) * 100;
      //   printSl = calcEquation([
      //     {
      //       v: printXgl,
      //     },{
      //       k: '/',
      //       v: printXgl + printCgl,
      //     },{
      //       k: '*',
      //       v: 100,
      //     }
      //   ], 0);
      //   // printSl = (printXgl / (printXgl + printCgl)) * 100;

      //   // 容重改为整数
      //   productionRz = calcEquation([
      //     {
      //       v: productionRz,
      //     },{
      //       k: '*',
      //       v: 1,
      //     }
      //   ], 0);
        
      //   printRz = calcEquation([
      //     {
      //       v: printRz,
      //     },{
      //       k: '*',
      //       v: 1,
      //     }
      //   ], 0);


      //   this.params.productionSjb = productionSjb;
      //   this.params.productionSl = productionSl;
      //   this.params.productionRz = productionRz;
      //   this.params.productionCl = productionCl;
      //   this.params.printSjb = printSjb;
      //   this.params.printSl = printSl;
      //   this.params.printRz = printRz;
      //   this.params.printCl = printCl;

      // },

      saveConsumptionTarget() {
        if (this.editForm.prop == 'wcl2') {
          this.editForm.scyl = cpEvenRound(this.editForm.scyl, 1);
          this.editForm.dyyl = cpEvenRound(this.editForm.dyyl, 1);
        }
        let orgJson = this.tableData[this.editForm.index].json;
        let ylJson = {
          "scyl": this.editForm.scyl,
          "scylb": this.editForm.scylb,
          "dyyl": this.editForm.dyyl,
          "dyylb": this.editForm.dyylb
        }
        if (this.editForm.prop === 'water' || this.editForm.prop === 'wcl1' || this.editForm.prop === 'wcl2') {
          ylJson["clmc"] = this.editForm.clmc;
          ylJson["clgg"] = this.editForm.clgg;
          ylJson["wlmc"] = this.editForm.wlmc;
          ylJson["wlgg"] = this.editForm.wlgg;
          ylJson["cj"] = this.editForm.cj;
          ylJson["cjjc"] = this.editForm.cjjc;
          ylJson["gys"] = this.editForm.gys;
          ylJson["gysjc"] = this.editForm.gysjc;
        }
        this.$set(this.tableData,this.editForm.index,{
          ...this.tableData[this.editForm.index],
          json: {
            ...orgJson,
            ...ylJson,
          }
        });

        this.showConsumption = false;

        // 计算生产 和 打印的四个参数
        let productionSjb = 0;
        let productionSl = 0;
        let productionRz = 0;
        let productionCl = 0;
        let printSjb = 0;
        let printSl = 0;
        let printRz = 0;
        let printCl = 0;

        // 水的生产用量
        let productionWater = 0;
        // 水泥+矿粉+粉煤灰 生产用量和
        let productionSjbSum = 0;

        // 水的打印用量
        let printWater = 0;
        // 水泥+矿粉+粉煤灰 打印用量和
        let printSjbSum = 0;

        // 外加剂掺量生产
        let productionWjj = 0;
        let productionWjjclSum = 0;
        // 外加剂掺量打印
        let printWjj = 0;
        let printWjjclSum = 0;

        // 细骨料用量
        let productionXgl = 0;
        let printXgl = 0;
        // 粗骨料用量
        let productionCgl = 0;
        let printCgl = 0;

        for (let i=0; i < this.tableData.length; i++) {
                    let item = this.tableData[i];
                let itemJson = item.json;

                let snObj = this.tableData[0].json;
                // 打印用量
                let sn_dyyl = snObj.dyyl;
                let res = calcEquation([
                    {
                        v: itemJson.dyyl,
                    },
                    {
                        k: '/',
                        v: sn_dyyl,
                    },
                    ], 2)


                    // 生产用量

                    let sn_scyl = snObj.scyl;
            let resSc = calcEquation([
              {
                v: itemJson.scyl,
              },
              {
                k: '/',
                v: sn_scyl,
              },
            ], 2)
                let ylJsonInfo = {
                "dyylb": res,
                "scylb": resSc
            }
            this.$set(this.tableData, i,{
                ...this.tableData[i],
                json: {
                    ...itemJson,
                    ...ylJsonInfo,
                }
            });
          // 生产容重等于json 中的生产用量之和
          productionRz = productionRz + ((itemJson.scyl !== '---' && itemJson.scyl !== '') ? parseFloat(itemJson.scyl) : 0);
          // 打印容重等于json 中的打印用量之和
          printRz = printRz + (itemJson.dyyl !== '---' && itemJson.dyyl !== '') ? parseFloat(itemJson.dyyl) : 0;
          // 水胶比公式：(水 / （水泥+矿粉+粉煤灰）生产用量)*100)/1000
          if (item.key == 'water') {
            productionWater = (itemJson.scyl !== '---' && itemJson.scyl !== '') ? parseFloat(itemJson.scyl) : 0;
            printWater = (itemJson.dyyl !== '---' && itemJson.dyyl !== '') ? parseFloat(itemJson.dyyl) : 0;
          }
          if (item.key == 'sn' || item.key == 'fmh' || item.key == 'kzf' || item.key == 'wcl1') {
            productionSjbSum = productionSjbSum + ((itemJson.scyl !== '---' && itemJson.scyl !== '') ? parseFloat(itemJson.scyl) : 0);
            printSjbSum = printSjbSum + ((itemJson.dyyl !== '---' && itemJson.dyyl !== '') ? parseFloat(itemJson.dyyl) : 0);
          }

          // 外加剂掺量：外加剂/（水泥+矿粉+掺和料）
          if (item.key == 'wjj1') {
            productionWjj = (itemJson.scyl !== '---' && itemJson.scyl !== '') ? parseFloat(itemJson.scyl) : 0;
            printWjj = (itemJson.dyyl !== '---' && itemJson.dyyl !== '') ? parseFloat(itemJson.dyyl) : 0;
          }
          
          if (item.key == 'sn' || item.key == 'kzf' || item.key == 'wcl2') {
            productionWjjclSum = productionWjjclSum + ((itemJson.scyl !== '---' && itemJson.scyl !== '') ? parseFloat(itemJson.scyl) : 0);
            printWjjclSum = printWjjclSum + ((itemJson.dyyl !== '---' && itemJson.dyyl !== '') ? parseFloat(itemJson.dyyl) : 0);
          }

          // 砂率： 细骨料用量/(细骨料生产用量+粗骨料生产用量)*100
          if (item.key == 'xgl') {
            productionXgl = (itemJson.scyl !== '---' && itemJson.scyl !== '') ? parseFloat(itemJson.scyl) : 0;
            printXgl = (itemJson.dyyl !== '---' && itemJson.dyyl !== '') ? parseFloat(itemJson.dyyl) : 0;
          }
          if (item.key == 'cgl') {
            productionCgl = (itemJson.scyl !== '---' && itemJson.scyl !== '') ? parseFloat(itemJson.scyl) : 0;
            printCgl = (itemJson.dyyl !== '---' && itemJson.dyyl !== '') ? parseFloat(itemJson.dyyl) : 0;
          }
        }

        // 水胶比公式：(水 / （水泥+矿粉+粉煤灰）生产用量)
        productionSjb = calcEquation([
          {
            v: productionWater,
          },{
            k: '/',
            v: productionSjbSum,
          }
        ], 2);
        printSjb = calcEquation([
          {
            v: printWater,
          },{
            k: '/',
            v: printSjbSum,
          }
        ], 2);
        // 外加剂掺量：外加剂/（水泥+矿粉+掺和料）
        productionCl = calcEquation([
          {
            v: productionWjj,
          },{
            k: '/',
            v: productionWjjclSum,
          }
        ], 2);

        printCl = calcEquation([
          {
            v: printWjj,
          },{
            k: '/',
            v: printWjjclSum,
          }
        ], 2);

        // 砂率： 细骨料用量/(细骨料生产用量+粗骨料生产用量)*100
        productionSl = calcEquation([
          {
            v: productionXgl,
          },{
            k: '/',
            v: productionXgl + productionCgl,
          },{
            k: '*',
            v: 100,
          }
        ], 0);

        printSl = calcEquation([
          {
            v: printXgl,
          },{
            k: '/',
            v: printXgl + printCgl,
          },{
            k: '*',
            v: 100,
          }
        ], 0);

        // 容重改为整数
        productionRz = calcEquation([
          {
            v: productionRz,
          },{
            k: '*',
            v: 1,
          }
        ], 0);
        
        printRz = calcEquation([
          {
            v: printRz,
          },{
            k: '*',
            v: 1,
          }
        ], 0);


        this.params.productionSjb = productionSjb;
        this.params.productionSl = productionSl;
        this.params.productionRz = productionRz;
        this.params.productionCl = productionCl;
        this.params.printSjb = printSjb;
        this.params.printSl = printSl;
        this.params.printRz = printRz;
        this.params.printCl = printCl;
      },
      
      addProportion() {
        let proportionMaterial = {};
        this.tableData.map(item => {
          proportionMaterial[item.key] = item.json;
        });
        let parmas = {
          ...this.params,
          proportionMaterial: proportionMaterial
        }
        if (this.params.id && this.$route.query.actType != 'copy') {
          // 编辑
          this.$api.mixProportionUpdate(parmas, this).then((res) => {
            if (res.succ) {
              this.$message({
                showClose: true,
                message: "修改成功",
                type: "success",
              });
              // this.$store.dispatch("tagsView/delVisitedView", this.$route);
            } else {
              this.$message({
                showClose: true,
                message: res.msg,
                type: "error",
              });
            }
          });
        }else{
          delete parmas.id;
          this.$api.mixProportionCreate(parmas, this).then((res) => {
            if (res.succ) {
              let newId = res.data.id;
              this.$message({
                showClose: true,
                message: "新增成功",
                type: "success",
              });
              // this.$store.dispatch("tagsView/delVisitedView", this.$route);
              this.$router.push({name: 'addMixProportion', query: {
                ...this.$route.query,
                id: newId,
                actType: 'edit',
              } }).then(() => {
                this.reload();
              });
            } else {
              this.$message({
                showClose: true,
                message: res.msg,
                type: "error",
              });
            }
          });
        }
      },

      queryMixProportionDetailResp() {
        let id = this.$route.query.id;
        this.$api.queryMixProportionDetail(`id=${id}`, this).then(res => {
            if (res.code == 1) {
              this.handleDetailInfo(res.data);
            }else{
                this.$message.error(res.msg || '查询失败')
            }
        });
      },

      queryMixProportionDetailByFphbNoResp() {
        let fphbNo = this.$route.query.fphbNo;
        this.$api.queryMixProportionAll({
          proportionPhb: fphbNo
        }, this).then(res => {
          let resData = res.data.list[0];
          if (res.code == 1 && resData) {
            this.handleDetailInfo(resData);
          }else{
              this.$message.error(res.msg || '查询失败')
          }
        }).catch(err => {
          this.$message.error('查询失败');
        })
      },

      handleDetailInfo(detail) {
        this.params.id = detail.id || "";
        this.params.erpPhbId = detail.erpPhbId || "";
        this.params.backwardCompatible = detail.backwardCompatible || 0;
        this.params.newRatioString = detail.newRatioString || "";
        this.params.proportionMaterial = detail.proportionMaterial || {}
        this.params.proportionPhb = detail.proportionPhb || '';
        this.params.proportionSjyj = detail.proportionSjyj || '';
        this.params.proportionQddj = detail.proportionQddj || '';
        this.params.proportionKzqd = detail.proportionKzqd || '';
        this.params.proportionLq = detail.proportionLq || '';
        this.params.proportionKsdj = detail.proportionKsdj || '';
        this.params.proportionRemarks = detail.proportionRemarks || '';
        this.params.proportionTld = detail.proportionTld || {};
        this.params.proportionQllz = detail.proportionQllz || '';
        this.params.proportionJbsj = detail.proportionJbsj || "";

        this.params.proportionBsx = detail.proportionBsx || "";
        this.params.proportionZjx = detail.proportionZjx || "";
        this.params.reportDate = detail.reportDate || "";
        
        // this.params.proportionSjb = detail.proportionSjb || '';
        // this.params.proportionSl = detail.proportionSl || '';
        // this.params.proportionRz = detail.proportionRz || '';
        // 按照计算时候的修约方式修约
        if (detail.proportionSjb) {
          this.params.proportionSjb = cpEvenRound(detail.productionSjb, 2);
        }else{
          this.params.proportionSjb = '';
        }
        if (detail.productionSl) {
          this.params.productionSl = cpEvenRound(detail.productionSl, 0);
        }else{
          this.params.productionSl = '';
        }
        if (detail.productionRz) {
          this.params.productionRz = cpEvenRound(detail.productionRz, 0);
        }else{
          this.params.productionRz = '';
        }
        if (detail.productionCl) {
          this.params.productionCl = cpEvenRound(detail.productionCl, 2);
        }else{
          this.params.productionCl = '';
        }

        if (detail.printSjb) {
          this.params.printSjb = cpEvenRound(detail.printSjb, 2);
        }else{
          this.params.printSjb = '';
        }
        if (detail.printSl) {
          this.params.printSl = cpEvenRound(detail.printSl, 0);
        }else{
          this.params.printSl = '';
        }
        if (detail.printRz) {
          this.params.printRz = cpEvenRound(detail.printRz, 0);
        }else{
          this.params.printRz = '';
        }
        if (detail.printCl) {
          this.params.printCl = cpEvenRound(detail.printCl, 2);
        }else{
          this.params.printCl = '';
        }


        // this.params.productionSjb = detail.productionSjb || '';
        // this.params.productionSl = detail.productionSl || '';
        // this.params.productionRz = detail.productionRz || '';
        // this.params.productionCl = detail.productionCl || '';
        // this.params.printSjb = detail.printSjb || '';
        // this.params.printSl = detail.printSl || '';
        // this.params.printRz = detail.printRz || '';
        // this.params.printCl = detail.printCl || '';

        this.tableData.map(item => {
          item.json = this.params.proportionMaterial[item.key] || {};

          if (item.key == 'wjj1' || item.key == 'wjj2') {
            item.json.scyl = this.getYongLiangInt(item.json.scyl, 2);
            item.json.scylb = this.getYongLiangInt(item.json.scylb, 2);
            item.json.dyyl = this.getYongLiangInt(item.json.dyyl, 2);
            item.json.dyylb = this.getYongLiangInt(item.json.dyylb, 2);
          }else{
            item.json.scyl = this.getYongLiangInt(item.json.scyl, 0);
            item.json.scylb = this.getYongLiangInt(item.json.scylb, 2);
            item.json.dyyl = this.getYongLiangInt(item.json.dyyl, 0);
            item.json.dyylb = this.getYongLiangInt(item.json.dyylb, 2);
          }
        });
        // this.countTarget()
      },

      changeProportionKsdj(event) {
        this.params.backwardCompatible = 0;
        this.params.newRatio = [];
        this.params.newRatioString = "";
      },

      changeNewRatio(event) {
        let phb = this.params.proportionPhb;
        if (event == 1) {
          this.handleNewRatioChange();
        }else{
          this.params.newRatio = [];
          this.params.newRatioString = "";
        }
      },

      handleNewRatioChange() {
        let phb = this.params.proportionPhb;
        if (this.params.proportionKsdj === 'P6') {
          this.newRatioP6 = [
            {
              "xpebbh": phb + "P6",
              "xksdj": "P6"
            }
          ];
          this.params.newRatio = this.newRatioP6;
        }else if (this.params.proportionKsdj === 'P8') {
          this.newRatioP8 = [
            {
              "xpebbh": phb + "P6",
              "xksdj": "P6"
            },
            {
              "xpebbh": phb + "P8",
              "xksdj": "P8"
            }
          ];
          this.params.newRatio = this.newRatioP8;
        }else if (this.params.proportionKsdj === 'P10') {
          this.newRatioP10 = [
            {
              "xpebbh": phb + "P6",
              "xksdj": "P6"
            },
            {
              "xpebbh": phb + "P8",
              "xksdj": "P8"
            }, {
              "xpebbh": phb + "P10",
              "xksdj": "P10"
            }
          ];
          this.params.newRatio = this.newRatioP10;
        }else if (this.params.proportionKsdj === 'P12') {
          this.newRatioP12 = [
            {
              "xpebbh": phb + "P6",
              "xksdj": "P6"
            },
            {
              "xpebbh": phb + "P8",
              "xksdj": "P8"
            }, {
              "xpebbh": phb + "P10",
              "xksdj": "P10"
            }, {
              "xpebbh": phb + "P12",
              "xksdj": "P12"
            }
          ];
          this.params.newRatio = this.newRatioP12;
        }

        let newRatioString = '';
        this.params.newRatio.forEach((item, index) => {
          if (index == 0) {
            if (item.xpebbh) {
              newRatioString = item.xpebbh;
            }
          }else{
            if (item.xpebbh) {
              newRatioString = newRatioString + "，" + item.xpebbh;
            }
          }
        });
        this.params.newRatioString = newRatioString;
      },

      showNewRatioClick() {
        if (this.params.proportionKsdj === 'P12') {
          this.newRatioP8 = [
            {
              "xpebbh": "",
              "xksdj": "P6"
            }, {
              "xpebbh": "",
              "xksdj": ""
            }
          ];
          this.params.newRatio = this.newRatioP12;
          this.params.newRatioString = "";
        }else if (this.params.proportionKsdj === 'P10') {
          this.newRatioP10 = [
            {
              "xpebbh": "",
              "xksdj": "P6"
            }, {
              "xpebbh": "",
              "xksdj": "P8"
            }, {
              "xpebbh": "",
              "xksdj": ""
            }
          ];
          this.params.newRatio = this.newRatioP10;
          this.params.newRatioString = "";
        }else if (this.params.proportionKsdj === 'P8') {
          this.newRatioP12 = [
            {
              "xpebbh": "",
              "xksdj": "P6"
            }, {
              "xpebbh": "",
              "xksdj": "P8"
            }, {
              "xpebbh": "",
              "xksdj": "P10"
            }, {
              "xpebbh": "",
              "xksdj": ""
            }
          ];
          this.params.newRatio = this.newRatioP8;
          this.params.newRatioString = "";
        }else if (this.params.proportionKsdj === 'P6') {
          this.newRatioP6 = [
            {
              "xpebbh": "",
              "xksdj": ""
            }
          ];
          this.params.newRatio = this.newRatioP6;
          this.params.newRatioString = "";
        }

        if (this.params.backwardCompatible == 1) {
          this.showNewRatio = true;
        }
      },
      sureNewRatio() {
        let newRatioString = '';
        this.params.newRatio.forEach((item, index) => {
          if (index == 0) {
            if (item.xpebbh) {
              newRatioString = item.xpebbh + item.xksdj;
            }
          }else{
            if (item.xpebbh) {
              newRatioString = newRatioString + "，" + item.xpebbh + item.xksdj
            }
          }
        });

        this.params.newRatioString = newRatioString;
        this.showNewRatio = false;
      },
      // 查询材料规格
      queryMaterialsSpecListResp() {
        this.$api.queryMaterialsSpecList(`materialsType=6&materialsName=${this.editForm.wlmc}`, this).then(res => {
          if (res.code == 1) {
              this.materialSpecOpts = res.data.list.map(item => {
                  return {
                      label: `${item.wlgg}【${item.clgg}】`,
                      value: item,
                  }
              })
          }
        });
      },
      // 外掺料厂家
      changeMaterialSpec(item) {
        if (item) {
          this.editForm.wlmc = item.wlmc;
          this.editForm.wlgg = item.wlgg;
          this.editForm.clgg = item.clgg;
          this.editForm.clmc = item.clmc;
        }
        
        this.changeSupplierCompnayMaterialSpec();
        
        this.$api.queryMaterialsFactoryList(`materialsType=6&materialsName=${this.editForm.wlmc}&materialsSpec=${this.editForm.wlgg}`, this).then(res => {
          if (res.code == 1) {
              this.cjOpts = res.data.list.map(item => {
                  return {
                      label: item.manufacturers,
                      value: item,
                  }
              })
          }
        });
      },
      // 外掺料供应商
      changeSupplierCompnayMaterialSpec(item) {
        this.$api.queryMaterialSupplierCompanyList(`materialsType=6&materialsName=${this.editForm.wlmc}&materialsSpec=${this.editForm.wlgg}`, this).then(res => {
            if (res.code == 1) {
                this.supplierCompanyOpts = res.data.list.map(item => {
                  return {
                    label: item.supplierName,
                    value: item,
                  }
                });
            }
        })
      },
      // 厂家
      changeCj(item) {
        this.editForm.cj = item.manufacturers;
        this.editForm.cjjc = item.manufacturersCalled;
      },
      changeGys(item) {
        this.editForm.gys = item.supplierName;
        this.editForm.gysjc = item.supplierAbbreviation;
      }
    },
  }
</script>

<style lang="scss" scoped>
  .content-box {
    padding: 16px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #1F2329;

    .content {
      width: 100%;
      height: 100%;
      padding: 16px;
      background: #FFFFFF;
      border-radius: 16px;
      overflow: auto;
      overflow-x: hidden
    }
  }

  .row-title {
    border-bottom: #DDDFE6 1px solid;
    padding-bottom: 8px;

    .row-title-label {
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      font-size: 16px;
      color: #1F2329;
    }
  }

  .form-inline {
    display: inline-block;
    min-width: 300px
  }
  
  
  ::v-deep .el-input-group__append{
    width: 120px;
  }
  ::v-deep .claim-dialog-box{
    height: calc(100% - 40px);
    overflow: hidden;
    .el-dialog__body{
      padding: 0;
    }
  }
  .el-dialog__body{
    
  }
</style>