/*
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-05-25 00:09:18
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2024-06-09 23:25:26
 * @FilePath: /quality_center_web/src/pages/mixProportion/mixMixed.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

export const mixMixedDetail = {
    data() {
        return {
            djList: [
                {
                  label: "强度等级",
                  value: "--",
                  imgUrl: require("@/assets/images/icon-qddj.png"),
                  background: 'rgba(31, 87, 179, 0.1)'
                },
                {
                  label: "抗折强度",
                  value: "--",
                  imgUrl: require("@/assets/images/icon-kzqd.png"),
                  background: "rgba(9, 158, 122, 0.1)"
                },
                {
                  label: "抗渗等级",
                  value: "--",
                  imgUrl: require("@/assets/images/icon-ksdj.png"),
                  background: "rgba(80, 127, 153, 0.1)"
                },
                {
                  label: "抗氯离子",
                  value: "--",
                  imgUrl: require("@/assets/images/icon-kllz.png"),
                  background: "rgba(255, 123, 47, 0.1)"
                },
                {
                  label: "设计坍落度",
                  value: "--",
                  imgUrl: require("@/assets/images/icon-sjtld.png"),
                  background: "rgba(255, 0, 0, 0.1)"
                }
            ],
            adaTableData: [],
            adaTableColumn: [
                {
                    label: '试配编号',
                    prop: 'adaptationNo',
                    width: '140'
                },
                {
                    label: '试配日期',
                    prop: 'adaptationDate',
                    width: '140'
                },
                {
                    label: '试配依据',
                    prop: 'adaptationYj',
                    width: '140'
                },
                {
                    label: '试配方量(方)',
                    prop: 'adaptationSpfl',
                    width: '140'
                },
                {
                    label: '水(kg/m³)',
                    prop: 'adaptationWater',
                    width: '140'
                },
                {
                    label: '水泥(kg/m³)',
                    prop: 'adaptationSn',
                    width: '140'
                },
                {
                    label: '粉煤灰(kg/m³)',
                    prop: 'adaptationFmh',
                    width: '140'
                },
                {
                    label: '矿渣粉(kg/m³)',
                    prop: 'adaptationKzf',
                    width: '140'
                },
                {
                    label: '细骨料(kg/m³)',
                    prop: 'adaptationXgl',
                    width: '140'
                },
                {
                    label: '粗骨料(kg/m³)',
                    prop: 'adaptationCgl',
                    width: '140'
                },
                {
                    label: '减水剂(kg/m³)',
                    prop: 'adaptationJsj',
                    width: '140'
                },
                {
                    label: '膨胀剂(kg/m³)',
                    prop: 'adaptationPzj',
                    width: '140'
                },
                {
                    label: '是否合格',
                    prop: 'isQualified',
                    width: '140',
                    formatter: (row) => {
                        if (row.isQualified == 1) {
                            return "合格";
                        }else if (row.isQualified == 2) {
                            return "不合格";
                        }else{
                            return "试验中";
                        }
                    },
                },
            ],

            adaTotal: 0,
            adaPageObj: {
                pageNum: 1, // 页数
                pageSize: 10, // 条数
            },

            mid: '', // 配合比id
            detailInfo: {}, // 配合比详情json
            proportionJsphb: [], //计算配合比json
            proportionTld: {}, // 设计坍落度json
            proportionMaterial: {}, // 原材料json
            proportionSjb: {}, // 水胶比json
            proportionYsl: {}, // 用水量json
            proportionJncl: {}, // 凝胶材料用量json
            proportionSl: {}, // 砂率用量json
            proportionYz: {}, // 用量阈值json
            proportionYL: {}, // 技术参数用量部分

            loading: false,
        }
    },

    computed: {
        pzqd() {
            let qd = '--';
            if (this.detailInfo.proportionQddj) {
                let bzz = this.detailInfo.proportionQddj.substring(0,1);
                let bzc = 4;
                if (parseFloat(bzz) <= 20) {
                    bzc = 4;
                }else if (parseFloat(bzz) >= 25 && parseFloat(bzz) <= 45) {
                    bzc = 5;
                }else if (parseFloat(bzz) >= 50) {
                    bzc = 6;
                }
                qd = bzc * 1.645 + bzc;
            }
            
            return qd;
        },
        // 水胶比和强度
        sjbQDatas() {
            let list1 = [];
            let list2 = [];
            for (const item of this.proportionJsphb) {
                if (item.sjb) {
                    list1.push(parseFloat(item.sjb));
                }else{
                    list1.push(0);
                }

                if (item.kyqdPjz) {
                    list2.push(parseFloat(item.kyqdPjz))
                }else{
                    list2.push(0);
                }
            }
            return [list1, list2];
        }
    },

    mounted() {
        this.queryMixProportionDetailResp();
        this.handleAdaFilter();
    },
    
    methods: {
        chartViewDraw() {
            let qdsjbChart = this.$echarts.init(document.getElementById('qdsjb-chart'));
            qdsjbChart.setOption({
                xAxis: {
                    type: 'category',
                    data: this.sjbQDatas[0],
                    splitLine: {
                        show: true
                    },
                },
                yAxis: {
                    type: 'value',
                    // data: [""]
                    splitLine: {
                        show: true
                    }
                },
                series: [
                    {
                        data: this.sjbQDatas[1],
                        type: 'line',
                        color: '#099E7A',
                    }
                ],
                grid: {
                    left: '0',
                    right: '0',
                    bottom: '0',
                    containLabel: true
                },
            });
        },

        queryMixProportionDetailResp() {
            this.$api.queryMixProportionDetail(`id=${this.$route.query.id}`, this).then(res => {
                if (res.code == 1) {
                    this.detailInfo = res.data;
                    this.proportionJsphb = this.detailInfo.proportionJsphb || []
                    this.proportionTld = this.detailInfo.proportionTld || {};
                    this.proportionMaterial = this.detailInfo.proportionMaterial || {};
                    this.proportionSjb = this.detailInfo.proportionSjb || {};
                    this.proportionYsl = this.detailInfo.proportionYsl || {};
                    this.proportionJncl = this.detailInfo.proportionJncl || {};
                    this.proportionSl = this.detailInfo.proportionSl || {};
                    this.proportionYz = this.detailInfo.proportionYz || {};

                    this.handleDetailInfo();
                    this.chartViewDraw();
                }else{
                    this.$message.error(res.msg || '查询失败')
                }
            });
        },

        handleDetailInfo() {
            let newDjList = [...this.djList];
            for (let iterator of newDjList) {
                switch (iterator.label) {
                    case "强度等级":
                        iterator.value = this.detailInfo.proportionQddj || '--';
                        continue;
                    case "抗折强度":
                        iterator.value = this.detailInfo.proportionKzqd || '--';
                        continue;
                    case "抗渗等级":
                        iterator.value = this.detailInfo.proportionKsdj || '--';
                        continue;
                    case "抗氯离子":
                        iterator.value = this.detailInfo.proportionQllz || '--';
                        continue;
                    case "设计坍落度":
                        iterator.value = (this.detailInfo.proportionTld.qz || '--') + "±" + (this.detailInfo.proportionTld.hz || '--');
                        continue;
                    default:
                        continue;
                }
            }

            this.djList = newDjList;
            this.proportionYL = this.proportionJsphb.find((element) => element.jzphbbs == 1);
        },

        // 获取适配记录
        handleAdaFilter() {
            this.$api.queryAdaptationPage({
                ...this.adaPageObj,
                params: {mixId: this.$route.query.id}
            }).then(res => {
                if (res.code == 1) {
                    this.adaTableData = res.data.list;
                    this.adaTotal = res.data.total;
                }else{
                    this.$message.error(res.msg || '查询失败')
                }
            });
        },

        mixProportionUpdateResp() {
            let proportionYz = {}
            this.loading = true;
            for (const item of this.yzSetFormItems) {
                if (item.label == "水") {
                    proportionYz.water = `${item.qz}-${item.hz}`;
                }else if (item.label == "水泥") {
                    proportionYz.sn = `${item.qz}-${item.hz}`;
                }else if (item.label == "矿渣粉") {
                    proportionYz.kzf = `${item.qz}-${item.hz}`;
                }else if (item.label == "粉煤灰") {
                    proportionYz.fmh = `${item.qz}-${item.hz}`;
                }else if (item.label == "粗骨料") {
                    proportionYz.cgl = `${item.qz}-${item.hz}`;
                }else if (item.label == "细骨料") {
                    proportionYz.xgl = `${item.qz}-${item.hz}`;
                }else if (item.label == "外加剂1") {
                    proportionYz.wjj1 = `${item.qz}-${item.hz}`;
                }else if (item.label == "外加剂2") {
                    proportionYz.wjj2 = `${item.qz}-${item.hz}`;
                }
            }
            this.$api.mixProportionUpdate({
                id: this.$route.query.id,
                proportionYz: proportionYz
            }, this).then(res => {
                if (res.code == 1) {
                    this.queryMixProportionDetailResp()
                }else{
                    this.$message.error(res.msg || '修改失败')
                }
            }).finally(() => {
                this.loading = false;
            })
        }
    },
}