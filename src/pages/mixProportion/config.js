/*
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-08-08 22:44:19
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2024-12-04 00:16:15
 * @FilePath: /quality_center_web/src/pages/mixProportion/config.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import moment from 'moment';
export const panelColumn = [
  {
    label: '委托编号',
    prop: 'experimentNo',
  },
  {
    label: '报告编号',
    prop: 'reportId',
  },
  {
    label: '委托原因',
    prop: 'entrustReasonName'
  },
  {
    label: '代表数量',
    prop: 'behalfNumber'
  },
  {
    label: '是否合格',
    prop: 'isQualified',
    formatter: (row) => {
      if (row.isQualified == 1) {
          return "合格";
      }else if (row.isQualified == 2) {
          return "不合格";
      }else{
          return "--";
      }
    },
  },
  {
    label: '物料名称',
    prop: 'materialsName',
  },
  {
    label: '物料规格',
    prop: 'materialsSpecs',
  },
  {
    label: '样品等级',
    prop: 'sampleLevel'
  },
  {
    label: '厂家简称',
    prop: 'factoryCalled'
  },
  {
    label: '供应商简称',
    prop: 'supplyCompanyCalled'
  },
  {
    label: "检验类型",
    prop: "checkType",
    // formatter: (row) => {
    //   if(row.checkType == 1){
    //     return "快检"
    //   }else if(row.checkType == 2){
    //     return "批检"
    //   }else{
    //     return "--"
    //   }
    // },
  },
  {
    label: '委托人',
    prop: 'entrustPersonName'
  },
  {
    label: '委托时间',
    prop: 'entrustTime',
    formatter: (row) => {
      if (!row.entrustTime) {
        return '--';
      }
      return moment(row.entrustTime).format('YYYY-MM-DD')
    },
  },
  // {
  //   label: '试验人',
  //   prop: 'experimentPersonName'
  // },
  // {
  //   label: '试验时间',
  //   prop: 'experimentTime',
  //   formatter: (row) => {
  //     if (!row.experimentTime) {
  //       return '--';
  //     }
  //     return moment(row.experimentTime).format('YYYY-MM-DD')
  //   },
  // },
  
  
  // {
  //   label: '校核人',
  //   prop: 'checkPersonName'
  // },
  // {
  //   label: '校核时间',
  //   prop: 'checkTime'
  // },
  // {
  //   label: '批准人',
  //   prop: 'approvePersonName'
  // },
  // {
  //   label: '批准时间',
  //   prop: 'approveTime'
  // }
]