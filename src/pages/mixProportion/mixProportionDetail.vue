<!--
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-05-26 00:17:13
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2024-10-10 22:43:22
 * @FilePath: /quality_center_web/src/pages/mixProportion/mixProportionDetail.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="content-box">
      <div class="flex-box flex-column content">
        <!-- 配合比、技术参数 -->
        <el-row class="flex-row" style="align-items: flex-start;">
          <el-col :span="12" style="margin-right: 12px">
            <el-row class="row-title">
              <span class="row-title-label">配合比详情</span>
            </el-row>
            
            <el-row class="flex-row dg-card-view">
              <div class="flex-col dj-card" :style="{background: item.background}" v-for="(item, index) in djList" :key="index">
                <p class="dj-value">{{item.value}}</p>
                <p class="dj-label">{{item.label}}</p>
                <img class="dj-img" :src="item.imgUrl" />
              </div>
            </el-row>
            <p style="margin-top: 16px; font-size: 14px;">
              <span style="color: #6A727D;">备注：</span>
              <span style="color: #1F2329;">{{ detailInfo.proportionRemarks }}</span>
            </p>
          </el-col>
          <el-col :span="12" style="margin-left: 12px">
            <el-row class="row-title flex-row" style="justify-content: space-between;">
              <span class="row-title-label">技术参数</span>
              <el-button type="text" @click="gotoYZSetting" class="flex-row" style="cursor: pointer; font-weight: 600;font-size: 14px;color: #1F57B3;"><i class="el-icon-setting"></i> 用量阈值设置</el-button>
            </el-row>
            <el-row class="table-view">
              <table>
                <thead>
                  <tr class="tr-header">
                    <th style="background: white;">单位：kg/m³</th>
                    <th>水</th>
                    <th>水泥</th>
                    <th>矿渣粉</th>
                    <th>粉煤灰</th>
                    <th>粗骨料</th>
                    <th>细骨料</th>
                    <th>外加剂1</th>
                    <th>外加剂2</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>用量</td>
                    <td class="td-label">{{proportionYL.water}}</td>
                    <td class="td-label">{{proportionYL.cement}}</td>
                    <td class="td-label">{{proportionYL.kzf}}</td>
                    <td class="td-label">{{proportionYL.fmh}}</td>
                    <td class="td-label">{{proportionYL.cgl}}</td>
                    <td class="td-label">{{proportionYL.xgl}}</td>
                    <td class="td-label">{{proportionYL.jsj}}</td>
                    <td class="td-label">{{proportionYL.pzj}}</td>
                  </tr>
                  <tr>
                    <td>阈值范围</td>
                    <td>{{proportionYz.water}}</td>
                    <td>{{proportionYz.sn}}</td>
                    <td>{{proportionYz.kzf}}</td>
                    <td>{{proportionYz.fmh}}</td>
                    <td>{{proportionYz.cgl}}</td>
                    <td>{{proportionYz.xgl}}</td>
                    <td>{{proportionYz.wjj1}}</td>
                    <td>{{proportionYz.wjj2}}</td>
                  </tr>
                </tbody>
              </table>
            </el-row>
            <el-row class="flex-row cs-view">
              <div class="flex-row">
                <span>配置强度：</span>
                <span style="font-weight: 600; font-size: 18px; color: #1F2329;">{{pzqd}}MPa</span>
              </div>
              <div class="flex-row">
                <span>水胶比：</span>
                <span style="font-weight: 600; font-size: 18px; color: #1F2329;">{{proportionSjb.sjb || '--'}}</span>
              </div>
              <div class="flex-row">
                <span>砂率：</span>
                <span style="font-weight: 600; font-size: 18px; color: #1F2329;">{{proportionSl.sl || '--'}}%</span>
              </div>
              <div class="flex-row">
                <span>容重：</span>
                <span style="font-weight: 600; font-size: 18px; color: #1F2329;">{{detailInfo.proportionRz || '--'}}Kg</span>
              </div>
            </el-row>
          </el-col>
        </el-row>
        <!-- 强度与水胶比线形图 -->
        <el-row class="flex-row" style="margin-top: 40px">
          <el-col :span="24">
            <el-row class="row-title">
              <span class="row-title-label">强度与水胶比线形图</span>
            </el-row>

            <el-row class="flex-row">
              <el-col :span="12" class="chart-view">
                <div class="flex-row">
                  <span>强度MPa</span>
                  <!-- proportionJsphb 里面的 sjb 和 kyqdPjz -->
                  <span>强度与水胶比线形图</span>
                </div>
                <div id="qdsjb-chart" class="qdsjb-chart"></div>
              </el-col>

              <el-col :span="12" class="flex-row" style="margin-top: 24px">
                <div class="flex-col sjb-view" v-for="(item, index) in proportionJsphb" :key="index">
                  <div class="flex-col no-view">{{ item.proportionNo }}</div>
                  <div class="flex-col value-view">
                    <p class="value-label">{{ item.kyqdPjz || '--' }} MPa</p>
                    <p class="desc-label">强度</p>
                  </div>
                  <div style="width: 70%; height: 1px; background: #DDDFE6; margin-top: 37px" />
                  <div class="flex-col value-view">
                    <p class="value-label">{{ item.sjb || '--' }}</p>
                    <p class="desc-label">水胶比</p>
                  </div>
                </div>
              </el-col>
            </el-row>
          </el-col>
        </el-row>
        <!-- 试配记录 -->
        <el-row class="sub-table">
          <el-col :span="24">
            <el-row class="row-title">
              <span class="row-title-label">试配记录</span>
            </el-row>

            <el-row class="table-view">
              <el-table
                :data="adaTableData"
                height="100%"
                border
                >
                <template v-for="item in adaTableColumn" >
                  <el-table-column
                    :prop="item.prop" 
                    :label="item.label" 
                    :fixed="item.fixed" 
                    :width="item.width || ''"
                    :formatter="item.formatter"
                    align="center" 
                  />
                </template>
                
                
                <el-table-column width="100" label="操作" align="center" key="handle" fixed="right" :resizable="false">
                  <template slot-scope="scope">
                    <el-button type="text" size="small">查看试验</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-row>

            <el-row class="page-view">
              <Pagination
                :total="adaTotal" 
                :pageNum="adaPageObj.pageNum" 
                :pageSize="adaPageObj.pageSize" 
                @getData="handleAdaFilter" 
              />
            </el-row>
          </el-col>
        </el-row>
        <!-- 历史生产任务暂时没有 -->
        <el-row>

        </el-row>
      </div>

      <el-drawer
        title="用量阈值设置"
        :visible.sync="showYZSetting"
        :before-close="handleClose"
        >
        <div >
          <el-form>
            <el-form-item class="flex-row yz-form-item" v-for="(item, index) in yzSetFormItems" :key="index">
              <span style="width: 100px; text-align: right; display: inline-block;">{{ item.label }}：</span>
              <el-input v-model="item.qz" style="width: 140px;" />
              <span style="padding: 10px;">至</span>
              <el-input v-model="item.hz" style="width: 140px;"/>
              <span style="padding: 10px;">kg/m³</span>
            </el-form-item>
          </el-form>
          <div class="yzset-drawer__footer">
            <el-button >取 消</el-button>
            <el-button type="primary" :loading="loading" @click="mixProportionUpdateResp()">{{ loading ? '提交中 ...' : '确 定' }}</el-button>
          </div>
        </div>
      </el-drawer>
  </div>
</template>

<script>
import { mixMixedDetail } from './mixMixedDetail.js'
import Pagination from "@/components/Pagination/index.vue";
export default {
  mixins: [mixMixedDetail],
  components: {
    Pagination,
  },
  
  data() {
    return {
      showYZSetting: false,
      yzSetFormItems: [
        {
          label: "水",
          qz: "",
          hz: ""
        },
        {
          label: "水泥",
          qz: "",
          hz: ""
        },
        {
          label: "矿渣粉",
          qz: "",
          hz: ""
        },
        {
          label: "粉煤灰",
          qz: "",
          hz: ""
        },
        {
          label: "粗骨料",
          qz: "",
          hz: ""
        },
        {
          label: "细骨料",
          qz: "",
          hz: ""
        },
        {
          label: "外加剂1",
          qz: "",
          hz: ""
        },
        {
          label: "外加剂2",
          qz: "",
          hz: ""
        }
      ]
    }
  },

  methods: {
    gotoYZSetting() {
      this.showYZSetting = true;
      if (this.proportionYz) {
        for (const item of this.yzSetFormItems) {
          if (item.label == '水') {
            item.qz = (this.proportionYz.water || "-").split("-")[0];
            item.hz = (this.proportionYz.water || "-").split("-")[1];
          }else if (item.label == '水泥') {
            item.qz = (this.proportionYz.sn || "-").split("-")[0];
            item.hz = (this.proportionYz.sn || "-").split("-")[1];
          }else if (item.label == '矿渣粉') {
            item.qz = (this.proportionYz.kzf || "-").split("-")[0];
            item.hz = (this.proportionYz.kzf || "-").split("-")[1];
          }else if (item.label == '粉煤灰') {
            item.qz = (this.proportionYz.fmh || "-").split("-")[0];
            item.hz = (this.proportionYz.fmh || "-").split("-")[1];
          }else if (item.label == '粗骨料') {
            item.qz = (this.proportionYz.cgl || "-").split("-")[0];
            item.hz = (this.proportionYz.cgl || "-").split("-")[1];
          }else if (item.label == '细骨料') {
            item.qz = (this.proportionYz.xgl || "-").split("-")[0];
            item.hz = (this.proportionYz.xgl || "-").split("-")[1];
          }else if (item.label == '外加剂1') {
            item.qz = (this.proportionYz.wjj1 || "-").split("-")[0];
            item.hz = (this.proportionYz.wjj1 || "-").split("-")[1];
          }else if (item.label == '外加剂2') {
            item.qz = (this.proportionYz.wjj2 || "-").split("-")[0];
            item.hz = (this.proportionYz.wjj2 || "-").split("-")[1];
          }
        }
      }
    },

    handleClose(done) {
        this.$confirm('确认关闭？')
        .then(_ => {
            if(done){
                done();
            }
            this.showYZSetting = false;
        })
        .catch(_ => {});
    }
  },
}
</script>

<style lang="scss" scoped>
.content-box{
    padding: 16px;
}

.content{
    width: 100%;
    height: 100%;
    padding: 16px;
    background: #FFFFFF;
    border-radius: 16px;
    overflow: auto;
}
.row-title {
  border-bottom: #DDDFE6 1px solid;
  padding-bottom: 8px;
  .row-title-label {
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 16px;
    color: #1F2329;
  }
}
.dg-card-view {
  margin-top: 24px;
  width: 100%;
  .dj-card {
    width: 177px;
    height: 72px;
    border-radius: 4px;
    position: relative;
    justify-content: center;
    .dj-label {
      font-family: PingFangSC, PingFang SC;
      font-size: 14px;
      color: #6A727D;
      margin-top: 4px
    }
    .dj-value {
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      font-size: 20px;
      color: #1F2329;
    }
    .dj-img {
      width: 46px;
      height: 46px;
      position: absolute;
      right: 0;
      bottom: 0
    }
  }
}
.table-view {
  margin-top: 24px;
  width: 100%;
  table,table tr th, table tr td { border:1px solid #DDDFE6; padding: 11px}
  table { 
    text-align: center; 
    border-collapse: collapse; 
    width: 100%;
    font-size: 14px;
    color: #6A727D;
    .tr-header {
      th {
        background: #F2F6FE;
        min-width: 70px;
      }
    }
    .td-label {
      font-weight: 600;
      font-size: 18px;
      color: #1F2329;
    }
  }
}
.cs-view {
  width: '100%';
  height: 52px;
  background: #F2F6FE;
  border-radius: 4px;
  margin-top: 16px;
  padding-left: 16px;
  padding-right: 16px;
  font-size: 14px;
  color: #6A727D;
}
.chart-view {
  margin-right: 12px; 
  border: #DDDFE6 1px solid; 
  border-radius: 4px;
  height: 331px;
  margin-top: 24px;
  padding: 16px;
  .qdsjb-chart {
    width: 100%;
    height: 260px;
    margin-top: 16px;
  }
}
.sjb-view {
  width: calc((100% - 20px) / 3);
  height: 331px;
  border-radius: 4px;
  border: 1px solid #DDDFE6;
  justify-content: flex-start;

  .no-view {
    background: rgba(80,127,153,0.1);
    padding: 24px;
    width: 100%;
    text-align: center;
  }
  .value-view {
    margin-top: 38px;
    .value-label {
      font-weight: 600;
      font-size: 20px;
      color: #1F2329;
    }
    .desc-label {
      font-size: 14px;
      color: #6A727D;
    }
  }
}

.yz-form-item {
  font-weight: 400;
  font-size: 14px;
  color: #1F2021;
  justify-content: flex-start;
  padding-left: 24px;
  padding-bottom: 0px;
}
.yzset-drawer__footer {
  position: absolute;
  bottom: 30px;
  right: 30px;
}

.sub-table {
  position: relative;
  height: 400px;
  margin-top: 40px;
  .table-view {
    height: 360px
  }
  .page-view {
    margin-top: 10px
  }
}
::v-deep .el-table__empty-block { // Element自带类名
  height: 360px !important;
}
::v-deep .el-row::before, .el-row::after {
  content: revert;
}
::-webkit-scrollbar {
	display:none
}
</style>