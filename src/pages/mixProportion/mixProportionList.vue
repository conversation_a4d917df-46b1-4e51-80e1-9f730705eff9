<!--
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-05-24 23:38:58
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-08-03 15:35:09
 * @FilePath: /quality_center_web/src/pages/mixProportion/mixProportionList.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div class="content-box">
      <div class="flex-box flex-column content">
        <div class="search-box flex-box">
          <el-form class="flex-item" ref="searchForm" :inline="true" :model="searchForm">
            <el-form-item label="配合比编号：">
              <el-input v-model="searchForm.proportionPhb" clearable
                placeholder="请输入" 
                style="width: 180px" 
              />
            </el-form-item>
            <el-form-item label="强度等级：">
              <el-select
                v-model="searchForm.proportionQddjList" 
                filterable clearable 
                
                multiple
                placeholder="请选择等级强度" 
                style="width: 300px">
                
                <el-option
                  v-for="item in ddList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
  
            <el-form-item label="抗渗等级：">
              <el-select
                v-model="searchForm.proportionKsdjList" 
                filterable clearable 
                
                multiple
                placeholder="请选择抗渗等级" 
                style="width: 300px">
                
                <el-option
                  v-for="item in ksList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="备注：">
              <el-input v-model="searchForm.proportionRemarks" clearable
                placeholder="请输入" 
                style="width: 180px" 
              />
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" @click="handleFilter">搜索</el-button>
              <el-button type="text" icon="el-icon-refresh-right" @click="resetForm()">重置</el-button>
            </el-form-item>
          </el-form>
          <el-button type="primary" @click="gotoAdd">新增</el-button>
          <!-- <el-button type="primary" @click="printInfoByMixIds">打印</el-button> -->
          <!-- <el-button type="primary" @click="exportClick">导出配合比</el-button>
           -->
          <el-upload
              ref="uploadImport"
              :action="baseUrl + '/mixProportion/frontEndImportProportion'"
              :show-file-list="false"
              :auto-upload="true"
              :before-upload="importClickBefore"
              :on-success="importClickSuccess">
          </el-upload>
          <el-button style="margin-left: 10px;" type="primary" @click="importClick">导入配合比</el-button>
        </div>
        
        <div class="flex-item overHide">
          <div class="scroll-div">
            <el-table
              :data="tableData"
              :highlight-current-row="true"
              @selection-change="mixSelectionChange"
              >
              <el-table-column
                type="selection"
                width="55">
              </el-table-column>

              <el-table-column type="expand">
                <template slot-scope="props">
                  <div class="table-child-box">
                    <el-table :data="props.row.childrenData"
                      border
                      style="width: 100%;"
                    >
                      <el-table-column 
                        v-for="item in childrenColumn" 
                        :key="item.label"
                        :prop="item.prop"
                        :label="item.label" 
                        :formatter="item.formatter"
                        :show-overflow-tooltip="true"
                        align="center" 
                      >
                        <!-- <template slot-scope="scope">
                          
                        </template> -->
                      </el-table-column>
                    </el-table>
                  </div>
                </template>
              </el-table-column>
              
              <template v-for="item in tableColumn" >
                <el-table-column
                  :key="item.prop" 
                  :prop="item.prop" 
                  :label="item.label" 
                  :fixed="item.fixed" 
                  :width="item.width || ''"
                  :formatter="item.formatter"
                  :show-overflow-tooltip="true"
                  align="center" 
                />
              </template>
              
              <el-table-column width="240" label="操作" align="center" key="handle" fixed="right" :resizable="false">
                <template slot-scope="scope">
                  <!-- <el-button type="text" size="small" @click="goDetail(scope.row)">详情</el-button> -->
                  <!-- <el-button type="text" size="small" @click="modifiRecords(scope.row)">修改记录</el-button> -->
                  <el-button type="text" size="small" @click="mixProportionVerifyRecord(scope.row)">验证记录</el-button> 
                  <el-button type="text" size="small" @click="gotoEdit(scope.row)">编辑</el-button>
                  <el-button type="text" size="small" @click="gotoEdit(scope.row, 'copy')">复制新建</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
        <div class="mt16 mb4">
          <Pagination
            :total="total" 
            :pageNum="pageObj.pageNum" 
            :pageSize="pageObj.pageSize" 
            @getData="initData" 
          />
        </div>
      </div>

      <!-- 弹出一个dialog 上面显示历史列表 -->
      <el-dialog
        title="修改记录"
        width="700px"
        :visible.sync="updateTableShow"
        class="claim-dialog-box"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
      >
        <div class="flex-box flex-column h100p drawer-box">
          <div class="flex-item">
            <el-table
              ref="multipleTable"
              :data="updateTableData"
              tooltip-effect="dark"
              style="width: 100%"
            >
              <el-table-column
                type="selection"
                width="55">
              </el-table-column>
              <el-table-column
                prop="equipmentNo"
                label="设备编号"
                width="120">
              </el-table-column>
              <el-table-column
                prop="equipmentName"
                label="设备名称"
                width="120">
              </el-table-column>
              <el-table-column
                prop="equipmentType"
                label="设备类型"
                width="120">
              </el-table-column>
              <el-table-column
                prop="technicalParameter"
                label="技术参数"
                width="120">
              </el-table-column>
              <el-table-column
                prop="equipmentStatus"
                label="设备状态"
                width="120">
              </el-table-column>
              <el-table-column
                prop="custodyUserName"
                label="保管人"
                show-overflow-tooltip>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </el-dialog>

      <!-- 弹出一个dialog 上面显示历史列表 -->
      <el-dialog
        title="验证记录"
        width="90%"
        :visible.sync="verifyRecordShow"
        class="claim-dialog-box"
        @before-close="handleVerifyRecordClose"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
      >
          <div class="flex-box flex-column h100p drawer-box">
            <div class="search-box flex-box">
            <el-form class="flex-item" ref="verifyRecordSearchForm" :inline="true" :model="verifyRecordSearchForm" :disabled="verifyRecordLoading">
              <el-form-item label="工程名称：">
                <el-input v-model="verifyRecordSearchForm.projectName" clearable
                  placeholder="请输入工程名称" 
                  style="width: 360px" 
                />
              </el-form-item>
              
              <el-form-item>
                <el-button type="primary" :disabled="verifyRecordLoading" @click="handleVerifyRecordSerch">搜索</el-button>
              </el-form-item>
            </el-form>
            <el-button type="primary" @click="handleSetVerifyRecordTarget()">新增记录</el-button>
          </div>
          <div class="flex-item">
            <el-table
              :data="verifyRecordList"
              :loading="verifyRecordLoading"
              style="width: 100%; height: 500px;">
              <af-table-column
                v-for="item in verifyRecordColumn" 
                :key="item.prop" :prop="item.prop" 
                :label="item.label" 
                :formatter="item.formatter"
                :fixed="item.fixed" 
                :width="item.width || ''"
                :show-overflow-tooltip="true"
                align="center" 
              />
              <af-table-column width="150" label="操作" align="center" key="handle" :resizable="false">
                <template slot-scope="scope">
                  <el-button type="text" size="small" @click="handleSetVerifyRecordTarget(scope.row)">修改</el-button>
                  <el-button type="text" size="small" style="color: #ff0000;" @click="handleVerifyRecordDel(scope.row)">删除</el-button>
                </template>
              </af-table-column>
            </el-table>
          </div>

          <div class="mt16 mb4">
            <Pagination
              :total="verifyRecordTotal" 
              :pageNum="verifyRecordPageObj.pageNum" 
              :pageSize="verifyRecordPageObj.pageSize" 
              @getData="getMixProportionVerifyRecordListResp" 
            />
          </div>
        </div>
      </el-dialog>

      <el-drawer
        title="验证记录"
        :size="800"
        :visible.sync="editVerifyRecordShow"
        class="cus-drawer"
        :before-close="closeVerifyRecordShow"
      >
        <el-form ref="editVerifyRecordForm" label-width="160px" 
            :model="editVerifyRecordForm"
            :rules="editVerifyRecordRules"
            :show-message="false"
          >
            <div class="flex-row" style="justify-content: flex-start;">
              <div>
                <el-form-item
                  v-for="(el,index) in verifyRecordFormContent"
                  v-show="el.show"
                  :key="index"
                  :label="`${el.label}：`"
                  :required="el.required || false"
                  :prop="el.prop"
                >
                  <el-select
                    v-if="el.type === 'select'"
                    v-model="editVerifyRecordForm[el.prop]" 
                    :disabled="el.disabled"
                    :multiple="el.multiple"
                    @change="el['change']"
                    @visible-change="el['visibleChange']"
                    filterable clearable 
                    :value-key="el.valueKey"
                    :placeholder="el.placeholder || `请选择${el.label}`" 
                    :style="{'width': el.width ? el.width : 350 + 'px'}"
                  >
                    <el-option
                      v-for="(item, index) in el.options"
                      :key="index"
                      :label="item.label"
                      :value="item.value">
                    </el-option>
                  </el-select>

                  <el-input 
                    v-else-if="el.type === 'input'"
                    v-model="editVerifyRecordForm[el.prop]" 
                    :disabled="el.disabled"
                    clearable
                    :placeholder="el.placeholder || `请输入${el.label}`"
                    style="width: 350px"
                  />
                </el-form-item>
              </div>

              <div>
                <el-form-item
                  v-for="(el,index) in verifyRecordFormContentRight"
                  v-show="el.show"
                  :key="index"
                  :label="`${el.label}：`"
                  :required="el.required || false"
                  :prop="el.prop"
                >
                  <div >
                    <el-select
                      v-if="el.type === 'select'"
                      v-model="editVerifyRecordForm[el.prop]" 
                      :disabled="el.disabled"
                      :multiple="el.multiple"
                      @change="el['change']"
                      @visible-change="el['visibleChange']"
                      filterable clearable 
                      :value-key="el.valueKey"
                      :placeholder="el.placeholder || `请选择${el.label}`" 
                      :style="{'width': el.width ? el.width : 350 + 'px'}"
                    >
                      <el-option
                        v-for="(item, index) in el.options"
                        :key="index"
                        :label="item.label"
                        :value="item.value">
                      </el-option>
                    </el-select>

                    <el-input 
                      v-else-if="el.type === 'input'"
                      v-model="editVerifyRecordForm[el.prop]" 
                      :disabled="el.disabled"
                      clearable
                      :placeholder="el.placeholder || `请输入${el.label}`"
                      style="width: 350px"
                    />
                  </div>
                </el-form-item>
              </div>
            </div>
        </el-form>

        <div class="drawer-footer">
          <el-button type="primary" @click="closeVerifyRecordShow" plain>取消</el-button>
          <el-button type="primary" @click="saveEditVerifyRecordForm">保存</el-button>
        </div>
      </el-drawer>
    </div>
  </template>
  
<script>
import Pagination from "@/components/Pagination/index.vue";
import { mixMixedList } from './mixMixedList.js'
  import {calcEquation, cpEvenRound} from "@/utils/calculate.js"
import DrawerForm from "@/components/drawerForm.vue";
export default {
    components: {
      Pagination,
      DrawerForm
    },
    data() {
      return {
        baseUrl: process.env.NODE_ENV === 'development' ? "/api" : "/mes-platform",
        childrenColumn: [
          {
            prop: "name",
            label: "原材料名称",
          },
          {
            prop: "xhbgbh",
            label: "协会报告编号",
          },
          // {
          //   prop: "batch",
          //   label: "批号",
          // },
          // {
          //   prop: "ypmc",
          //   label: "样品名称",
          // },
          // {
          //   prop: "ypdj",
          //   label: "等级",
          // },
          // {
          //   prop: "ypgg",
          //   label: "规格",
          // },
          {
            prop: "clmc",
            label: "材料名称",
          },
          {
            prop: "clgg",
            label: "材料规格",
          },
          {
            prop: "cj",
            label: "厂家",
          },
          {
            prop: "scyl",
            label: "生产用量",
          },
          {
            prop: "scylb",
            label: "生产用量比例",
            formatter: (row) => {
              let value = row.scylb || '---';
              if(value !== '---') {
                return cpEvenRound(value, 2);
              }
              return '---'
            },
          },
          {
            prop: "dyyl",
            label: "打印用量",
          },
          {
            prop: "dyylb",
            label: "打印用量比例",
            formatter: (row) => {
              let value = row.dyylb || '---';
              if(value !== '---') {
                return cpEvenRound(value, 2);
              }
              return '---'
            },
          },
        ],
        updateTableData: [],
        updateTableShow: false,
        filePrefix: this.$store.state.loginStore.userInfo.filePrefix,

        verifyRecordSearchForm: {
          projectName: ""
        },
        verifyRecordShow: false,
        editVerifyRecordShow: false,
        verifyRecordList: [],
        selectedMixId: "",
        verifyRecordColumn: [
          {
            label: '工程名称',
            prop: 'projectName',
          },
          {
            label: '合同编号',
            prop: 'contractNo',
          },
          {
            label: '水泥厂家',
            prop: 'snFactory',
          },
          {
            label: '水泥供应商',
            prop: 'snSupplier',
          },
          {
            label: '粗骨料厂家',
            prop: 'cglFactory',
          },
          {
            label: '粗骨料供应商',
            prop: 'cglSupplier',
          },
          {
            label: '细骨料厂家',
            prop: 'xglFactory',
          },
          {
            label: '细骨料供应商',
            prop: 'xglSupplier',
          },
          {
            label: '粉煤灰厂家',
            prop: 'fmh1Factory',
          },
          {
            label: '粉煤灰供应商',
            prop: 'fmh1Supplier',
          },
          {
            label: '矿渣粉厂家',
            prop: 'kzf1Factory',
          },
          {
            label: '矿渣粉供应商',
            prop: 'kzf1Supplier',
          },
          {
            label: '外加剂1厂家',
            prop: 'wjj1Factory',
          },
          {
            label: '外加剂1供应商',
            prop: 'wjj1Supplier',
          },
          {
            label: '外加剂2厂家',
            prop: 'wjj2Factory',
          },
          {
            label: '外加剂2供应商',
            prop: 'wjj2Supplier',
          },
          {
            label: '外掺料1厂家',
            prop: 'wcl1Factory',
          },
          {
            label: '外掺料1供应商',
            prop: 'wcl1Supplier',
          },
          {
            label: '外掺料2厂家',
            prop: 'wcl2Factory',
          },
          {
            label: '外掺料2供应商',
            prop: 'wcl2Supplier',
          },
        ],
        verifyRecordTotal: 0,
        verifyRecordPageObj: {
          pageNum: 1,
          pageSize: 20
        },
        verifyRecordLoading: false,
        // 1-水泥 2-粉煤灰 3-矿渣粉 4-粗骨料 5-细骨料 6-外加剂 7-混凝土
        verifyRecordFormContent: [
          {
            type: 'select',
            label: '工程名称',
            prop: 'projectName',
            valueKey: "id",
            required: true,
            visibleChange: (item) => this.visibleProjectChangeHandle(item),
            change: (item) => this.selectProject(item),
            options: [],
            show: true
          },
          {
            type: 'select',
            label: '水泥厂家',
            prop: 'snFactory',
            valueKey: "manufacturers",
            visibleChange: (item) => this.visibleChangeHandle(item, 1, 1),
            change: (item) => this.selectFactory(item, 1, "snFactory"),
            mType: 1,
            options: [],
            show: true
          },
          {
            type: 'input',
            label: '水泥厂家简称',
            prop: 'snFactoryCalled',
            disabled: true,
            show: false
          },
          {
            type: 'select',
            label: '粗骨料厂家',
            prop: 'cglFactory',
            valueKey: "manufacturers",
            visibleChange: (item) => this.visibleChangeHandle(item, 4, 3),
            change: (item) => this.selectFactory(item, 4, "cglFactory"),
            mType: 4,
            options: [],
            show: true
          },
          {
            type: 'input',
            label: '粗骨料厂家简称',
            prop: 'cglFactoryCalled',
            disabled: true,
            show: false
          },
          {
            type: 'select',
            label: '细骨料厂家',
            prop: 'xglFactory',
            valueKey: "manufacturers",
            visibleChange: (item) => this.visibleChangeHandle(item, 5, 5),
            change: (item) => this.selectFactory(item, 5, "xglFactory"),
            mType: 5,
            options: [],
            show: true
          },
          {
            type: 'input',
            label: '细骨料厂家简称',
            prop: 'xglFactoryCalled',
            disabled: true,
            show: false
          },
          {
            type: 'select',
            label: '粉煤灰厂家',
            prop: 'fmh1Factory',
            valueKey: "manufacturers",
            visibleChange: (item) => this.visibleChangeHandle(item, 2, 7),
            change: (item) => this.selectFactory(item, 2, "fmh1Factory"),
            mType: 2,
            options: [],
            show: true
          },
          {
            type: 'input',
            label: '粉煤灰厂家简称',
            prop: 'fmh1FactoryCalled',
            disabled: true,
            show: false
          },
          {
            type: 'select',
            label: '矿渣粉厂家',
            prop: 'kzf1Factory',
            valueKey: "manufacturers",
            visibleChange: (item) => this.visibleChangeHandle(item, 3, 9),
            change: (item) => this.selectFactory(item, 3, "kzf1Factory"),
            mType: 3,
            options: [],
            show: true
          },
          {
            type: 'input',
            label: '矿渣粉厂家简称',
            prop: 'kzf1FactoryCalled',
            disabled: true,
            show: false
          },
          {
            type: 'select',
            label: '外加剂1厂家',
            prop: 'wjj1Factory',
            valueKey: "manufacturers",
            visibleChange: (item) => this.visibleChangeHandle(item, 6, 11),
            change: (item) => this.selectFactory(item, 6, "wjj1Factory"),
            mType: 6,
            options: [],
            show: true
          },
          {
            type: 'input',
            label: '外加剂1厂家简称',
            prop: 'wjj1FactoryCalled',
            disabled: true,
            show: false
          },
          {
            type: 'select',
            label: '外加剂2厂家',
            prop: 'wjj2Factory',
            valueKey: "manufacturers",
            visibleChange: (item) => this.visibleChangeHandle(item, 6, 13),
            change: (item) => this.selectFactory(item, 6, "wjj2Factory"),
            mType: 6,
            options: [],
            show: true
          },
          {
            type: 'input',
            label: '外加剂2厂家简称',
            prop: 'wjj2FactoryCalled',
            disabled: true,
            show: false
          },
          {
            type: 'select',
            label: '外掺料1厂家',
            prop: 'wcl1Factory',
            valueKey: "manufacturers",
            visibleChange: (item) => this.visibleChangeHandle(item, 2, 15),
            change: (item) => this.selectFactory(item, 2, "wcl1Factory"),
            mType: 2,
            options: [],
            show: true
          },
          {
            type: 'input',
            label: '外掺料1厂家简称',
            prop: 'wcl1FactoryCalled',
            disabled: true,
            show: false
          },
          {
            type: 'select',
            label: '外掺料2厂家',
            prop: 'wcl2Factory',
            valueKey: "manufacturers",
            visibleChange: (item) => this.visibleChangeHandle(item, 3, 17),
            change: (item) => this.selectFactory(item, 3, "wcl2Factory"),
            mType: 3,
            options: [],
            show: true
          },
          {
            type: 'input',
            label: '外掺料2厂家简称',
            prop: 'wcl2FactoryCalled',
            disabled: true,
            show: false
          },
        ],
        verifyRecordFormContentRight: [
          {
            type: 'input',
            label: '合同编号',
            prop: 'contractNo',
            disabled: true,
            show: true
          },
          {
            type: 'select',
            label: '水泥供应商',
            prop: 'snSupplier',
            valueKey: "supplierName",
            visibleChange: (item) => this.visibleSupplierChangeHandle(item, 1, 1),
            change: (item) => this.selectSupplier(item, 1, "snSupplier"),
            mType: 1,
            options: [],
            show: true
          },
          {
            type: 'input',
            label: '水泥供应商简称',
            prop: 'snSupplierCalled',
            disabled: true,
            show: false
          },
          {
            type: 'select',
            label: '粗骨料供应商',
            prop: 'cglSupplier',
            valueKey: "supplierName",
            visibleChange: (item) => this.visibleSupplierChangeHandle(item, 4, 3),
            change: (item) => this.selectSupplier(item, 4, "cglSupplier"),
            mType: 4,
            options: [],
            show: true
          },
          {
            type: 'input',
            label: '粗骨料供应商简称',
            prop: 'cglSupplierCalled',
            disabled: true,
            show: false
          },
          {
            type: 'select',
            label: '细骨料供应商',
            prop: 'xglSupplier',
            valueKey: "supplierName",
            visibleChange: (item) => this.visibleSupplierChangeHandle(item, 5, 5),
            change: (item) => this.selectSupplier(item, 5, "xglSupplier"),
            mType: 5,
            options: [],
            show: true
          },
          {
            type: 'input',
            label: '细骨料供应商简称',
            prop: 'xglSupplierCalled',
            disabled: true,
            show: false
          },
          {
            type: 'select',
            label: '粉煤灰供应商',
            prop: 'fmh1Supplier',
            valueKey: "supplierName",
            visibleChange: (item) => this.visibleSupplierChangeHandle(item, 2, 7),
            change: (item) => this.selectSupplier(item, 2, "fmh1Supplier"),
            mType: 2,
            options: [],
            show: true
          },
          {
            type: 'input',
            label: '粉煤灰供应商简称',
            prop: 'fmh1SupplierCalled',
            disabled: true,
            show: false
          },
          {
            type: 'select',
            label: '矿渣粉供应商',
            prop: 'kzf1Supplier',
            valueKey: "supplierName",
            visibleChange: (item) => this.visibleSupplierChangeHandle(item, 3, 9),
            change: (item) => this.selectSupplier(item, 3, "kzf1Supplier"),
            mType: 3,
            options: [],
            show: true
          },
          {
            type: 'input',
            label: '矿渣粉供应商简称',
            prop: 'kzf1SupplierCalled',
            disabled: true,
            show: false
          },
          {
            type: 'select',
            label: '外加剂1供应商',
            prop: 'wjj1Supplier',
            valueKey: "supplierName",
            visibleChange: (item) => this.visibleSupplierChangeHandle(item, 6, 11),
            change: (item) => this.selectSupplier(item, 6, "wjj1Supplier"),
            mType: 6,
            options: [],
            show: true
          },
          {
            type: 'input',
            label: '外加剂1供应商简称',
            prop: 'wjj1SupplierCalled',
            disabled: true,
            show: false
          },
          {
            type: 'select',
            label: '外加剂2供应商',
            prop: 'wjj2Supplier',
            valueKey: "supplierName",
            visibleChange: (item) => this.visibleSupplierChangeHandle(item, 6, 13),
            change: (item) => this.selectSupplier(item, 6, "wjj2Supplier"),
            mType: 6,
            options: [],
            show: true
          },
          {
            type: 'input',
            label: '外加剂2供应商简称',
            prop: 'wjj2SupplierCalled',
            disabled: true,
            show: false
          },
          {
            type: 'select',
            label: '外掺料1供应商',
            prop: 'wcl1Supplier',
            valueKey: "supplierName",
            visibleChange: (item) => this.visibleSupplierChangeHandle(item, 2, 15),
            change: (item) => this.selectSupplier(item, 2, "wcl1Supplier"),
            mType: 2,
            options: [],
            show: true
          },
          {
            type: 'input',
            label: '外掺料1供应商简称',
            prop: 'wcl1SupplierCalled',
            disabled: true,
            show: false
          },
          {
            type: 'select',
            label: '外掺料2供应商',
            prop: 'wcl2Supplier',
            valueKey: "supplierName",
            visibleChange: (item) => this.visibleSupplierChangeHandle(item, 3, 17),
            change: (item) => this.selectSupplier(item, 3, "wcl2Supplier"),
            mType: 3,
            options: [],
            show: true
          },
          {
            type: 'input',
            label: '外掺料2供应商简称',
            prop: 'wcl2SupplierCalled',
            disabled: true,
            show: false
          },
        ],
        editVerifyRecordForm: {
          id: "",
          mixId: "",
          projectId: "",
          projectName: "",
          contractNo: "",

          snFactory: "",
          snFactoryCalled: "",

          cglFactory: "",
          cglFactoryCalled: "",

          xglFactory: "",
          xglFactoryCalled: "",

          fmh1Factory: "",
          fmh1FactoryCalled: "",

          kzf1Factory: "",
          kzf1FactoryCalled: "",

          wjj1Factory: "",
          wjj1FactoryCalled: "",
          wjj2Factory: "",
          wjj2FactoryCalled: "",

          wcl1Factory: "",
          wcl1FactoryCalled: "",
          wcl2Factory: "",
          wcl2FactoryCalled: "",

          snSupplier: "",
          cglSupplier: "",
          xglSupplier: "",
          fmh1Supplier: "",
          kzf1Supplier: "",
          wjj1Supplier: "",
          wjj2Supplier: "",
          wcl1Supplier: "",
          wcl2Supplier: "",

          snSupplierCalled: "",
          cglSupplierCalled: "",
          xglSupplierCalled: "",
          fmh1SupplierCalled: "",
          kzf1SupplierCalled: "",
          wjj1SupplierCalled: "",
          wjj2SupplierCalled: "",
          wcl1SupplierCalled: "",
          wcl2SupplierCalled: "",

        },
        editVerifyRecordRules: {},

        mixSelects: []
      };
    },
    mixins: [mixMixedList],
    
    mounted() {
      this.queryMaterialsSpecConfigAllResp();
      this.initData();
    },
    methods: {
      goDetail(row) {
        this.$router.push({
          name: 'mixProportionDetail',
          query: {id: row.id}
        })
      },

      gotoAdd() {
        this.$router.push({
          name: 'addMixProportion',
          query: {
            timestemp: Date.now()
          }
        })
      },

      gotoEdit(row, actType) {
        this.$router.push({
          name: 'addMixProportion',
          query: {
            id: row.id,
            timestemp: Date.now(),
            actType: actType
          }
        })
      },

      modifiRecords(row) {
        this.updateTableShow = true;
        this.$api.mixProportionUpdateLogGetAll({
          mixId: row.id
        }).then(res => {
          if(res.succ){
            this.updateTableData = res.data.list;
          }else{
              this.$message.error(res.msg || '查询失败')
          }
        })
      },

      exportClick() {
        this.$api.exportMixProportion().then(res => {
          if(res.succ){
            let filePath = res.data.filePath;
            window.open(filePath, '_blank');
          }else{
              this.$message.error(res.msg || '查询失败')
          }
        })
      },

      mixProportionVerifyRecord(row) {
        this.verifyRecordShow = true;
        this.selectedMixId = row.id;

        this.getMixProportionVerifyRecordListResp();
      },
      handleSetVerifyRecordTarget(row) {
        this.editVerifyRecordShow = true;
        if (row) {
          this.editVerifyRecordForm = {
            id: row.id,
            mixId: this.selectedMixId,
            projectId: row.projectId,
            projectName: row.projectName,
            contractNo: row.contractNo,

            snFactory: row.snFactory,
            snFactoryCalled: row.snFactoryCalled,
            
            cglFactory: row.cglFactory,
            cglFactoryCalled: row.cglFactoryCalled,

            xglFactory: row.xglFactory,
            xglFactoryCalled: row.xglFactoryCalled,

            fmh1Factory: row.fmh1Factory,
            fmh1FactoryCalled: row.fmh1FactoryCalled,

            kzf1Factory: row.kzf1Factory,
            kzf1FactoryCalled: row.kzf1FactoryCalled,

            wcl1Factory: row.wcl1Factory,
            wcl1FactoryCalled: row.wcl1FactoryCalled,
            wcl2Factory: row.wcl2Factory,
            wcl2FactoryCalled: row.wcl2FactoryCalled,

            wjj1Factory: row.wjj1Factory,
            wjj1FactoryCalled: row.wjj1FactoryCalled,
            wjj2Factory: row.wjj2Factory,
            wjj2FactoryCalled: row.wjj2FactoryCalled,

            snSupplier: row.snSupplier,
            cglSupplier: row.cglSupplier,
            xglSupplier: row.xglSupplier,
            fmh1Supplier: row.fmh1Supplier,
            kzf1Supplier: row.kzf1Supplier,
            wjj1Supplier: row.wjj1Supplier,
            wjj2Supplier: row.wjj2Supplier,
            wcl1Supplier: row.wcl1Supplier,
            wcl2Supplier: row.wcl2Supplier,

            snSupplierCalled: row.snSupplierCalled,
            cglSupplierCalled: row.cglSupplierCalled,
            xglSupplierCalled: row.xglSupplierCalled,
            fmh1SupplierCalled: row.fmh1SupplierCalled,
            kzf1SupplierCalled: row.kzf1SupplierCalled,
            wjj1SupplierCalled: row.wjj1SupplierCalled,
            wjj2SupplierCalled: row.wjj2SupplierCalled,
            wcl1SupplierCalled: row.wcl1SupplierCalled,
            wcl2SupplierCalled: row.wcl2SupplierCalled,

            // 只有修改编辑的时候传入这三个字段，后端用来判断是否编辑，杨科要求逻辑 20240413
            createTime: row.createTime,
            creater: row.creater,
            isDel: row.isDel,
          };
        }else{
          this.editVerifyRecordForm = {
            id: "",
            mixId: this.selectedMixId,
            projectId: "",
            projectName: "",
            contractNo: "",
            snFactory: "",
            snFactoryCalled: "",

            cglFactory: "",
            cglFactoryCalled: "",

            xglFactory: "",
            xglFactoryCalled: "",

            fmh1Factory: "",
            fmh1FactoryCalled: "",

            kzf1Factory: "",
            kzf1FactoryCalled: "",

            wjj1Factory: "",
            wjj1FactoryCalled: "",
            wjj2Factory: "",
            wjj2FactoryCalled: "",

            wcl1Factory: "",
            wcl1FactoryCalled: "",
            wcl2Factory: "",
            wcl2FactoryCalled: "",

            snSupplier: "",
            cglSupplier: "",
            xglSupplier: "",
            fmh1Supplier: "",
            kzf1Supplier: "",
            wjj1Supplier: "",
            wjj2Supplier: "",
            wcl1Supplier: "",
            wcl2Supplier: "",

            snSupplierCalled: "",
            cglSupplierCalled: "",
            xglSupplierCalled: "",
            fmh1SupplierCalled: "",
            kzf1SupplierCalled: "",
            wjj1SupplierCalled: "",
            wjj2SupplierCalled: "",
            wcl1SupplierCalled: "",
            wcl2SupplierCalled: "",
          };
        }
        
      },

      handleVerifyRecordClose() {
        this.verifyRecordShow = false;
        this.selectedMixId = undefined;
        this.verifyRecordList = [];
        this.verifyRecordPageObj = {
          pageNum: 1,
          pageSize: 20
        };
        this.verifyRecordTotal = 0;
      },

      handleVerifyRecordSerch() {
        this.verifyRecordList = [];
        this.verifyRecordPageObj = {
          pageNum: 1,
          pageSize: 20
        };
        this.verifyRecordTotal = 0;
        this.getMixProportionVerifyRecordListResp();
      },

      getMixProportionVerifyRecordListResp(opageNum, opageSize) {
        this.verifyRecordLoading = true;
        if (opageNum) this.verifyRecordPageObj.pageNum = opageNum;
        if (opageSize) this.verifyRecordPageObj.pageSize = opageSize;
        let params ={
          ...this.pageObj,
          params: {
            mixId: this.selectedMixId,
            projectName: this.verifyRecordSearchForm.projectName
          }
        }

        this.$api.getMixProportionVerifyRecordList(params).then(res => {
          this.verifyRecordLoading = false;
          if(res.succ){
            this.verifyRecordList = res.data.list;
            this.verifyRecordTotal = res.data.total;
          }else{
            this.$message.error(res.msg || '查询失败')
          }
        }).catch(err => {
          this.verifyRecordLoading = false;
          this.$message.error('查询失败')
        });
      },

      visibleChangeHandle(show, mType, index) {
        if (show && this.verifyRecordFormContent[index].options.length === 0) {
          if (index >= 15) {
            let clmc = index === 15 ? '膨胀剂' : '纤维';
            this.$api.queryMaterialsFactoryList(`materialsType=6&materialsName=${clmc}`, this).then(res => {
              if (res.code == 1) {
                  this.verifyRecordFormContent[index].options = res.data.list.map(item => {
                      return {
                          label: item.manufacturers,
                          value: item,
                      }
                  })
              }
            });
          }else{
            this.$api.getFactoryListByMaterialsType(`materialsType=${mType}`).then(res => {
              if(res.succ){
                this.verifyRecordFormContent[index].options = res.data.list.map(item => {
                  return {
                    label: item.manufacturers,
                    value: item
                  }
                })
              }else{
                this.$message.error(res.msg || '查询失败')
              }
            })
          }
        }
      },
      visibleSupplierChangeHandle(show, mType, index) {
        if (show && this.verifyRecordFormContentRight[index].options.length === 0) {
          if (index >= 15) {
            let clmc = index === 15 ? '膨胀剂' : '纤维';
            this.$api.querySupplierCompanyListByType(`materialsType=6&materialsName=${clmc}`, this).then(res => {
              if (res.code == 1) {
                  this.verifyRecordFormContentRight[index].options = res.data.list.map(item => {
                      return {
                          label: item.supplierName,
                          value: item,
                      }
                  })
              }
            });
          }else{
            this.$api.querySupplierCompanyListByType(`materialsType=${mType}`).then(res => {
              if(res.succ){
                this.verifyRecordFormContentRight[index].options = res.data.list.map(item => {
                  return {
                    label: item.supplierName,
                    value: item
                  }
                })
              }else{
                this.$message.error(res.msg || '查询失败')
              }
            })
          }
        }
      },
      selectFactory(event, mType, prop) {
        this.$set(this.editVerifyRecordForm, `${prop}`, event.manufacturers);
        this.$set(this.editVerifyRecordForm, `${prop}Called`, event.manufacturersCalled);
      },
      selectSupplier(event, mType, prop) {
        this.$set(this.editVerifyRecordForm, `${prop}`, event.supplierName);
        this.$set(this.editVerifyRecordForm, `${prop}Called`, event.supplierAbbreviation);
      },
      visibleProjectChangeHandle(event) {
        if (event && this.verifyRecordFormContent[0].options.length === 0) {
          this.$api.getEngineeringListAll({}).then(res => {
            if(res.succ){
              this.verifyRecordFormContent[0].options = res.data.list.map(item => {
                return {
                  label: item.projectName + `【${item.contractNo}】`,
                  value: item
                };
              });
            }else{
              this.$message.error(res.msg || '查询失败')
            }
          })
        }
      },
      selectProject(event) {
        this.editVerifyRecordForm.projectId = event.id;
        this.editVerifyRecordForm.projectName = event.projectName;
        this.editVerifyRecordForm.contractNo = event.contractNo;
      },

      saveEditVerifyRecordForm() {
        console.log(this.editVerifyRecordForm);
        let api = "addMixProportionVerifyRecord";
        if (this.editVerifyRecordForm.id) {
          api = "updateMixProportionVerifyRecord";
        }
        this.$api[api](this.editVerifyRecordForm).then(res => {
          if (res.succ) {
            this.$message({
              showClose: true,
              message: "操作成功",
              type: "success",
            });
            this.editVerifyRecordShow = false;
            for (let i = 0; i < this.verifyRecordFormContent.length; i++) {
              this.verifyRecordFormContent[i].options = [];
            }
            
            this.getMixProportionVerifyRecordListResp();
          }else{
            this.$message.error(res.msg || '操作失败')
          }
        })
      },
      handleVerifyRecordDel(row) {
        this.$confirm('确认删除吗？')
        .then(_ => {
          this.$api.deleteMixProportionVerifyRecord({id: row.id}).then(res => {
            if (res.succ) {
              this.$message({
                showClose: true,
                message: "操作成功",
                type: "success",
              });
              this.getMixProportionVerifyRecordListResp();
            }else{
              this.$message.error(res.msg || '操作失败')
            }
          })
        })
      },
      closeVerifyRecordShow() {
        this.$confirm('确认关闭？')
        .then(_ => {
          this.editVerifyRecordShow = false;
          for (let i = 0; i < this.verifyRecordFormContent.length; i++) {
            this.verifyRecordFormContent[i].options = [];
          }
        })
        .catch(_ => {});
      },

      importClick() {
        this.$refs.uploadImport.$refs['upload-inner'].handleClick()
      },

      importClickBefore(file, fileList) {
        return new Promise((resolve, reject) => {
          this.$confirm('确认导入该配合比数据吗？')
          .then(_ => {
            resolve(true);
          })
          .catch(_ => {
            reject(false);
          });
        })
      },

      importClickSuccess(response, file, fileList) {
        if (response.code == 1) {
          this.$message({
            message: "导入成功",
            type: "success",
          });
        }else{
          this.$message.error('导入失败，请重试')
        }
      },

      mixSelectionChange(val) {
        this.mixSelects = val;
      },

      printInfoByMixIds() {
        if (this.mixSelects.length == 0) {
          this.$message({
            showClose: true,
            message: "请选择需要打印的配合比",
            type: "warning",
          });
          return;
        }
        let routeData = this.$router.resolve({
          path: "/printMixContent",
          query: {
            mixIdList: this.mixSelects.map(i => i.id).join(','),
          }
        });
        window.open(routeData.href, '_blank');
      }
    }
};
</script>
  
<style scoped lang="scss">
::v-deep .el-button{
    padding-left: 13px;
    padding-right: 13px;
}
.el-form-item{
    margin-bottom: 8px;
}
::v-deep .el-form--inline{
    .el-form-item{
    margin-right: 24px;
    margin-bottom: 0;
    &:last-child{
        margin: 0;
    }
    }
}
::v-deep .cus-drawer .el-drawer {
  width: 1100px !important;
}
::v-deep .el-table{
    .expanded,.expanded:hover{
    background-color: #FFFBD9;
    
    }
    .expanded + tr{
    background-color: #FFFBD9;
    td{
        background-color: #FFFBD9;
    }
    }
    
    .table-child-box{
    margin: 16px;
    padding: 16px;
    background: #FFFFFF;
    }
}

.content-box{
    padding: 16px;
}
.content{
    width: 100%;
    height: 100%;
    padding: 16px;
    background: #FFFFFF;
    border-radius: 16px;
}

.search-box{
    padding-bottom: 16px;
    line-height: 40px;
}
.drawer-footer{
  border-top: 0px solid #DDDFE6;
  float: right;
  margin-top: 40px;
  padding-right: 130px;
}
</style>