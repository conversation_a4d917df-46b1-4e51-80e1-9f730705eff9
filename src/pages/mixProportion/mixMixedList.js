/*
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-05-25 00:09:18
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2025-01-01 17:21:02
 * @FilePath: /quality_center_web/src/pages/mixProportion/mixMixed.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import moment from "moment";

export const mixMixedList = {
    data() {
        return {
            searchForm: {
                proportionNo: ""
            },
            ddList: [{
                label: "C10",
                value: "C10"
            },{
                label: "C15",
                value: "C15"
            },{
                label: "C20",
                value: "C20"
            },{
                label: "C25",
                value: "C25"
            },{
                label: "C30",
                value: "C30"
            },{
                label: "C35",
                value: "C35"
            },{
                label: "C40",
                value: "C40"
            },{
                label: "C45",
                value: "C45"
            },{
                label: "C50",
                value: "C50"
            },{
                label: "C55",
                value: "C55"
            }],
            ksList: [{
                label: "P6",
                value: "P6"
            },{
                label: "P8",
                value: "P8"
            },{
                label: "P10",
                value: "P10"
            },{
                label: "P12",
                value: "P12"
            }],
            pageObj: {
                pageNum: 1, // 页数
                pageSize: 10, // 条数
            },
            total: 1,
            tableData: [],
            tableColumn: [
                {
                    label: '配合比编号',
                    prop: 'proportionPhb',
                },
                {
                    label: '设计依据	',
                    prop: 'proportionSjyj',
                },
                {
                    label: '强度等级',
                    prop: 'proportionQddj',
                },
                {
                    label: '龄期',
                    prop: 'proportionLq',
                },
                {
                    label: '粘聚性',
                    prop: 'proportionZjx',
                },
                {
                    label: '保水性',
                    prop: 'proportionBsx',
                },
                {
                    label: '抗渗等级',
                    prop: 'proportionKsdj',
                },
                {
                    label: '抗折强度',
                    prop: 'proportionKzqd',
                },
                {
                    label: '强氯离子',
                    prop: 'proportionQllz',
                },
                {
                    label: '设计坍落度	',
                    prop: 'proportionTld',
                    formatter: (row) => {
                      return row.proportionTld?.qz ? row.proportionTld?.qz + '±' + row.proportionTld?.hz : '--'
                    },
                },
                {
                    label: '水胶比',
                    prop: 'productionSjb',
                },
              
                // {
                //   prop: "water",
                //   label: "水",
                //   formatter: (row) => {
                //     return row.proportionScyl.water || '--'
                //   },
                // },
                // {
                //   prop: "sn",
                //   label: "水泥",
                //   formatter: (row) => {
                //     return row.proportionScyl.sn || '--'
                //   },
                // },
                // {
                //   prop: "xgl1",
                //   label: "细骨料1",
                //   formatter: (row) => {
                //     return row.proportionScyl.xgl1 || '--'
                //   },
                // },
                // {
                //   prop: "xgl2",
                //   label: "细骨料2",
                //   formatter: (row) => {
                //     return row.proportionScyl.xgl2 || '--'
                //   },
                // },
                // {
                //   prop: "cgl1",
                //   label: "粗骨料1",
                //   formatter: (row) => {
                //     return row.proportionScyl.cgl1 || '--'
                //   },
                // },
                // {
                //   prop: "cgl2",
                //   label: "粗骨料2",
                //   formatter: (row) => {
                //     return row.proportionScyl.cgl2 || '--'
                //   },
                // },
                // {
                //   prop: "chl1",
                //   label: "掺合料1",
                //   formatter: (row) => {
                //     return row.proportionScyl.chl1 || '--'
                //   },
                // },
                // {
                //   prop: "chl2",
                //   label: "掺合料2",
                //   formatter: (row) => {
                //     return row.proportionScyl.chl2 || '--'
                //   },
                // },
                // {
                //   prop: "wjj1",
                //   label: "外加剂1",
                //   formatter: (row) => {
                //     return row.proportionScyl.wjj1 || '--'
                //   },
                // },
                // {
                //   prop: "wjj2",
                //   label: "外加剂2",
                //   formatter: (row) => {
                //     return row.proportionScyl.wjj2 || '--'
                //   },
                // },
                // {
                //   prop: "wjj3",
                //   label: "外加剂3",
                //   formatter: (row) => {
                //     return row.proportionScyl.wjj3 || '--'
                //   },
                // },
              
                
                
                
                
                {
                    label: '容重(kg)',
                    prop: 'productionRz',
                },
                {
                    label: '砂率(%)',
                    prop: 'productionSl',
                },
                // {
                //     label: '创建人/派单人',
                //     prop: 'creater',
                // },
                {
                    label: '创建时间',
                    prop: 'createTime',
                    formatter: (row) => {
                        return row.createTime ? moment(row.createTime).format('YYYY-MM-DD') : '--'
                    },
                    width: '130'
                },
                {
                    label: '备注',
                    prop: 'proportionRemarks',
                },
                {
                    label: '兼容说明',
                    prop: 'newRatioString',
                },
            ]
        }
    },

    methods: {
        handleFilter() {
            this.initData(1);
        },
        resetForm(){
            this.searchForm = {};
            this.initData(1);
        },
        initData(opageNum, opageSize) {
            if (opageNum) this.pageObj.pageNum = opageNum;
            if (opageSize) this.pageObj.pageSize = opageSize;
                
            const params ={
                ...this.pageObj,
                params: {
                    ...this.searchForm
                }
            }
            
            this.$api.queryMixProportionPage(params, this).then(res => {
                if(res.succ){
                    let datas = [].concat(res.data.list);
                    datas.map(preItem => {
                        let tempList = [];
                        const sortedObj = {};
                        const obj = preItem.proportionMaterial;
                        ["sn", "water", "fmh", "kzf", "cgl", "xgl", "wjj1", "wjj2", "wcl1", "wcl2"].forEach((key) => {
                            if (obj.hasOwnProperty(key)) {
                                sortedObj[key] = obj[key];
                            }
                            if (key === 'sn') {
                                tempList.push({
                                    ...preItem.proportionMaterial[`${key}`],
                                    name: "水泥"
                                })
                            }else if (key === 'water') {
                                tempList.push({
                                    ...preItem.proportionMaterial[`${key}`],
                                    name: "水"
                                })
                            }else if (key === 'kzf') {
                                tempList.push({
                                    ...preItem.proportionMaterial[`${key}`],
                                    name: "矿渣粉"
                                })
                            }else if (key === 'fmh') {
                                tempList.push({
                                    ...preItem.proportionMaterial[`${key}`],
                                    name: "粉煤灰"
                                })
                            }else if (key === 'xgl') {
                                tempList.push({
                                    ...preItem.proportionMaterial[`${key}`],
                                    name: "细骨料"
                                })
                            }else if (key === 'cgl') {
                                tempList.push({
                                    ...preItem.proportionMaterial[`${key}`],
                                    name: "粗骨料"
                                })
                            }else if (key === 'wjj1') {
                                tempList.push({
                                    ...preItem.proportionMaterial[`${key}`],
                                    name: "外加剂1"
                                })
                            }else if (key === 'wjj2') {
                                tempList.push({
                                    ...preItem.proportionMaterial[`${key}`],
                                    name: "外加剂2"
                                })
                            }else if (key === 'wcl1') {
                                tempList.push({
                                    ...preItem.proportionMaterial[`${key}`],
                                    name: "外掺料1"
                                })
                            }else if (key === 'wcl2') {
                                tempList.push({
                                    ...preItem.proportionMaterial[`${key}`],
                                    name: "外掺料2"
                                })
                            }
                        });

                        // Object.keys(preItem.proportionMaterial).map(item => {
                        //     if (item === 'sn') {
                        //         tempList.push({
                        //             ...preItem.proportionMaterial[`${item}`],
                        //             name: "水泥"
                        //         })
                        //     }else if (item === 'water') {
                        //         tempList.push({
                        //             ...preItem.proportionMaterial[`${item}`],
                        //             name: "水"
                        //         })
                        //     }else if (item === 'kzf') {
                        //         tempList.push({
                        //             ...preItem.proportionMaterial[`${item}`],
                        //             name: "矿渣粉"
                        //         })
                        //     }else if (item === 'fmh') {
                        //         tempList.push({
                        //             ...preItem.proportionMaterial[`${item}`],
                        //             name: "粉煤灰"
                        //         })
                        //     }else if (item === 'xgl') {
                        //         tempList.push({
                        //             ...preItem.proportionMaterial[`${item}`],
                        //             name: "细骨料"
                        //         })
                        //     }else if (item === 'cgl') {
                        //         tempList.push({
                        //             ...preItem.proportionMaterial[`${item}`],
                        //             name: "粗骨料"
                        //         })
                        //     }else if (item === 'wjj1') {
                        //         tempList.push({
                        //             ...preItem.proportionMaterial[`${item}`],
                        //             name: "外加剂1"
                        //         })
                        //     }else if (item === 'wjj2') {
                        //         tempList.push({
                        //             ...preItem.proportionMaterial[`${item}`],
                        //             name: "外加剂2"
                        //         })
                        //     }else if (item === 'wcl1') {
                        //         tempList.push({
                        //             ...preItem.proportionMaterial[`${item}`],
                        //             name: "外掺料1"
                        //         })
                        //     }else if (item === 'wcl2') {
                        //         tempList.push({
                        //             ...preItem.proportionMaterial[`${item}`],
                        //             name: "外掺料2"
                        //         })
                        //     }
                        // })
                        preItem.childrenData = tempList;
                    })
                    this.tableData = datas;
                    this.total = res.data.total;
                }else{
                    this.$message.error(res.msg || '查询失败')
                }
            });
        },

        queryMaterialsSpecConfigAllResp() {
          this.$api.queryMaterialsSpecConfigAll({"materialsType":7,"materialsName":"混凝土"}, this).then(res => {
              if (res.code == 1) {
                this.ddList = [];
                  this.ddList = res.data.list.map(item => {
                      return {
                          label: item.materialsSpec || item.materialsName,
                          value: item.materialsSpec || item.materialsName,
                      }
                  })
              }
          })
        },
    },
}