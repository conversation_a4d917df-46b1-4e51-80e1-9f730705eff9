<template>
    <div class="content-box">
        <div class="flex-box flex-column content">
            <div class="search-box flex-box">
                <el-form class="flex-item" ref="searchForm" :inline="true" :model="searchForm" :disabled="loading">
                <el-form-item label="工程名称：">
                    <el-input v-model="searchForm.projectName" clearable
                        placeholder="请输入" 
                        style="width: 180px" 
                    />
                </el-form-item>
                <el-form-item label="配合比：">
                    <el-input v-model="searchForm.proportionPhb" clearable
                        placeholder="请输入" 
                        style="width: 180px" 
                    />
                </el-form-item>
                <el-form-item label="年份：">
                    <el-select
                    v-model="searchForm.years" 
                    filterable clearable multiple 
                    collapse-tags
                    placeholder="请选择" 
                    style="width: 180px">
                    
                        <el-option
                            v-for="item in yearOpts"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value">
                        </el-option>
                    </el-select>
                </el-form-item>
                
                <el-form-item>
                    <el-button type="primary" :disabled="loading" @click="handleFilter">搜索</el-button>
                    <el-button type="text" icon="el-icon-refresh-right" :disabled="loading" @click="resetForm()">重置</el-button>
                    
                </el-form-item>
                <el-form-item style="float: right;">
                    <el-button type="primary" @click="batchPrintClick">批量打印</el-button>
                    <div style="width: 20px;"></div>
                </el-form-item>
                </el-form>
            </div>
        
            <div class="flex-item overHide">
                <div class="scroll-div">
                <el-table
                    :data="tableData"
                    v-loading="loading"
                    style="width: 100%"
                    @selection-change="panelSelectionChange"
                >

    <el-table-column
      type="selection"
      width="55">
    </el-table-column>
                    <af-table-column
                        v-for="item in tableColumn" 
                        :key="item.prop" :prop="item.prop" 
                        :label="item.label" 
                        :formatter="item.formatter"
                        :fixed="item.fixed" 
                        :width="item.width || ''"
                        align="center" 
                    />
                    <af-table-column width="180" fixed="right" label="操作" align="center" key="handle" :resizable="false">
                    <template slot-scope="scope">
                        <!-- <el-button type="text" size="small" @click="handleDetailTarget(scope.row)">详情</el-button> -->
                        <el-button type="text" size="small" @click="handleEditTarget(scope.row)">修改</el-button>
                        <el-button type="text" size="small" @click="printRow(scope.row)">打印</el-button>
                    </template>
                    </af-table-column>
                </el-table>
                </div>
            </div>
            <div class="mt16 mb4">
                <Pagination
                :total="total" 
                :pageNum="pageObj.pageNum" 
                :pageSize="pageObj.pageSize" 
                @getData="initData" 
                />
            </div>
        </div>

        <el-dialog
            title="修改记录"
            width="700px"
            :visible.sync="updateTableShow"
            class="claim-dialog-box"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            @close="updateClose"
        >
            <el-form label-width="160px" 
                :model="quickForm"
            >
                <el-form-item :label="item.testProjectName" v-for="item in quickData" :key="item.testProjectCode">
                    <el-radio-group v-model="quickForm[item.testProjectCode].objJson.val" 
                        v-if="item.testProjectName === '流动性' 
                        || item.testProjectName === '保水性' 
                        || item.testProjectName === '粘聚性'"
                    >
                        <el-radio label="fcc">非常差</el-radio>
                        <el-radio label="yb">一般</el-radio>
                        <el-radio label="hh">良好</el-radio>
                    </el-radio-group>
                    
                    <template v-else-if="item.testProjectName === '实测坍落度' || item.testProjectName === '坍落度'">
                        <el-input
                        type="number"
                        v-model="quickForm[item.testProjectCode].objJson.tld1" 
                        clearable
                        placeholder="请输入"
                        :style="{'width': 150 + 'px'}"
                        >
                        </el-input>
                    </template>
                </el-form-item>
            </el-form>

            <div class="footer-btn flex-row" style="justify-content: flex-end;">
                <el-button type="primary" @click="updateClose()"plain>取消</el-button>
                <el-button type="primary" @click="saveUpdateData()">保存</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import Pagination from "@/components/Pagination/index.vue";
import moment from "moment";
export default {
    name:'paramsConfig',
    components: {
        Pagination,
    },
    data() {
        return {
            tableColumn: [
                {
                    label: '记录编号',
                    prop: 'appraisalNo',
                },
                {
                    label: '台账编号',
                    prop: 'ledgerNo',
                },
                {
                    label: '配比编号',
                    prop: 'proportionPhb',
                },

                {
                    label: '设计强度等级',
                    prop: 'rwdextraPrintInfo.mixProportionInfo.proportionQddj',
                },
                {
                    label: '工程名称',
                    prop: 'projectName',
                },
                {
                    label: '年份',
                    prop: 'year',
                },
                {
                    label: '任务单号',
                    prop: 'frwno',
                },


                {
                    label: '协会任务编号',
                    prop: 'rwdextra.rwdextraExtend.produceIndexId',
                    width: 120,
                },
                {
                    label: '申请单日期',
                    prop: 'rwdextra.rwdextraInfo.fjhrq',
                    width: 120,
                    formatter: (row) => {
                        return moment(row.rwdextra.rwdextraInfo.fjhrq).format('YYYY-MM-DD')
                    },
                },
                {
                    label: '施工部位',
                    prop: 'rwdextra.rwdextraInfo.fjzbw',
                    width: 210,
                },
                {
                    label: '施工单位',
                    prop: 'rwdextra.rwdextraInfo.fhtdw',
                    width: 210,
                },




                {
                    label: '车号',
                    prop: 'carNo',
                },
                {
                    label: '车牌号',
                    prop: 'carCard',
                },
                {
                    label: '创建时间',
                    prop: 'createTime',
                },
            ],
            getListApi: 'queryOpenAppraisalPage',

            loading: false,
            searchForm: {
                
            },
            pageObj: {
                pageNum: 1, // 页数
                pageSize: 10, // 条数
            },
            total: 1,
            tableData: [],
            yearOpts: [],
            selectedDataList:[],

            updateTableShow: false,
            quickData: [],
            quickForm: {},
        };
    },

    created() {
        // 获取当前年份到2024年中间所有年份
        let year = moment().year();
        for (let i = 2024; i <= year; i++) {
            this.yearOpts.push({
                value: i,
                label: i
            })
        }
    },

    activated() {
        this.initData();
    },

    methods: {
        batchPrintClick(){
            console.log('批量打印');
            if(this.selectedDataList.length == 0){
                this.$message({
                    showClose: true,
                    message: "请选择需要打印的记录",
                    type: "warning",
                });
                return;
            }

            let routeData = this.$router.resolve({
                path: "/printTaskContent",
                query: {
                    taskIds: this.selectedDataList.map(i => i.frwdh).join(','),
                    printType: 'a2'
                }
            });
            window.open(routeData.href, '_blank');
        },
        panelSelectionChange(val) {
            console.log('选择批量打印：',val);
            this.selectedDataList = val;
        },
        printRow(row){

            let routeData = this.$router.resolve({
                path: "/printTaskContent",
                query: {
                    taskIds: row.frwdh,
                    printType: 'a2'
                }
            });
            window.open(routeData.href, '_blank');
        },
        handleFilter() {
            console.log("handleFilter", this.searchForm)
            this.initData(1);
        },
        resetForm(){
            this.searchForm = {};
            this.initData(1);
        },
        initData(opageNum, opageSize){
            this.loading = true;
            if (opageNum) this.pageObj.pageNum = opageNum;
            if (opageSize) this.pageObj.pageSize = opageSize;
                
            const params ={
                ...this.pageObj,
                params: this.searchForm
            }
            //获取列表
            this.$api[this.getListApi](params, this).then(res => {
                this.loading = false;
                if(res.succ){
                    this.tableData = res.data.list;
                    this.total = res.data.total;
                }else{
                    this.$message.error(res.msg || '查询失败')
                }
            }).catch(err => {
                this.loading = false;
            });
        },

        handleDetailTarget(row) {

        },
        handleEditTarget(row) {
            this.updateTableShow = true;
            this.$api.queryOpenAppraisalById('id=' + row.id, this).then(res => {
                if(res.succ){
                    this.$api.getExperimentDetail({
                        experimentId: res.data
                    }, this).then(res2 => {
                        if(res2.succ){
                            this.quickData = [];
                            this.quickForm = {};
                            res2.data.list.map(item => {
                                if(item.testProjectName === '流动性' || item.testProjectName === '保水性' 
                                    || item.testProjectName === '粘聚性'|| item.testProjectName === '实测坍落度' 
                                    || item.testProjectName === '目测砂率' 
                                ){
                                    // if (item.testProjectName === '坍落度') {
                                    //   if (!item.objJson.tldfs) {
                                    //     item.objJson.tldfs = "目测";
                                    //   }
                                    // }
                                    if((item.testProjectName === '流动性' || item.testProjectName === '保水性' 
                                    || item.testProjectName === '粘聚性')){
                                        // 接口没有val 字段，根据判断转化一个加进去自己用
                                        item.objJson.val = 'hh'
                                        if (item.objJson.hh == 1) {
                                            item.objJson.val = 'hh'
                                        }else if (item.objJson.yb == 1) {
                                            item.objJson.val = 'yb'
                                        }else if (item.objJson.fcc == 1) {
                                            item.objJson.val = 'fcc'
                                        }
                                    }
                                    this.quickData.push(item)
                                    this.quickForm[item.testProjectCode] = item;
                                }
                            })
                        }else{
                            this.$message.error(res2.msg || '查询失败')
                        }
                    })
                }else{
                    this.$message.error(res.msg || '查询失败')
                }
            })
        },
        updateClose() {
            this.updateTableShow = false;
            this.quickForm = {};
            this.quickData = [];
        },

        saveUpdateData() {
            this.$confirm('确认保存吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                let oQuickForm = this.quickForm;
                let odata = this.quickData;
                for(let i = 0; i< odata.length; i++){
                    let objJson = {}
                    if(odata[i].testProjectName === '流动性' || odata[i].testProjectName === '保水性' 
                    || odata[i].testProjectName === '粘聚性'|| odata[i].testProjectName === '实测坍落度' 
                    || odata[i].testProjectName === '目测砂率' ){
                        objJson = oQuickForm[odata[i].testProjectCode].objJson;
                        if(odata[i].testProjectName === '流动性' || odata[i].testProjectName === '保水性' || odata[i].testProjectName === '粘聚性'){
                            const oVal = objJson.val;
                            for(let item in objJson){
                                if(item != 'val'){
                                    objJson[item] = 0;
                                    if(item == oVal){
                                        objJson[item] = 1;
                                    }
                                }
                            }
                        }
                    }

                    this.saveExperimentInfo(odata[i], i);
                }
            });
        },
        //保存
        saveExperimentInfo(item, index){
            this.$api.setExperimentDetail({
                id: item.id,
                experimentId: item.experimentId,
                checkType: item.checkType,//1-快检 2-批检 == 1 ? 1 : 2
                testProjectCode: item.testProjectCode,
                testProjectName: item.testProjectName,
                objJson: item.objJson,
            }, this).then(res => {
                if(res.succ){
                    if (index == this.quickData.length - 1) {
                        this.$message.success("保存成功")
                        this.updateClose();
                    }
                }else{
                    this.$message.error(res.msg || '保存失败')
                }
            })
        },
    },
};
</script>

<style scoped lang="scss">
.multi-form-item-box{
    padding: 0 0 4px 0;
    .el-select,.el-input{
    margin-right: 20px;
    }
}
::v-deep .el-button{
    padding-left: 13px;
    padding-right: 13px;
}
.el-form-item{
    margin-bottom: 8px;
}
::v-deep .el-form--inline{
    .el-form-item{
    margin-right: 24px;
    margin-bottom: 0;
    &:last-child{
        margin: 0;
    }
    }
}
::v-deep .el-table{
    .expanded,.expanded:hover{
    background-color: #FFFBD9;
    
    }
    .expanded + tr{
    background-color: #FFFBD9;
    td{
        background-color: #FFFBD9;
    }
    }
    
    .table-child-box{
    margin: 16px;
    padding: 16px;
    background: #FFFFFF;
    }
}

.content-box{
    padding: 16px;
}
.content{
    width: 100%;
    height: 100%;
    padding: 16px;
    background: #FFFFFF;
    border-radius: 16px;
}

.search-box{
    padding-bottom: 16px;
    line-height: 40px;
}
</style>