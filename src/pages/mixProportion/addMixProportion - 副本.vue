<!--
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-05-29 23:45:07
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2024-05-31 01:17:05
 * @FilePath: /quality_center_web/src/pages/mixProportion/addMinProportion.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div class="content-box">
      <div class="flex-box flex-column content">
        <el-col :span="24">
            <el-row class="row-title">
              <el-button size="small" type="primary" class="fr" @click="addProportion">新增</el-button>
              <span class="row-title-label">试配记录</span>
              
            </el-row>
            <el-row style="margin-top: 24px; width: 100%;">
                <el-form :model="params" style="width: 100%;">
                    <el-form-item label="设计依据：" prop="proportionSjyj">
                        <el-input v-model="params.proportionSjyj" placeholder="请输入设计依据" style="width: 80%;"></el-input>
                    </el-form-item>

                    <el-row class="flex-row" style="width: 100%; justify-content: flex-start">
                      <template v-for="item, index in Object.keys(designFrom)">
                        <el-form-item v-if="index > 1 && index < 6" :key="item" class="form-inline"  :label="designFrom[`${item}`].label">
                        
                            <el-select
                                v-model="params[`${item}`]" 
                                filterable clearable 
                                placeholder="请选择" 
                            >
                                <el-option
                                    v-for="op in designFrom[item].opts"
                                    :key="item + op.label"
                                    :label="op.label"
                                    :value="op.value"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                      </template>
                        <el-form-item class="form-inline"  label="设计坍落度：">
                            <el-row class="flex-row">
                                <el-input v-model="params.proportionTld.qz" placeholder="请输入" style="width: 100px;"></el-input>
                                <span style="padding: 4px">±</span>
                                <el-input v-model="params.proportionTld.hz" placeholder="请输入" style="width: 100px;"></el-input>
                            </el-row>
                        </el-form-item>
                    </el-row>

                    <el-form-item label="备注：" prop="proportionRemarks">
                        <el-input v-model="params.proportionRemarks" placeholder="请输入备注" style="margin-left: 25px;width: 80%;"></el-input>
                    </el-form-item>
                </el-form>
            </el-row>
        </el-col>

        <el-col :span="24">
            <el-row class="row-title">
              <span class="row-title-label">配合比设计</span>
            </el-row>
            <el-row class="flex-row" style="align-items: flex-start">
                <el-col :span="18">
                    <el-row class="flex-row" style="justify-content: flex-start; margin-top: 24px">
                        <div style="width: 3px;height: 22px;background: #6A727D;" />
                        <div class="flex-row" style="height: 22px;background: #E0E8EB; width: 100%">
                            <span style="margin-left: 6px; font-weight: 600;font-size: 14px;color: #1F2329;">步骤1：选择原材料</span>
                        </div>
                    </el-row>
                    <el-row v-for="item, index in proportionMaterialFrom" :key="index" class="flex-row" style="justify-content: flex-start; margin-top: 16px">
                        <el-row v-for="subItem, subInx in item" :key="subInx" class="flex-row" style="width: 100%">
                            <div v-if="subItem.type === 'select'" style="width: 60px; text-align: right">{{subItem.label}}</div>
                            <el-select
                                v-if="subItem.type === 'select'"
                                v-model="params.proportionMaterial[subItem.key]" 
                                filterable clearable 
                                placeholder="请选择" 
                            >
                                <el-option
                                    v-for="op in item.opts"
                                    :key="op.value"
                                    :label="op.label"
                                    :value="op.value">
                                </el-option>
                            </el-select>
                            <el-input v-else v-model="params.proportionMaterial[subItem.key]" placeholder="请输入" style="min-width: 230px; margin-left: 24px"></el-input>
                        </el-row>
                    </el-row>
                </el-col>
                <el-col style="margin-left: 24px" :span="6">
                    <el-row class="flex-row" style="justify-content: flex-start; margin-top: 24px">
                        <div style="width: 3px;height: 22px;background: #6A727D;" />
                        <div class="flex-row" style="height: 22px;background: #E0E8EB; width: 100%">
                            <span style="margin-left: 6px; font-weight: 600;font-size: 14px;color: #1F2329;">步骤2：确认配置强度</span>
                        </div>
                    </el-row>
                    <el-row style="margin-top: 16px;background: linear-gradient( 167deg, #EEF2F5 0%, #FFFFFF 100%);border-radius: 4px; border: 1px solid #DDE6F3; height: 376px; padding: 26px 16px">
                        <el-row class="flex-row" style="justify-content: flex-start; align-items: center">
                            <span>标准差：</span>
                            <el-input v-model="params.proportionBzc" placeholder="请输入" style="margin-left: 25px; width: 180px"></el-input>
                        </el-row>
                        <div style="margin-top: 26px">混凝土强度：22Mpa</div>
                    </el-row>
                </el-col>
            </el-row>
        </el-col>

        <el-col :span="24">
            <el-row class="flex-row" style="align-items: flex-start; justify-content: flex-start">
                <el-col :span="6">
                    <el-row class="flex-row" style="justify-content: flex-start; margin-top: 24px">
                        <div style="width: 3px;height: 22px;background: #6A727D;" />
                        <div class="flex-row" style="height: 22px;background: #E0E8EB; width: 100%">
                            <span style="margin-left: 6px; font-weight: 600;font-size: 14px;color: #1F2329;">步骤3：确认水胶比及凝胶材料</span>
                        </div>
                    </el-row>
                    <el-row class="flex-col" style="align-items: flex-start; justify-content: flex-start; margin-top: 16px;background: linear-gradient( 167deg, #EEF2F5 0%, #FFFFFF 100%);border-radius: 4px; border: 1px solid #DDE6F3; height: 376px; padding: 26px 16px">
                        <el-row>
                            <span>回归系数aa：{{params.proportionSjb.hgxsaa}}</span>
                            <span style="margin-left: 66px">回归系数ab：{{params.proportionSjb.hgxsab}}</span>
                        </el-row>
                        <el-row style="margin-top: 16px">
                            <span>配置强度：{{params.proportionSjb.pzqd}}MPa</span>
                        </el-row>
                        <el-row class="flex-row" style="margin-top: 16px">
                            <span>胶凝材料28天胶砂强度值：</span>
                            <el-input v-model="params.proportionSjb.jncljsqdz" placeholder="请输入" style="width: 100px"></el-input>
                            <span style="margin-left: 16px">MPa</span>
                        </el-row>
                        <el-row class="flex-row" style="margin-top: 16px">
                            <span>水胶比：</span>
                            <el-input v-model="params.proportionSjb.jnscz" placeholder="请输入" style="width: 100px"></el-input>
                        </el-row>
                    </el-row>
                </el-col>
                <el-col :span="6" style="margin-left: 24px">
                    <el-row class="flex-row" style="justify-content: flex-start; margin-top: 24px">
                        <div style="width: 3px;height: 22px;background: #6A727D;" />
                        <div class="flex-row" style="height: 22px;background: #E0E8EB; width: 100%">
                            <span style="margin-left: 6px; font-weight: 600;font-size: 14px;color: #1F2329;">步骤4：确定用水量</span>
                        </div>
                    </el-row>
                    <el-row style="margin-top: 16px;background: linear-gradient( 167deg, #EEF2F5 0%, #FFFFFF 100%);border-radius: 4px; border: 1px solid #DDE6F3; height: 376px; padding: 16px 0px">
                        <el-row class="flex-row" style="justify-content: flex-start">
                            <div style="width: 100px; text-align: right">用水量：</div>
                            <el-input v-model="params.proportionYsl.ysl" placeholder="请输入" style="width: 180px"></el-input>
                            <span style="margin-left: 12px">kg</span>
                        </el-row>
                        <el-row class="flex-row" style="justify-content: flex-start; margin-top: 16px">
                            <div style="width: 100px; text-align: right">用水量：</div>
                            <el-switch
                                v-model="params.proportionYsl.sfjwjj == 'true' ? true : false"
                                active-color="#2367B1">
                            </el-switch>
                        </el-row>
                        <el-row class="flex-row" style="justify-content: flex-start; margin-top: 16px">
                            <div style="width: 100px; text-align: right">外加剂名称：</div>
                            <el-input v-model="params.proportionYsl.wjjmc" placeholder="请输入" style="width: 180px"></el-input>
                        </el-row>
                        <el-row class="flex-row" style="justify-content: flex-start; margin-top: 16px">
                            <div style="width: 100px; text-align: right">减水率：</div>
                            <el-input v-model="params.proportionYsl.jsl" placeholder="请输入" style="width: 180px"></el-input>
                            <span style="margin-left: 12px">%</span>
                        </el-row>
                    </el-row>
                </el-col>
                <el-col :span="6" style="margin-left: 24px">
                    <el-row class="flex-row" style="justify-content: flex-start; margin-top: 24px">
                        <div style="width: 3px;height: 22px;background: #6A727D;" />
                        <div class="flex-row" style="height: 22px;background: #E0E8EB; width: 100%">
                            <span style="margin-left: 6px; font-weight: 600;font-size: 14px;color: #1F2329;">步骤5：确定胶凝材料用量</span>
                        </div>
                    </el-row>
                    <el-row style="margin-top: 16px;background: linear-gradient( 167deg, #EEF2F5 0%, #FFFFFF 100%);border-radius: 4px; border: 1px solid #DDE6F3; height: 376px; padding: 26px 16px">
                        <el-row class="flex-row" style="justify-content: flex-start">
                            <div style="width: 100px; text-align: right">胶凝材料用量：</div>
                            <el-input v-model="params.proportionJncl.njclyl" placeholder="请输入" style="width: 180px"></el-input>
                            <span style="margin-left: 12px">kg</span>
                        </el-row>
                        <el-row class="flex-row" style="align-items: flex-start; justify-content: flex-start; margin-top: 16px">
                            <div style="width: 100px; text-align: right">掺和料：</div>
                            <div class="flex-col" style="justify-content: flex-start">
                                <p>粉煤灰：{{params.proportionJncl.fmh}}kg</p>
                                <p style="margin-top: 16px">矿渣粉：{{params.proportionJncl.kzf}}kg</p>
                                <p style="margin-top: 16px">水泥：{{params.proportionJncl.sn}}kg</p>
                            </div>
                        </el-row>
                    </el-row>
                </el-col>
                <el-col :span="6" style="margin-left: 24px">
                    <el-row class="flex-row" style="justify-content: flex-start; margin-top: 24px">
                        <div style="width: 3px;height: 22px;background: #6A727D;" />
                        <div class="flex-row" style="height: 22px;background: #E0E8EB; width: 100%">
                            <span style="margin-left: 6px; font-weight: 600;font-size: 14px;color: #1F2329;">步骤6：确定砂率及用量</span>
                        </div>
                    </el-row>
                    <el-row style="margin-top: 16px;background: linear-gradient( 167deg, #EEF2F5 0%, #FFFFFF 100%);border-radius: 4px; border: 1px solid #DDE6F3; height: 376px; padding: 26px 16px">
                        <el-row class="flex-row" style="justify-content: flex-start">
                            <div style="width: 100px; text-align: right">砂率：</div>
                            <el-input v-model="params.proportionSl.sl" placeholder="请输入" style="width: 180px"></el-input>
                            <span style="margin-left: 12px">kg</span>
                        </el-row>
                        <el-row class="flex-row" style="align-items: flex-start; justify-content: flex-start; margin-top: 16px">
                            <div style="width: 100px; text-align: right">掺和料：</div>
                            <div class="flex-col" style="justify-content: flex-start">
                                <p>细骨料用量：{{params.proportionSl.xgl}}kg</p>
                                <p style="margin-top: 16px">粗骨料用量：{{params.proportionSl.cgl}}kg</p>
                            </div>
                        </el-row>
                    </el-row>
                </el-col>
            </el-row>
        </el-col>

        <el-col :span="24" style="margin-top: 24px;">
            <el-row class="row-title">
              <span class="row-title-label">配合比设计</span>
            </el-row>
            <el-row class="table-view" style="margin-top: 24px;">
              <el-table
                :data="proportionJsphb"
                height="100%"
                border
                >
                <template v-for="item in proportionTableColumn" >
                  <el-table-column
                    :key="item.prop" 
                    :prop="item.prop" 
                    :label="item.label" 
                    :fixed="item.fixed" 
                    :width="item.width || ''"
                    :formatter="item.formatter"
                    align="center" 
                  />
                </template>
                
                
                <el-table-column width="100" label="操作" align="center" key="handle" fixed="right" :resizable="false">
                  <template slot-scope="scope">
                    <el-button type="text" size="small">详情</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-row>
        </el-col>
      </div>
    </div>
</template>

<script>
import { addMixed } from './addMixed.js'
export default {
    mixins: [addMixed],
    
    data() {
        return {
          params: {
            proportionSjyj: '',//设计依据	
            proportionQddj: '',//强度等级	
            proportionKzqd: '',//抗折强度	
            proportionKsdj: '',//抗渗等级	
            proportionRemarks: '',//备注	
            proportionBzc: '',//配合比标准差	
            proportionRz: '',//容重	
            proportionQllz: '',//强氯离子	
            proportionTld: {//设计坍落度json	
              qz: "",
              hz: "",
            },
            proportionMaterial: {//原材料json	
              snbgdh:"",//水泥报告编号	
              snybmz:"",//水泥样本名称	
              snggxh:"",//水泥规格型号	
              sncj:"",//水泥厂家	
              cglbgdh:"",//粗骨料报告编号	
              cglybmz:"",//粗骨料样本名称	
              cglggxh:"",//粗骨料规格型号	
              cglcj: "",//粗骨料厂家  
              xglbgdh:"",//细骨料报告编号	
              xglybmz:"",//细骨料样本名称	
              xglggxh:"",//细骨料规格型号	
              xglcj:"",//细骨料厂家	
              fmhbgdh:"",//粉煤灰报告编号	
              fmhybmz:"",//粉煤灰样本名称	
              fmhggxh:"",//粉煤灰规格型号	
              fmhcj:"",//粉煤灰厂家	
              kzfbgdh:"",//矿渣粉报告编号	
              kzfybmz:"",//矿渣粉样本名称	
              kzfggxh:"",//矿渣粉规格型号	
              kzfcj:"",//矿渣粉厂家	
              wjjobgdh:"",//外加剂1报告编号	
              wjjoybmz:"",//外加剂1样本名称	
              wjjoggxh:"",//外加剂1规格型号	
              wjjocj:"",//外加剂1厂家	
              wjjtbgdh:"",//外加剂2报告编号	
              wjjtybmz:"",//外加剂2样本名称	
              wjjtggxh:"",//外加剂2规格型号	
              wjjtcj:"",//外加剂2厂家
            },
            proportionSjb: {//水胶比json	
              hgxsaa: "",//回归系数aa	
              hgxsab: "",//回归系数ab	
              pzqd: "",//配置强度	
              jncljsqdz: "",//胶凝材料28天胶砂强度值	
              jnscz: "",//水胶比	
              
              
              
              fmhcl: "",//粉煤灰掺量	
              kzfcl: "",//矿渣粉掺量	
              fmhxs: "",//粉煤灰系数	
              kzfxs: "",//矿渣粉系数	
              sfysnjsqdz: "",//是否有28天水泥胶砂强度值	
              snjsqdz: "",//28天水泥胶砂强度值	
              jnclsnqdz: "",//胶凝材料28天水泥强度值	
              snfyxs: "",//              水泥富裕系数	
              snypdj: "",//              水泥样品等级
            },
            proportionYsl: {//用水量json	
              ysl:'', //用水量	
              sfjwjj:'', //是否加外加剂	
              wjjmc:'', //外加剂名称	
              jsl:'', //减水率	
              
              
              wjjsl:'', //外加剂掺量
            },
            proportionJncl: {//凝胶材料用量json	
              njclyl:"",//凝胶材料用量	
              fmh:"",//粉煤灰	
              kzf:"",//矿渣粉	
              sn:"",//水泥
            },
            proportionSl: {//砂率用量json	
              sl:"",//砂率	
              xgl:"",//细骨料	
              cgl:"",//粗骨料
            },
            proportionJsphb: [{//计算配合比json	
              sjb:"", //水胶比	
              sl:"", //砂率	
              water:"",  //水	
              cement:"", //水泥	
              fmh:"", //粉煤灰	
              kzf:"", //矿渣粉	
              xgl:"", //细骨料	
              cgl:"", //粗骨料	
              jsj:"", //减水剂	
              pzj:"", //膨胀剂
            }],
          }
        }
    },

    mounted() {
      
    },
    methods: {
      addProportion() {
        this.$api.mixProportionCreate(this.params,this).then((res) => {
          if (res.succ) {
            this.$message({
              showClose: true,
              message: "新增成功",
              type: "success",
            });
          }else{
            this.$message({
              showClose: true,
              message: res.msg,
              type: "error",
            });
          }
        });
      }
    },
}
</script>

<style lang="scss" scoped>
.content-box{
    padding: 16px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #1F2329;
    .content{
        width: 100%;
        height: 100%;
        padding: 16px;
        background: #FFFFFF;
        border-radius: 16px;
        overflow: auto;
        overflow-x: hidden
    }
}
.row-title {
  border-bottom: #DDDFE6 1px solid;
  padding-bottom: 8px;
  .row-title-label {
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 16px;
    color: #1F2329;
  }
}
.form-inline {
    display: inline-block;
    min-width: 300px
}
</style>