<template>
    <div class="content-box">
      <div class="flex-box flex-column content">
        <div class="search-box flex-box">
          <el-form class="flex-item" ref="searchForm" :inline="true" :model="searchForm" :disabled="loading">
            <el-form-item label="委托编号：">
              <el-input v-model="searchForm.experimentNo" clearable
                placeholder="请输入" 
                style="width: 160px" 
              />
            </el-form-item>
            <el-form-item label="报告编号：">
              <el-input v-model="searchForm.reportId" clearable
                placeholder="请输入" 
                style="width: 160px" 
              />
            </el-form-item>
            <el-form-item label="日期" prop="takeEffectDate">
              <el-date-picker type="daterange" 
                v-model="searchForm.takeEffectDate" 
                :picker-options="pickerOptions"
                start-placeholder="开始日期" end-placeholder="结束日期" 
                format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                :clearable="true" :disabled="loading" style="width: 260px"
              >
              </el-date-picker>
            </el-form-item>
            
            <!-- <el-form-item label="检验类型：">
              <el-radio-group v-model="searchForm.checkType">
                <el-radio :label="1">快检</el-radio>
                <el-radio :label="2">批检</el-radio>
              </el-radio-group>
            </el-form-item> -->
            
            
            <el-form-item>
              <el-button type="primary" :disabled="loading" @click="handleFilter">搜索</el-button>
            </el-form-item>
          </el-form>
          <!-- <slot name="printBtn"></slot> -->
        </div>
        
        <div class="flex-item overHide">
          <div class="scroll-div">
            
            <el-table
              :data="tableData"
              v-loading="loading"
              @expand-change="getExperimentDetail"
              :expand-row-keys="expands"
              row-key="id"
              :key="2"
              :show-overflow-tooltip="true"
              @selection-change="panelSelectionChange"
              >
              <template v-for="(item, index) in tableColumn" >
                <el-table-column v-if="item.prop == 'checkType'" :key="item.prop" :label="item.label" :fixed="item.fixed" align="center" >
                  <template slot-scope="scope">
                    <span class="type-span" style="border-radius: 3px; padding: 1px 3px" :style="{backgroundColor: scope.row.checkType == '1' ? '#61A480' : '#496BF9', color: '#fff'}">{{ scope.row.checkType == '1' ? '快检' : '批检' }}</span>
                  </template>
                </el-table-column>
  
                <el-table-column
                  v-else
                  :key="index" 
                  :prop="item.prop" 
                  :label="item.label" 
                  :fixed="item.fixed" 
                  :width="item.width || ''"
                  :formatter="item.formatter"
                  align="center" 
                />
              </template>
              
              
              <el-table-column width="80" label="操作" align="center" key="handle" :resizable="false">
                <template slot-scope="scope">
                  <el-button type="text" size="small" @click="tableOperated(scope.row)">选择</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
        <div class="mt16 mb4">
          <Pagination
            :total="total" 
            :pageNum="pageObj.pageNum" 
            :pageSize="pageObj.pageSize" 
            @getData="initData" 
          />
        </div>
      </div>

    </div>
  </template>
  
  <script>
  import { panelColumn} from "./config.js"
  import Pagination from "@/components/Pagination/index.vue";
  import moment from "@/utils/moment";
  export default {
    components: {
      Pagination
    },
    props:{
        materialType: {
            type: String,
            required: false,
        }
    },
    data() {
      return {
        loading: false,
        loading2: false,
        // 设置时间选择
        pickerOptions: {
          disabledDate(time) {
            let deadline = Date.now() - 60 * 60 * 1000;
            return time.getTime() > deadline //
          },
        },
        searchForm: {
          takeEffectDate: [],
          entrustReasonCodeList: [],
          checkType: 2
        },
        
        pageObj: {
          pageNum: 1, // 页数
          pageSize: 10, // 条数
        },
        total: 1,
        
        tableData: [],
        experimentCheckRecord: [], // 根据试验台账id获取所有检测记录信息
        expands: [],
  
        tableColumn: panelColumn,
        activeName: 'second',
        
        entrustReasonList: [],
        materialTypeList: [],

        panelSelects: [],
        
        materialType2: '',
      };
    },
    
    created: function() {
      this.$api.getDictValue({
        dictCode: 'ENTRUST_REASON'
      }, this).then(res => {
        this.entrustReasonList = res.data.list
      })
      
      this.$api.getDictValue({
        dictCode: 'MASTERIAL_TYPE'
      }, this).then(res => {
        if(res.succ){
          this.materialTypeList = res.data.list
          .filter(i => this.materialType ? i.dictValueCode == this.materialType : i.dictValueCode != 7)
          .map(i => {
            return {
              label: i.dictValueName,
              dictCode: i.dictCode,
              id: i.id,
              value: i.dictValueCode
            }
          })
          console.log(">>this.materialTypeList>>", this.materialTypeList)
          if(this.materialType){
            this.searchForm.experimentTypeList = [this.materialType]
          }
          
        }
      })
      
    },
    methods: {
      handleFilter() {
        console.log(this.searchForm)
        this.initData(1);
      },
      tableOperated(row){
        this.$emit('setSelectInfo', row)
      },
      isEmpty(val) {
        if (typeof val === "boolean") {
          return false;
        }
        if (typeof val === "number") {
          return false;
        }
        if (val instanceof Array) {
          if (val.length === 0) return true;
        } else if (val instanceof Object) {
          if (JSON.stringify(val) === "{}") return true;
        } else {
          if (
            val === "null" ||
            val == null ||
            val === "undefined" ||
            val === undefined ||
            val === ""
          )
            return true;
          return false;
        }
        return false;
      },
      resetForm(){
        this.searchForm = {
          takeEffectDate: [],
          entrustReasonCodeList: [],
        };
        this.initData(1);
      },
      initData(opageNum, opageSize,type){
        this.loading = true;
        if (opageNum) this.pageObj.pageNum = opageNum;
        if (opageSize) this.pageObj.pageSize = opageSize;
        
        if(type){
          this.searchForm.experimentTypeList = [type];
        }
        
        const params ={
          ...this.pageObj,
          params: {
            // "experimentStatus":3,//试验状态固定写死为3已完成
            ...this.searchForm
          } 
        }
        params.params.isAssociation = 1;
        if (!this.isEmpty(this.searchForm.takeEffectDate)) {
          params.params.beginTime = this.searchForm.takeEffectDate[0]
            ? this.searchForm.takeEffectDate[0]
            : "";
          params.params.comTime = this.searchForm.takeEffectDate[1]
            ? this.searchForm.takeEffectDate[1]
            : "";
        }
        delete params.params.takeEffectDate
        
        
        //params.params.entrustReasonCode = params.params.code.join()
        //获取列表
        this.tableData = [];
        this.$api.getExperimentList(params, this).then(res => {
          this.loading = false;
          if(res.succ){
            // this.$refs.tablePer.
            this.tableData = res.data.list;
            this.total = res.data.total;
          }else{
            this.$message.error(res.msg || '查询失败')
          }
        })
      },
      
      getExperimentDetail(row, expanded){
        //this.expands = [];
        if(row.childern && row.childern.length > 0){
          return false;
        }
        let index;
        for(let i = 0; i< this.tableData.length; i++){
          if(this.tableData[i].id == row.id){
            this.tableData[i].childern
            index = i;
          }
        }
        
        if(index >= 0){
          this.loading2 = true;
          //获取列表xiang请
          this.$api.getExperimentListDetail(`experimentId=${row.id}`, this).then(res => {
            this.loading2 = false;
            if(res.succ){
              console.log(index,res.data.list)
              this.tableData[index].childern = res.data.list;
              // this.$set(this.tableData[index],)
              this.$forceUpdate();
              console.log(this.tableData)
            }else{
              this.$message.error(res.msg || '查询失败')
            }
          })
        }
      },
      
      
      
      handleDel(row){
        this.$confirm("作废后不能恢复，确定要作废吗？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
          //作废
          this.$api.delExperiment({
            id: row.id
          },this).then((res) => {
            if (res.succ) {
              this.$message({
                showClose: true,
                message: "作废成功",
                type: "success",
              });
              
              this.initData();
            }
          });
        }).catch(() => {
          this.$message({
            type: "info",
            message: "已取消作废",
          });
        });
      },
      
      

      panelSelectionChange(val) {
        this.panelSelects = val;
      },

      getPrintType(val) {
        if (val.length == 0) {
          this.$message({
            showClose: true,
            message: "请选择打印数据表",
            type: "warning",
          });
          return;
        }
        this.printBtnClick(val.join(","))
      },
      printBtnClick(types) {
        if (this.panelSelects.length == 0) {
          // 弹出提示
          this.$message({
            showClose: true,
            message: "请选择试验台账",
            type: "warning",
          });
          return;
        }
        let routeData = this.$router.resolve({
          path: "/printRawContent",
          query: {
            // this.panelSelects 去除id 并逗号拼接
            experimentIds: this.panelSelects.map(i => i.id).join(','),
            printType: types
          }
        });
        window.open(routeData.href, '_blank');
      },
      
    },
  };
  </script>
  
  <style scoped lang="scss">
    ::v-deep .el-button{
      padding-left: 13px;
      padding-right: 13px;
    }
    .el-form-item{
      margin-bottom: 8px;
    }
    ::v-deep .el-form--inline{
      .el-form-item{
        margin-right: 24px;
        margin-bottom: 0;
        &:last-child{
          margin: 0;
        }
      }
    }
    ::v-deep .el-table{
      .expanded,.expanded:hover{
        background-color: #FFFBD9;
        
      }
      .expanded + tr{
        background-color: #FFFBD9;
        td{
          background-color: #FFFBD9;
        }
      }
      
      .table-child-box{
        margin: 16px;
        padding: 16px;
        background: #FFFFFF;
      }
    }
    
    .content-box{
      padding: 16px;
    }
    .content{
      width: 100%;
      height: 100%;
      padding: 16px;
      background: #FFFFFF;
      border-radius: 16px;
    }
    
    .search-box{
      padding-bottom: 16px;
      line-height: 40px;
    }
    
  </style>