<template>
  <div class="content-box">
    <div class="flex-box flex-column content">
      <div class="search-box flex-box">
        <el-form class="flex-item" ref="searchForm" :inline="true" :model="searchForm" :disabled="loading">
          
          
          <el-form-item label="工单编号：">
            <el-input v-model="searchForm.orderNo" clearable
              placeholder="请输入" 
              style="width: 180px" 
            />
          </el-form-item>
          
          <el-form-item label="工程名称：">
            <el-input v-model="searchForm.projectName" clearable
              placeholder="请输入" 
              style="width: 180px" 
            />
          </el-form-item>
          <el-form-item label="合同编号：">
            <el-input v-model="searchForm.contractNo" clearable
              placeholder="请输入" 
              style="width: 180px" 
            />
            <!-- <el-select
              v-model="searchForm.contractNo" 
              filterable clearable 
              placeholder="请选择合同编号" 
              style="width: 180px">
              <el-option
                v-for="item in contractList"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select> -->
          </el-form-item>
          
          <!-- <el-form-item label="合同名称：">
            <el-input v-model="searchForm.contractName" clearable
              placeholder="请输入" 
              style="width: 180px" 
            /> -->
            <!-- <el-select
              v-model="searchForm.contractName" 
              filterable clearable 
              placeholder="请选择合同编号" 
              style="width: 180px">
              <el-option
                v-for="item in contractList"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select> -->
          <!-- </el-form-item> -->
          
          <el-form-item label="工单类型：">
              <!-- multiple -->
            <el-select
              v-model="searchForm.orderTypeCode" 
              filterable clearable 
              placeholder="请选择工单类型" 
              style="width: 180px">
              <el-option
                v-for="item in orderTypeList"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
            
          </el-form-item>
          <el-form-item>
            <el-button type="primary" :disabled="loading" @click="handleFilter">搜索</el-button>
            <el-button type="text" icon="el-icon-refresh-right" :disabled="loading" @click="resetForm()">重置</el-button>
          </el-form-item>
          <!-- <el-form-item label="创建时间：" prop="takeEffectDate">
            <el-date-picker type="daterange" 
              v-model="searchForm.takeEffectDate" 
              start-placeholder="开始日期" end-placeholder="结束日期" 
              format="yyyy-MM-dd" value-format="yyyy-MM-dd"
              :clearable="true" :disabled="loading" style="width: 360px"
            >
            </el-date-picker>
            
            
          </el-form-item> -->
          
        </el-form>
        <el-button v-if="roleId == '6'" type="primary" @click="handleSetTarget()">新建工单</el-button>
        <!-- <el-button type="primary" plain :disabled="loading" @click="handleSetTarget()">导出数据</el-button> -->
      </div>
      
      <div class="flex-item overHide">
        <div class="scroll-div">
          <el-table
            :data="tableData"
            v-loading="loading"
            style="width: 100%">
            <template v-for="item in tableColumn">

              <af-table-column
                v-if="item.prop == 'orderStatusText'"
                :key="item.prop"
                :prop="item.prop" 
                :label="item.label" 
                :formatter="item.formatter"
                :fixed="item.fixed" 
                :width="item.width || ''"
                align="center" 
              >
                <template slot-scope="scope">
                  <!-- 工单状态  0-待处理  1-接受  2-完成  3-拒绝 4 作废-->
                  <el-row class="cell-state">
                    <label v-if="scope.row.orderStatusText == '待处理'" class="rda-task-state dqr">{{ scope.row.orderStatusText }}</label>
                    <label v-if="scope.row.orderStatusText == '接受'" class="rda-task-state ddd">已接受</label>
                    <label v-if="scope.row.orderStatusText == '完成'" class="rda-task-state yqx">已完成</label>
                    <label v-if="scope.row.orderStatusText == '拒绝'" class="rda-task-state yqx">已拒绝</label>
                    <label v-if="scope.row.orderStatusText == '作废'" class="rda-task-state yqx">已作废</label>
                  </el-row>
                </template>
              </af-table-column>

              <af-table-column
                v-else
                :key="item.prop"
                :prop="item.prop" 
                :label="item.label" 
                :formatter="item.formatter"
                :fixed="item.fixed" 
                :width="item.width || ''"
                show-overflow-tooltip
                align="center" 
              />
            </template>
            
            <!-- 工单状态  0-待处理  1-接受  2-完成  3-拒绝 -->
            <af-table-column width="230" label="操作" fixed="right" align="left" key="handle" :resizable="false">
              <template slot-scope="scope">
                <el-button v-if="roleId == '6' && (scope.row.orderStatus == 0 || scope.row.orderStatus == 1)" type="text" size="mini" @click="handleSetTarget(scope.row)">编辑</el-button>
                
                
                <el-button v-if="userId === scope.row.creater && scope.row.orderStatus != '2' && scope.row.orderStatus != '4' && roleId == '6'" type="text" size="mini" @click="handleSetTransfer(scope.row, 'setWorkOrderTransfer')">转派</el-button>
                <template v-else-if="userId === scope.row.creater && scope.row.orderStatus == '0'">
                  <el-button type="text" size="mini" @click="handleSetState(scope.row, 'setWorkOrderAccept')">接收</el-button>
                  <el-button type="text" size="mini" @click="handleSetState(scope.row, 'setWorkOrderRefuse')">拒绝</el-button>
                </template>
                <template v-else-if="scope.row.orderStatus == '1'">
                  <el-button type="text" size="mini" @click="handleSetState(scope.row, 'setWorkOrderFinish')">完成</el-button>
                </template>
                
                
                <el-button type="text" size="mini" @click="goDetail(scope.row)">详情</el-button>
                <el-button v-if="scope.row.orderStatus != 2 && scope.row.orderStatus != 3 && scope.row.orderStatus != 4" type="text" size="mini" style="color: #ff0000;" @click="handleDel(scope.row)">作废</el-button>
              </template>
            </af-table-column>
          </el-table>
        </div>
      </div>
      <div class="mt16 mb4">
        <Pagination
          :total="total" 
          :pageNum="pageObj.pageNum" 
          :pageSize="pageObj.pageSize" 
          @getData="initData" 
        />
      </div>
    </div>
    
    
    <DrawerForm
      :formContent="formContent"
      ref="drawerForm"
      @saveTarget="saveTarget"
    >
      <template #customForm>
        <el-form-item
            :label="`订单：`"
          >
          <el-input 
            @click.native="showOrderDialogClick"
            readonly
            :value="currentOrder.trwdId ? `${currentOrder.ftpz || ''},${currentOrder.fjzbw || ''},${currentOrder.fphbNo || ''}` : ''"
            :placeholder="`请选择订单`"
            :style="{'width': 350 + 'px'}"
          />
        </el-form-item>
      </template>
    </DrawerForm>
    
    <el-dialog :title="'转派工单：' + activeObj.orderNo" :visible.sync="dialogFormVisible">
      <el-form>
        <el-form-item label="客服员">
          <el-select v-model="serviceObj" value-key="value" placeholder="请选择">
            <el-option
              v-for="item in userListData"
              :key="item.value"
              :label="item.label"
              :value="item">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSetTransfer2">确 定</el-button>
      </div>
    </el-dialog>

    <!-- <el-dialog title="选择订单" :visible.sync="showOrderDialog">
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSetTransfer2">确 定</el-button>
      </div>
    </el-dialog> -->
    <PageTableDialog 
      @setData="setOrderData" 
      :tableColumn="orderTableColumn" 
      ref="orderDialog" 
    >
      <template #search>
        <div class="search-box flex-box">
          <el-form class="flex-item" ref="orderSearchForm" :inline="true" :model="orderSearchForm" :disabled="orderLoading">
            <el-form-item label="订单编号：">
              <el-input v-model="orderSearchForm.frwno" clearable
                placeholder="请输入" 
                style="width: 180px" 
              />
            </el-form-item>
            <el-form-item label="工程名称：">
              <el-input v-model="orderSearchForm.fgcmc" clearable
                placeholder="请输入" 
                style="width: 180px" 
              />
            </el-form-item>
            <el-form-item label="客户名称：">
              <el-input v-model="orderSearchForm.fhtdw" clearable
                placeholder="请输入" 
                style="width: 180px" 
              />
            </el-form-item>
            <!-- <el-form-item label="合同编号：">
              <el-input v-model="searchForm.fhtbh" clearable
                placeholder="请输入" 
                style="width: 180px" 
              />
            </el-form-item> -->

            <el-form-item>
              <el-button type="primary" :disabled="orderLoading" @click="handleOrderFilter">搜索</el-button>
              <el-button type="text" icon="el-icon-refresh-right" :disabled="orderLoading" @click="resetOrderForm">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </template>
    </PageTableDialog>

    <el-dialog
      v-if="workOrderFinishVisible"
      title=""
      :visible.sync="workOrderFinishVisible"
      width="30%"
      @close="workOrderFinishVisible = false">
      <span>{{ '确定完成工单： ‘' + activeRow.orderNo + '’ 吗？' }}</span>
      <span slot="footer" class="dialog-footer">
        <el-button style="float: left;" @click="workOrderFinishVisible = false">取消</el-button>
        <el-button type="success"  @click="setWorkOrderFinishResp(1)">完成并生产快检</el-button>
        <el-button type="primary" @click="setWorkOrderFinishResp(0)">完成工单</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { woColumn as tableColumn } from "./config.js"
import Pagination from "@/components/Pagination/index.vue";
import DrawerForm from "@/components/drawerForm.vue";
import PageTableDialog from "@/components/pageTableDialog.vue";
import getOpt from "@/common/js/getListData.js"
export default {
  name:'workOrderList',
  components: {
    Pagination,
    DrawerForm,
    PageTableDialog
  },
  data() {
    return {
      activeObj: {},//转派对象
      serviceObj: {},
      dialogFormVisible: false,
      addApi: 'addWorkOrder',
      delApi: 'delWorkOrder',
      cancelApi: 'cancelWorkOrder',
      updateApi: 'setWorkOrder',
      getListApi: 'getWorkOrderList',
      userId: this.$store.state.loginStore.userInfo.userId,
      roleId: this.$store.state.loginStore.userInfo.roleId,
      contractList: [],//合同列表
      loading: false,
      filePrefix: this.$store.state.loginStore.userInfo.filePrefix,
      searchForm: {},
      
      orderTypeList: [],
      tableColumn: tableColumn,
      pageObj: {
        pageNum: 1, // 页数
        pageSize: 10, // 条数
      },
      total: 1,
      tableData: [],
      
      activeRow: {},//编辑对象
      
      formContent: [],
      rules: {},
      
      contractopt:[],
      contractoptOpt: [],

      tglOrderOpt:[],
      tglOrderData: [],

      workOrderFinishVisible: false,

      userListData: [],
      roleList:[{
            label: '管理员',
            value: 1
          },{
            label: '非管理员',
            value: 2
          }],
      
      showOrderDialog: false,
      currentOrder: {}, // 当前订单
      orderSearchForm: {},
      orderLoading: false,
      orderTableColumn: [
        {
          label: '任务单号',
          prop: 'rwdextraInfo.frwno',
        },
        {
          label: '单位名称',
          prop: 'rwdextraInfo.fhtdw',
        },
        {
          label: '工程名称',
          width: "200px",
          prop: 'rwdextraInfo.fgcmc',
        },
        {
          label: '施工部位',
          width: "200px",
          prop: 'rwdextraInfo.fjzbw',
        },
        {
          label: '浇筑方式',
          prop: 'rwdextraInfo.fjzfs',
        },
        {
          label: '坍落度',
          prop: 'rwdextraInfo.ftld',
        },
        {
          label: '合同编号',
          prop: 'rwdextraInfo.fhtbh',
        },
        {
          label: '计划时间',
          prop: 'rwdextraInfo.fjhrq'
        },
        {
          label: '砼品种',
          prop: 'rwdextraInfo.ftpz'
        },
        {
          label: '施工配比',
          prop: 'rwdextraInfo.fphbNo'
        },
        {
          label: '工程区域',
          prop: 'rwdextraInfo.fgcdz',
          width: "200px"
        },
      ]
    };
  },
  
  created() {
    this.initData();
    
     
    // this.$api.getContractPageList({
    //     ...this.pageObj,
    //     params: {}
    // }).then(res =>{
    //   if(res.succ){
    //     this.contractList = res.list
    //   }
    // })
    
    this.$api.getDictValue({
      dictCode: 'ORDER_TYPE'
    }).then(res =>{
      if(res.succ){
        this.orderTypeList = res.data.list.map(i => {
          return {
            label: i.dictValueName,
            value: i.dictValueCode
          }
        })
        this.getOptMethod()
      }
    })
  },
  methods: {
    async getTglTrwdInfo() {
      this.$refs.drawerForm.setEditFormValue('trwdId', `${this.currentOrder.trwdId}`)
      this.$refs.drawerForm.setEditFormValue('projectName', this.currentOrder.fgcmc)
      this.$refs.drawerForm.setEditFormValue('contractNo', this.currentOrder.fhtbh)
      this.$refs.drawerForm.setEditFormValue('contractName', this.currentOrder.contractName)
      this.$refs.drawerForm.setEditFormValue('customerName', this.currentOrder.fhtdw)
    },
    
    async getOptMethod(){
      this.userListData = await getOpt.getUserAll(this,{
        isArea: 1,
      });
      this.formContent = [
        {
          type: 'input',
          label: '合同编号',
          prop: 'contractNo',
          disabled: true,
        },
        {
          type: 'input',
          label: '客户名称',
          prop: 'customerName',
          disabled: true,
        },
        {
          type: 'input',
          label: '工程名称',
          prop: 'projectName',
          // options: this.contractoptOpt,
          disabled: true,
        },
        {
          type: 'select',
          label: '客服员',
          prop: 'userId',
          options: this.userListData
        },
        {
          type: 'select',
          label: '工单类型',
          prop: 'orderTypeCode',
          options: this.orderTypeList,
        },
      ]
    },
    goDetail(row){
      this.$router.push({
        path: '/engineeringService/workOrderDetial',
        query: {
          id: row.id
        }
      })
    },
    handleFilter() {
      console.log(this.searchForm)
      this.initData(1);
    },
    resetForm(){
      this.searchForm = {};
      this.initData(1);
    },
    initData(opageNum, opageSize){
      this.loading = true;
      if (opageNum) this.pageObj.pageNum = opageNum;
      if (opageSize) this.pageObj.pageSize = opageSize;
        
      const params ={
        ...this.pageObj,
        params: this.searchForm
      }
      //获取列表
      this.$api[this.getListApi](params, this).then(res => {
        this.loading = false;
        if(res.succ){
          this.tableData = res.data.list;
          this.total = res.data.total;
        }else{
          this.$message.error(res.msg || '查询失败')
        }
      })
    },
    //转派
    handleSetTransfer(row, type){
      this.dialogFormVisible = true;
      this.activeObj = row;
    },handleSetTransfer2(){
      this.$confirm('确定转派工单： ‘' + this.activeObj.orderNo + '’ 给‘' + this.serviceObj.label + '’吗？', "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.$api.setWorkOrderTransfer({
          id: this.activeObj.id,
          userId: this.serviceObj.value,
          userName: this.serviceObj.label
        }, this).then((res) => {
          if (res.succ) {
            this.dialogFormVisible = false;
            this.$message({
              showClose: true,
              message: "操作成功",
              type: "success",
            });
            this.initData();
          }
        });
      }).catch(() => {
        this.$message({
          type: "info",
          message: "已取消",
        });
      });
    },
    
    //接受，完成 拒绝
    handleSetState(row, type){
      this.activeRow = row;
      let tip = ''
      if(type == 'setWorkOrderAccept'){
        tip = '确定接收工单： ‘' + row.orderNo + '’ 吗？'
      }else if(type == 'setWorkOrderFinish'){
        // tip = '确定完成工单： ‘' + row.orderNo + '’ 吗？'
        this.workOrderFinishVisible = true;
        return;
      }else if(type == 'setWorkOrderRefuse'){
        tip = '确定拒绝工单： ‘' + row.orderNo + '’ 吗？'
      }
      
      this.$confirm(tip, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        //删除
        this.$api[type]({
          id: row.id
        }, this).then((res) => {
          if (res.succ) {
            this.$message({
              showClose: true,
              message: "操作成功",
              type: "success",
            });
            this.initData();
          }else{
            this.$message({
              showClose: true,
              message: res.msg,
              type: "error",
            });
          }
        });
      })
    },

    setWorkOrderFinishResp(genQuick) {
      this.$api["setWorkOrderFinish"]({
          id: this.activeRow.id,
          genQuick: genQuick
        }, this).then((res) => {
          if (res.succ) {
            this.$message({
              showClose: true,
              message: "操作成功",
              type: "success",
            });
            this.initData();
          }else{
            this.$message({
              showClose: true,
              message: res.msg,
              type: "error",
            });
          }

          this.workOrderFinishVisible = false;
          this.activeRow = {};
        })
    },
    
    //删除
    handleDel(row){
      this.$confirm("删除后不能恢复，确定要删除吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        //删除
        this.$api[this.delApi]({
          id: row.id
        }, this).then((res) => {
          if (res.succ) {
            this.$message({
              showClose: true,
              message: "删除成功",
              type: "success",
            });
            if(this.tableData.length == 1 && this.pageObj.pageNum > 1){
              this.pageObj.pageNum = this.pageObj.pageNum -1
            }
            this.initData();
          }
        });
      }).catch(() => {
        this.$message({
          type: "info",
          message: "已取消删除",
        });
      });
    },

    //删除
    handleDel(row){
      this.$confirm("确定要作废吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        //删除
        this.$api[this.cancelApi]({
          id: row.id
        }, this).then((res) => {
          if (res.succ) {
            this.$message({
              showClose: true,
              message: "删除成功",
              type: "success",
            });
            if(this.tableData.length == 1 && this.pageObj.pageNum > 1){
              this.pageObj.pageNum = this.pageObj.pageNum -1
            }
            this.initData();
          }
        });
      }).catch(() => {
        this.$message({
          type: "info",
          message: "已取消删除",
        });
      });
    },
    
    handleSetTarget(row){
      if(this.roleId != '6'){
        if (row) {
          this.$message.error("您不是 客服组长，不能修改工单")
        }else{
          this.$message.error("您不是 客服组长，不能新建工单")
        }
        return false;
      }
      this.activeRow = row;
      this.currentOrder = {}
      if(row){
        this.formContent[3].type="input";
        this.formContent[3].prop = "userName";
        this.formContent[3].disabled = true;

        if (row.trwdId && row.rwdextraInfo) {
          this.currentOrder = {
            trwdId: row.trwdId,
            ftpz: row.rwdextraInfo.ftpz,
            fjzbw: row.rwdextraInfo.fjzbw,
            fphbNo: row.rwdextraInfo.fphbNo,
            fgcmc: row.rwdextraInfo.fgcmc,
            fhtbh: row.rwdextraInfo.fhtbh,
            contractName: row.rwdextraInfo.contractName,
            fhtdw: row.rwdextraInfo.fhtdw,
            ftpz: row.rwdextraInfo.ftpz,
            fjzbw: row.rwdextraInfo.fjzbw,
          }

          this.getTglTrwdInfo();
          return;
        }
      }else{
        this.formContent[3].type="select";
        this.formContent[3].prop = "userId";
        this.formContent[3].disabled = false;
      }
      this.$refs.drawerForm.initData(row);
      // this.getTglTrwdAllListResp(row.trwdId);
    },
    getName(oVal, list){
      for(let i =0;i<list.length; i++){
        if(oVal == list[i].value){
          return list[i].label;
        }
      }
    },
    getName2(oVal, list){
      for(let i =0;i<list.length; i++){
        if(oVal == list[i].value){
          return list[i].id;
        }
      }
    },
    getName3(oVal, list){
      for(let i =0;i<list.length; i++){
        if(oVal == list[i].value){
          return list[i].contractId;
        }
      }
    },
    
    saveTarget(formData){
      if (!formData.trwdId) {
        this.$message({
          message: "请选择订单",
        });
        return;
      }
      formData.orderTypeName = this.getName(formData.orderTypeCode, this.orderTypeList)

      if(formData.id){//修改this.activeRow
        formData.userId = this.activeRow.userId;
        formData.userName = this.activeRow.userName;

        this.$api[this.updateApi](formData, this).then((res) => {
          if (res.succ) {
            this.$message({
              showClose: true,
              message: "修改成功",
              type: "success",
            });
            this.$refs.drawerForm.handleClose();
            this.initData();
          }
        });
      }else{
        formData.userName = this.getName(formData.userId, this.userListData)
        
        this.$api[this.addApi](formData, this).then((res) => {
          if (res.succ) {
            this.$message({
              showClose: true,
              message: "添加成功",
              type: "success",
            });
            this.initData();
            this.$refs.drawerForm.handleClose();
          }
        });
      }
    },

    showOrderDialogClick() {
      this.showOrderDialog = true;
      this.$refs.orderDialog.initData('getTglTrwdPageList', {isCarryOut: 1, isProjectArea: 1})
    },
    setOrderData(val) {
      this.currentOrder = {
        trwdId: val.rwdextraInfo.frwdh,
        fgcmc: val.rwdextraInfo.fgcmc,
        fhtbh: val.rwdextraInfo.fhtbh,
        contractName: val.rwdextraInfo.contractName,
        fhtdw: val.rwdextraInfo.fhtdw,
        ftpz: val.rwdextraInfo.ftpz,
        fjzbw: val.rwdextraInfo.fjzbw,
        fphbNo: val.rwdextraInfo.fphbNo
      }

      this.getTglTrwdInfo()
    },

    getTglTrwdAllListResp(frwdh) {
      this.$api.getTglTrwdAllList({
        frwdh: frwdh
      }).then(res => {
        if (res.succ && res.data.list) {
          let val = res.data.list[0];
          this.currentOrder = {
            trwdId: val.rwdextraInfo.frwdh,
            fgcmc: val.rwdextraInfo.fgcmc,
            fhtbh: val.rwdextraInfo.fhtbh,
            contractName: val.rwdextraInfo.contractName,
            fhtdw: val.rwdextraInfo.fhtdw,
            ftpz: val.rwdextraInfo.ftpz,
            fjzbw: val.rwdextraInfo.fjzbw,
            fphbNo: val.rwdextraInfo.fphbNo
          };
        }
      })
    },

    handleOrderFilter() {
      this.$refs.orderDialog.tableDataSearch({
        ...this.orderSearchForm,
        isCarryOut: 1
      });
    },
    resetOrderForm() {
      this.orderSearchForm = {};
      this.$refs.orderDialog.tableDataSearchReset({isCarryOut: 1});
    }
  },
};
</script>

<style scoped lang="scss">
  
  ::v-deep .el-select-dropdown__item{
    max-width: 500px;
  }
  ::v-deep .el-button{
    padding-left: 13px;
    padding-right: 13px;
    height: 40px;
  }
  .el-form-item{
    margin-bottom: 8px;
  }
  ::v-deep .el-form--inline{
    .el-form-item{
      margin-right: 24px;
      margin-bottom: 0;
      &:last-child{
        margin: 0;
      }
    }
  }
  ::v-deep .el-table{
    .expanded,.expanded:hover{
      background-color: #FFFBD9;
      
    }
    .expanded + tr{
      background-color: #FFFBD9;
      td{
        background-color: #FFFBD9;
      }
    }
    
    .table-child-box{
      margin: 16px;
      padding: 16px;
      background: #FFFFFF;
    }
  }
  
  .content-box{
    padding: 16px;
  }
  .content{
    width: 100%;
    height: 100%;
    padding: 16px;
    background: #FFFFFF;
    border-radius: 16px;
  }
  
  .search-box{
    padding-bottom: 16px;
    line-height: 40px;
  }
  .cell-state {
    .rda-task-state {
        display: inline-block;
        width: 43px;
        height: 18px;
        line-height: 18px;
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #FFFFFF;
        text-align: center;
        background: #1F7AFF;
        border-radius: 2px;
    }
    .dqr {
        background: #DC3290;
    }
    .ddd {
        background: #3369FF;
    }
    .dwc {
        background: #1FAE66;
    }
    .yqx {
        background: #D6D6D6;
    }
    .yjj {
        background: #ADAA00;
    }
    .ywc {
        background: #515157;
    }
  }
</style>