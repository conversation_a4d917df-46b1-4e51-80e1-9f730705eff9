
<template>
  <div class="content-box">
    <div class="flex-box flex-column content">
      <div class="search-box flex-box">
        <el-form class="flex-item" ref="searchForm" :inline="true" :model="searchForm">
          <el-form-item label="合同工程名称：">
            <el-input v-model="searchForm.projectName" clearable placeholder="请输入合同工程名称" style="width: 180px" />
          </el-form-item>
          <el-form-item label="协会工程名称：">
            <el-input v-model="searchForm.laboratoryProjectName" clearable placeholder="请输入协会工程名称" style="width: 180px" />
          </el-form-item>
          <!-- <el-form-item label="合同名称：">
            <el-input v-model="searchForm.contractName" clearable
              placeholder="请输入合同名称" 
              style="width: 180px" 
            />
          </el-form-item> -->

          <el-form-item label="收货单位：">
            <el-input v-model="searchForm.receiveCompanyName" clearable placeholder="请输入收货单位" style="width: 180px" />
          </el-form-item>
          <!-- 合同类型 单选 -->
          <el-form-item label="合同类型：">
            <el-select v-model="searchForm.contractType" placeholder="请选择合同类型" clearable style="width: 180px">
              <el-option
                v-for="item in [{label: '报建合同', value: '0'}, {label: '非报建合同', value: '1'}]"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleFilter">搜索</el-button>
            <el-button type="text" icon="el-icon-refresh-right" @click="resetForm()">重置</el-button>
          </el-form-item>
        </el-form>
        <!-- <el-button type="primary" @click="handleSetTarget()">新增工程</el-button> -->
        <!-- <el-button v-if="roleId == '5'" type="primary" @click="initEngineering()">初始化协会工程</el-button> -->
      </div>

      <div class="flex-item overHide">
        <div class="scroll-div">
          <el-table :data="tableData" style="width: 100%" :row-style="cellStyle">
            <af-table-column type="expand">
                <template slot-scope="scope">
                  <div class="table-child-box">
                    <el-table :data="scope.row.childernData"
                      :key="scope.row.id"
                      border
                      style="width: 100%;"
                    >
                      <af-table-column 
                        v-for="subItem in childernDataColumn" 
                        :key="subItem.prop" :prop="subItem.prop" 
                        :label="subItem.label" 
                        :fixed="subItem.fixed" 
                        :width="subItem.width || ''"
                        :formatter="subItem.formatter"
                        align="center" 
                        :resizable="false" 
                        :show-overflow-tooltip="true"
                      >
                      </af-table-column>
                    </el-table>
                  </div>
                </template>
              </af-table-column>

            <template v-for="item in tableColumn">
              <af-table-column v-if="item.prop == 'isUpload'" :show-overflow-tooltip="true" :key="item.prop" :label="item.label" :fixed="item.fixed" align="center">
                <template slot-scope="scope">
                  <el-row class="cell-state">
                    <label v-if="scope.row.isUpload == '1'" class="rda-task-state yqx">已上传</label>
                    <label v-else class="rda-task-state ddd">未上传</label>
                  </el-row>
                </template>
              </af-table-column>
              <af-table-column v-else :key="item.prop" :prop="item.prop" :label="item.label" :formatter="item.formatter" :fixed="item.fixed" :width="item.width || ''" :show-overflow-tooltip="true" align="center" />
            </template>

            <af-table-column width="200" label="操作" fixed="right" align="center" key="handle" :resizable="false">
              <template slot-scope="scope">
                <el-button type="text" size="small" @click="handleSetTarget(scope.row)">编辑</el-button>
                <el-button type="text" size="small" @click="goDetail(scope.row)">详情</el-button>
                <el-button type="text" size="small" v-if="!(scope.row.isUpload == 1)" @click="uploadRow(scope.row)">上传</el-button>
                <el-button type="text" size="small" @click="bindWLOperated(scope.row)">绑定物料</el-button>
                <!-- <el-button type="text" size="small" @click="tableOperated(scope.row)">查看订单</el-button>
                <el-button type="text" size="small" @click="handleSetTarget2(scope.row)">位置修改</el-button> -->
                <!-- <el-button type="text" size="small" style="color: #ff0000;" @click="handleDel(scope.row)">删除</el-button> -->
              </template>
            </af-table-column>
          </el-table>
        </div>
      </div>
      <div class="mt16 mb4">
        <Pagination :total="total" :pageNum="pageObj.pageNum" :pageSize="pageObj.pageSize" @getData="initData" />
      </div>
    </div>

    <DrawerForm :formContent="formContent" ref="drawerForm" @saveTarget="saveTarget">
    </DrawerForm>

    <DrawerForm :formContent="formContent2" ref="drawerForm2" @saveTarget="saveTarget2">
    </DrawerForm>

    <el-dialog 
      title=""
      :visible.sync="bindWLDialogFormVisible"
      width="70%"
    >
      <el-row v-if="bindWLDialogFormVisible" style="font-size: 20px; font-weight: bold;">物料信息</el-row>
        <div>
            <el-table :data="selectedTableData.childernData"
                class="mt16"
                border :max-height="600"
                style="width: 100%;"
                :row-style="{height: '40px'}"
                :cell-style="{padding: '0'}"
            >
                
                <el-table-column 
                    v-for="item in childernDataColumn" 
                    :key="item.prop" :prop="item.prop" 
                    :label="item.label" 
                    :fixed="item.fixed" 
                    :formatter="item.formatter"
                    align="center" 
                    :resizable="false" 
                    :show-overflow-tooltip="true"
                />
                <el-table-column  width="300" label="操作" align="center" key="handle" :resizable="false">
                    <template slot-scope="scope">
                      <el-button type="text" size="small" @click="handleSetWLTarget(scope.row, scope.$index)">选择原材料</el-button>
                      <el-button style="margin-left: 30px;color: #ff0000;" type="text" size="small" @click="resetConsumptionTarget(scope.row, scope.$index)">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <div class="footer-btn">
            <el-button type="primary" @click="bindWLDialogClose" plain>取消</el-button>
            <el-button type="primary" @click="saveSelected">保存</el-button>
        </div>
    </el-dialog>

    <el-dialog 
      title="" 
      :visible.sync="bindWLValidationVisible"
      width="90%"
    >
      <MaterialsListPanel v-if="bindWLValidationVisible" ref="materialListPanel" @selected="selectedMaterialListPanel" />
    </el-dialog>
  </div>
</template>

<script>
import { engineeringColumn as tableColumnData } from './config.js'
import { tieBiaoEngineeringColumn as tieBiaoTableColumnData } from './config.js'
import Pagination from '@/components/Pagination/index.vue'
import DrawerForm from '@/components/drawerForm.vue'
import MaterialsListPanel from './materialsListPanel.vue'

export default {
  name: 'engineeringList',
  components: {
    Pagination,
    DrawerForm,
    MaterialsListPanel
  },
  data() {
    return {
      isTieBiao: this.$store.state.loginStore.tieBiaoFlag,
      addApi: 'addEngineering',
      delApi: 'delEngineering',
      updateApi: 'setEngineering',
      getListApi: 'queryNewEngineeringList',
      roleId: this.$store.state.loginStore.userInfo.roleId,
      loading: false,
      searchForm: {},
      tableColumn: [],
      childernDataColumn: [],
      selectedTableData: [],
      childernDataOrg: [
        {
          name: '水泥',
          key: 'sn',
          type: 1,
          json: {
            "wlid":"",
            "wllx":"",
            "wlmc":"",
            "wlgg":"",
            "cllx":"",
            "clmc":"",
            "clgg":"",
            "cj":"",
            "cjjc":"",
            "gys":"",
            "gysjc":""
          }
        },
        {
            name: '粉煤灰',
            key: 'fmh',
            type: 2,
            json: {
              "wlid":"",
              "wllx":"",
              "wlmc":"",
              "wlgg":"",
              "cllx":"",
              "clmc":"",
              "clgg":"",
              "cj":"",
              "cjjc":"",
              "gys":"",
              "gysjc":""
            }
        },
        {
            name: '矿渣粉',
            key: 'kzf',
            type: 3,
            json: {
              "wlid":"",
              "wllx":"",
              "wlmc":"",
              "wlgg":"",
              "cllx":"",
              "clmc":"",
              "clgg":"",
              "cj":"",
              "cjjc":"",
              "gys":"",
              "gysjc":""
            }
        },
        {
            name: '粗骨料',
            key: 'cgl',
            type: 4,
            json: {
              "wlid":"",
              "wllx":"",
              "wlmc":"",
              "wlgg":"",
              "cllx":"",
              "clmc":"",
              "clgg":"",
              "cj":"",
              "cjjc":"",
              "gys":"",
              "gysjc":""
            }
        },
        {
            name: '细骨料',
            key: 'xgl',
            type: 5,
            json: {
              "wlid":"",
              "wllx":"",
              "wlmc":"",
              "wlgg":"",
              "cllx":"",
              "clmc":"",
              "clgg":"",
              "cj":"",
              "cjjc":"",
              "gys":"",
              "gysjc":""
            }
        },
        {
            name: '外加剂1',
            key: 'wjj1',
            type: 6,
            json: {
              "wlid":"",
              "wllx":"",
              "wlmc":"",
              "wlgg":"",
              "cllx":"",
              "clmc":"",
              "clgg":"",
              "cj":"",
              "cjjc":"",
              "gys":"",
              "gysjc":""
            }
        },
        {
            name: '外加剂2',
            key: 'wjj2',
            type: 6,
            json: {
              "wlid":"",
              "wllx":"",
              "wlmc":"",
              "wlgg":"",
              "cllx":"",
              "clmc":"",
              "clgg":"",
              "cj":"",
              "cjjc":"",
              "gys":"",
              "gysjc":""
            }
        },
        {
            name: '外掺料1',
            key: 'wcl1',
            type: 6,
            json: {
              "wlid":"",
              "wllx":"",
              "wlmc":"",
              "wlgg":"",
              "cllx":"",
              "clmc":"",
              "clgg":"",
              "cj":"",
              "cjjc":"",
              "gys":"",
              "gysjc":""
            }
        },
        {
            name: '外掺料2',
            key: 'wcl2',
            type: 6,
            json: {
              "wlid":"",
              "wllx":"",
              "wlmc":"",
              "wlgg":"",
              "cllx":"",
              "clmc":"",
              "clgg":"",
              "cj":"",
              "cjjc":"",
              "gys":"",
              "gysjc":""
            }
        },
      ],
      pageObj: {
        pageNum: 1, // 页数
        pageSize: 10 // 条数
      },
      total: 1,
      tableData: [],

      activeRow: {}, //编辑对象

      formContent2: [],
      formContent: [],
      rules: {},

      bindWLDialogFormVisible: false,
      bindWLValidationVisible: false,
      selectedMaterialsIndex: 0,
    }
  },

  created() {
    this.tableColumn = this.isTieBiao ? tieBiaoTableColumnData : tableColumnData,
    this.childernDataColumn = [{
        prop: "name",
        label: "原材料名称",
        width: '240'
      },
      {
        prop: "json.clmc",
        label: "材料名称",
        width: '240',
        formatter: function(row, column) {
          return (row.json.clmc || '--') + `${row.json.wlmc ? '(' + row.json.wlmc + ')' : ''}`
        }
      },
      {
        prop: "json.clgg",
        label: "材料规格",
        width: '240',
        formatter: function(row, column) {
          return (row.json.clgg || '--') + `${row.json.wlgg ? '(' + row.json.wlgg + ')' : ''}`
        }
      },
      {
        prop: "json.gys",
        label: "供应商",
        width: '240',
      },
      {
        prop: "json.gysjc",
        label: "供应商简称",
        width: '240',
      },
      {
        prop: "json.cj",
        label: "厂家",
        width: '240',
      },
      {
        prop: "json.cjjc",
        label: "厂家简称",
        width: '240',
      },
      ],
    this.initData()
    this.formContent2 = [
      {
        type: 'input',
        label: '位置',
        prop: 'projectAdd'
      }
    ]

    this.formContent = this.isTieBiao ? [
      {
        type: 'input',
        label: '合同编号',
        prop: 'contractNo',
        disabled: true
      },
      {
        type: 'input',
        label: '合同工程名称',
        prop: 'projectName',
        disabled: true
      },
      {
        type: 'input',
        label: '协会工程名称',
        prop: 'laboratoryProjectName'
      },
      {
        type: 'input',
        label: '建设单位',
        prop: 'jsdw'
      },
      {
        type: 'input',
        label: '施工单位',
        prop: 'sgdw'
      },
      {
        type: 'input',
        label: '监理单位',
        prop: 'jldw'
      },
      {
        type: 'input',
        label: '工程监督号',
        prop: 'jdh'
      },
      {
        type: 'input',
        label: '收货单位',
        prop: 'shdw'
      },
      {
        type: 'radio',
        label: '合同类型',
        prop: 'contractType',
        options: [
          { value: 0, label: '报建合同' },
          { value: 1, label: '非报建合同' }
        ]
      },
      {
        type: 'radio',
        label: '铁标工程',
        prop: 'isIronMark',
        options: [
          { value: 0, label: '否' },
          { value: 1, label: '是' }
        ]
      }
    ] : [

      {
        type: 'input',
        label: '合同编号',
        prop: 'contractNo',
        disabled: true
      },
      {
        type: 'input',
        label: '合同工程名称',
        prop: 'projectName',
        disabled: true
      },
      {
        type: 'input',
        label: '协会工程名称',
        prop: 'laboratoryProjectName'
      },
      {
        type: 'input',
        label: '建设单位',
        prop: 'jsdw'
      },
      {
        type: 'input',
        label: '施工单位',
        prop: 'sgdw'
      },
      {
        type: 'input',
        label: '监理单位',
        prop: 'jldw'
      },
      {
        type: 'input',
        label: '工程监督号',
        prop: 'jdh'
      },
      {
        type: 'input',
        label: '收货单位',
        prop: 'shdw'
      },
      {
        type: 'radio',
        label: '合同类型',
        prop: 'contractType',
        options: [
          { value: 0, label: '报建合同' },
          { value: 1, label: '非报建合同' }
        ]
      },
    ]
  },
  methods: {
    cellStyle({ row, column, rowIndex, columnIndex }) {
      if (row.isUpdate == '0') {
        // return {color: "#FF7F50"};
        return { color: '#FF4500' }
      } else {
        return { color: '#333' }
      }
    },
    goDetail(row) {
      this.$router.push({
        path: '/engineeringService/engineeringDetial',
        query: {
          id: row.id
        }
      })
    },
    handleFilter() {
      console.log(this.searchForm)
      this.initData(1)
    },
    resetForm() {
      this.searchForm = {}
      this.initData(1)
    },
    initData(opageNum, opageSize) {
      this.loading = true
      if (opageNum) this.pageObj.pageNum = opageNum
      if (opageSize) this.pageObj.pageSize = opageSize

      const params = {
        ...this.pageObj,
        params: this.searchForm
      }
      //获取列表
      this.$api[this.getListApi](params, this).then((res) => {
        this.loading = false
        if (res.succ) {
          let tempList = res.data.list;
          this.tableData = tempList.map((item, index) => {
            item.childernData = JSON.parse(JSON.stringify(this.childernDataOrg));
            if (item.projectMaterial) {
              for (let subIndex = 0; subIndex < item.childernData.length; subIndex++) {
                  let element = item.childernData[subIndex];
                  let key = element["key"];
                  if (item.projectMaterial[key]) {
                      element.json = {
                          ...item.projectMaterial[key]
                      };
                  }
              }
            }

            return item;
          });
          console.log(">>>childernData>>>", this.tableData)
          this.total = res.data.total
        } else {
          this.$message.error(res.msg || '查询失败')
        }
      })
    },
    handleDel(row) {
      this.$confirm('删除后不能恢复，确定要删除吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          //删除
          this.$api[this.delApi](
            {
              id: row.id
            },
            this
          ).then((res) => {
            if (res.succ) {
              this.$message({
                showClose: true,
                message: '删除成功',
                type: 'success'
              })
              if (this.tableData.length == 1 && this.pageObj.pageNum > 1) {
                this.pageObj.pageNum = this.pageObj.pageNum - 1
              }
              this.initData()
            }
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },

    handleSetTarget(row) {
      //this.activeRow = row;
      this.$refs.drawerForm.initData(row)
    },
    getName(oVal, list) {
      for (let i = 0; i < list.length; i++) {
        if (oVal == list[i].value) {
          return list[i].label
        }
      }
    },

    handleSetTarget2(row) {
      //this.activeRow = row;
      this.$refs.drawerForm2.initData(row)
    },
    saveTarget2(formData) {
      this.$api
        .setEngAddress({
          projectAdd: formData.projectAdd,
          longitude: '10',
          latitude: '10',
          id: formData.id
        })
        .then((res) => {
          if (res.succ) {
            this.$message({
              showClose: true,
              message: '修改成功',
              type: 'success'
            })
            this.$refs.drawerForm2.handleClose()
            this.initData()
          } else {
            this.$message({
              showClose: true,
              message: res.msg,
              type: 'error'
            })
          }
        })
    },

    uploadRow(row) {
      this.$api
        .uploadLaboratory(
          {
            id: row.id
          },
          this
        )
        .then((res) => {
          if (res.succ) {
            this.$message({
              showClose: true,
              message: '上传成功',
              type: 'success'
            })
            this.initData()
          }
        })
    },

    saveTarget(formData) {
      if (formData.id) {
        //修改this.activeRow
        this.$api[this.updateApi](formData, this).then((res) => {
          if (res.succ) {
            this.$message({
              showClose: true,
              message: '修改成功',
              type: 'success'
            })
            this.$refs.drawerForm.handleClose()
            this.initData()
          }
        })
      } else {
        // this.$api[this.addApi](formData, this).then((res) => {
        //   if (res.succ) {
        //     this.$message({
        //       showClose: true,
        //       message: "添加成功",
        //       type: "success",
        //     });
        //     this.initData();
        //     this.$refs.drawerForm.handleClose();
        //   }
        // });
      }
    },

    bindWLOperated(row) {
      this.selectedTableData = row;
      this.bindWLDialogFormVisible = true;
    },
    bindWLDialogClose() {
      this.$confirm('确认关闭？')
      .then(_ => {
        this.bindWLDialogFormVisible = false;
      })
      .catch(_ => {});
    },

    handleSetWLTarget(row,index){
      this.bindWLValidationVisible = true;
      this.selectedMaterialsType = row.type;
      this.selectedMaterialsIndex = index;
      let materialsConfigName = "";
      if (row.key === 'wcl1') {
        materialsConfigName = "膨胀剂";
      }else if (row.key === 'wcl2') {
        materialsConfigName = "纤维";
      }else if (row.key === 'wjj1') {
        materialsConfigName = "减水剂";
      }
      this.$nextTick(()=>{
        this.$refs.materialListPanel.initData(1,10,row.type, materialsConfigName);
      },200)
    },
    resetConsumptionTarget(row,index) {
      this.selectedTableData.childernData[index].json = {
        "wlid":"",
        "wllx":"",
        "wlmc":"",
        "wlgg":"",
        "cllx":"",
        "clmc":"",
        "clgg":"",
        "cj":"",
        "cjjc":"",
        "gys":"",
        "gysjc":""
      }
    },
    handleBindWLValidationVisibleClose() {
      this.bindWLValidationVisible = false;
    },
    selectedMaterialListPanel(item) {
      this.selectedTableData.childernData[this.selectedMaterialsIndex].json = {
        wlid: item.id,
        clmc: item.materialsConfigName,
        clgg: item.materialsConfigSpec,
        wlmc: item.materialsName,
        wlgg: item.materialsSpec,
        cllx: item.materialsConfigType,
        cj: item.manufacturers,
        cjjc: item.manufacturersCalled,
        gys: item.supplierCompanyName,
        gysjc: item.supplierAbbreviation,
      };

      this.handleBindWLValidationVisibleClose();
    },
    
    saveSelected() {
      let projectMaterial = {};
      this.selectedTableData.childernData.map(item => {
        projectMaterial[item.key] = item.json;
      });
      let parmas = {
        id: this.selectedTableData.id,
        projectMaterial: projectMaterial
      }
      this.$api.configErpProject(parmas).then(res => {
        if (res.succ) {
          this.$message({
            showClose: true,
            message: "保存成功",
            type: "success",
          });
          this.bindWLDialogFormVisible = false;
          this.initData();
        } else {
          this.$message({
            showClose: true,
            message: res.msg,
            type: "error",
          });
        }
      });
    },

    initEngineering() {
      this.$confirm('确认初始化协会工程吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
          this.$api.syncLaboratoryContractInfo().then(res => {
              if (res.code == 1) {
                  this.$message({
                      showClose: true,
                      message: "操作成功",
                      type: "success",
                  });

                  this.initData();
              }
          })
      });
    }
  }
}
</script>

<style scoped lang="scss">
.multi-form-item-box {
  padding: 0 0 4px 0;
  // display: flex;
  // justify-content: space-between;
  .el-select,
  .el-input {
    margin-right: 20px;
  }
}
::v-deep .el-button {
  padding-left: 13px;
  padding-right: 13px;
}
.el-form-item {
  margin-bottom: 8px;
}
::v-deep .el-form--inline {
  .el-form-item {
    margin-right: 24px;
    margin-bottom: 0;
    &:last-child {
      margin: 0;
    }
  }
}
::v-deep .el-table {
  .expanded,
  .expanded:hover {
    background-color: #fffbd9;
  }
  .expanded + tr {
    background-color: #fffbd9;
    td {
      background-color: #fffbd9;
    }
  }

  .table-child-box {
    margin: 16px;
    padding: 16px;
    background: #ffffff;
  }
}

.content-box {
  padding: 16px;
}
.content {
  width: 100%;
  height: 100%;
  padding: 16px;
  background: #ffffff;
  border-radius: 16px;
}

.search-box {
  padding-bottom: 16px;
  line-height: 40px;
}
.cell-state {
  .rda-task-state {
    display: inline-block;
    width: 43px;
    height: 18px;
    line-height: 18px;
    font-size: 12px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #ffffff;
    text-align: center;
    background: #1f7aff;
    border-radius: 2px;
  }
  .dqr {
    background: #dc3290;
  }
  .ddd {
    background: #3369ff;
  }
  .dwc {
    background: #1fae66;
  }
  .yqx {
    background: #d6d6d6;
  }
  .yjj {
    background: #adaa00;
  }
  .ywc {
    background: #515157;
  }
}
.footer-btn {
  margin-top: 30px;
  margin-left: 50%;
}
</style>