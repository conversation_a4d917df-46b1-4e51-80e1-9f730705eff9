<template>
  <div class="content-box">
    <div class="content flex-box">
      <div>
        
        <div class="g-card">
          <p class="gc-main">工单编号：{{detailData.orderNo}}</p>
          <div class="fl">
            <p>合同编号：<span>{{detailData.contractNo}}</span></p>
            <p>客户单位：<span>{{detailData.customerName}}</span></p>
            <p v-if="orderType != '质量投诉'">派单时间：<span>{{detailData.createTime}}</span></p>
            <p v-if="orderType != '质量投诉'">服务完成时间：<span>{{detailData.finishServiceTime}}</span></p>
          </div>
          <div class="fl">
            <p>合同名称：<span>{{detailData.contractName}}</span></p>
            <!-- <p v-if="orderType != '质量投诉'">已派次数：<span>{{detailData.orderNo}}</span></p> -->
            <p>客服员：<span>{{detailData.userName}}</span></p>
            <p v-if="orderType != '质量投诉'">打卡地点：<span>{{detailData.clockAddress}}</span></p>
          </div>
            
          <div class="fl">
            <p>工程名称：<span>{{detailData.projectName}}</span></p>
            <p v-if="orderType != '质量投诉'">派单人：<span>{{detailData.createrName}}</span></p>
            <p>开始服务时间：<span>{{detailData.startServiceTime}}</span></p>
            
          </div>
          <div class="cb"></div>
        </div>
        <div class="h48"></div>
        
        
        <div class="g-card">
          <p class="gc-main">{{orderType}}详情</p>
          <div class="fl">
            <p v-for="(item, i) in detailList" :key="i">{{item.label}}：<span>{{detailInfo[item.prop]}}</span></p>
          </div>
          <div class="cb"></div>
          <p class="mt24" v-if="orderType == '强度回弹'">原始回弹记录</p>
          <p class="mt24" v-else-if="detailInfo.videoArr || detailInfo.imgArr">现场照片、视频</p>
        </div>
        
        <div class="region-con" v-if="detailInfo.htInfoList && orderType == '强度回弹'">
          <div class="video-box" v-for="(item,index) in detailInfo.htInfoList" :key="(((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1)">
            <div style="margin-right: 20px; float: left;">
              <p>回弹位置：<span>{{item.htwz}}</span></p>
              <p>回弹强度：<span>{{item.htqd}} {{item.htqd ? 'Mpa' : ''}}</span></p>
            </div>
            <div class="video-item clearfloat">
              <img v-for="iImg in item.htimgArr" :key="iImg" :src="filePrefix + iImg" alt="" @click="openBigImg(filePrefix + iImg)" />
            </div>
            <div class="cb"></div>
          </div>
        </div> 
        <div class="region-con"  v-if="detailInfo.videoArr || detailInfo.imgArr">
          <div class="video-box">
            <div class="video-item video-item2" v-if="detailInfo.videoArr">
              <video class="art-video"
                v-for="(item,index) in detailInfo.videoArr"
                :key="index"
                controls
                preload="auto" crossorigin="anonymous" autoplay="" 
                :src="filePrefix + item">
              </video>
            </div>
            <div class="video-item video-item2" v-if="detailInfo.imgArr">
              <img
               v-for="(item,index) in detailInfo.imgArr"
               :key="index"
               :src="filePrefix + item" alt="" @click="openBigImg(filePrefix + item)"/>
            </div>
            <div class="cb"></div>
          </div>
        </div>
    
      </div>
      <div class="flex-item" style="min-width: 350px;">
        <!-- <el-steps direction="vertical" :space="50">
          <el-step v-for="item in orderDynamic" :key="item.id" icon="el-icon-time" 
          :description="item.remark">
          </el-step>
        </el-steps> -->
        
        <div v-for="item in orderDynamic" :key="item.id" class="flex-box stepsOrder">
          <div class="stepsOrder-item flex-box">
            <img src="@/assets/images/define-user-icon.png" alt="" />
            <div class="flex-item" style="padding-left: 8px;">
              <div class="flex-box" style="padding-bottom: 2px;">
                <span style="color: #1F2329; line-height: 20px; min-width:60px;">{{item.userName}}</span>
                <span style="line-height: 20px;">{{item.dataAction + ' ' +  item.dataType}}</span>
                <span style="line-height: 20px; text-align: right;" class="flex-item">{{item.dataTime}}</span>
              </div>
              <p style="display: block; font-size: 12px;">{{item.memo}}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 大图查看 -->
    <el-dialog :title="''" :visible.sync="bigImgDialogVisible" width="800px" style="color: #1C1E21;"  @closed="closeBigImgDialog" center>
      <el-image :src="bindBigImg()" id="scoreBigImg" style="width: 100%; height: 600px;" fit="contain"></el-image>
      <el-row style="display: flex; flex-direction: row; margin-top: 13px; width: 100%; justify-content: space-between;">
        <img style="width: 30px; height: 30px; cursor: pointer;" src="../../assets/images/rotate_left.png" @click="bigImgRotateClick('left')" />
        <img style="width: 30px; height: 30px; cursor: pointer;" src="../../assets/images/rotate_right.png" @click="bigImgRotateClick('right')" />
      </el-row>
    </el-dialog>
  </div>
</template>

<script>
  // "ZLT5"dictValueName:orderNo: 8,isDel:
  // ctCode:"ORDER TYPE"，dictWalueCode:0DHT"dictValueName:"强度回弹”，orderNo: 8, isDel:
  // ctCode:ORDER TYPE"，dictValueCode:"XMHF"dictValueName:"项目回访”，orderNo: 8,isDel:
  // ORDER TYPE"2156"质量事故”，orderNo: 8，isDel:ctCode:dictvalueCode:dictValueName:检测跟进”，orderNo: 8，isDel:ctCode: "ORDER TYPE", dictValueCode:"]CG]"dictValueName:现场反馈”，orderNo: 8，isDel:ctCode: "ORDER TYPE"， dictvalueCode: "XCFK"
import { ZLTS,XMHF,QDHT,ZLSG,JCGJ,XCFK } from "./detialconfig.js"
import Pagination from "@/components/Pagination/index.vue";
export default {
  components: {
    Pagination
  },
  data() {
    return {
      filePrefix: this.$store.state.loginStore.userInfo.filePrefix,
      orderType: '',
      detailData:{},
      detailInfo:{},
      orderDynamic: [],
      
      detailList: [],

      // 放大图片
      bigImgDialogVisible: false,
      bigImgUrl: '',
      bigImgRotate: 0
    };
  },
  
  created: function() {
    this.initData();
  },
  methods: {
    initData(){
      this.$api.getWorkOrderById(`id=${this.$route.query.id}`).then(res =>{
        if(res.succ){
          let detailInfo = res.data.detailInfo;
          if(detailInfo.htInfoList && detailInfo.htInfoList.length > 0){
            detailInfo.htInfoList = detailInfo.htInfoList.map(item =>{
              if(item.htimg){
                item.htimgArr = item.htimg.split(',')
              }
              return item;
            })
          }
          if(res.data.detailFileInfo.img){
            detailInfo.imgArr = res.data.detailFileInfo.img.split(',');
          }
          if(res.data.detailFileInfo.video){
            detailInfo.videoArr = res.data.detailFileInfo.video.split(',');
          }
          console.log(detailInfo)
          this.detailInfo = detailInfo;
          
          
          this.detailData = res.data;
          this.orderType = res.data.orderTypeName;
          if(res.data.orderTypeCode === 'ZLTS'){
            this.detailList = ZLTS;
          }else if(res.data.orderTypeCode === 'XMHF'){
            this.detailList = XMHF;
          }else if(res.data.orderTypeCode === 'QDHT'){
            this.detailList = QDHT;
          }else if(res.data.orderTypeCode === 'ZLSG'){
            this.detailList = ZLSG;
          }else if(res.data.orderTypeCode === 'JCGJ'){
            this.detailList = JCGJ;
          }else if(res.data.orderTypeCode === 'XCFK'){
            this.detailList = XCFK;
          }
          
          console.log(this.detailList)
        }
      })
      this.$api.getOrderDynamic(`orderId=${this.$route.query.id}`).then(res =>{
        if(res.succ){
          this.orderDynamic = res.data.list
        }
      })
      
      
    },
    
    // 查看大图相关
    openBigImg(bigUrl) {
      this.bigImgUrl = bigUrl;
      this.bigImgDialogVisible = true;
    },
    bindBigImg() {
        return this.bigImgUrl;
    },
    closeBigImgDialog() {
        this.bigImgUrl = "";
    },
    bigImgRotateClick(type) {
      if (type == 'left') {
        this.bigImgRotate = this.bigImgRotate - 1
      }else{
        this.bigImgRotate = this.bigImgRotate + 1
      }
      var box = document.getElementById('scoreBigImg');
      box.style.transform = 'rotateZ(' + 90 * this.bigImgRotate + 'deg)';
    },
  },
};
</script>

<style scoped lang="scss">
  
  .content-box{
    padding: 16px;
    width: 100vw;
    overflow-x: auto;
    box-sizing: border-box;
  }
  .content{
    width: 100%;
    height: 100%;
    padding: 16px;
    background: #FFFFFF;
    border-radius: 16px;
    overflow-y: auto;
    .region-con{
      overflow: hidden;
      .el-row,.el-col{
        height: 100%;
      }
      
      
    }
  }
  .h48{
    height: 48px;
    width: 100%;
  }
  .cc-info{
    margin: 0 0px 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #E8E8E8;
    &:last-child{
      margin-bottom: 0;
      border: none;
      padding: 0;
    }
  }
  .pb16{
    padding-bottom: 16px !important;
  }
  
  .g-card{
    width: 100%;
    padding: 8px 0 0px;
    margin-bottom: 0;
    & > div{
      margin-right: 80px;
    }
    p{
      color: $color-txt;
      line-height: 20px;
      letter-spacing: 1px;
      padding-bottom: 8px;
      &:last-child{
        padding: 0;
      }
      span{
        color: #1F2329;
      }
    }
    .gc-main{
      font-size: 16px;
      font-weight: 600;
      color: #1F2329;
      line-height: 22px;
      letter-spacing: 1px;
      margin-bottom: 16px;
      span{
        line-height: 20px;
        height: 20px;
        background: #FFE9D1;
        border-radius: 10px;
        font-size: 12px;
        font-weight: 600;
        display: inline-block;
        vertical-align: middle;
        margin-top: -2px;
        color: #FF7B2F;
        background: #FFE9D1;
        padding: 0 7px;
        &.succ{
          color: $color-success;
          background: #DDEFEA;
        }
        &.red{
          color: #FF2F2F;
          background: #FFE9E9;
        }
      }
    }
  }
  
  
  
  
  .video-box{
    overflow-x: auto;
    background: #fafafa;
    padding: 20px 20px 12px;
    border-radius: 10px;
    margin: 20px 0;
    display: flex;
    flex-direction: column;
    // border-bottom: 1px solid #E8E8E8;
    &.video-box2{
      background: none;
    }
    &:last-child{
      border: none;
    }
    p{
      color: $color-txt;
      line-height: 20px;
      letter-spacing: 1px;
      padding-bottom: 8px;
      &:last-child{
        padding: 0;
      }
      span{
        color: #1F2329;
      }
    }
    .video-item{
      margin-right: 16px;
      margin-top: 10px;
      .art-video{
        width: 100%;
      }
      img{
        // max-width: 160px;
        // max-height: 160px;
        cursor: pointer;
        width: 100px;
        height: 100px;
        float: left;
        margin-right: 16px;
        margin-bottom: 8px;
        display: block;
        // margin-top: 8px;
        border-radius: 8px;
      }
      p{
        height: 20px;
        font-size: 14px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #1F2329;
        line-height: 20px;
        letter-spacing: 1px;
        text-align: center;
        margin-top: 8px;
        margin-bottom: 24px;
      }
    }
    .video-item2{
      width: auto;
      clear: both;
      img{
        float: left;
        margin-right: 15px;
      }
    }
  }
  .mt24{
    margin-top: 24px;
  }
  ::v-deep .el-steps{
    .el-step__head.is-wait,.el-step__description.is-wait{
      color: #1F2329;
    }
  }
  
  
  .stepsOrder{
    align-items: center;
    width: 350px;
    margin-left: 10px;
    &:first-child{
      .stepsOrder-item{
        &::after{
          height: 35px;
          top: 35px;
        }
      }
    }
    &:last-child{
      .stepsOrder-item{
        &::after{
          height: 35px;
          top: 0px;
        }
      }
    }
    
    .stepsOrder-item{
      position: relative;
      &::before{
        width: 8px;
        height: 8px;
        position: absolute;
        left: -20px;
        top: 31px;
        border-radius: 4px;
        background: #1F57B3;
        content: " ";
        z-index: 10;
      }
      &::after{
        width: 2px;
        height: 70px;
        position: absolute;
        left: -17px;
        top: 0px;
        background: #C0C4CC;
        content: " ";
      }
      
      
      width: 100%;
      align-items: center;
      height: 70px;
      color: #6A727D;
      img{
        width: 30px;
        height: 30px;
      }
      // span:nth-child(2){
      //   padding: 0 10px;
      //   display: inline-block;
      //   font-size: 14px;
      //   min-width: 80px;
      //   color: #1F2329;
      // }
      // span:nth-child(4){
      //   text-align: right;
      //   color: #818c9b;
      //   font-size: 12px;
      // }
    }
    
  }


.stepsOrder:first-child:last-child{
  .stepsOrder-item{
    &::after{
      height: 0px !important;
      top: 0px;
    }
  }
}
</style>