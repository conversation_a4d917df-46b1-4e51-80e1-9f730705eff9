import { h } from 'vue'
export const tieBiaoEngineeringColumn = [
  {
    label: '合同编号',
    prop: 'contractNo',
  },
  {
    label:'合同类型',
    prop:'contractType',
     width: "90px",
    formatter: (row) => {
        if(row.contractType == 0){
          return '报建合同'
        }else if(row.contractType == 1){
          return '非报建合同'
        }else {
          return ''
        }
      },
  },
  {
    label: '铁标工程',
    prop: 'isIronMark',
    width: "100px",
    formatter: (row) => {
        if(row.isIronMark == 1){
          return '是'
        }else if(row.isIronMark == 0){
          return '否'
        }else {
          return '否'
        }
      },
  },
  {
    label: '合同工程名称',
    prop: 'projectName',
    width: "200px"
  },
  {
    label: '协会工程名称',
    prop: 'laboratoryProjectName',
    width: "200px"
  },
  {
    label: '建设单位',
    prop: 'jsdw',
    width: "200px"
  },
  {
    label: '施工单位',
    prop: 'sgdw',
    width: "200px"
  },
  {
    label: '监理单位',
    prop: 'jldw',
    width: "200px"
  },
  {
    label: '收货单位',
    prop: 'shdw',
    width: "200px"
  },
  {
    label: '工程地址',
    prop: 'projectAddress',
    width: "200px"
  },
  {
    label: '工程监督号',
    prop: 'jdh'
  },
  {
    label: '状态',
    prop: 'isUpload',
    width: "80",
  }
]

export const engineeringColumn = [
  {
    label: '合同编号',
    prop: 'contractNo',
  },
  {
    label:'合同类型',
    prop:'contractType',
     width: "90px",
    formatter: (row) => {
        if(row.contractType == 0){
          return '报建合同'
        }else if(row.contractType == 1){
          return '非报建合同'
        }else {
          return ''
        }
      },
  },{
    label: '工程区域',
    prop: 'projectArea',
    width: "200px"
  },
  {
    label: '合同工程名称',
    prop: 'projectName',
    width: "200px"
  },
  {
    label: '协会工程名称',
    prop: 'laboratoryProjectName',
    width: "200px"
  },
  {
    label: '建设单位',
    prop: 'jsdw',
    width: "200px"
  },
  {
    label: '施工单位',
    prop: 'sgdw',
    width: "200px"
  },
  {
    label: '监理单位',
    prop: 'jldw',
    width: "200px"
  },
  {
    label: '收货单位',
    prop: 'shdw',
    width: "200px"
  },
  {
    label: '工程地址',
    prop: 'projectAddress',
    width: "200px"
  },
  {
    label: '工程监督号',
    prop: 'jdh'
  },
  {
    label: '状态',
    prop: 'isUpload',
    width: "80",
  }
]

export const woColumn = [
  {
    label: '工单编号',
    prop: 'orderNo',
    width: "200px"
  },
  {
    label: '工程名称',
    prop: 'projectName',
    width: "250px"
  },
  {
    label: '合同编号',
    prop: 'contractNo',
    width: "200px"
  },
  // {
  //   label: '合同名称',
  //   prop: 'contractName',
  //   width: "250px",
  //   formatter: (row) => {
  //     if(!row.contractName || row.contractName == 'null'){
  //       return '--'
  //     }else{
  //       return row.contractName
  //     }
  //   },
  // },
  {
    label: '工单类型',
    prop: 'orderTypeName'
  },
  {
    label: '工单状态',
    prop: 'orderStatusText'//工单状态  0-待处理  1-接受  2-完成  3-拒绝
  },
  {
    label: '派单人',
    prop: 'createrName'
  },
  
  
  {
    label: '派单时间',
    prop: 'createTime'
  },
  {
    label: '客服员',
    prop: 'userName',
    width: "100px"
  },
  {
    label: '工程联系人',
    prop: 'linkMan',
    width: "100px"
  },
  {
    label: '开始服务时间',
    prop: 'startServiceTime',
    formatter: (row) => {
      return row.startServiceTime || '--'
    },
  },
  {
    label: '服务完成时间',
    prop: 'finishServiceTime',
    formatter: (row) => {
      return row.finishServiceTime || '--'
    },
  },
]


export const engDetailColumn = [
  {
    label: '工单编号',
    prop: 'orderNo',
  },
  {
    label: '工程名称',
    prop: 'projectName',
  },
  {
    label: '合同编号',
    prop: 'contractNo',
  },
  {
    label: '合同名称',
    prop: 'contractName'
  },
  {
    label: '工单类型',
    prop: 'orderTypeName'
  },
  {
    label: '工单状态',
    prop: 'orderStatusText'//工单状态  0-待处理  1-接受  2-完成  3-拒绝
  },
  {
    label: '派单人',
    prop: 'createrName'
  },
  
  
  {
    label: '派单时间',
    prop: 'createTime'
  },
  {
    label: '客服员',
    prop: 'userName'
  },
  {
    label: '开始服务时间',
    prop: 'startServiceTime'
  },
  {
    label: '服务完成时间',
    prop: 'finishServiceTime'
  },
]


export const engDetailColumn2 = [
  {
    label: '委托编号',
    prop: 'experimentNo',
  },
  {
    label: '报告编号',
    prop: 'reportNo',
  },
  
  {
    label: '委托时间',
    prop: 'entrustTime'
  },
  {
    label: '完成时间',
    prop: 'finishTime'
  },
  {
    label: '试验人',
    prop: 'experimentPersonName'
  },
  {
    label: '试验时间',
    prop: 'experimentTime'
  },
  {
    label: '校核人',
    prop: 'checkPersonName'
  },
  {
    label: '校核时间',
    prop: 'checkTime'
  },
  {
    label: '批准人',
    prop: 'approvePersonName'
  },
  {
    label: '批准时间',
    prop: 'approveTime'
  },
  {
    label: '是否合格',
    prop: 'isQualified',
    formatter: (row) => {
      if (row.isQualified == 1) {
          return "合格";
      }else if (row.isQualified == 2) {
          return "不合格";
      }else{
          return "--";
      }
    },
  },
  {
    label: '试验状态',
    prop: 'experimentStatus',//0-待接收  1-待取样  2-试验中 3-已完成 4-已拒绝
    formatter: (row) => {
      if(row.experimentStatus == 1){
        return '待取样'
      }else if(row.experimentStatus == 2){
        return '试验中'
      }else if(row.experimentStatus == 3){
        return '已完成'
      }else if(row.experimentStatus == 4){
        return '已拒绝'
      }else if(row.experimentStatus == 0){
        return '待接收'
      }
    },
  },
]