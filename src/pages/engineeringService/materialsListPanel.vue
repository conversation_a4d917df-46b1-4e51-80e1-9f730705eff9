<!--
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-07-16 22:48:09
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2024-12-15 22:32:14
 * @FilePath: /quality_center_web/src/pages/supplierMgt/supplierList.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div class="content-box">
      <div class="flex-box flex-column content">
        <div class="search-box flex-box">
          <el-form class="flex-item" ref="searchForm" :inline="true" :model="searchForm" :disabled="loading">
            <!-- <el-form-item label="材料类型：">
                <el-select v-model="searchForm.materialsType" placeholder="请选择" @change="initData()" clearable>
                    <el-option v-for="item in materialsTypeOpts" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
            </el-form-item> -->

            <el-form-item label="供应商：">
              <el-input v-model="searchForm.supplierCompanyName" clearable
                placeholder="请输入供应商" 
                style="width: 180px" 
              />
            </el-form-item>
            
            
            <el-form-item label="材料名称：">
              <el-input v-model="searchForm.materialsConfigName" clearable
                placeholder="请输入材料名称" 
                style="width: 180px" 
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" :disabled="loading" @click="handleFilter">搜索</el-button>
              <!-- <el-button type="text" icon="el-icon-refresh-right" :disabled="loading" @click="resetForm()">重置</el-button> -->
            </el-form-item>
          </el-form>
        </div>
        
        <div class="flex-item overHide">
          <div class="scroll-div">
            <el-table
              :data="tableData"
              v-loading="loading"
              :row-style="cellStyle"
              style="width: 100%">
              <af-table-column
                v-for="item in tableColumn" 
                :key="item.prop" :prop="item.prop" 
                :label="item.label" 
                :formatter="item.prop === 'materialsType' ? (row) => item.formatter(row,materialsTypeOpts) : item.formatter"
                :fixed="item.fixed" 
                :width="item.width || ''"
                align="center" 
              />
              <af-table-column width="180" label="操作" align="center" key="handle" :resizable="false">
                <template slot-scope="scope">
                    <el-button type="text" size="small" @click="selectedPanel(scope.row)">选择</el-button>
                </template>
              </af-table-column>
            </el-table>
          </div>
        </div>
        <div class="mt16 mb4">
          <Pagination
            :total="total" 
            :pageNum="pageObj.pageNum" 
            :pageSize="pageObj.pageSize" 
            @getData="initData" 
          />
        </div>
      </div>
    </div>
  </template>
  
<script>
import Pagination from "@/components/Pagination/index.vue";
import moment from 'moment';
export default {
    components: {
        Pagination,
    },
    data() {
        return {
            loading: false,
            searchForm: {
                materialsConfigName: "",
                materialsType: "",
                supplierCompanyName: "",
            },
            tableColumn: [
                {
                    label: '物料名称',
                    prop: 'materialsName',
                    formatter: (row) => {
                        return `${row.materialsName} / ${row.materialsSpec}`
                    },
                },
                {
                    label: '材料类型',
                    prop: 'materialsType',
                    formatter: (row,materialsTypeOpts) => {
                        for(let i =0;i<materialsTypeOpts.length; i++){
                            if(row.materialsType == materialsTypeOpts[i].value){
                                return materialsTypeOpts[i].label;
                            }
                        }
                    },
                },
                {
                    label: '材料名称',
                    prop: 'materialsConfigName',
                },
                {
                    label: '材料规格',
                    prop: 'materialsConfigSpec',
                    formatter: (row) => {
                        return row.materialsConfigSpec ? row.materialsConfigSpec : row.materialsConfigName;
                    },
                },
                {
                    label: '厂家',
                    prop: 'manufacturers',
                },
                {
                    label: '厂家简称',
                    prop: 'manufacturersCalled',
                },
                {
                    label: '单价',
                    prop: 'unitPrice',
                },
                {
                    label: '供应商名称',
                    prop: 'supplierCompanyName',
                },
                {
                    label: '型式检验编号',
                    prop: 'inspectionNo',
                },
                {
                    label: '型式检验结果',
                    prop: 'inspectionResult',
                },
                {
                    label: '型式检验时间',
                    prop: 'inspectionDateStart',
                    formatter: (row) => {    
                    if (row.inspectionDateStart && row.inspectionDateEnd) {
                        return `${moment(row.inspectionDateStart).format("YYYY-MM-DD")} 至 ${moment(row.inspectionDateEnd).format("YYYY-MM-DD")}`
                    }
                        return "--";
                    },
                },
                {
                    label: '是否甲供',
                    prop: 'selfSupply',
                    formatter: (row) => {
                    if (row.selfSupply == 1) {
                        return '是'
                    }
                        return '否'
                    }
                },
            ],
            pageObj: {
                pageNum: 1, // 页数
                pageSize: 10, // 条数
            },
            total: 1,
            tableData: [],
            materialsTypeOpts: [],
        };
    },

    created() {
        this.$api.getDictValue({
            dictCode: 'MASTERIAL_TYPE'
        }, this).then(res => {
            this.materialsTypeOpts = res.data.list.map(item => {
                return {
                    label: item.dictValueName,
                    value: item.dictValueCode + '',
                }
            });
        })
        // this.initData();
    },
    methods: {
        cellStyle({row, column, rowIndex, columnIndex}) {
            if (row.warningStatus == '1') {
                return {color: "#FF7F50"};
            } else if (row.warningStatus == '2') {
                return {color: "#FF4500"};
            } else {
                return {color: "#333"};
            }
        },
        handleFilter() {
            this.initData(1);
        },
        resetForm(){
            this.searchForm = {};
            this.initData(1);
        },
        initData(opageNum, opageSize, materialsType, materialsConfigName){
            this.loading = true;
            if (opageNum) this.pageObj.pageNum = opageNum;
            if (opageSize) this.pageObj.pageSize = opageSize;
            if (materialsType) this.searchForm.materialsType = materialsType;
            if (materialsConfigName) this.searchForm.materialsConfigName = materialsConfigName;
                
            const params ={
                ...this.pageObj,
                params: {
                    ...this.searchForm,
                    materialsType: this.searchForm.materialsType,
                    materialsConfigName: this.searchForm.materialsConfigName,
                }
            }
            this.$api.querySupplierCompanyMaterialsListPage(params, this)
            .then(res => {
                this.loading = false;
                if (res.code == 1) {
                    this.tableData = res.data.list;
                    this.total = res.data.total;
                }else{
                    this.$message.error(res.msg || '查询失败')
                }
            });
        },

        selectedPanel(item) {
            this.$emit('selected', item);
        }
    },
};
</script>

<style scoped lang="scss">
.multi-form-item-box{
    padding: 0 0 4px 0;
    // display: flex;
    // justify-content: space-between;
    .el-select,.el-input{
    margin-right: 20px;
    }
}
::v-deep .el-button{
    padding-left: 13px;
    padding-right: 13px;
}
.el-form-item{
    margin-bottom: 8px;
}
::v-deep .el-form--inline{
    .el-form-item{
    margin-right: 24px;
    margin-bottom: 0;
    &:last-child{
        margin: 0;
    }
    }
}
::v-deep .el-table{
    .expanded,.expanded:hover{
    background-color: #FFFBD9;
    
    }
    .expanded + tr{
    background-color: #FFFBD9;
    td{
        background-color: #FFFBD9;
    }
    }
    
    .table-child-box{
    margin: 16px;
    padding: 16px;
    background: #FFFFFF;
    }
}

.content-box{
    padding: 16px;
}
.content{
    width: 100%;
    height: 100%;
    padding: 16px;
    background: #FFFFFF;
    border-radius: 16px;
}

.search-box{
    padding-bottom: 16px;
    line-height: 40px;
}
</style>