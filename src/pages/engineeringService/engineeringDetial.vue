<template>
  <div class="content-box">
    <div class="content">
      <div class="g-card">
        <!-- isDel   0-正常 1-作废 -->
        <p class="gc-main">工程编号：{{detailData.erpId}}<span class="nom">{{detailData.isDel == 1 ?'作废': '正常'}}</span></p>
        <div class="flex-box" style="margin: 0;">
          <div class="ed-info">
            <p>质量监督号：<span>{{detailData.jdh}}</span></p>
            <p>工程名称：<span>{{detailData.projectName}}</span></p>
            <p>工程地址：<span>{{detailData.projectAddress}}</span></p>
            <p>工程联系人：<span>{{detailData.linkMan}}: {{detailData.linkTel}}</span></p>
            <p>收货单位：<span>{{detailData.shdw}}</span></p>
            <div class="edi-box cbo">
              <div class="edi-item fl">
                <span>{{detailData.experimentCount}}</span>
                <span>累计抽检 (个)</span>
              </div>
              <div class="edi-item fl">
                <span>{{detailData.experimentExpCount}}</span>
                <span>抽检异常 (个)</span>
              </div>
              <div class="edi-item fl">
                <span>{{detailData.taskListNum}}</span>
                <span>累计订单 (个)</span>
              </div>
              <div class="edi-item fl">
                <span>{{detailData.totalVolume}}</span>
                <span>累计方量 (m³)</span>
              </div>
            </div>
          </div>
          <div class="flex-item map-box">
            <baidu-map style="width: 100%; height: 100%;" :center="center" :zoom="zoom" @ready="handler"></baidu-map>
          </div>
        </div>
        <div class="cb"></div>
      </div>
      <div class="h56"></div>
      
      <p class="title">工单列表</p>
      <el-table
        :data="tableData"
        :key="1"
        v-loading="loading1"
        style="width: 100%">
        <af-table-column
          v-for="item in tableColumn" 
          :key="Math.random()" :prop="item.prop" 
          :label="item.label" 
          :fixed="item.fixed" 
          :width="item.width || ''"
          align="center" 
        />
        <af-table-column width="100" label="操作" align="center" key="handle" :resizable="false">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="tableOperated(scope.row, 'gd')">查看详情</el-button>
          </template>
        </af-table-column>
      </el-table>
      <div class="mt16">
        <Pagination
          :total="total" 
          :pageNum="pageObj.pageNum" 
          :pageSize="pageObj.pageSize" 
          @getData="initData1" 
        />
      </div>
      <div class="h56"></div>
      
     <!-- <p class="title">生产订单</p>
      <el-table
        :data="tableData2"
        v-loading="loading"
        style="width: 100%">
        <af-table-column
          v-for="item in tableColumn2" 
          :key="Math.random()" :prop="item.prop" 
          :label="item.label" 
          :fixed="item.fixed" 
          :width="item.width || ''"
          align="center" 
        />
        <af-table-column width="100" label="操作" align="center" key="handle" :resizable="false">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="tableOperated(scope.row)">查看详情</el-button>
          </template>
        </af-table-column>
      </el-table>
      <div class="mt16">
        <Pagination
          :total="total2" 
          :pageNum="pageObj2.pageNum" 
          :pageSize="pageObj2.pageSize" 
          @getData="handleFilter" 
        />
      </div> -->
      
      <!-- <p class="title">试验抽检</p>
      <el-table
        :data="tableData3"
        v-loading="loading"
        :key="3"
        style="width: 100%">
        <af-table-column
          v-for="item in tableColumn2" 
          :key="Math.random()" :prop="item.prop" 
          :label="item.label" 
          :fixed="item.fixed" 
          :width="item.width || ''"
          align="center" 
        />
        <af-table-column width="100" label="操作" align="center" key="handle" :resizable="false">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="tableOperated(scope.row, 'tz')">查看详情</el-button>
          </template>
        </af-table-column>
      </el-table>
      <div class="mt16">
        <Pagination
          :total="total3" 
          :pageNum="pageObj3.pageNum" 
          :pageSize="pageObj3.pageSize" 
          @getData="initData3" 
        />
      </div> -->
    </div>
  </div>
</template>

<script>
import { engDetailColumn,
engDetailColumn2 } from "./config.js"
import Pagination from "@/components/Pagination/index.vue";
export default {
  components: {
    Pagination
  },
  data() {
    return {
      detailData: {},
      tableData: [],
      tableColumn: engDetailColumn,
      tableColumn2: engDetailColumn2,
      filePrefix: this.$store.state.loginStore.userInfo.filePrefix,
      loading1: false,
      loading2: false,
      loading3: false,
      pageObj: {
        pageNum: 1, // 页数
        pageSize: 5, // 条数
      },
      total: 1,
      pageObj2: {
        pageNum: 1, // 页数
        pageSize: 5, // 条数
      },
      total2: 1,
      pageObj3: {
        pageNum: 1, // 页数
        pageSize: 5, // 条数
      },
      total3: 1,
      // 定位位置
        center: {lng: 121.5150, lat: 31.3036},
      // 地图放大等级
        zoom: 15
    };
  },
  
  created: function() {
    this.initData();
    console.log( this.$store.state.loginStore.userInfo.job)
    this.initData1();
    this.initData3();
  },
  methods: {
    initData(){
      this.$api.getNewEngineeringById(`id=${this.$route.query.id}`).then(res =>{
        if(res.succ){
          this.detailData = res.data
        }
      })
    },
    
    initData1(opageNum, opageSize){
      this.loading1 = true;
      if (opageNum) this.pageObj.pageNum = opageNum;
      if (opageSize) this.pageObj.pageSize = opageSize;
        
      const params ={
        ...this.pageObj,
        params: {
          projectId: this.$route.query.id
        }
      }
      //获取列表
      this.$api.getWorkOrderList(params, this).then(res => {
        this.loading1 = false;
        if(res.succ){
          this.tableData = res.data.list;
          this.total = res.data.total;
          this.$forceUpdate();
        }else{
          this.$message.error(res.msg || '查询失败')
        }
      })
    },
    initData3(opageNum, opageSize){
      this.loading3 = true;
      if (opageNum) this.pageObj3.pageNum = opageNum;
      if (opageSize) this.pageObj3.pageSize = opageSize;
        
      const params ={
        ...this.pageObj3,
        params: {
          projectId: this.$route.query.id
        }
      }
      //获取列表
      this.$api.getExperimentList(params, this).then(res => {
        this.loading3 = false;
        if(res.succ){
          this.tableData3 = res.data.list;
          this.total3 = res.data.total;
          this.$forceUpdate();
        }else{
          this.$message.error(res.msg || '查询失败')
        }
      })
    },
    
    tableOperated(row, type){
      if(type === 'gd'){
        this.$router.push({
          path: '/engineeringService/workOrderDetial',
          query: {
            id: row.id
          }
        })
      }else if(type === 'tz'){
        this.$router.push({
          path: '/qualityControl/experimentBoard',
          query: {
            id: row ? row.id : undefined
          }
        })
      }
    },
    //地图// 实例对象
    handler ({BMap, map}) {
      console.log(BMap, map)
      // 经度
      //this.center.lng = 116.404
      // 纬度
      //this.center.lat = 39.915
      // this.detailData.projectAdd
      if (this.detailData.longitude && this.detailData.latitude) {
        this.center = {
          lng: this.detailData.longitude,
          lat: this.detailData.latitude,
        }
      }
    }
  },
};
</script>

<style scoped lang="scss">
  .title{
    font-size: 16px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    color: #1F2329;
    line-height: 22px;
    letter-spacing: 1px;
    padding-bottom: 8px;
    border-bottom: 1px solid #E8E8E8;
    margin-bottom: 16px;
  }
  .content-box{
    padding: 16px;
  }
  .content{
    width: 100%;
    height: 100%;
    padding: 16px;
    background: #FFFFFF;
    border-radius: 16px;
    overflow-y: auto;
    .region-con{
      overflow: hidden;
      .el-row,.el-col{
        height: 100%;
      }
      
      
    }
  }
  .h56{
    height: 56px;
    width: 100%;
  }
  .cc-info{
    margin: 0 0px 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #E8E8E8;
    &:last-child{
      margin-bottom: 0;
      border: none;
      padding: 0;
    }
  }
  .pb16{
    padding-bottom: 16px !important;
  }
  
  .g-card{
    width: 100%;
    padding: 8px 0 0px;
    margin-bottom: 0;
    & > div{
      margin-right: 80px;
    }
    p{
      color: $color-txt;
      line-height: 20px;
      letter-spacing: 1px;
      padding-bottom: 16px;
      &:last-child{
        padding: 0;
      }
      span{
        color: #1F2329;
      }
    }
    .gc-main{
      font-size: 16px;
      font-weight: 600;
      color: #1F2329;
      line-height: 22px;
      letter-spacing: 1px;
      margin-bottom: 24px;
      span{
        line-height: 20px;
        height: 20px;
        background: #FFE9D1;
        border-radius: 10px;
        font-size: 12px;
        font-weight: 600;
        display: inline-block;
        vertical-align: middle;
        margin-top: -2px;
        color: #FF7B2F;
        padding: 0 7px;
        margin-left: 16px;
        &.nom{
          color: #1D6DFF;
          background: #DDEFEA;
        }
        &.succ{
          color: $color-success;
          background: #E2ECFF;
        }
        &.red{
          color: #FF2F2F;
          background: #FFE9E9;
        }
      }
    }
  }
  
  .ed-info{
    width: 672px;
    padding-right: 72px;
    border-right: 1px solid #BDBDBD;
    .edi-box{
      margin-top: 40px;
    }
    .edi-item{
      width: 140px;
      height: 72px;
      background: #F2F6FE;
      border-radius: 4px;
      text-align: center;
      padding: 8px;
      margin-right: 8px;
      &:last-child{
        margin-right: 0;
      }
      span{
        font-size: 20px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 600;
        color: #1F2329;
        line-height: 28px;
        display: block;
        padding: 0 0 4px;
        &:nth-child(2){
          font-size: 14px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #6A727D;
          line-height: 20px;
        }
      }
    }
  }
  
  .mt24{
    margin-top: 24px;
  }
  .map-box{
    padding-left: 72px;
  }
</style>