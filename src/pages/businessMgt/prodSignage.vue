<template>
  <div class="flex-box flex-column">
    <div class="search-box">
      <el-form ref="searchForm" :inline="true" :model="searchForm" :disabled="loading">
        <el-form-item label="">
        	<el-input v-model="searchForm.likeKey" clearable
            placeholder="请输入订单号、工程名称、砼品种" 
            style="width: 270px" 
          />
        </el-form-item>
      
      	<el-form-item label="发生日期" prop="takeEffectDate">
            <!-- :picker-options="pickerOptions" -->
          <el-date-picker type="daterange" 
            v-model="searchForm.takeEffectDate" 
            start-placeholder="开始日期" end-placeholder="结束日期" 
            format="yyyy-MM-dd" value-format="yyyy-MM-dd"
            :clearable="true" :disabled="loading" style="width: 360px"
          >
          </el-date-picker>
      	</el-form-item>
        
        <el-form-item>
          <el-button type="primary" :disabled="loading" @click="handleFilter">搜索</el-button>
          <el-button type="text" icon="el-icon-refresh-right" :disabled="loading" @click="resetForm()">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="flex-item content">
      <div class="flex-box">
        
      
      <div class="con-lef">
        <div class="g-card">
          <p class="gc-main">SNWT000000001 <span class="fr">待检测</span></p>
          <p class="gc-main pb16">水泥：PO42.5</p>
          <p>供货商：显示供货商名称显示供货商名称</p>
          <p>委托时间：</p>
          <p>代表数量：</p>
        </div>
        <div class="g-card">
          <p class="gc-main">SNWT000000001 <span class="fr">待检测</span></p>
          <p class="gc-main pb16">水泥：PO42.5</p>
          <p>供货商：显示供货商名称显示供货商名称</p>
          <p>委托时间：</p>
          <p>代表数量：</p>
        </div>
      </div>
      <div class="flex-item con-center">
        <div class="cc-box">
          <h4>委托信息</h4>
          <div class="cc-info">
            <p class="pb16 cci-main">SNWT000000001</p>
            <p>委托原因：更换配合比</p>
            <p><span>样品等级：PO42.5</span><span>委托时间：2023-12-23 00:23</span></p>
            <p>委托试验：和易性检测、保水性检测、抗压强度检测</p>
          </div>
          <div class="cc-info">
            <p class="pb16 cci-main">SNWT000000001</p>
            <p>委托原因：更换配合比</p>
            <p><span>样品等级：PO42.5</span><span>委托时间：2023-12-23 00:23</span></p>
            <p>委托试验：和易性检测、保水性检测、抗压强度检测</p>
          </div>
          <div class="cc-info">
            <p class="pb16 cci-main">采购信息</p>
            <p class="cci-main">CGRWD0000001</p>
            <p><span>样品等级：PO42.5</span><span>委托时间：2023-12-23 00:23</span></p>
            <p>委托试验：和易性检测、保水性检测、抗压强度检测</p>
          </div>
          <div class="cc-info">
            <p class="cci-main">CGRWD0000001</p>
            <p><span>样品等级：PO42.5</span><span>委托时间：2023-12-23 00:23</span></p>
            <p>委托试验：和易性检测、保水性检测、抗压强度检测</p>
          </div>
        </div>
        <div class="cc-box">
          <h4>样品管理</h4>
          <div class="cc-info">
            <el-form ref="searchForm2" :inline="true" :model="searchForm2" :disabled="loading">
              <el-form-item label="抽样数量：">
              	<el-input v-model="searchForm2.likeKey" clearable
                  placeholder="请输入" 
                  style="width: 150px" 
                />
                <span> 吨</span>
              </el-form-item>
            
            	<el-form-item label="样品状态：">
            		<el-input v-model="searchForm2.likeKey" clearable
            	    placeholder="请输入" 
            	    style="width: 150px" 
            	  />
            	</el-form-item>
              <el-form-item label="取样车号：">
              	<el-input v-model="searchForm2.likeKey" clearable
                  placeholder="请输入" 
                  style="width: 150px" 
                />
                <el-button type="text" @click="handleFilter">选择车辆</el-button>
              </el-form-item>
              
              <el-form-item class="fr">
                <el-button type="primary" :disabled="loading" @click="handleFilter">保存抽样信息</el-button>
              </el-form-item>
            </el-form>
            
            <el-table :data="tableData" 
              v-loading="loading" 
              class="mt16"
              border :max-height="500"
              ref="tableDataDom" 
              style="width: 100%;"
            >
            	
            	<el-table-column 
                v-for="item in tableColumn" 
                :key="item.prop" :prop="item.prop" 
                :label="item.label" 
                :fixed="item.fixed" 
                :width="item.width || ''"
                align="center" 
                :resizable="false" 
              />
            	<el-table-column width="220" label="操作" align="center" key="handle" :resizable="false">
            		<template slot-scope="scope">
            			<el-button type="text" size="small" @click="tableOperated('3', scope.row)">删除</el-button>
            		</template>
            	</el-table-column>
            </el-table>
            <div class="mt16 mb4">
              <Pagination
                :total="total" 
                :page.sync="pageObj.page" 
                :limit.sync="pageObj.size" 
                style="text-align: center" 
                @pagination="handleFilter" 
              />
            </div>
          </div>
        </div>
        <div class="cc-box">
          <h4>试验信息</h4>
          <div class="cc-info">
            <el-form  label-width="84px" ref="searchForm4" :model="searchForm4" :disabled="loading">
              <el-row :gutter="40">
                <el-col :span="9">
                  <el-form-item label="检测方法：">
                    <el-input v-model="searchForm4.likeKey" clearable
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="9">
                  <el-form-item label="判定依据：">
                    <el-input v-model="searchForm4.likeKey" clearable
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="18">
                  <el-form-item label="仪器设备：">
                    <el-input v-model="searchForm4.likeKey" clearable
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="9">
                  <el-form-item label="环境温度：">
                    <el-input v-model="searchForm4.likeKey" clearable
                      placeholder="请输入" 
                    >
                      <template slot="append">℃</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="9">
                  <el-form-item label="环境湿度：">
                    <el-input v-model="searchForm4.likeKey" clearable
                      placeholder="请输入" 
                    >
                      <template slot="append">RH%</template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
            
            <el-tabs v-model="activeName" @tab-click="handleClick">
              <el-tab-pane label="快检" name="first">
                <div>
                  <el-form :inline="true" ref="searchForm5" :model="searchForm5" :disabled="loading">
                    <el-row>
                      <el-col :span="24" style="margin-bottom: 8px;">
                        <el-form-item label="颜色：">
                          <el-input v-model="searchForm5.likeKey" clearable 
                            style="width: 400px" 
                          />
                        </el-form-item>
                      </el-col>
                      <el-col :span="24">
                        <el-form-item label="图片：">
                          <div class="cbo">
                            <div class="img-box">
                              <img src="" alt="" />
                              <span>抽样时颜色抽样时颜色抽样时颜色抽样时颜色</span>
                            </div>
                            <div class="img-box">
                              <img src="" alt="" />
                              <span>抽样时颜色抽样时颜色抽样时颜色抽样时颜色</span>
                            </div>
                          </div>
                        </el-form-item>
                      </el-col>
                    </el-row>
                  </el-form>
                </div>
              </el-tab-pane>
              <el-tab-pane label="标准稠度" name="second">
                <div>
                  <el-form :inline="true" ref="searchForm6" :model="searchForm6" :disabled="loading">
                    <el-row>
                      <el-col :span="24" style="margin-bottom: 8px;">
                        <el-form-item label="下沉深度：">
                          <el-input v-model="searchForm6.likeKey" clearable 
                          >
                            <template slot="append">mm</template>
                          </el-input>
                        </el-form-item>
                        <el-form-item label="用水量：">
                          <el-input v-model="searchForm6.likeKey" clearable 
                          >
                            <template slot="append">g</template>
                          </el-input>
                        </el-form-item>
                        <el-form-item label="标准稠度用水量：">
                          <el-input v-model="searchForm6.likeKey" clearable 
                          >
                            <template slot="append">%</template>
                          </el-input>
                        </el-form-item>
                      </el-col>
                    </el-row>
                    <el-form-item label="图片：">
                      <div class="cbo">
                        <div class="img-box">
                          <img src="" alt="" />
                          <span>抽样时颜色抽样时颜色抽样时颜色抽样时颜色</span>
                        </div>
                        <div class="img-box">
                          <img src="" alt="" />
                          <span>抽样时颜色抽样时颜色抽样时颜色抽样时颜色</span>
                        </div>
                      </div>
                    </el-form-item>
                  </el-form>
                </div>
              </el-tab-pane>
              <el-tab-pane label="凝结时间" name="third">
                <div>
                  <el-form :inline="true" ref="searchForm7" :model="searchForm7" :disabled="loading">
                    <el-row>
                      <el-col :span="24" style="margin-bottom: 8px;">
                        <el-form-item label="加水时间：">
                          <el-input v-model="searchForm7.likeKey" clearable 
                            style="width: 228px"
                          >
                            <template slot="append">h:mm</template>
                          </el-input>
                        </el-form-item>
                      </el-col>
                      <el-col :span="7" style="margin-bottom: 8px;">
                        <el-form-item label="到达初凝时间：">
                          <el-input v-model="searchForm7.likeKey" clearable style="width: 200px"
                          >
                            <template slot="append">h:mm</template>
                          </el-input>
                        </el-form-item>
                      </el-col>
                      <el-col :span="7" style="margin-bottom: 8px;">
                        <el-form-item label="初凝时间：">
                          <el-input v-model="searchForm7.likeKey" clearable style="width: 200px"
                          >
                            <template slot="append">h:mm</template>
                          </el-input>
                        </el-form-item>
                      </el-col>
                      <el-col :span="10" style="margin-bottom: 8px;">
                        <el-form-item label="结论：">
                          <el-input v-model="searchForm7.likeKey" clearable style="width: 400px"
                          
                          >
                          </el-input>
                        </el-form-item>
                      </el-col>
                    </el-row>
                    <el-form-item label="图片：">
                      <div class="cbo">
                        <div class="img-box">
                          <img src="" alt="" />
                          <span>抽样时颜色抽样时颜色抽样时颜色抽样时颜色</span>
                        </div>
                        <div class="img-box">
                          <img src="" alt="" />
                          <span>抽样时颜色抽样时颜色抽样时颜色抽样时颜色</span>
                        </div>
                      </div>
                    </el-form-item>
                  </el-form>
                </div>
              </el-tab-pane>
              <el-tab-pane label="安定性" name="fourth">定时任务补偿</el-tab-pane>
              <el-tab-pane label="强度测定" name="5">定时任务补偿</el-tab-pane>
              <el-tab-pane label="细度" name="6">定时任务补偿</el-tab-pane>
            </el-tabs>
          </div>
          
        </div>
        <div class="cc-box">
          <h4>试验结论</h4>
          <div class="cc-info">
            <el-form label-width="100px" ref="searchForm3" :model="searchForm3" :disabled="loading">
              <el-form-item label="是否合格：">
              	<el-radio-group v-model="searchForm3.resource">
                  <el-radio label="非常差"></el-radio>
                  <el-radio label="一般"></el-radio>
                  <el-radio label="良好"></el-radio>
                </el-radio-group>
              </el-form-item>
            
            	<el-form-item label="结论：">
            		<el-input v-model="searchForm3.likeKey" clearable
            	    placeholder="请输入" 
            	  />
            	</el-form-item>
              <el-form-item label="备注：">
              	<el-input v-model="searchForm3.likeKey" clearable
                  placeholder="请输入" 
                />
              </el-form-item>
            </el-form>
          </div>
        </div>
        
        <div class="center-footer">
          <el-button type="primary" :disabled="loading" @click="handleFilter">保存</el-button>
          <el-button type="primary" :disabled="loading" @click="handleFilter">完成</el-button>
        </div>
      </div>
      <div class="con-right">
        <div class="g-card">
          <p class="gc-main">SNWT000000001 <span class="fr succ">已完成</span></p>
          <p class="gc-main pb16">水泥：PO42.5</p>
          <p>供货商：显示供货商名称显示供货商名称</p>
          <p>委托时间：</p>
          <p>代表数量：</p>
        </div>
      </div>
    
      </div>
    </div>
    
    
  </div>
</template>

<script>
import { rangePrecondTableProp } from "./config.js"
import Pagination from "@/components/Pagination/index.vue";
export default {
  components: {
    Pagination
  },
  data() {
    return {
      loading: false,
      // 设置时间选择
      pickerOptions: {
        disabledDate(time) {
          let deadline = Date.now() - 60 * 60 * 1000;
          return time.getTime() > deadline //
        },
      },
      searchForm: {
        takeEffectDate: [],
      },
      
      pageObj: {
        page: 1, // 页数
        size: 10, // 条数
      },
      total:0,
      searchForm2: {
        takeEffectDate: [],
      },
      tableData: [],
      tableColumn: rangePrecondTableProp,
      
      searchForm3:{},
      searchForm4:{},
      searchForm5:{},
      searchForm6:{},
      searchForm7:{},
      searchForm8:{},
      activeName: 'second'
    };
  },
  
  created: function() {

  },
  methods: {
    handleFilter(value) {
      if (value === 1) {
         this.pageObj.page = 1
       }
    },
    handleClick(tab, event) {
            console.log(tab, event);
          }
  },
};
</script>

<style scoped lang="scss">
  ::v-deep .el-button{
    padding-left: 13px;
    padding-right: 13px;
  }
  .el-form-item{
    margin-bottom: 8px;
  }
  ::v-deep .el-form--inline{
    .el-form-item{
      margin-right: 24px;
      margin-bottom: 0;
      &:last-child{
        margin: 0;
      }
    }
  }
  .el-tabs{
    margin-top: 10px;
  }
  .search-box{
    padding: 16px;
    width: 100%;
    background-color: #fff;
  }
  
  .content{
    width: 100%;
    height: 100%;
    overflow-y: auto;
    padding: 14px 0 16px;
    .con-lef,.con-right{
      width: 360px;
      background: #D2D8DB;
      border-radius: 16px;
      padding: 8px 8px 0;
      margin: 0 16px;
    }
    .con-center{
      background: #D2D8DB;
      border-radius: 16px;
      padding: 8px 8px 0;
    }
  }
  
  
  .g-card{
    width: 100%;
    background: #FFFFFF;
    border-radius: 8px;
    padding: 8px;
    margin-bottom: 8px;
    min-width: 340px;
    p{
      color: $color-txt;
      line-height: 20px;
      letter-spacing: 1px;
      padding-bottom: 4px;
      &:last-child{
        padding: 0;
      }
    }
    .gc-main{
      font-size: 14px;
      font-weight: 600;
      color: #1F2329;
      line-height: 20px;
      letter-spacing: 1px;
      span{
        line-height: 20px;
        height: 20px;
        background: #FFE9D1;
        border-radius: 10px;
        font-size: 12px;
        font-weight: 600;
        color: #FF7B2F;
        background: #FFE9D1;
        padding: 0 7px;
        &.succ{
          color: $color-success;
          background: #DDEFEA;
        }
      }
    }
  }
  
  
  
  .cc-box{
    width: 100%;
    background: #FFFFFF;
    border-radius: 8px;
    padding: 16px 16px 12px;
    margin-bottom: 8px;
    &:last-child{
      margin-bottom: 0;
    }
    h4{
      font-size: 16px;
      font-weight: 600;
      color: #1F2329;
      line-height: 22px;
      letter-spacing: 1px;
      padding-bottom: 24px;
    }
    .cc-info{
      margin: 0 16px 16px;
      padding-bottom: 12px;
      border-bottom: 1px solid #E8E8E8;
      &:last-child{
        margin-bottom: 0;
        border: none;
        padding: 0;
      }
      p{
        color: #6A727D;
        line-height: 20px;
        letter-spacing: 1px;
        padding-bottom: 4px;
        span{
          margin-right: 60px;
        }
      }
      .cci-main{
        font-weight: 600;
        line-height: 20px;
        letter-spacing: 1px;
        color: #1F2329;
      }
    }
  }
  
  .pb16{
    padding-bottom: 16px !important;
  }
  
  
  .center-footer{
    width: calc(100% + 16px);
    height: 72px;
    background: #FFFFFF;
    border-radius: 0px 0px 16px 16px;
    margin-left: -8px;
    display: flex;
    justify-content: flex-end;
    padding: 16px 24px;
    .el-button{
      width: 98px;
      margin-left: 32px;
    }
  }
  
  .img-box{
    width: 140px;
    margin-right: 8px;
    float: left;
    img{
      display: block;
      background: #E0E8EB;
      border-radius: 8px;
      width: 140px;
      height: 140px;
    }
    span{
      font-size: 14px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #1F2329;
      line-height: 20px;
      letter-spacing: 1px;
      display: block;
      padding: 8px 4px;
    }
  }
</style>