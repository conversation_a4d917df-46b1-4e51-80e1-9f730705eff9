import { h } from 'vue'
export const rangePrecondTableProp = [
  {
    label: '样品编号',
    prop: 'sndDate',
    // formatter: (row) => {
    //   return row.createTime.split(' ')[0];
    // },
  },
  {
    label: '样品用途',
    prop: 'ventureName',
  },
  {
    label: '抽样人',
    prop: 'shopName'
  },
  {
    label: '抽样时间',
    prop: 'tradeNo2'
  },
  {
    label: '处置人',
    prop: 'goodsNo'
  },
  {
    label: '处置时间',
    prop: 'goodsName'
  }
]

export const rdColumn = [
  {
    label: '安定性',
    prop: 'title',
  },
  {
    label: '标准稠度',
    prop: 'ventureName',
  },
  {
    label: '凝结时间',
    prop: 'shopName'
  },
  {
    label: '抗压强度-7d',
    prop: 'tradeNo2'
  },
  {
    label: '抗压强度-28天',
    prop: 'goodsNo'
  },
  {
    label: '抗折强度-7天',
    prop: 'goodsName'
  },
  {
    label: '抗折强度-28天',
    prop: 'goodsNo'
  },
  {
    label: '细度',
    prop: 'goodsName'
  }
]



