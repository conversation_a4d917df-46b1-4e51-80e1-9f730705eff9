<template>
  <div class="content-box">
    <div class="content flex-box flex-column ">
      <div class="g-card">
        <p class="gc-main">SNWT000000001 <span class="red">等待配比</span></p>
        <p class="gc-main pb16">水泥：PO42.5</p>
        <div class="fl">
          <p>浇筑部位：2号楼11F楼板、梁、柱</p>
          <p>计划方量：：</p>
        </div>
        <div class="fl">
          <p>浇筑部位：2号楼11F楼板、梁、柱</p>
          <p>计划方量：：</p>
        </div>
        <div class="fl">
          <p>浇筑部位：2号楼11F楼板、梁、柱</p>
          <p>计划方量：：</p>
        </div>
        <div class="cb"></div>
      </div>
      <div class="h40"></div>
      <div class="region-con">
        <p class="title">报告列表</p>
        <p class="rc-item">
          <span>水泥报告</span>
          <span>BG00001</span>
          <el-button type="text" size="small" @click="tableOperated()">打印</el-button>
        </p>
        <p class="rc-item">
          <span>水泥报告</span>
          <span>BG00001</span>
          <el-button type="text" size="small" @click="tableOperated()">打印</el-button>
        </p>
      </div>
      <div class="h40"></div>
      <p class="title">小票列表</p>
      <div class="region-con flex-item">
        <el-row :gutter="32">
          <el-col :span="8">
            <div class="receipt-list">
              <div class="receipt-item" v-for="(item,index) in 20">
                <p>XP000001</p>
                <p>生产<span>20.00</span>发货<span>20.00</span></p>
                <p><img src="@/assets/images/icon_car_blue.png" alt="" />
                  <span>608，沪00000</span>五大有</p>
              </div>
              <div class="receipt-item">
                <p>XP000001</p>
                <p>生产<span>20.00</span>发货<span>20.00</span></p>
                <p><img src="@/assets/images/icon_car_blue.png" alt="" />
                  <span>608，沪00000</span>五大有</p>
              </div>
              <div class="receipt-item">
                <p>XP000001</p>
                <p>生产<span>20.00</span>发货<span>20.00</span></p>
                <p><img src="@/assets/images/icon_car_blue.png" alt="" />
                  <span>608，沪00000</span>五大有</p>
              </div>
            </div>
          </el-col>
          <el-col :span="16">
            <div>
              <p class="title-public">生产视频：</p>
              <div class="video-box clearfloat">
                <div class="video-item">
                  <video class="art-video"
                    controls
                    preload="auto" crossorigin="anonymous" autoplay="" 
                    src="https://upload.wikimedia.org/wikipedia/commons/8/87/Schlossbergbahn.webm">
                  </video>
                  <p>dsaf</p>
                </div>
                <div class="cb"></div>
              </div>
              <p class="title-public">装车视频：</p>
              <div class="video-box clearfloat">
                <div class="video-item">
                  <video class="art-video"
                    controls
                    preload="auto" crossorigin="anonymous" autoplay="" 
                    src="https://upload.wikimedia.org/wikipedia/commons/8/87/Schlossbergbahn.webm">
                  </video>
                  <p>dsaf</p>
                </div>
                <div class="cb"></div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script>
import { panelColumn,
panelChildColumn } from "./config.js"
import Pagination from "@/components/Pagination/index.vue";
export default {
  components: {
    Pagination
  },
  data() {
    return {};
  },
  
  created: function() {

  },
  methods: {
    
  },
};
</script>

<style scoped lang="scss">
  
  .content-box{
    padding: 16px;
  }
  .content{
    width: 100%;
    height: 100%;
    padding: 16px;
    background: #FFFFFF;
    border-radius: 16px;
    .title{
      font-size: 16px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      color: #1F2329;
      line-height: 22px;
      letter-spacing: 1px;
      padding-bottom: 8px;
      border-bottom: 1px solid #E8E8E8;
      margin-bottom: 8px;
    }
    .region-con{
      overflow: hidden;
      .el-row,.el-col{
        height: 100%;
      }
      
      .rc-item{
        padding-top: 8px;
        height: 28px;
        span{
          color: $color-txt;
          line-height: 20px;
          letter-spacing: 1px;
          padding-right: 16px;
        }
        .el-button{
          height: 20px;
          padding: 0;
        }
      }
    }
  }
  .h40{
    height: 40px;
    width: 100%;
  }
  .cc-info{
    margin: 0 0px 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #E8E8E8;
    &:last-child{
      margin-bottom: 0;
      border: none;
      padding: 0;
    }
  }
  .pb16{
    padding-bottom: 16px !important;
  }
  
  .g-card{
    width: 100%;
    padding: 16px 0 0;
    margin-bottom: 0;
    & > div{
      margin-right: 62px;
    }
    p{
      color: $color-txt;
      line-height: 20px;
      letter-spacing: 1px;
      padding-bottom: 8px;
      &:last-child{
        padding: 0;
      }
    }
    .gc-main{
      font-size: 16px;
      font-weight: 600;
      color: #1F2329;
      line-height: 22px;
      letter-spacing: 1px;
      span{
        line-height: 20px;
        height: 20px;
        background: #FFE9D1;
        border-radius: 10px;
        font-size: 12px;
        font-weight: 600;
        display: inline-block;
        vertical-align: middle;
        margin-top: -2px;
        color: #FF7B2F;
        margin-left: 16px;
        padding: 0 7px;
        &.succ{
          color: $color-success;
          background: #DDEFEA;
        }
        &.red{
          color: #FF2F2F;
          background: #FFE9E9;
        }
      }
    }
  }
  
  .receipt-list{
    width: 100%;
    height: 100%;
    background: #EFF1F2;
    border-radius: 8px;
    padding: 16px;
    overflow-y: auto;
    display: flex;
    flex-wrap: wrap;
    gap: 16px 8px;
    .receipt-item{
      width: calc(50% - 4px);
      height: 106px;
      background: #FFFFFF;
      border-radius: 8px;
      padding: 8px;
      p{
        &:nth-child(1){
          height: 22px;
          font-size: 16px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 600;
          color: #1F2329;
          line-height: 22px;
          letter-spacing: 1px;
        }
        &:nth-child(2){
          padding: 16px 0 7px;
          color: #1F2329;
          line-height: 25px;
          height: 48px;
          letter-spacing: 1px;
          span{
            font-size: 16px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 600;
            padding: 0 16px 0 4px;
          }
        }
        &:nth-child(3){
          height: 20px;
          font-size: 14px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #6A727D;
          line-height: 20px;
          letter-spacing: 1px;
          span{
            padding: 0 16px 0 4px;
          }
          img{
            width: 18px;
            height: 18px;
            display: inline-block;
            vertical-align: middle;
            margin-top: -2px;
          }
        }
      }
    }
  }
  
  
  .video-box{
    width: 100%;
    overflow-x: auto;
    padding-top: 8px;
    border-bottom: 1px solid #E8E8E8;
    margin-bottom: 24px;
    height: 185px;
    &:last-child{
      border: none;
    }
    .video-item{
      width: 220px;
      height: 140px;
      margin-right: 8px;
      float: left;
      .art-video{
        width: 100%;
      }
      p{
        height: 20px;
        font-size: 14px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #1F2329;
        line-height: 20px;
        letter-spacing: 1px;
        text-align: center;
        margin-top: 8px;
        margin-bottom: 24px;
      }
    }
  }
</style>