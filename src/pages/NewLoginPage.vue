<template>
    <div class="login-container">
        <div class="flex-box flex-column align-item-center" style="font-size: 32px; font-family: PingFangSC, PingFang SC; font-weight: 600; color: #FFFFFF;">
            <img src="../assets/images/login-logo.png" style="width: 218px; height: 160px;" />
            砼学
        </div>
        <el-col class="form-view" :span="15" >
            <el-form id="selectForm" :model="loginFormData" ref="loginForm" class="ms-content" size="small">
                <el-form-item class="form-item" prop="username" label="">
                    <el-row class="flex-box flex-row align-item-center">
                        <img src="../assets/images/icon_login_username.png" class="icon-input" />
                        <el-input placeholder="请输入手机号/账号" class="input-view" v-model="loginFormData.username" maxlength="11"></el-input>
                    </el-row>
                </el-form-item>
                <el-form-item class="form-item" prop="code" label="">
                    <el-row class="flex-box flex-row align-item-center">
                        <img src="../assets/images/icon_login_psw.png" class="icon-input" />
                        <el-input placeholder="请输入密码" class="input-view" type="password" v-model="loginFormData.code" @keyup.enter.native="submitForm()"></el-input>
                    </el-row>
                    
                    <!-- <el-row>
                        <el-col :span="15">
                            <el-input v-model="loginFormData.code" @keyup.enter.native="submitForm()"></el-input>
                        </el-col>
                        <el-col :span="4" style="padding-left: 4px;">
                            <el-button class="sms-code-btn" :disabled="smsloginBtnCount > 0 ? true : false" @click="sendSMSAction()">{{smsloginBtnCount > 0 ? `${smsloginBtnCount}` : '获取验证码'}} </el-button>
                        </el-col>
                    </el-row> -->
                </el-form-item>
                <div class="login-btn">
                    <el-button type="danger" @click="submitForm()">登录</el-button>
                </div>
            </el-form>
        </el-col>
    </div>
</template>

<script>
import util from '../common/js/util'
import API from '../api/index'

// 图片组件
import UploadFile from '@/components/UploadFile.vue';

export default {
    components: {
        UploadFile
	},
    data: function() {
        return {
            isSign: false,       // 是否可注册
            
            loginFormData: {
                username: '',// '13661759691',
                code: '',//123456
                device: 'PC',
                companyType: 1
            },
            registerFormData: {
                code: '',
                phone: '',
                pictureUrl: {'path': '', 'filePath': ''},
                pumpTeamName: "",
                pumpTeamNick: "",
                userName: ""
            },
            registerFormRules: {
                userName: [{ required: true, message: '请填写负责人', trigger: 'blur' }],
                pumpTeamNick: [{ required: false }],
                pumpTeamName: [{ required: true, message: '请填写承运方名称!', trigger: 'blur' }],
                phone: [{ required: true, message: '请填写联系方式!', trigger: 'blur' }],
                code: [{ required: true, message: '请输入验证码', trigger: 'blur' }],
            },

            signType: 'login',   // login sign
            smsloginBtnCount: 0, // 登录发送验证码
            smsSignBtnCount: 0,  // 注册发送验证码
        };
    },
    methods: {
        submitForm() {
            if (this.signType == 'sign') {
                this.$refs.registerForm.validate(valid => {
                    if (valid) {
                        this.registerUser()
                    } else {
                        console.log('error submit!!');
                        return false;
                    }
                });
            }else {
                if(!this.loginFormData.username) {
                    this.$message({
                        message: '请填写手机号！',
                        type: 'warning'
                    });
                } else if(!this.loginFormData.code) {
                    this.$message({
                        message: '请填写密码！',
                        type: 'warning'
                    });
                } else {
                    this.loginReq();
                }
            }
        },
        getTieBiaoFlag(){
            let self = this;
            this.$api.getIsIronMarkStatus(self)
            .then(res => {
                if (res.code == '1') {
                    console.log('铁标开关接口返回数据：', res.data);
                    self.$store.commit("updateTieBiaoFlag", res.data?.status || false)
                    // self.$store.commit("updateTieBiaoFlag", true)
                } else {
                    self.$store.commit("updateTieBiaoFlag", false)
                    self.$message({
                        message: res.msg,
                        type: 'error'
                    });
                }
            }).catch(err => {
                self.$store.commit("updateTieBiaoFlag", false)
                
            })
        },

        // 登录后获取铁标开关
        loginReq() {
            let self = this;
            let loginParams = { 
                userPhone: self.loginFormData.username, 
                password: self.loginFormData.code,
                device: self.loginFormData.device,
                // companyType: self.loginFormData.companyType
            };
            this.$api.requestLoginCode(loginParams,self)
            .then(res => {
                if (res.code == '1') {
                  this.$api.getPrefixPath().then(urlres => {
                    console.log(urlres.data.filePrefix)
                    res.data.filePrefix = urlres.data.filePrefix;
                    // self.$message.success('登录成功');
                    // 增加token存储
                    localStorage.setItem('bbToken', res.data.tokenValue);
                    API.token = res.data.token;                    
                    self.$store.commit("updateUserInfo", res.data)
                    // this.$router.push('/firstpage');
                    this.$router.push('/qualityControl/experimentBoard');
                    this.getTieBiaoFlag();
                    //self.queryLoginUserInfo()    // TODO
                  })
                } else {
                    util.userData.clearLocalStorage();
                    self.$message({
                        message: res.msg,
                        type: 'error'
                    });
                }
            }).catch(err => {
                util.userData.clearLocalStorage();
                
            })
        },
        queryLoginUserInfo() {
            let self = this
            queryLoginUserInfo({},self)
            .then(res => {
                if (res.code == '0') {
                    let menuData = util.handleMenuItems(res.data.ztreeList)
                    self.$store.commit("updateUserInfo", res.data)
                    let path = ""
                    if (menuData.length > 0) {
                        let menu = menuData[0]
                        if (menu.subs && menu.subs.length > 0) {
                            path = menu.subs[0].index
                        }else{
                            path = menu.index
                        }
                    }
                    if (path) {
                        self.$router.push('/' + path);
                    }else{
                        self.$confirm('暂无权限，请联系管理员', '', {
                            confirmButtonText: '确定',
                            showCancelButton: false,
                            type: 'warning'
                        }).then(() => {
                        }).catch(() => {
                        });
                        util.userData.clearLocalStorage();
                    }
                }else{
                    self.$message({
                        message: res.msg,
                        type: 'error'
                    });
                    // 移除token存储
                    util.userData.clearLocalStorage();
                }
            }).catch(err => {
                self.$message({
					message: '查询用户信息失败，请重试',
					type: 'error'
				});
                // 移除token存储
                util.userData.clearLocalStorage();
            })
        },
        gotoSign() {
            this.signType = 'sign'
        },
        gotoLogin() {
            this.signType = 'login'
        },
        sendSMSAction() {
            let self = this
            if (this.signType == 'sign') {
                if (self.registerFormData.phone.length == 0) {
                    self.$message({
                        message: "请输入联系方式",
                        type: "warning"
                    });
                    return
                }
            }else {
                if (self.loginFormData.username.length == 0) {
                    self.$message({
                        message: "请输入手机号",
                        type: "warning"
                    });
                    return
                }
                if (self.loginFormData.companyType.length == 0) {
                    self.$message({
                        message: "请选择隶属",
                        type: "warning"
                    });
                    return
                }
            }
            let loginParams = { 
                phone: this.signType == 'sign' ? self.registerFormData.phone : self.loginFormData.username, 
                device: self.loginFormData.device,
                companyType: self.loginFormData.companyType 
            };
            this.$api.requestCode(loginParams, self)
            .then(res => {
                if (res.code == '1') {
                    if (this.signType == 'sign') {
                        self.smsSignBtnCount = 59
                        let timer = setInterval(() => {
                            if (self.smsSignBtnCount > 1) {
                                self.smsSignBtnCount--
                            }else{
                                self.smsSignBtnCount = 0
                                clearInterval(timer)
                            }
                        }, 1000);
                    } else {
                        self.smsloginBtnCount = 59
                        let timer = setInterval(() => {
                            if (self.smsloginBtnCount > 1) {
                                self.smsloginBtnCount--
                            }else{
                                self.smsloginBtnCount = 0
                                clearInterval(timer)
                            }
                        }, 1000);
                    }
                    
                    // self.loginFormData.code = res.data.securityCode;
                    self.$message({
                        message: "验证码获取成功",
                        type: 'success'
                    });
                }else{
                    self.$message({
                        message: res.msg,
                        type: 'error'
                    });
                }
            }).catch(err => {
                self.$message({
					message: '发送失败，请重试',
					type: 'error'
				});
            })
        },
        // 图片上传地址
        getFileUrl: function (path, filePath) {
            this.registerFormData.pictureUrl = {'path': path, 'filePath': filePath};
            // 改变组件的图片地址
            this.$refs.UploadLogoFile.changeFileUrl(path);
        },
        registerUser() {
            let self = this
            
            registerUser(self.registerFormData,self)
            .then(res => {
                if (res.code == '0') {
                    self.$notify.success({
                        title: '注册成功！请登陆',
                        message: '',
                        offset: 100,
                        showClose: false,
                        customClass: "loginPage"
                    });
                    // self.$message({
                    //     message: "注册成功，请重新登录",
                    //     type: "success"
                    // });
                    self.gotoLogin()
                }else{
                    self.$message({
                        message: res.msg,
                        type: 'error'
                    });
                }
            }).catch(err => {
                
            })
        }
    },
    // 创建VUE实例后的钩子
    created: function () {
        
    },
};
</script>

<style scoped>
#selectForm >>> .el-form-item__label {
    font-size: 14px;
    font-weight: 400;
    letter-spacing: 0px;
    color: rgba(28, 30, 33, 1);
    text-align: right;
    vertical-align: top;
}
#selectForm >>> .el-input--small .el-input__inner {
    height: 36px;
    line-height: 36px;
    color: white;
    background-color: #2D3052;
    border: none;
}

#selectForm >>> .el-button--small {
    padding-left: 0;
    padding-right: 0;
    text-align: center;
}

#selectForm >>> .el-form-item--mini.el-form-item, .el-form-item--small.el-form-item {
    margin-bottom: 13px;
}

.login-container {
    position: relative;
    width: 100%;
    height: 100%;
    background: linear-gradient(84deg, #513778 0%, #2F3C63 48%, #2C626E 100%);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.ms-title {
    width: 100%;
    line-height: 50px;
    text-align: center;
    font-size: 20px;
    color: #fff;
    border-bottom: 1px solid #ddd;
}
.ms-content {
    padding: 72px 65px 50px 50px;
}
.login-btn {
    margin-left: 32px;
    height: 38px;
    margin-top: 40px;
}
.login-btn button {
    width: 100%;
    height: 100%;
    border-radius: 6px;
    background: #3370FF;
    border: 1px solid #3370FF;
    font-size: 14px;
    font-weight: 400;
    letter-spacing: 0px;
    color: rgba(255, 255, 255, 1);
    vertical-align: top;
}
.sign-btn button {
    width: 250px;
    height: 38px;
    margin-top: 30px;
    margin-left: 111px;
    border-radius: 6px;
    background: rgba(51, 112, 255, 1);
    border: 1px solid rgba(51, 112, 255, 1);
    font-size: 14px;
    font-weight: 400;
    letter-spacing: 0px;
    color: rgba(255, 255, 255, 1);
    vertical-align: top;
}
.login-tips {
    font-size: 12px;
    line-height: 10px;
    width: 100%;
    text-align: right;
}
.form-view {
    width: 400px;
    height: 337px;
    background: #232643;
    box-shadow: 0px 10px 40px 0px rgba(0,0,0,0.2);
    border-radius: 16px;
    overflow: hidden;
    margin-top: 23px;
}
.icon_login_car {    
    position: relative;
    top: -38px;
    width: 263px;
    height: 371px;
}
.title-label-view {
    margin-top: 45px;
    margin-left: 40px;
}
.login-sys {
    font-size: 24px;
    font-weight: 500;
    letter-spacing: 0px;
    color: rgba(255, 255, 255, 1);
    text-align: left;
    vertical-align: top;
    font-family: PingFangSC-Medium, PingFang SC;
}
.logo-img {
    position: relative;
    top: 6px;
    width: 22px;
    height: 30px;
    margin-right: 3px;
}
.sms-code-btn {
    width: 88px;
    height: 36px;
    border-radius: 4px;
    background: rgba(255, 255, 255, 1);
    border: 1px solid rgba(197, 201, 208, 1);
    font-size: 14px;
    font-weight: 400;
    letter-spacing: 0px;
    color: rgba(28, 30, 33, 1);
    text-align: left;
    vertical-align: top;
}
.ms-sign {
    width: 470px;
    height: 560px;
    border-radius: 5px;
    background: #fff;
    overflow: hidden;
}
.noaccount {
    font-size: 12px;
    font-weight: 400;
    letter-spacing: 0px;
    color: rgba(102, 102, 102, 1);
    text-align: right;
    vertical-align: top;
}
.register-btn {
    font-size: 12px;
    font-weight: 400;
    letter-spacing: 0px;
    color: rgba(51, 112, 255, 1);
    text-align: right;
    vertical-align: top;
}

.icon-input {
    width: 22px;
    height: 22px;
    margin-right: 9px;
}
.input-view {
    /* background: #2D3052;
    border-radius: 4px;
    border: 1px solid transparent; */
}
</style>