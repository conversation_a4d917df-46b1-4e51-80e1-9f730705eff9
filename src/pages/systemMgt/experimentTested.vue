<template>
    <div class="content-box">
        <div class="flex-box flex-column content">
            <div class="search-box flex-box">
                <el-form class="flex-item" ref="searchForm" :inline="true" :model="searchForm" :disabled="loading">
                    <el-form-item label="检查单位：">
                        <el-input v-model="searchForm.inspectCompany" clearable placeholder="请输入检查单位"
                            style="width: 180px" />
                    </el-form-item>
                    <el-form-item label="检查内容：">
                        <el-input v-model="searchForm.inspectContent" clearable placeholder="请输入检查内容"
                            style="width: 180px" />
                    </el-form-item>
                    <el-form-item label="检查日期" prop="takeEffectDate">
                        <el-date-picker type="daterange" 
                        v-model="searchForm.takeEffectDate" 
                        start-placeholder="开始日期" end-placeholder="结束日期" 
                        format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                        :clearable="true" :disabled="loading" style="width: 360px"
                        >
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" :disabled="loading" @click="handleFilter">搜索</el-button>
                        <el-button type="text" icon="el-icon-refresh-right" :disabled="loading" @click="resetForm()">重置</el-button>
                    </el-form-item>
                </el-form>
                <el-button type="primary" @click="handleSetTarget()">新增</el-button>
            </div>

            <div class="flex-item overHide">
                <div class="scroll-div">
                    <el-table :data="tableData" v-loading="loading" style="width: 100%">
                        <af-table-column v-for="item in tableColumn" :key="item.prop" :prop="item.prop"
                            :label="item.label" :formatter="item.formatter" :fixed="item.fixed"
                            :width="item.width || ''" align="center" />
                        <af-table-column width="200" label="操作" align="center"
                            key="handle" :resizable="false">
                            <template slot-scope="scope">
                                <el-button type="text" size="small" @click="handleSetTarget(scope.row)">修改</el-button>
                                <el-button type="text" size="small" @click="previewAttachmentsTarget(scope.row)">附件预览</el-button>
                                <el-button type="text" size="small" style="color: #ff0000;"
                                    @click="handleDel(scope.row)">删除</el-button>
                            </template>
                        </af-table-column>
                    </el-table>
                </div>
            </div>
            <div class="mt16 mb4">
                <Pagination :total="total" :pageNum="pageObj.pageNum" :pageSize="pageObj.pageSize"
                    @getData="initData" />
            </div>
        </div>

        <DrawerForm :formContent="formContent" ref="drawerForm" @saveTarget="saveTarget">
            <template #customLastForm>
                <el-form-item
                    :label="`附件：`"
                >
                    <div class="img-box">
                        <el-upload
                        :action="baseUrl + '/upload/file'"
                        :file-list="attachmentsImgList"
                        :on-success="handleAttachmentsImgSuccess"
                        :on-remove="handleAttachmentsImgRemove">
                        <i class="el-icon-plus avatar-uploader-icon" style="color: #424E73;">点击添加附件</i>
                        </el-upload>
                    </div>
                </el-form-item>
            </template>
        </DrawerForm>

        <!-- 弹出一个模态框显示所有附件-->
        <el-dialog
                title="附件"
                :visible.sync="attachmentsImgVisible"
                width="40%"
                :before-close="handleClose"
                :close-on-click-modal="false"
                :close-on-press-escape="false"
                >
                <div class="img-box">
                    <div v-for="(item, index) in attachmentsImgList" :key="item.filePath" style="position: relative; margin-bottom: 10px;">
                        <a :href="item.url" target="_blank">
                            <i class="el-icon-document" style="margin-right: 6px;" />
                            <span>{{ item.name }}</span>
                        </a>
                    </div>
                </div>
        </el-dialog>
    </div>
</template>

<script>
import Pagination from "@/components/Pagination/index.vue";
import DrawerForm from "@/components/drawerForm.vue";
import moment from 'moment';

export default {
    name: 'equipmentConfig',
    components: {
        Pagination,
        DrawerForm
    },
    data() {
        return {
            addApi: 'addExperimentTested',
            delApi: 'deleteExperimentTested',
            updateApi: 'updateExperimentTested',
            getListApi: 'queryExperimentTestedPage',
            baseUrl: process.env.NODE_ENV === 'development' ? "/api" : "/mes-platform",
            filePrefix: this.$store.state.loginStore.userInfo.filePrefix,

            loading: false,
            searchForm: {
                takeEffectDate: []
            },

            tableColumn: [
                {
                    label: '检查单位',
                    prop: 'inspectCompany',
                },
                {
                    label: '受检单位',
                    prop: 'testedCompany',
                },
                {
                    label: '检查日期',
                    prop: 'inspectTime',
                    formatter(row) {
                        return row.inspectTime ? moment(row.inspectTime).format('YYYY-MM-DD') : '';
                    }
                },
                {
                    label: '检查内容',
                    prop: 'inspectContent',
                },
                {
                    label: '检查汇总',
                    prop: 'inspectCollect',
                },
                {
                    label: '检查结果',
                    prop: 'inspectResult',
                },
                {
                    label: '纠正措施',
                    prop: 'correctiveAction',
                },
            ],
            pageObj: {
                pageNum: 1, // 页数
                pageSize: 10, // 条数
            },
            total: 1,
            tableData: [],

            activeRow: {},//编辑对象

            formContent: [
                {
                    type: 'input',
                    label: '检查单位',
                    prop: 'inspectCompany',
                },
                {
                    type: 'input',
                    label: '受检单位',
                    prop: 'testedCompany',
                },
                {
                    type: 'date',
                    label: '检查日期',
                    prop: 'inspectTime',
                    format: 'yyyy-MM-dd',
                    valueFormat: "yyyy-MM-dd HH:mm:ss"
                },
                {
                    type: 'input',
                    label: '检查内容',
                    prop: 'inspectContent',
                }, {
                    type: 'input',
                    label: '检查汇总',
                    prop: 'inspectCollect',
                },
                {
                    type: 'input',
                    label: '检查结果',
                    prop: 'inspectResult',
                },
                {
                    type: 'input',
                    label: '纠正措施',
                    prop: 'correctiveAction',
                },
            ],
            rules: {},
            attachmentsImgList: [],
            attachments: [],
            attachmentsImgVisible: false,
        };
    },

    created() {
        this.initData();
    },
    methods: {
        handleFilter() {
            console.log(this.searchForm)
            this.initData(1);
        },
        resetForm() {
            this.searchForm = {};
            this.initData(1);
        },
        initData(opageNum, opageSize) {
            this.loading = true;
            if (opageNum) this.pageObj.pageNum = opageNum;
            if (opageSize) this.pageObj.pageSize = opageSize;

            const reqParma = {
                ...this.pageObj,
                params: {
                    ...this.searchForm
                }
            }

            if (!this.isEmpty(this.searchForm.takeEffectDate)) {
                reqParma.params.startTime = this.searchForm.takeEffectDate[0]
                    ? this.searchForm.takeEffectDate[0]
                    : "";
                reqParma.params.endTime = this.searchForm.takeEffectDate[1]
                    ? this.searchForm.takeEffectDate[1]
                    : "";
            }
            reqParma.params.takeEffectDate = undefined;

            //获取列表
            this.$api[this.getListApi](reqParma, this).then(res => {
                this.loading = false;
                if (res.succ) {
                    this.tableData = res.data.list;
                    this.total = res.data.total;
                } else {
                    this.$message.error(res.msg || '查询失败')
                }
            })
        },
        handleDel(row) {
            this.$confirm("删除后不能恢复，确定要删除吗？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                //删除
                this.$api[this.delApi]({
                    id: row.id
                }, this).then((res) => {
                    if (res.succ) {
                        this.$message({
                            showClose: true,
                            message: "删除成功",
                            type: "success",
                        });
                        
                        this.initData(1);
                    }
                });
            }).catch(() => {
                this.$message({
                    type: "info",
                    message: "已取消删除",
                });
            });
        },

        handleSetTarget(row) {
            this.attachments = [];
            this.attachmentsImgList = [];
            if (row) {
                let initRow = {
                    ...row
                };
                if (row.attachments) {
                    row.attachments.split(",").map((item, index) => {
                        this.attachmentsImgList.push({
                            name: item,
                            url: this.filePrefix + item
                        })
                    });
                    this.attachments = row.attachments.split(",");
                }
                this.$refs.drawerForm.initData(initRow);
            } else {
                this.$refs.drawerForm.initData();
            }

        },

        saveTarget(formData) {
            formData.attachments = this.attachments.join(",");
            if (formData.id) {//修改this.activeRow
                this.$api[this.updateApi](formData, this).then((res) => {
                    if (res.succ) {
                        this.$message({
                            showClose: true,
                            message: "修改成功",
                            type: "success",
                        });
                        this.$refs.drawerForm.handleClose();
                        this.initData();
                    }
                });
            } else {
                this.$api[this.addApi](formData, this).then((res) => {
                    if (res.succ) {
                        this.$message({
                            showClose: true,
                            message: "添加成功",
                            type: "success",
                        });
                        this.initData();
                        this.$refs.drawerForm.handleClose();
                    }
                });
            }
        },

        handleAttachmentsImgRemove(file, fileList) {
            this.attachmentsImgList = fileList;
            this.attachments = [];
            fileList.map(item => {
                this.attachments.push(item.name);
            });
        },
        handleAttachmentsImgSuccess(response) {
            if (response.code == 1) {
                this.attachmentsImgList.push({
                    name: response.data.fileName,
                    url: response.data.filePath,
                });
                this.attachments.push(response.data.fileName);
            }
        },

        previewAttachmentsTarget(row) {
            this.attachments = [];
            this.attachmentsImgList = [];
            if (row.attachments) {
                row.attachments.split(",").map((item, index) => {
                    this.attachmentsImgList.push({
                        name: item,
                        url: this.filePrefix + item
                    })
                });
                this.attachments = row.attachments.split(",");
            }
            this.attachmentsImgVisible = true;
        },
        handleClose() {
            this.attachments = [];
            this.attachmentsImgList = [];
            this.attachmentsImgVisible = false;
        },

        isEmpty(val) {
            if (typeof val === "boolean") {
                return false;
            }
            if (typeof val === "number") {
                return false;
            }
            if (val instanceof Array) {
                if (val.length === 0) return true;
            } else if (val instanceof Object) {
                if (JSON.stringify(val) === "{}") return true;
            } else {
                if (
                val === "null" ||
                val == null ||
                val === "undefined" ||
                val === undefined ||
                val === ""
                )
                return true;
                return false;
            }
            return false;
        },
    },
};
</script>

<style scoped lang="scss">
.multi-form-item-box {
    padding: 0 0 4px 0;

    // display: flex;
    // justify-content: space-between;
    .el-select,
    .el-input {
        margin-right: 20px;
    }
}

::v-deep .el-button {
    padding-left: 13px;
    padding-right: 13px;
}

.el-form-item {
    margin-bottom: 8px;
}

::v-deep .el-form--inline {
    .el-form-item {
        margin-right: 24px;
        margin-bottom: 0;

        &:last-child {
            margin: 0;
        }
    }
}

::v-deep .el-table {

    .expanded,
    .expanded:hover {
        background-color: #FFFBD9;

    }

    .expanded+tr {
        background-color: #FFFBD9;

        td {
            background-color: #FFFBD9;
        }
    }

    .table-child-box {
        margin: 16px;
        padding: 16px;
        background: #FFFFFF;
    }
}

.content-box {
    padding: 16px;
}

.content {
    width: 100%;
    height: 100%;
    padding: 16px;
    background: #FFFFFF;
    border-radius: 16px;
}

.search-box {
    padding-bottom: 16px;
    line-height: 40px;
}
</style>