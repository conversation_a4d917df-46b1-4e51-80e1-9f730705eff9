<template>
  <div class="content-box">
    <div class="flex-box flex-column content">
      <div class="search-box flex-box">
        <el-form class="flex-item" ref="searchForm" :inline="true" :model="searchForm" :disabled="loading">
          <el-form-item label="工程区域：">
            <el-input v-model="searchForm.projectArea" clearable
              placeholder="工程区域" 
              style="width: 180px" 
            />
          </el-form-item>
          <el-form-item label="区域组长：">
            <el-select
              v-model="searchForm.headmanId" 
              filterable clearable 
              placeholder="请选择工程区域" 
              style="width: 180px">
              <el-option
                v-for="item in userAllOpt"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="客服员：">
            <el-select
              v-model="searchForm.customerServiceId" 
              filterable clearable 
              placeholder="请选择客服员" 
              style="width: 180px">
              <el-option
                v-for="item in userAllOpt"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" :disabled="loading" @click="handleFilter">搜索</el-button>
            <el-button type="text" icon="el-icon-refresh-right" :disabled="loading" @click="resetForm()">重置</el-button>
          </el-form-item>
        </el-form>
        <!-- <el-button type="primary" @click="handleSetTarget()">新增工程区域</el-button> -->
      </div>
      
      <div class="flex-item overHide">
        <div class="scroll-div">
          <el-table
            :data="tableData"
            v-loading="loading"
            style="width: 100%">
            <af-table-column
              v-for="item in tableColumn" 
              :key="item.prop" :prop="item.prop" 
              :label="item.label" 
              :formatter="item.formatter"
              :fixed="item.fixed" 
              :width="item.width || ''"
              align="center" 
            />
            <af-table-column width="150" label="操作" align="center" key="handle" :resizable="false">
              <template slot-scope="scope">
                <el-button type="text" size="small" @click="handleSetTarget(scope.row)">修改</el-button>
                <el-button type="text" size="small" style="color: #ff0000;" @click="handleDel(scope.row)">删除</el-button>
              </template>
            </af-table-column>
          </el-table>
        </div>
      </div>
      <div class="mt16 mb4">
        <Pagination
          :total="total" 
          :pageNum="pageObj.pageNum" 
          :pageSize="pageObj.pageSize" 
          @getData="initData" 
        />
      </div>
    </div>
    
    
    <DrawerForm
      :formContent="formContent"
      ref="drawerForm"
      @saveTarget="saveTarget"
    >
    </DrawerForm>
  </div>
</template>

<script>
import { regionalismColumn as tableColumn } from "./config.js"
import Pagination from "@/components/Pagination/index.vue";
import DrawerForm from "@/components/drawerForm.vue";
import getOpt from "@/common/js/getListData.js"
export default {
  name:'regionalismConfig',
  components: {
    Pagination,
    DrawerForm
  },
  data() {
    return {
      addApi: 'addRegion',
      delApi: 'delRegion',
      updateApi: 'setRegion',
      getListApi: 'getRegionList',
      
      
      loading: false,
      // 设置时间选择
      pickerOptions: {
        disabledDate(time) {
          let deadline = Date.now() - 60 * 60 * 1000;
          return time.getTime() > deadline //
        },
      },
      searchForm: {},
      categoryList: [],
      
      tableColumn: tableColumn,
      pageObj: {
        pageNum: 1, // 页数
        pageSize: 10, // 条数
      },
      total: 1,
      tableData: [],
      
      activeRow: {},//编辑对象
      
      formContent: [],
      rules: {},
      userAllOpt: [],
    };
  },
  
  created() {
    this.initData();
    
    this.setDrawerData()
  },
  methods: {
    async setDrawerData(){
      this.userAllOpt = await getOpt.getUserAll(this)
      
      let headmanlist = this.userAllOpt.filter(item => {
        
        
        return item.roleId == 6
      })
      console.log(headmanlist, this.userAllOpt)
      this.formContent = [
        {
          type: 'input',
          label: '工程区域',
          prop: 'projectArea',
        },
        {
          type: 'select',
          label: '区域组长',
          prop: 'headmanId',
          options: headmanlist
        },
        {
          type: 'select',
          label: '客服员',
          multiple: true,
          prop: 'customerServiceId',
          options: this.userAllOpt
        },
      ]
      
      
    },
    handleFilter() {
      console.log(this.searchForm)
      
      this.initData(1);
    },
    resetForm(){
      this.searchForm = {};
      this.initData(1);
    },
    initData(opageNum, opageSize){
      this.loading = true;
      if (opageNum) this.pageObj.pageNum = opageNum;
      if (opageSize) this.pageObj.pageSize = opageSize;
        
      const params ={
        ...this.pageObj,
        params: this.searchForm
      }
      //获取列表
      this.$api[this.getListApi](params, this).then(res => {
        this.loading = false;
        if(res.succ){
          this.tableData = res.data.list;
          this.total = res.data.total;
        }else{
          this.$message.error(res.msg || '查询失败')
        }
      })
    },
    handleDel(row){
      this.$confirm("删除后不能恢复，确定要删除吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        //删除
        this.$api[this.delApi]({
          id: row.id
        }, this).then((res) => {
          if (res.succ) {
            this.$message({
              showClose: true,
              message: "删除成功",
              type: "success",
            });
            if(this.tableData.length == 1 && this.pageObj.pageNum > 1){
              this.pageObj.pageNum = this.pageObj.pageNum -1
            }
            this.initData();
          }
        });
      }).catch(() => {
        this.$message({
          type: "info",
          message: "已取消删除",
        });
      });
    },
    
    handleSetTarget(row){
      //this.activeRow = row;
      this.formContent[0].disabled = false;
      if(row && row.headmanId && row.customerServiceId){
        row.headmanId = parseInt(row.headmanId);
        row.customerServiceId = row.customerServiceId.split(',').map(item => parseInt(item)) // parseInt();
        this.formContent[0].disabled = true;
      }
      this.$refs.drawerForm.initData(row);
    },
    
    saveTarget(formDataOld){
      let formData = JSON.parse(JSON.stringify(formDataOld))
      formData.headmanName = getOpt.getNameFromId(formData.headmanId, this.userAllOpt);
      formData.customerServiceName = formData.customerServiceId.map(item =>{
        return getOpt.getNameFromId(item, this.userAllOpt)
      })
      formData.customerServiceId = formData.customerServiceId.join(',');
      formData.customerServiceName = formData.customerServiceName.join(',');
      
      if(formData.id){//修改this.activeRow
        this.$api[this.updateApi](formData, this).then((res) => {
          if (res.succ) {
            this.$message({
              showClose: true,
              message: "修改成功",
              type: "success",
            });
            this.$refs.drawerForm.handleClose();
            this.initData();
          }
        });
      }else{
        
        this.$api[this.addApi](formData, this).then((res) => {
          if (res.succ) {
            this.$message({
              showClose: true,
              message: "添加成功",
              type: "success",
            });
            this.initData();
            this.$refs.drawerForm.handleClose();
          }
        });
      }
      
    },
  },
};
</script>

<style scoped lang="scss">
  .multi-form-item-box{
    padding: 0 0 4px 0;
    // display: flex;
    // justify-content: space-between;
    .el-select,.el-input{
      margin-right: 20px;
    }
  }
  ::v-deep .el-button{
    padding-left: 13px;
    padding-right: 13px;
  }
  .el-form-item{
    margin-bottom: 8px;
  }
  ::v-deep .el-form--inline{
    .el-form-item{
      margin-right: 24px;
      margin-bottom: 0;
      &:last-child{
        margin: 0;
      }
    }
  }
  ::v-deep .el-table{
    .expanded,.expanded:hover{
      background-color: #FFFBD9;
      
    }
    .expanded + tr{
      background-color: #FFFBD9;
      td{
        background-color: #FFFBD9;
      }
    }
    
    .table-child-box{
      margin: 16px;
      padding: 16px;
      background: #FFFFFF;
    }
  }
  
  .content-box{
    padding: 16px;
  }
  .content{
    width: 100%;
    height: 100%;
    padding: 16px;
    background: #FFFFFF;
    border-radius: 16px;
  }
  
  .search-box{
    padding-bottom: 16px;
    line-height: 40px;
  }
</style>