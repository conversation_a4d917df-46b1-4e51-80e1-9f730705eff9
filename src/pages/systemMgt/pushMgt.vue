<template>
    <div class="content-box">
        <div class="flex-box flex-column content">
            <div>
                <div class="flex-row row-title" v-for="item in systemParamList" :key="item.id" style="margin-top: 20px;">
                    <div>{{ item.systemName }}：</div>
                    <el-radio-group :disabled="!isOpen" v-model="item.systemValue">
                        <el-radio :label="'1'">开启</el-radio>
                        <el-radio :label="'0'">关闭</el-radio>
                    </el-radio-group>
                </div>
            </div>
            <div style="margin-left: calc(100% - 200px); margin-top: 40px; position: absolute; bottom: 20px;">
                <el-button type="primary" @click="cancelTarget" plain>{{ isOpen ? '取消' : '编辑' }}</el-button>
                <el-button type="primary" @click="saveTarget">保存</el-button>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    data() {
        return {
            systemParamList: [],
            isOpen: false,
        }
    },

    mounted() {
        this.queryAllSystemResp();
    },

    methods: {
        
        queryAllSystemResp() {
            this.$api.getAllSystemParam().then(res => {
                if (res.code == 1 && res.data.list) {
                    // 遍历取出需要的字段
                    
                    this.systemParamList = res.data.list.map(item => {
                        return {
                            id: item.id,
                            systemName: item.systemName,
                            systemValue: item.systemValue,
                            systemCode: item.systemCode,
                            systemDesc: item.systemDesc
                        };
                    });
                }
            })
        },

        saveTarget() {
            let self = this;
            this.$confirm('确认保存吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                self.updateSystemParamListResp();
            });
        },

        updateSystemParamListResp() {
            this.$api.updateSystemParamList({systemParamList: this.systemParamList}, this).then(res => {
                if (res.succ) {
                    this.$message({
                        showClose: true,
                        message: "操作成功",
                        type: "success",
                    });
                    this.cancelTarget();
                    this.queryAllSystemResp();
                }
            });
        },

        cancelTarget() {
            this.isOpen = !this.isOpen;
        }
    }
}
</script>

<style lang="scss" scoped>
.content-box{
    padding: 24px 16px;
}
.content{
    width: 100%;
    height: 100%;
    padding: 16px;
    background: #FFFFFF;
    border-radius: 16px;
    overflow: auto;
    align-items: flex-start;
    position: relative;
    .row-cell {
        margin-top: 16px;
        justify-content: flex-start;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #1F2329;

       
    }
}
.row-title {
    font-weight: 600;
    font-size: 16px;
    color: #1F2329;
    margin-right: 60px;
}
.row-title-left {
    margin-left: 96px;
}
</style>