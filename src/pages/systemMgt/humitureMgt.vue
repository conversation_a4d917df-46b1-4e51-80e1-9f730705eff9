<template>
  <div class="content-box">
    <div class="flex-box flex-column content">
      <div class="search-box flex-box">
        <el-form class="flex-item" ref="searchForm" :inline="true" :model="searchForm" :disabled="loading">
          
          <el-form-item label="类型：">
            <el-checkbox-group v-model="searchForm.typeNo" @change="initData(1)">
              <el-checkbox v-for="item in categoryList"
                :key="item.value" :label="item.value">{{item.label}}</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          
          <el-form-item label="日期：">
              <!-- :picker-options="pickerOptions" -->
            <el-date-picker type="daterange" 
              v-model="searchForm.takeEffectDate" 
              start-placeholder="开始日期" end-placeholder="结束日期" 
              format="yyyy-MM-dd" value-format="yyyy-MM-dd"
              :clearable="true" style="width: 360px"
            >
            </el-date-picker>
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" :disabled="loading" @click="handleFilter">搜索</el-button>
            <el-button type="text" icon="el-icon-refresh-right" :disabled="loading" @click="resetForm()">重置</el-button>
          </el-form-item>
        </el-form>
        <el-button type="primary" @click="handleSetTarget()">新增温湿度</el-button>
      </div>
      
      <div class="flex-item overHide">
        <div class="scroll-div">
          <el-table
            :data="tableData"
            v-loading="loading"
            style="width: 100%">
            <af-table-column
              v-for="item in tableColumn" 
              :key="item.prop" :prop="item.prop" 
              :label="item.label" 
              :formatter="item.formatter"
              :fixed="item.fixed" 
              :width="item.width || ''"
              align="center" 
            />
            <af-table-column width="150" label="操作" align="center" key="handle" :resizable="false">
              <template slot-scope="scope">
                <el-button type="text" size="small" @click="handleSetTarget(scope.row)">修改</el-button>
                <el-button type="text" size="small" style="color: #ff0000;" @click="handleDel(scope.row)">删除</el-button>
              </template>
            </af-table-column>
          </el-table>
        </div>
      </div>
      <div class="mt16 mb4">
        <Pagination
          :total="total" 
          :pageNum="pageObj.pageNum" 
          :pageSize="pageObj.pageSize" 
          @getData="initData" 
        />
      </div>
    </div>
    
    
    <DrawerForm
      :formContent="formContent"
      ref="drawerForm"
      @saveTarget="saveTarget"
    >
    </DrawerForm>
  </div>
</template>

<script>
import { humitureColumn as tableColumn } from "./config.js"
import Pagination from "@/components/Pagination/index.vue";
import DrawerForm from "@/components/drawerForm.vue";
import utilMoment from "@/utils/moment";
import moment from "moment";
import getOpt from "@/common/js/getListData.js"
export default {
  name:'humitureMgt',
  components: {
    Pagination,
    DrawerForm
  },
  data() {
    return {
      addApi: 'addhumiture',
      delApi: 'delhumiture',
      updateApi: 'setHumiture',
      getListApi: 'gethumitureList',
      
      roleId: this.$store.state.loginStore.userInfo.roleId,
      loading: false,
      // 设置时间选择
      pickerOptions: {
        disabledDate(time) {
          let deadline = Date.now() - 60 * 60 * 1000;
          return time.getTime() > deadline //
        },
      },
      searchForm: {
        typeNo: [],
        takeEffectDate: [],//moment.twoWeekAgo(), moment.beforeYesterday()
      },
      categoryList: [],
      
      tableColumn: tableColumn,
      pageObj: {
        pageNum: 1, // 页数
        pageSize: 10, // 条数
      },
      total: 1,
      tableData: [],
      
      activeRow: {},//编辑对象
      
      formContent: [],
      rules: {},
    };
  },
  
  created() {
    this.initData();
    this.$api.getDictValue({
      dictCode: 'WSD_TYPE'
    }, this).then(res =>{
      if (res.succ) {
        this.categoryList = res.data.list.map(i => {
          return {
            label: i.dictValueName,
            value: i.dictValueCode
          }
        })
        console.log(this.categoryList)
        
        this.setOptMethod()
      }
    })
    
  },
  methods: {
    async setOptMethod(){
      let odata = await getOpt.getUserAll(this)
      odata.map(item => {
        item.value = item.label
        return item
      })
      console.log(odata)
      this.formContent = [
        {
          type: 'number',
          label: '温度',
          prop: 'temperature',
          slot: 'append',
          slotVal: '℃',
        },{
          type: 'number',
          label: '湿度',
          prop: 'humidness',
          slot: 'append',
          slotVal: 'RH%',
        },{
          type: 'select',
          label: '记录人',
          prop: 'recordName',
          options: odata
        },{
          type: 'datetime',
          format: 'yyyy-MM-dd HH:mm:ss',
          label: '记录时间',
          prop: 'recordTime',
          valueFormat: 'yyyy-MM-dd HH:mm:ss',
        }
        ,{
          type: 'radio',
          label: '类型',
          prop: 'typeNo',
          options: this.categoryList
        }
      ]
    },
    handleFilter() {
      console.log(this.searchForm)
      this.initData(1);
    },
    resetForm(){
      this.searchForm = {
        typeNo: [],
        takeEffectDate: []
      };
      this.initData(1);
    },
    initData(opageNum, opageSize){
      this.loading = true;
      if (opageNum) this.pageObj.pageNum = opageNum;
      if (opageSize) this.pageObj.pageSize = opageSize;
      let params ={
        ...this.pageObj,
        params: {
          typeNo: this.searchForm.typeNo
        }
      }
      
      params.params.typeNo = params.params.typeNo ? params.params.typeNo.join(",") : '';
      if (!this.isEmpty(this.searchForm.takeEffectDate)) {
        params.params.startTime = this.searchForm.takeEffectDate[0]
          ? this.searchForm.takeEffectDate[0]
          : "";
        params.params.endTime = this.searchForm.takeEffectDate[1]
          ? this.searchForm.takeEffectDate[1]
          : "";
      }
      delete params.params.takeEffectDate
      // else{
      //   this.$message.error("请选择日期");
      //   return false;
      // }
      
      
      
      //获取列表
      this.$api[this.getListApi](params, this).then(res => {
        this.loading = false;
        if(res.succ){
          this.tableData = res.data.list;
          this.total = res.data.total;
        }else{
          this.$message.error(res.msg || '查询失败')
        }
      })
    },
    handleDel(row){
      this.$confirm("删除后不能恢复，确定要删除吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        //删除
        this.$api[this.delApi]({
          id: row.id
        }, this).then((res) => {
          if (res.succ) {
            this.$message({
              showClose: true,
              message: "删除成功",
              type: "success",
            });
            if(this.tableData.length == 1 && this.pageObj.pageNum > 1){
              this.pageObj.pageNum = this.pageObj.pageNum -1
            }
            this.initData();
          }
        });
      }).catch(() => {
        this.$message({
          type: "info",
          message: "已取消删除",
        });
      });
    },
    
    handleSetTarget(row){
      //this.activeRow = row;
      if(!row){
        let orow = {};
        console.log(this.$store.state.loginStore.userInfo)
        orow.recordName = this.$store.state.loginStore.userInfo.userName;
        // orow.recordTime = moment.today() + " 00:00:00",
        orow.recordTime = moment().format("YYYY-MM-DD HH:mm:ss");
        orow.typeNo = this.categoryList[0].value
        this.$refs.drawerForm.initData(orow);
      }else{
        
        this.$refs.drawerForm.initData(row);
      }
    },
    
    saveTarget(formData){
      formData.typeName = this.getName(formData.typeNo, this.categoryList)
      if(formData.id){//修改this.activeRow
        this.$api[this.updateApi](formData, this).then((res) => {
          if (res.succ) {
            this.$message({
              showClose: true,
              message: "修改成功",
              type: "success",
            });
            this.$refs.drawerForm.handleClose();
            this.initData();
          }
        });
      }else{
        this.$api[this.addApi](formData, this).then((res) => {
          if (res.succ) {
            this.$message({
              showClose: true,
              message: "添加成功",
              type: "success",
            });
            this.initData();
            this.$refs.drawerForm.handleClose();
          }
        });
      }
    },
    getName(oVal, list){
      for(let i =0;i<list.length; i++){
        if(oVal == list[i].value){
          return list[i].label;
        }
      }
    },
    isEmpty(val) {
      if (typeof val === "boolean") {
        return false;
      }
      if (typeof val === "number") {
        return false;
      }
      if (val instanceof Array) {
        if (val.length === 0) return true;
      } else if (val instanceof Object) {
        if (JSON.stringify(val) === "{}") return true;
      } else {
        if (
          val === "null" ||
          val == null ||
          val === "undefined" ||
          val === undefined ||
          val === ""
        )
          return true;
        return false;
      }
      return false;
    },
  },
};
</script>

<style scoped lang="scss">
  .multi-form-item-box{
    padding: 0 0 4px 0;
    // display: flex;
    // justify-content: space-between;
    .el-select,.el-input{
      margin-right: 20px;
    }
  }
  ::v-deep .el-button{
    padding-left: 13px;
    padding-right: 13px;
  }
  .el-form-item{
    margin-bottom: 8px;
  }
  ::v-deep .el-form--inline{
    .el-form-item{
      margin-right: 24px;
      margin-bottom: 0;
      &:last-child{
        margin: 0;
      }
    }
  }
  ::v-deep .el-table{
    .expanded,.expanded:hover{
      background-color: #FFFBD9;
      
    }
    .expanded + tr{
      background-color: #FFFBD9;
      td{
        background-color: #FFFBD9;
      }
    }
    
    .table-child-box{
      margin: 16px;
      padding: 16px;
      background: #FFFFFF;
    }
  }
  
  .content-box{
    padding: 16px;
  }
  .content{
    width: 100%;
    height: 100%;
    padding: 16px;
    background: #FFFFFF;
    border-radius: 16px;
  }
  
  .search-box{
    padding-bottom: 16px;
    line-height: 40px;
  }
</style>