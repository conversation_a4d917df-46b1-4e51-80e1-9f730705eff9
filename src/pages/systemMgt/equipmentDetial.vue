<template>
  <div class="content-box">
    <div class="content flex-box flex-column ">
      <div class="g-card">
        <p class="gc-main pb16">{{taskData.equipmentName}}<span class="red">{{ taskData.state == 1 ? '正常' : taskData.state == 2 ? '异常' : '禁用'}}</span></p>
        <p class="">场景名称	：{{taskData.scenarioName}}</p>
        <p class="">搅拌站名称	：{{taskData.mixingPlantName}}</p>
        <div class="fl">
          <p>创建人：{{taskData.creater}}</p>
          <p>对接方式：{{taskData.dockingWay}}</p>
          <p>心跳请求	：{{taskData.fgcjb}}</p>
        </div>
        <div class="fl">
          <p>创建时间：{{taskData.createTime}}</p>
          <p>IP地址：{{taskData.ipAddress}}</p>
          <p>心跳返回：{{taskData.heartSuccess || taskData.heartException}}</p>
        </div>
        <div class="fl">
          <p>修改人：{{taskData.update}}</p>
          <p>端口号：{{taskData.portNumber}}</p>
        </div>
        <div class="fl">
          <p>修改时间：{{taskData.updateTime}}</p>
        </div>
        <div class="cb"></div>
      </div>
      <div class="h40"></div>
      <!-- 设备校准记录 -->
      <div class="title flex-row">
        <span>设备校准记录</span>
        <el-button type="primary" size="small" @click="addStandardizingRecord">新增校准记录</el-button>
      </div>
      <el-table
        :data="workTableDatas"
        empty-text="暂无数据"
        style="width: 100%; min-height: 300px;">
        <template v-for="item in engDetailColumn">
          <af-table-column
            :key="item.prop"
            :prop="item.prop" 
            :label="item.label" 
            :formatter="item.formatter"
            :fixed="item.fixed" 
            :width="item.width || ''"
            show-overflow-tooltip
            align="center" 
          />
        </template>

        <el-table-column width="240" label="操作" align="center" key="handle" :resizable="false">
            <template slot-scope="scope">
                <el-button type="text" size="small" @click="previewAttachmentsTarget(scope.row)">附件预览</el-button>
                <el-button type="text" size="small" style="margin-left: 20px;" @click="addStandardizingRecord(scope.row)">修改</el-button>
                <el-button type="text" size="small" style="margin-left: 20px;" @click="printConfirmTable(scope.row)">打印</el-button>
                <el-button type="text" size="small" style="color: #ff0000; margin-left: 20px;"
                                    @click="deleteStandardizingRecord(scope.row)">删除</el-button>
            </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 弹出一个模态框显示所有附件-->
    <el-dialog
        title="附件"
        :visible.sync="attachmentsImgVisible"
        width="40%"
        :before-close="handleClose"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        >
        <div class="img-box">
            <div v-for="(item, index) in attachmentsImgList" :key="item.filePath" style="position: relative; margin-bottom: 10px;">
                <a :href="item.url" target="_blank">
                    <i class="el-icon-document" style="margin-right: 6px;" />
                    <span>{{ item.name }}</span>
                </a>
            </div>
        </div>
    </el-dialog>

    <DrawerForm
      :formContent="formRecordContent"
      ref="recordForm"
      @drawerClose="handleRecordClose"
      @saveTarget="saverecordTarget"
    >
      <template #customLastForm>
          <el-form-item
              :label="`校准附件：`"
          >
              <div class="img-box">
                  <el-upload
                  :action="baseUrl + '/upload/file'"
                  :file-list="attachmentsImgList"
                  :on-success="handleAttachmentsImgSuccess"
                  :on-remove="handleAttachmentsImgRemove">
                  <i class="el-icon-plus avatar-uploader-icon" style="color: #424E73;">点击添加附件</i>
                  </el-upload>
              </div>
          </el-form-item>
      </template>
    </DrawerForm>
  </div>
</template>

<script>
import DrawerForm from "@/components/drawerForm.vue";
export default {
  data() {
    return {
      taskData: {},
      loading: false,
      receiptData: [],
      baseUrl: process.env.NODE_ENV === 'development' ? "/api" : "/mes-platform",
      filePrefix: this.$store.state.loginStore.userInfo.filePrefix,

      engDetailColumn: [
        {
          label: '设备名称',
          prop: 'equipmentName',
        }, 
        {
          label: '报告编号',
          prop: 'reportNo',
        },{
          label: '校准单位',
          prop: 'calibrationUnit',
        },{
          label: '校准结果',
          prop: 'calibrationText',
        },{
          label: '校准时间',
          prop: 'calibrationTime',
          width: 200,
        },
        // {
        //   label: '校准附件',
        //   prop: 'calibrationAnnex',
        //   formatter: (row) => {
        //     return row.calibrationAnnex ? `<a href="${this.$store.state.loginStore.userInfo.filePrefix}${row.calibrationAnnex}" target="_blank">查看</a>` : ''
        //   },
        // }
      ],
      workTableDatas: [],
      formRecordContent: [
        {
          type: 'input',
          label: '设备名称',
          prop: 'equipmentName',
          disabled: true,
        },{
          type: 'input',
          label: '报告编号',
          prop: 'reportNo',
        },{
          type: 'input',
          label: '校准单位',
          prop: 'calibrationUnit',
        },{
          type: 'datetime',
          label: '校准时间',
          prop: 'calibrationTime',
          format: 'yyyy-MM-dd HH:mm:ss',
          valueFormat: 'yyyy-MM-dd HH:mm:ss',
        },{
          type: 'input',
          label: '校准结果',
          prop: 'calibrationText',
        }
      ],
      attachmentsImgList: [],
      attachments: [],
      attachmentsImgVisible: false,
      listParma: {}
    };
  },

  components: {
    DrawerForm,
  },
  
  created: function() {
    this.getTaskInfo(this.$route.query.id);
  },
  methods: {
    initData(row){
      
    },
    //获取任务信息
    getTaskInfo(id){
      this.$api.getEquipmentDetail(`id=${id}`, this).then(res => {
        this.loading = false;
        if(res.succ){
          this.taskData = res.data
          this.showWorkOrders();
        }else{
          this.$message.error(res.msg || '查询失败')
        }
      })
    },

    // 根据任务单展示异常工单
    showWorkOrders() {
      //获取列表
      this.$api.getCalibrationRecordAll({
        equipmentId: this.$route.query.id
      }, this).then(res => {
        this.loading = false;
        if(res.succ){
          this.workTableDatas = res.data.list;
        }else{
          this.$message.error(res.msg || '查询失败')
        }
      })
    },

    previewAttachmentsTarget(row) {
        this.attachments = [];
        this.attachmentsImgList = [];
        if (row.calibrationAnnex) {
            row.calibrationAnnex.split(",").map((item, index) => {
                this.attachmentsImgList.push({
                    name: item,
                    url: this.filePrefix + item
                })
            });
            this.attachments = row.calibrationAnnex.split(",");
        }
        this.attachmentsImgVisible = true;
    },

    //新增记录
    addStandardizingRecord(row){
      this.$refs.recordForm.initData(row);
      this.$refs.recordForm.setEditFormValue('equipmentName',this.taskData.equipmentName);
    },

    handleClose(done) {
        if(done){
            done();
        }else{
            this.attachmentsImgVisible = false;
        }
    },

    saverecordTarget(formData){
      console.log(formData)
      formData.equipmentId = this.taskData.id
      formData.calibrationAnnex = this.attachments.join(",");
      if (formData.id) {
        this.$api.setCalibrationRecord(formData, this).then((res) => {
          if (res.succ) {
            this.$message({
              showClose: true,
              message: "操作成功",
              type: "success",
            });
            this.$refs.recordForm.handleClose();
            this.showWorkOrders();
          }
        });
      }else{
        this.$api.addCalibrationRecord(formData, this).then((res) => {
          if (res.succ) {
            this.$message({
              showClose: true,
              message: "添加成功",
              type: "success",
            });
            this.$refs.recordForm.handleClose();
            this.showWorkOrders();
          }
        });
      }
    },

    deleteStandardizingRecord(row) {
      this.$confirm("删除后不能恢复，确定要删除吗？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
      }).then(() => {
          //删除
          this.$api.delCalibrationRecord({id: row.id}, this).then(res => {
              if (res.succ) {
                this.$message({
                    showClose: true,
                    message: "操作成功",
                    type: "success",
                });
                this.showWorkOrders();
              }
          })
      });
    },

    handleRecordClose() {
      this.attachmentsImgList = [];
      this.attachments = [];
    },

    handleAttachmentsImgRemove(file, fileList) {
        this.attachmentsImgList = fileList;
        this.attachments = [];
        fileList.map(item => {
            this.attachments.push(item.name);
        });
    },
    handleAttachmentsImgSuccess(response) {
        if (response.code == 1) {
            this.attachmentsImgList.push({
                name: response.data.fileName,
                url: response.data.filePath,
            });
            this.attachments.push(response.data.fileName);
        }
    },

    printConfirmTable(row) {
      let routeData = this.$router.resolve({
        path: "/printConfirmTable",
        query: {
          cid: row.id
        }
      });
      window.open(routeData.href, '_blank');
    },
  },
};
</script>

<style scoped lang="scss">
  
  .content-box{
    padding: 16px;
  }
  .content{
    width: 100%;
    height: 100%;
    padding: 16px;
    background: #FFFFFF;
    border-radius: 16px;
    .title{
      font-size: 16px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      color: #1F2329;
      line-height: 22px;
      letter-spacing: 1px;
      padding-bottom: 8px;
      border-bottom: 1px solid #E8E8E8;
      margin-bottom: 8px;
    }
    .region-con{
      overflow: hidden;
      .el-row,.el-col{
        height: 100%;
      }
      
      .rc-item{
        padding-top: 8px;
        height: 28px;
        span{
          color: $color-txt;
          line-height: 20px;
          letter-spacing: 1px;
          padding-right: 16px;
        }
        .el-button{
          height: 20px;
          padding: 0;
        }
      }
    }
  }
  .h40{
    height: 40px;
    width: 100%;
  }
  .cc-info{
    margin: 0 0px 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #E8E8E8;
    &:last-child{
      margin-bottom: 0;
      border: none;
      padding: 0;
    }
  }
  .pb16{
    padding-bottom: 16px !important;
  }
  
  .g-card{
    width: 100%;
    padding: 16px 0 0;
    margin-bottom: 0;
    & > div{
      margin-right: 62px;
    }
    p{
      color: $color-txt;
      line-height: 20px;
      letter-spacing: 1px;
      padding-bottom: 8px;
      &:last-child{
        padding: 0;
      }
    }
    .gc-main{
      font-size: 16px;
      font-weight: 600;
      color: #1F2329;
      line-height: 22px;
      letter-spacing: 1px;
      span{
        line-height: 20px;
        height: 20px;
        background: #FFE9D1;
        border-radius: 10px;
        font-size: 12px;
        font-weight: 600;
        display: inline-block;
        vertical-align: middle;
        margin-top: -2px;
        color: #FF7B2F;
        margin-left: 16px;
        padding: 0 7px;
        &.succ{
          color: $color-success;
          background: #DDEFEA;
        }
        &.red{
          color: #FF2F2F;
          background: #FFE9E9;
        }
      }
    }
  }
  
  .receipt-list{
    width: 100%;
    height: 100%;
    background: #EFF1F2;
    border-radius: 8px;
    padding: 16px;
    overflow-y: auto;
    display: flex;
    flex-wrap: wrap;
    gap: 16px 8px;
    align-content: flex-start;
    .receipt-item{
      width: calc(50% - 4px);
      height: 106px;
      background: #FFFFFF;
      border-radius: 8px;
      padding: 8px;
      p{
        &:nth-child(1){
          height: 22px;
          font-size: 16px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 600;
          color: #1F2329;
          line-height: 22px;
          letter-spacing: 1px;
        }
        &:nth-child(2){
          padding: 16px 0 7px;
          color: #1F2329;
          line-height: 25px;
          height: 48px;
          letter-spacing: 1px;
          span{
            font-size: 16px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 600;
            padding: 0 16px 0 4px;
          }
        }
        &:nth-child(3){
          height: 20px;
          font-size: 14px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #6A727D;
          line-height: 20px;
          letter-spacing: 1px;
          span{
            padding: 0 16px 0 4px;
          }
          img{
            width: 18px;
            height: 18px;
            display: inline-block;
            vertical-align: middle;
            margin-top: -2px;
          }
        }
      }
    }
  }
  
  
  .video-box{
    width: 100%;
    overflow-x: auto;
    padding-top: 8px;
    border-bottom: 1px solid #E8E8E8;
    margin-bottom: 24px;
    height: 185px;
    &:last-child{
      border: none;
    }
    .video-item{
      width: 220px;
      height: 140px;
      margin-right: 8px;
      float: left;
      .art-video{
        width: 100%;
      }
      p{
        height: 20px;
        font-size: 14px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #1F2329;
        line-height: 20px;
        letter-spacing: 1px;
        text-align: center;
        margin-top: 8px;
        margin-bottom: 24px;
      }
    }
  }

  .cell-state {
    .rda-task-state {
        display: inline-block;
        width: 43px;
        height: 18px;
        line-height: 18px;
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #FFFFFF;
        text-align: center;
        background: #1F7AFF;
        border-radius: 2px;
    }
    .dqr {
        background: #DC3290;
    }
    .ddd {
        background: #3369FF;
    }
    .dwc {
        background: #1FAE66;
    }
    .yqx {
        background: #D6D6D6;
    }
    .yjj {
        background: #ADAA00;
    }
    .ywc {
        background: #515157;
    }
  }
</style>