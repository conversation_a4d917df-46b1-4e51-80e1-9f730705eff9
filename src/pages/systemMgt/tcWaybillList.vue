<template>
    <div class="content-box">
        <div class="flex-box flex-column content">
            <div class="search-box flex-box">
                <el-form class="flex-item" ref="searchForm" :inline="true" :model="searchForm">
                    <el-form-item label="运单编号：">
                        <el-input v-model="searchForm.waybillCode" clearable
                        placeholder="请输入" 
                        style="width: 180px" 
                        />
                    </el-form-item>
                    
                    <!-- <el-form-item label="状态：">
                        <el-checkbox-group v-model="searchForm.fztList">
                            <el-checkbox v-for="item in fztStatusOpts" :key="item" :label="item"></el-checkbox>
                        </el-checkbox-group>
                    </el-form-item> -->
                    
                    <el-form-item>
                        <el-button type="primary" @click="handleFilter">搜索</el-button>
                        <el-button type="text" icon="el-icon-refresh-right" @click="resetForm()">重置</el-button>
                    </el-form-item>
                </el-form>
                <el-button type="primary" @click="batchUploadClick">生成委托单</el-button>
            </div>
        
            <div class="flex-item overHide">
                <div class="scroll-div">
                <el-table
                    :data="tableData"                
                    :key="1"
                    :row-style="cellStyle"
                    @selection-change="taskSelectionChange"
                    >
                    <el-table-column type="selection" align="center" :selectable="checkSelectable"></el-table-column>
                    <template v-for="(item, index) in tableColumn">
                        <el-table-column
                            v-if="item.prop === 'state'"
                            :prop="item.prop" 
                            :label="item.label" 
                            :fixed="item.fixed" 
                            :width="item.width || ''"
                            :formatter="item.formatter"
                            :show-overflow-tooltip="true"
                            align="center" 
                        >
                            <template slot-scope="scope">
                                <div class="cell-state">
                                    <label v-if="scope.row.state == 0" class="zt-font zf">作废</label>
                                    <label v-if="scope.row.state == 1" class="zt-font dyy">待预约</label>
                                    <label v-if="scope.row.state == 2" class="zt-font dcf">待出发</label>
                                    <label v-if="scope.row.state == 3" class="zt-font ycf">已出发</label>
                                    <label v-if="scope.row.state == 4" class="zt-font ddgb">等待过磅</label>
                                    <label v-if="scope.row.state == 5" class="zt-font gbz">过磅中</label>
                                    <label v-if="scope.row.state == 6" class="zt-font ddxl">等待卸料</label>
                                    <label v-if="scope.row.state == 7" class="zt-font xlz">卸料中</label>
                                    <label v-if="scope.row.state == 8" class="zt-font ddcc">等待出厂</label>
                                    <label v-if="scope.row.state == 9" class="zt-font ywc">已完成</label>
                                    <label v-if="scope.row.state == 10" class="zt-font zf">拒收</label>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column
                            v-else
                            :prop="item.prop" 
                            :label="item.label" 
                            :fixed="item.fixed" 
                            :width="item.width || ''"
                            :formatter="item.formatter"
                            :show-overflow-tooltip="true"
                            align="center" 
                        >
                        </el-table-column>
                    </template>
                    
                    
                    <!-- <el-table-column width="220" label="操作" align="center" key="handle" :resizable="false">
                        <template slot-scope="scope">
                            <el-button type="text" size="small" @click="printSigle(scope.row)">打印</el-button>
                            <el-button type="text" size="small" @click="gotoDetail(scope.row)">详情</el-button>
                        </template>
                    </el-table-column> -->
                </el-table>
                </div>
            </div>
            <div class="mt16 mb4">
                <Pagination
                :total="total" 
                :pageNum="pageObj.pageNum" 
                :pageSize="pageObj.pageSize" 
                @getData="initData" 
                />
            </div>
        </div>

    </div>
</template>  

<script>
import Pagination from "@/components/Pagination/index.vue";
import moment from "moment";
export default {
    components: {
        Pagination,
    },
    data() {
        return {
            searchForm: {
                waybillCode: '', // 运单编号
            },
            pageObj: {
                pageNum: 1, // 页数
                pageSize: 10, // 条数
            },
            total: 1,
            tableData: [],
            taskSelects: [],
            tableColumn: [
                {
                    label: '运单编号',
                    prop: 'waybillCode',
                    width: '120'
                },
                {
                    label: '承运司机',
                    prop: 'driverName',
                    formatter(row, column) {
                        return `${row.driverName || '--'}/${row.driverTel || '--'}`;
                    }
                },
                {
                    label: '承运车辆',
                    prop: 'vehicleNum',
                    formatter(row, column) {
                        return `${row.vehicleNum || '--'}/${row.vehiclePlate || '--'}`;
                    }
                },
                {
                    label: '材料名称',
                    prop: 'projectName',
                },
                {
                    label: '材料规格',
                    prop: 'projectSpecs',
                },
                {
                    label: '生产厂家',
                    prop: 'factory',
                },
                {
                    label: '运输数量(吨)',
                    prop: 'transportQuantity',
                },
                {
                    label: '签收数量(吨)',
                    prop: 'signQuantity',
                },
                {
                    label: '异常数量(吨)',
                    prop: 'abnormalQuantity',
                },
                {
                    label: '运单状态',
                    prop: 'state',
                    width: '80'
                },
                {
                    label: '是否有异常',
                    prop: 'hasAbnormal',
                    formatter(row, column) {
                        return row.hasAbnormal == '1' ? '有' : '否';
                    }
                },
                {
                    label: '异常信息',
                    prop: 'abnormalInfo',
                    width: '180'
                },
                {
                    label: '进场时间',
                    prop: 'entryTime',
                },
            ],
        }
    },

    mounted() {
        this.initData();
    },

    methods: {
        cellStyle({ row, column, rowIndex, columnIndex }) {
            if (row.isQuick == '0' && row.isBatch == '0') {
                return { color: '#FF4500' }
            } else {
                return { color: '#606266' }
            }
        },
        checkSelectable(row) {
            return row.isQuick == '0' && row.isBatch == '0';
        },
        handleFilter() {
            this.initData(1);
        },
        resetForm(){
            this.searchForm = {
                waybillCode: '',
            };
            this.initData();
        },
        initData(opageNum, opageSize) {
            if (opageNum) this.pageObj.pageNum = opageNum;
            if (opageSize) this.pageObj.pageSize = opageSize;

            const params ={
                ...this.pageObj,
                params: {
                    ...this.searchForm,
                },
            }

            this.$api.getTCWaybillList({
                ...params,
            }, this).then(res => {
                if(res.succ){
                    this.tableData = res.data.list;
                    this.total = res.data.total;
                }else{
                    this.$message.error(res.msg || '查询失败')
                }
            });
        },

        taskSelectionChange(val) {
            this.taskSelects = val;
        },

        batchUploadClick() {
            if (this.taskSelects.length == 0) {
                // 弹出提示
                this.$message({
                    showClose: true,
                    message: "请选择运单",
                    type: "warning",
                });
                return;
            }
            let waybillIds = [];
            waybillIds = this.taskSelects.map(item => item.id);
            this.$api.addTCWaybillExperimentBatch({
                waybillIds: waybillIds
            }, this).then((res) => {
                if (res.succ) {
                    this.$message({
                        showClose: true,
                        message: "操作成功",
                        type: "success",
                    });
                    this.initData();
                }
            });
        },
    },
}
</script>

<style lang="scss" scoped>
 ::v-deep .el-button{
    padding-left: 13px;
    padding-right: 13px;
  }
  .el-form-item{
    margin-bottom: 8px;
  }
  ::v-deep .el-form--inline{
    .el-form-item{
      margin-right: 24px;
      margin-bottom: 0;
      &:last-child{
        margin: 0;
      }
    }
  }
  ::v-deep .el-table{
    .expanded,.expanded:hover{
      background-color: #FFFBD9;
      
    }
    .expanded + tr{
      background-color: #FFFBD9;
      td{
        background-color: #FFFBD9;
      }
    }
    
    .table-child-box{
      margin: 16px;
      padding: 16px;
      background: #FFFFFF;
    }
  }
  
  .content-box{
    padding: 16px;
  }
  .content{
    width: 100%;
    height: 100%;
    padding: 16px;
    background: #FFFFFF;
    border-radius: 16px;
  }
  
  .search-box{
    padding-bottom: 16px;
    line-height: 40px;
  }
  .cell-state {
    .rda-task-state {
        display: inline-block;
        padding-left: 10px;
        padding-right: 10px;
        height: 18px;
        line-height: 18px;
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #FFFFFF;
        text-align: center;
        background: #1F7AFF;
        border-radius: 2px;
    }
    .zt-font {
        font-size: 14px;
        font-weight: 400;
        letter-spacing: 0px;
        color: rgba(255, 255, 255, 1);
        text-align: center;
        vertical-align: top;
        border-radius: 4px;
        padding: 0px 7px;
    }
    .zf {
        background: rgb(118, 121, 128);
    }
    .dyy {
        background: rgba(255, 99, 99, 1);
    }
    .dcf {
        background: rgba(149, 90, 255, 1);
    }
    .ycf {
        background: rgba(196, 190, 0, 1);
    }
    .ddgb {
        background: rgba(0, 156, 255, 1);
    }
    .gbz {
        background: rgba(84, 183, 38, 1);
    }
    .ddxl {
        background: rgba(0, 171, 142, 1);
    }
    .xlz {
        background: rgba(0, 199, 205, 1);
    }
    .ddcc {
        background: rgba(119, 100, 141, 1);
    }
    .ywc {
        background: rgba(88, 108, 156, 1);
    }
  }
</style>