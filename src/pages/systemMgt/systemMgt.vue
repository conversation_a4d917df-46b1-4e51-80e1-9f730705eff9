<template>
    <div class="content-box">
        <div class="flex-box flex-column content">
            <div v-for="(item, index) in formData" :key="index">                
                <div class="flex-row content-row">
                    <span class="content-title" style="width: 150px;">{{ item.waringName }}：</span>
                    
                    <div class="flex-row" style="margin-left: 14px; ">
                        <span style="margin-right: 6px;">提前</span>
                        <el-input :disabled="!isOpen" oninput="value=value.replace(/[^\d]/g,'')" v-model="item.warningTime" style="width: 140px; height: 40px;"  />
                        <span style="margin-right: 64px; margin-left: 6px;">天预警</span>
                    </div>
                    
                    <i style="color: #FF8D19;" class="el-icon-warning-outline" />
                    <span style="margin-right: 64px; margin-left: 6px; color: red;">如输入负数或不输入，则不开启预警</span>
                </div>
            </div>

            <div style="margin-left: calc(100% - 200px); margin-top: 40px; position: absolute; bottom: 20px;">
                <el-button type="primary" @click="cancelTarget" plain>{{ isOpen ? '取消' : '编辑' }}</el-button>
                <el-button type="primary" @click="saveTarget">保存</el-button>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    data() {
        return {
            formData: [],
            isOpen: false,
        }
    },

    mounted() {
        this.queryWarningAllResp();
    },

    methods: {
        
        queryWarningAllResp() {
            this.$api.queryWarningAll({}, this).then(res => {
                if (res.code == 1) {
                    this.formData = res.data.list;
                }
            })
        },

        saveTarget() {
            let self = this;
            this.$confirm('确认保存吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                self.updateWarningResp();
            });
        },

        updateWarningResp() {
            console.log(">>this.formData>>", this.formData)
            this.$api.updateWarning(this.formData, this).then(res => {
                if (res.succ) {
                    this.$message({
                        showClose: true,
                        message: "操作成功",
                        type: "success",
                    });
                    this.cancelTarget();
                    this.queryWarningAllResp();
                }
            });
        },

        cancelTarget() {
            this.isOpen = !this.isOpen;
        }
    }
}
</script>

<style lang="scss" scoped>
.content-box{
    padding: 24px 16px;
}
.content{
    width: 100%;
    height: 100%;
    padding: 16px;
    background: #FFFFFF;
    border-radius: 16px;
    overflow: auto;
    position: relative;
    .content-title {
        font-weight: 600;
        font-size: 16px;
        color: #1F2329;
    }
    .content-row {
        height: 40px;
        margin-top: 16px;
        justify-content: flex-start;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #1F2329;
    }
}
</style>