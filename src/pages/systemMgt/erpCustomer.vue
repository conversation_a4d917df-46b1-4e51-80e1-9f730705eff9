<template>
    <div class="content-box">
      <div class="flex-box flex-column content">
        <div class="search-box flex-box">
          <el-form class="flex-item" ref="searchForm" :inline="true" :model="searchForm" :disabled="loading">
            <el-form-item label="客户姓名：">
              <el-input v-model="searchForm.customerName" clearable
                placeholder="请输入客户姓名" 
                style="width: 180px" 
              />
            </el-form-item>
            
            
            <el-form-item label="手机号码：">
              <el-input v-model="searchForm.linkPhone" clearable
                placeholder="请输入手机号码" 
                style="width: 180px" 
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" :disabled="loading" @click="handleFilter">搜索</el-button>
              <el-button type="text" icon="el-icon-refresh-right" :disabled="loading" @click="resetForm()">重置</el-button>
            </el-form-item>
          </el-form>
          <!-- <el-button v-if="roleId == '5'" type="primary" @click="initCustomer()">初始化协会客户</el-button> -->
        </div>
        
        <div class="flex-item overHide">
          <div class="scroll-div">
            <el-table
              :data="tableData"
              v-loading="loading"
              style="width: 100%">

              <template v-for="item in tableColumn" >
                <af-table-column v-if="item.prop == 'isUpload'" :key="item.prop" :label="item.label" :fixed="item.fixed" align="center" >
                  <template slot-scope="scope">
                    <el-row class="cell-state">
                      <label v-if="scope.row.isUpload == '1'" class="rda-task-state yqx">已上传</label>
                      <label v-else class="rda-task-state ddd">未上传</label>
                    </el-row>
                  </template>
                </af-table-column>
                <af-table-column v-else 
                :key="item.prop" :prop="item.prop" 
                :label="item.label" 
                :formatter="item.formatter"
                :fixed="item.fixed" 
                :width="item.width || ''"
                align="center" 
                />
              </template>

              <af-table-column width="200" label="操作" align="center" key="handle" :resizable="false">
                <template slot-scope="scope">
                  <el-button  type="text" size="small" @click="handleSetTarget(scope.row)">修改</el-button>
                  <el-button type="text" size="small" v-if="!(scope.row.isUpload == 1)" @click="uploadRow(scope.row)">上传</el-button>

                </template>
              </af-table-column>
            </el-table>
          </div>
        </div>
        <div class="mt16 mb4">
          <Pagination
            :total="total" 
            :pageNum="pageObj.pageNum" 
            :pageSize="pageObj.pageSize" 
            @getData="initData" 
          />
        </div>
      </div>
      
      
      <DrawerForm
        :formContent="formContent"
        ref="drawerForm"
        @saveTarget="saveTarget"
      >
      </DrawerForm>
    </div>
  </template>
  
  <script>
  import Pagination from "@/components/Pagination/index.vue";
  import DrawerForm from "@/components/drawerForm.vue";
  export default {
    name:'userMgt',
    components: {
      Pagination,
      DrawerForm
    },
    data() {
      return {
        // addApi: 'addUser',
        // delApi: 'delUser',
        getListApi: 'queryCustomerPage',
        updateApi: 'updateErpCustomer',
        roleId: this.$store.state.loginStore.userInfo.roleId,
        loading: false,
        
        searchForm: {},
        
        tableColumn: [
            {
                label: '客户编号',
                prop: 'customerNo',
                width: 150,
            },
            {
                label: '客户名称',
                prop: 'customerName',
                width: 200,
            },
            {
                label: '客户地址',
                prop: 'customerAddress',
                width: 250,
            },
            {
                label: '法定代表人',
                prop: 'legalPerson',
            },
            {
                label: '法定委托人',
                prop: 'consigner',
            },
            {
                label: '联系人',
                prop: 'linkman',
            },
            {
                label: '联系电话',
                prop: 'linkPhone',
            },
            {
              label: '状态',
              prop: 'isUpload',
              width: 80,
            },
            {
                label: '创建时间',
                prop: 'createTime',
                width: 170,
            },
        ],
        pageObj: {
          pageNum: 1, // 页数
          pageSize: 10, // 条数
        },
        total: 1,
        tableData: [],
        
        activeRow: {},//编辑对象
        
        formContent: [
            {
                type: 'input',
                label: '客户编号',
                prop: 'customerNo',
                disabled: true,
            },
            {
                type: 'input',
                label: '法定代表人',
                prop: 'legalPerson',
            },
            {
                type: 'input',
                label: '法定委托人',
                prop: 'consigner',
            },
            {
                type: 'input',
                label: '联系人',
                prop: 'linkman',
            },
            {
                type: 'input',
                label: '联系电话',
                prop: 'linkPhone',
            },
            {
                type: 'input',
                label: '客户地址',
                prop: 'customerAddress',
            }
        ],
      };
    },
    
    created() {
      this.initData();
    },
    methods: {
      
      handleFilter() {
        console.log(this.searchForm)
        this.initData(1);
      },
      resetForm(){
        this.searchForm = {};
        this.initData(1);
      },
      initData(opageNum, opageSize){
        this.loading = true;
        if (opageNum) this.pageObj.pageNum = opageNum;
        if (opageSize) this.pageObj.pageSize = opageSize;
          
        const params ={
          ...this.pageObj,
          params: this.searchForm
        }
        //获取列表
        this.$api[this.getListApi](params, this).then(res => {
          this.loading = false;
          if(res.succ){
            this.tableData = res.data.list;
            this.total = res.data.total;
          }else{
            this.$message.error(res.msg || '查询失败')
          }
        })
      },
      handleSetTarget(row){
        if (row) {
          this.activeRow = row;
          this.$refs.drawerForm.initData(row);
        }
      },
      saveTarget(formData){
        if(formData.id){//修改this.activeRow
          this.$api[this.updateApi](formData, this).then((res) => {
            if (res.succ) {
              this.$message({
                showClose: true,
                message: "修改成功",
                type: "success",
              });
              this.$refs.drawerForm.handleClose();
              this.initData();
            }
          });
        }
      },

      uploadRow(row) {
        this.$api.uploadCustomer({
          id: row.id
        }, this).then((res) => {
            if (res.succ) {
              this.$message({
                showClose: true,
                message: "上传成功",
                type: "success",
              });
              this.initData();
            }
          });
      },

      initCustomer() {
        this.$confirm('确认初始化协会客户吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
            this.$api.syncLaboratoryCustomInfo().then(res => {
                if (res.code == 1) {
                    this.$message({
                        showClose: true,
                        message: "操作成功",
                        type: "success",
                    });

                    this.initData();
                }
            })
        });
      }
    },
  };
  </script>
  
  <style scoped lang="scss">
    .multi-form-item-box{
      padding: 0 0 4px 0;
      // display: flex;
      // justify-content: space-between;
      .el-select,.el-input{
        margin-right: 20px;
      }
    }
    ::v-deep .el-button{
      padding-left: 13px;
      padding-right: 13px;
    }
    .el-form-item{
      margin-bottom: 8px;
    }
    ::v-deep .el-form--inline{
      .el-form-item{
        margin-right: 24px;
        margin-bottom: 0;
        &:last-child{
          margin: 0;
        }
      }
    }
    ::v-deep .el-table{
      .expanded,.expanded:hover{
        background-color: #FFFBD9;
        
      }
      .expanded + tr{
        background-color: #FFFBD9;
        td{
          background-color: #FFFBD9;
        }
      }
      
      .table-child-box{
        margin: 16px;
        padding: 16px;
        background: #FFFFFF;
      }
    }
    
    .content-box{
      padding: 16px;
    }
    .content{
      width: 100%;
      height: 100%;
      padding: 16px;
      background: #FFFFFF;
      border-radius: 16px;
    }
    
    .search-box{
      padding-bottom: 16px;
      line-height: 40px;
    }
    .cell-state {
      .rda-task-state {
          display: inline-block;
          width: 43px;
          height: 18px;
          line-height: 18px;
          font-size: 12px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #FFFFFF;
          text-align: center;
          background: #1F7AFF;
          border-radius: 2px;
      }
      .dqr {
          background: #DC3290;
      }
      .ddd {
          background: #3369FF;
      }
      .dwc {
          background: #1FAE66;
      }
      .yqx {
          background: #D6D6D6;
      }
      .yjj {
          background: #ADAA00;
      }
      .ywc {
          background: #515157;
      }
    }
  </style>