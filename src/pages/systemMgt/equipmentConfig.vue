<template>
  <div class="content-box">
    <div class="flex-box flex-column content">
      <div class="search-box flex-box">
        <el-form class="flex-item" ref="searchForm" :inline="true" :model="searchForm" :disabled="loading">
          <el-form-item label="设备名称：">
            <el-input v-model="searchForm.equipmentName" clearable
              placeholder="请输入设备名称" 
              style="width: 180px" 
            />
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" :disabled="loading" @click="handleFilter">搜索</el-button>
            <el-button type="text" icon="el-icon-refresh-right" :disabled="loading" @click="resetForm()">重置</el-button>
          </el-form-item>
        </el-form>
        <el-button type="primary" @click="batchClick()">批量修改周期</el-button>
        <el-button type="primary" @click="handleSetTarget()">新增设备</el-button>
        <el-button type="primary" @click="printListClick()">打印设备一览表</el-button>
        <el-button type="primary" @click="printAppraisalCycleClick()">鉴定周期一览表</el-button>
        <el-button type="primary" @click="importEquipmentExcelClick()">
          <el-upload
              v-show="false"
              ref="uploadImport"
              :action="baseUrl + '/devops/importEquipmentExcel'"
              :show-file-list="false"
              :auto-upload="true"
              :before-upload="importClickBefore"
              :on-success="importClickSuccess">
          </el-upload>
          导入设备
        </el-button>
        <el-button type="primary" @click="downloadTemplate()">下载模板</el-button>
      </div>
      
      <div class="flex-item overHide">
        <div class="scroll-div">
          <el-table
            :data="tableData"
            v-loading="loading"
            style="width: 100%"
            @selection-change="selectionChange"
          >
            <af-table-column type="selection" align="center"></af-table-column>
            <af-table-column
              v-for="item in tableColumn" 
              :key="item.prop" :prop="item.prop" 
              :label="item.label" 
              :formatter="item.formatter"
              :fixed="item.fixed" 
              :width="item.width || ''"
              :show-overflow-tooltip="true"
              align="center" 
            />
            <af-table-column width="280" label="操作" align="center" key="handle" fixed="right" :resizable="false">
              <template slot-scope="scope">
                <el-button type="text" size="small" @click="handleSetTarget(scope.row)">修改</el-button>
                <el-button type="text" size="small" @click="gotoDetail(scope.row)">详情</el-button>
                <el-button type="text" size="small" @click="addStandardizingRecord(scope.row)">新增校准记录</el-button>
                <el-button type="text" size="small" style="color: #ff0000;" @click="handleDel(scope.row)">删除</el-button>
              </template>
            </af-table-column>
          </el-table>
        </div>
      </div>
      <div class="mt16 mb4">
        <Pagination
          :total="total" 
          :pageNum="pageObj.pageNum" 
          :pageSize="pageObj.pageSize" 
          @getData="initData" 
        />
      </div>
    </div>
    
    
    <DrawerForm
      :formContent="formContent"
      ref="drawerForm"
      @saveTarget="saveTarget"
    >
    </DrawerForm>

    <DrawerForm
      :formContent="batchFormContent"
      ref="batchForm"
      dialogTitle="批量修改"
      @saveTarget="saveBatchTarget"
    >
    </DrawerForm>
    
    
    <DrawerForm
      :formContent="formRecordContent"
      ref="recordForm"
      @drawerClose="handleRecordClose"
      @saveTarget="saverecordTarget"
    >
      <template #customLastForm>
          <el-form-item
              :label="`校准附件：`"
          >
              <div class="img-box">
                  <el-upload
                  :action="baseUrl + '/upload/file'"
                  :file-list="attachmentsImgList"
                  :on-success="handleAttachmentsImgSuccess"
                  :on-remove="handleAttachmentsImgRemove">
                  <i class="el-icon-plus avatar-uploader-icon" style="color: #424E73;">点击添加附件</i>
                  </el-upload>
              </div>
          </el-form-item>
      </template>
    </DrawerForm>
  </div>
</template>

<script>
import { equipmentColumn as tableColumn } from "./config.js"
import Pagination from "@/components/Pagination/index.vue";
import DrawerForm from "@/components/drawerForm.vue";
import getOpt from "@/common/js/getListData.js"

export default {
  name:'equipmentConfig',
  components: {
    Pagination,
    DrawerForm
  },
  data() {
    return {
      addApi: 'addEquipment',
      delApi: 'delEquipment',
      updateApi: 'setEquipment',
      getListApi: 'getEquipmentList',
      roleId: this.$store.state.loginStore.userInfo.roleId,
      baseUrl: process.env.NODE_ENV === 'development' ? "/api" : "/mes-platform",
      filePrefix: this.$store.state.loginStore.userInfo.filePrefix,
      
      
      loading: false,
      // 设置时间选择
      pickerOptions: {
        disabledDate(time) {
          let deadline = Date.now() - 60 * 60 * 1000;
          return time.getTime() > deadline //
        },
      },
      searchForm: {},
      categoryList: [],
      userAllOpt: [],
      
      tableColumn: tableColumn,
      pageObj: {
        pageNum: 1, // 页数
        pageSize: 10, // 条数
      },
      total: 1,
      tableData: [],
      
      activeRow: {},//编辑对象
      
      formContent: [],
      rules: {},

      equipmentList: [],
      batchFormContent: [
        {
          type: 'number',
          label: '检定周期',
          prop: 'verificationCycle',
          slotVal: '天',
          slot: 'append',
          required: true
        },
        {
          type: 'number',
          label: '预警设置',
          prop: 'warningCycle',
          slotVal: '天',
          slot: 'append',
          required: true
        },
      ],
      
      formRecordContent:[],
      recordEquipmentId: '',
      
      attachmentsImgList: [],
      attachments: [],
    };
  },
  
  created() {
    this.initData();
    this.getMaterialType();
  },
  methods: {
    async getMaterialType(){
      this.userAllOpt = await getOpt.getUserAll(this)
      this.userAllOpt = this.userAllOpt.map(item => {
        item.value = item.label;
        return item
      })
      const res = await this.$api.getDictValue({
        dictCode: 'TEST_PROJECT'
      },this)
      
      if(res.succ){
        this.categoryList = res.data.list.map(i => {
          return {
            label: i.dictValueName,
            value: i.dictValueCode
          }
        })
        this.formContent = [
          {
            type: 'input',
            label: '设备编号',
            prop: 'equipmentNo',
            required: true
          },
          {
            type: 'input',
            label: '设备名称',
            prop: 'equipmentName',
            required: true
          },
          {
            type: 'input',
            label: '型号规格',
            prop: 'equipmentType',
          },
          {
            type: 'input',
            label: '出厂编号',
            prop: 'factoryNumber',
          },{
            type: 'input',
            label: '生产厂家',
            prop: 'manufacturer',
          },{
            type: 'input',
            label: '准确度/技术参数',
            prop: 'technicalParameter',
          },{
            type: 'number',
            label: '价格',
            placeholder: '单位（元）',
            slot: 'append',
            slotVal: '元',
            prop: 'price',
          },{
            type: 'datetime',
            label: '购置日期',
            prop: 'buyTime',
            format: 'yyyy-MM-dd HH:mm:ss',
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
          },{
            type: 'datetime',
            label: '启用时间',
            prop: 'enableTime',
            format: 'yyyy-MM-dd HH:mm:ss',
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
          },{
            type: 'input',
            label: '存放地点',
            prop: 'saveAddress',
          },{
            type: 'input',
            label: '设备状态',
            prop: 'equipmentStatus',
          },{
            type: 'select',
            label: '设备类别',
            prop: 'equipmentCategory',
            required: true,
            options: [{
              label: 'A类',
              value: 'A'
            },{
              label: 'B类',
              value: 'B'
            },{
              label: 'C类',
              value: 'C'
            }]
          },{
            type: 'select',
            label: '保管员',
            prop: 'custodyUserName',
            required: true,
            options: this.userAllOpt
          },
          {
            type: 'number',
            label: '检定周期',
            prop: 'verificationCycle',
            slotVal: '天',
            slot: 'append',
            required: true
          },
          {
            type: 'number',
            label: '预警设置',
            prop: 'warningCycle',
            slotVal: '天',
            slot: 'append',
            required: true
          },
          // {
          //   type: 'checkbox',
          //   label: '选择试验项目',
          //   prop: 'testProjectJson',
          //   // vModel: 'obj',
          //   // props:{
          //   //   label: 'name',
          //   //   value: 'no',
          //   // },
          //   options: this.categoryList
          // }
        ]
      }
    },
    
    
    handleFilter() {
      console.log(this.searchForm)
      this.initData(1);
    },
    resetForm(){
      this.searchForm = {};
      this.initData(1);
    },
    initData(opageNum, opageSize){
      this.loading = true;
      if (opageNum) this.pageObj.pageNum = opageNum;
      if (opageSize) this.pageObj.pageSize = opageSize;
        
      const params ={
        ...this.pageObj,
        params: this.searchForm
      }
      //获取列表
      this.$api[this.getListApi](params, this).then(res => {
        this.loading = false;
        if(res.succ){
          this.tableData = res.data.list;
          this.total = res.data.total;
        }else{
          this.$message.error(res.msg || '查询失败')
        }
      })
    },
    handleDel(row){
      this.$confirm("删除后不能恢复，确定要删除吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        //删除
        this.$api[this.delApi]({
          id: row.id
        }, this).then((res) => {
          if (res.succ) {
            this.$message({
              showClose: true,
              message: "删除成功",
              type: "success",
            });
            if(this.tableData.length == 1 && this.pageObj.pageNum > 1){
              this.pageObj.pageNum = this.pageObj.pageNum -1
            }
            this.initData();
          }
        });
      }).catch(() => {
        this.$message({
          type: "info",
          message: "已取消删除",
        });
      });
    },
    gotoDetail(row) {
      // 跳转详情
      this.$router.push({
        path: "/systemMgt/equipmentDetial",
        query: {
          id: row.id,
        },
      });
    },
    //新增记录
    addStandardizingRecord(row){
      this.formRecordContent = [
        {
          type: 'input',
          label: '设备名称',
          prop: 'equipmentName',
          disabled: true,
        },{
          type: 'input',
          label: '报告编号',
          prop: 'reportNo',
        },{
          type: 'input',
          label: '校准单位',
          prop: 'calibrationUnit',
        },{
          type: 'datetime',
          label: '校准时间',
          prop: 'calibrationTime',
          format: 'yyyy-MM-dd HH:mm:ss',
          valueFormat: 'yyyy-MM-dd HH:mm:ss',
        },{
          type: 'input',
          label: '校准结果',
          prop: 'calibrationText',
        }
      ]
      this.$refs.recordForm.initData();
      this.recordEquipmentId = row.id;
      this.$refs.recordForm.setEditFormValue('equipmentName',row.equipmentName);
    },
    
    handleSetTarget(rowInt){
      this.formContent = [
        {
          type: 'input',
          label: '设备编号',
          prop: 'equipmentNo',
          required: true
        },
        {
          type: 'input',
          label: '设备名称',
          prop: 'equipmentName',
          required: true
        },
        {
          type: 'input',
          label: '型号规格',
          prop: 'equipmentType',
        },
        {
          type: 'input',
          label: '出厂编号',
          prop: 'factoryNumber',
        },{
          type: 'input',
          label: '生产厂家',
          prop: 'manufacturer',
        },{
          type: 'input',
          label: '准确度/技术参数',
          prop: 'technicalParameter',
        },{
          type: 'number',
          label: '价格',
          placeholder: '单位（元）',
          slot: 'append',
          slotVal: '元',
          prop: 'price',
        },{
          type: 'number',
          label: '数量',
          prop: 'number',
        },{
          type: 'date',
          label: '购置日期',
          prop: 'buyTime',
          format: 'yyyy-MM-dd',
          valueFormat: 'yyyy-MM-dd HH:mm:ss',
        },{
          type: 'date',
          label: '出厂时间',
          prop: 'factoryTime',
          format: 'yyyy-MM-dd',
          valueFormat: 'yyyy-MM-dd HH:mm:ss',
        },{
          type: 'date',
          label: '启用时间',
          prop: 'enableTime',
          format: 'yyyy-MM-dd',
          valueFormat: 'yyyy-MM-dd HH:mm:ss',
        },{
          type: 'input',
          label: '分度值',
          prop: 'division',
        },{
          type: 'input',
          label: '精度等级',
          prop: 'accuracyClass',
        },{
          type: 'input',
          label: '存放地点',
          prop: 'saveAddress',
        },{
          type: 'input',
          label: '安放地点',
          prop: 'placementLocation',
        },{
          type: 'input',
          label: '设备状态',
          prop: 'equipmentStatus',
        },{
            type: 'select',
            label: '设备类别',
            prop: 'equipmentCategory',
            required: true,
            options: [{
              label: 'A类',
              value: 'A'
            },{
              label: 'B类',
              value: 'B'
            },{
              label: 'C类',
              value: 'C'
            }]
        },{
          type: 'select',
          label: '保管员',
          prop: 'custodyUserName',
          required: true,
          options: this.userAllOpt
        },
        {
          type: 'number',
          label: '检定周期',
          prop: 'verificationCycle',
          slotVal: '天',
          slot: 'append',
          required: true
        },
        {
          type: 'number',
          label: '预警设置',
          prop: 'warningCycle',
          slotVal: '天',
          slot: 'append',
          required: true
        },
        // {
        //   type: 'checkbox',
        //   label: '选择试验项目',
        //   prop: 'testProjectJson',
        //   // vModel: 'obj',
        //   // props:{
        //   //   label: 'name',
        //   //   value: 'no',
        //   // },
        //   options: this.categoryList
        // },
        {
          type: 'input',
          label: '备注',
          prop: 'notes',
        }
      ]
      //this.activeRow = row;
      if(rowInt){
        let row = JSON.parse(JSON.stringify(rowInt))
        // if(row && row.testProjectJson){
        //   row.testProjectJson = row.testProjectJson.map(item => {
        //     return item.no
        //   })
        // }
        this.$refs.drawerForm.initData(row);
      }else{
        this.$refs.drawerForm.initData();
      }
    },
    
    saveTarget(formData) {
      // console.log(formData.testProjectJson)
      // let testProjectJson = []
      // formData.testProjectJson.forEach((item) => {
      //   let testProjectName = getOpt.getNameFromId(item, this.categoryList)
      //   if (!testProjectName) {
      //     return
      //   }
      //   testProjectJson.push({no:item,name:testProjectName})
      // })
      // formData.testProjectJson = testProjectJson
      // console.log(formData.testProjectJson)
        if (formData.id) {
          //修改this.activeRow
          this.$api[this.updateApi](formData, this).then((res) => {
            if (res.succ) {
              this.$message({
                showClose: true,
                message: '修改成功',
                type: 'success'
              })
              this.$refs.drawerForm.handleClose()
              this.initData()
            }
          })
        } else {
          this.$api[this.addApi](formData, this).then((res) => {
            if (res.succ) {
              this.$message({
                showClose: true,
                message: '添加成功',
                type: 'success'
              })
              this.initData()
              this.$refs.drawerForm.handleClose()
            }
          })
        }
    },

    selectionChange(val) {
      this.equipmentList = val;
    },

    batchClick() {
      if (!this.equipmentList.length) {
        this.$message({
          showClose: true,
          message: "请选择设备",
          type: "warning",
        });
        return;
      }

      this.$refs.batchForm.initData();
    },

    saveBatchTarget(formData) {
      
      let parma = {
        verificationCycle: formData.verificationCycle,
        warningCycle: formData.warningCycle,
        ids: this.equipmentList.map(item => item.id)
      }
      this.$api.updateBatchCycle(parma, this).then((res) => {
        if (res.succ) {
          this.$message({
            showClose: true,
            message: "批量修改成功",
            type: "success",
          });
          this.$refs.batchForm.handleClose();
          this.initData();
        }
      });
    },
    
    saverecordTarget(formData){
      console.log(formData)
      formData.equipmentId = this.recordEquipmentId
      formData.calibrationAnnex = this.attachments.join(",");
      this.$api.addCalibrationRecord(formData, this).then((res) => {
        if (res.succ) {
          this.$message({
            showClose: true,
            message: "添加成功",
            type: "success",
          });
          this.$refs.recordForm.handleClose();
          this.initData();
        }
      });
    },

    handleRecordClose() {
      this.attachmentsImgList = [];
      this.attachments = [];
    },

    handleAttachmentsImgRemove(file, fileList) {
        this.attachmentsImgList = fileList;
        this.attachments = [];
        fileList.map(item => {
            this.attachments.push(item.name);
        });
    },
    handleAttachmentsImgSuccess(response) {
        if (response.code == 1) {
            this.attachmentsImgList.push({
                name: response.data.fileName,
                url: response.data.filePath,
            });
            this.attachments.push(response.data.fileName);
        }
    },

    printListClick() {
      let routeData = this.$router.resolve({
        path: "/printEquipmentList",
        query: {}
      });
      window.open(routeData.href, '_blank');
    },
    printAppraisalCycleClick() {
      let routeData = this.$router.resolve({
        path: "/printAppraisalCycle",
        query: {}
      });
      window.open(routeData.href, '_blank');
    },

    importEquipmentExcelClick() {
      this.$refs.uploadImport.$refs['upload-inner'].handleClick()
    },

    importClickBefore(file, fileList) {
      return new Promise((resolve, reject) => {
        this.$confirm('确认导入设备数据吗？')
        .then(_ => {
          resolve(true);
        })
        .catch(_ => {
          reject(false);
        });
      })
    },

    importClickSuccess(response, file, fileList) {
      if (response.code == 1) {
        this.$message({
          message: "导入成功",
          type: "success",
        });
      }else{
        this.$message.error('导入失败，请重试')
      }
    },

    downloadTemplate() {
      this.$api.downloadCheckBatchConfigTemplate('', this).then(res => {
        if(res.succ){
          const url = res.data.data || '';
          window.open(url, '_blank');
        }else{
          this.$message.error(res.msg || '导出失败')
        }
      });
    }
  },
};
</script>

<style scoped lang="scss">
  .multi-form-item-box{
    padding: 0 0 4px 0;
    // display: flex;
    // justify-content: space-between;
    .el-select,.el-input{
      margin-right: 20px;
    }
  }
  ::v-deep .el-button{
    padding-left: 13px;
    padding-right: 13px;
  }
  .el-form-item{
    margin-bottom: 8px;
  }
  ::v-deep .el-form--inline{
    .el-form-item{
      margin-right: 24px;
      margin-bottom: 0;
      &:last-child{
        margin: 0;
      }
    }
  }
  ::v-deep .el-table{
    .expanded,.expanded:hover{
      background-color: #FFFBD9;
      
    }
    .expanded + tr{
      background-color: #FFFBD9;
      td{
        background-color: #FFFBD9;
      }
    }
    
    .table-child-box{
      margin: 16px;
      padding: 16px;
      background: #FFFFFF;
    }
  }
  
  .content-box{
    padding: 16px;
  }
  .content{
    width: 100%;
    height: 100%;
    padding: 16px;
    background: #FFFFFF;
    border-radius: 16px;
  }
  
  .search-box{
    padding-bottom: 16px;
    line-height: 40px;
  }
</style>