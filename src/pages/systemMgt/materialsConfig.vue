<!--
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-06-11 22:20:34
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-08-03 15:45:52
 * @FilePath: /quality_center_web/src/pages/systemMgt/specConfig.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div class="content-box">
        <div class="flex-box flex-column content">
            <div class="search-box flex-box">
                <el-form class="flex-item" ref="searchForm" :inline="true" :model="searchForm">
                    <el-form-item label="材料类型：">
                        <el-checkbox-group v-model="searchForm.materialsTypeList"
                        @change="handleFilter"
                        >
                            <el-checkbox v-for="item in materialsTypeOpts" :key="item.value" :label="item.value">{{item.label}}</el-checkbox>
                        </el-checkbox-group>
                    </el-form-item>
                    <el-form-item label="指标类型：">
                        <el-radio-group v-model="searchForm.isConfig" @change="handleFilter">
                            <el-radio :label="0">全部</el-radio>
                            <el-radio :label="1">未设置</el-radio>
                            <el-radio :label="2">已设置</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    
                    <el-form-item>
                        <el-button type="primary" @click="handleFilter">搜索</el-button>
                        <el-button type="text" icon="el-icon-refresh-right" @click="resetForm()">重置</el-button>
                    </el-form-item>
                </el-form>
            </div>

            <div class="flex-item overHide">
                <div class="scroll-div" style="height: calc(100% - 50px);">
                    <el-table
                        :data="tableData"
                        :row-style="cellStyle"
                        >
                        <template v-for="item in tableColumn">
                            <el-table-column

              :cell-class-name="nameColumnClass"
                                v-if="item.prop != 'specId'"
                                :prop="item.prop" 
                                :label="item.label" 
                                :fixed="item.fixed" 
                                :width="item.width || ''"
                                :formatter="item.prop === 'materialsConfigType' ? (row) => item.formatter(row,materialsTypeOpts) : item.formatter"
                                align="center" 
                            />
                            <el-table-column 
                                v-else 

              :cell-class-name="nameColumnClass"
                                :prop="item.prop"  
                                :label="item.label" 
                                :width="item.width || ''" 
                                align="center" 
                            >
                                <template slot-scope="scope">
                                    <el-row class="flex-row" style="justify-content: center;" v-if="scope.row.specId">
                                        <div style="width: 6px;height: 6px;background: #45B97B;border-radius: 6px;" />
                                        <div style="font-size: 14px;color: #1F2329;margin-left: 8px;">已设置</div>
                                    </el-row>
                                    <el-row class="flex-row" style="justify-content: center;" v-else>
                                        <div style="width: 6px;height: 6px;background: #E0E8EB;border-radius: 6px;" />
                                        <div style="font-size: 14px;color: #868E9B;margin-left: 8px;">未设置</div>
                                    </el-row>
                                </template>
                            </el-table-column>
                        </template>
                        
                        
                        <el-table-column width="220" label="操作" align="center" key="handle" :resizable="false">
                            <template slot-scope="scope">
                                <el-button type="text" size="small" @click="setMaterials(scope.row)">设置</el-button>
                                <!-- v-if="scope.row.isIronMark == 1"  绑定配合比 开关  -->
                                <el-button type="text" size="small" @click="materialsBindPhbNoResp(scope.row)">绑定配合比</el-button>
                                <el-button type="text" size="small" style="color: #ff0000;" @click="deleteMaterials(scope.row)">删除</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
                <div class="mt16 mb4">
                    <Pagination
                    :total="total" 
                    :pageNum="pageObj.pageNum" 
                    :pageSize="pageObj.pageSize" 
                    @getData="initData" 
                    />
                </div>
            </div>
        </div>

        <el-drawer
            title="物料规则设置"
            :visible.sync="drawer"
            direction="rtl"
            :before-close="handleClose"
        >
            <div class="flex-box flex-column h100p drawer-box">
                <div class="flex-item ofy-auto">
                    <el-form label-width="160px" 
                        :model="editForm"
                    >
                    <el-form-item
                        v-for="(el,index) in formContent"
                        :key="index"
                        :label="`${el.label}：`"
                        :required="el.required || false"
                    >
                        <el-select
                            v-if="el.type === 'select'"
                            v-model="editForm[el.prop]"
                            @change="el['change']"
                            :value-key="el.prop === 'materialsName' ? 'materialsName' : 'id'"
                            filterable clearable 
                            :placeholder="'请选择' + el.placeholder"
                            style="width: 350px"
                        >
                            <el-option
                                v-for="item in el.options"
                                :key="item.label"
                                :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                        <el-input 
                            v-else-if="el.type === 'input'"
                            v-model="editForm[el.prop]" 
                            :disabled="el.disabled"
                            clearable
                            :placeholder="el.placeholder || `请输入${el.label}`"
                            style="width: 350px"
                        />
                    </el-form-item>
                    </el-form>
                </div>
                <div class="drawer-footer">
                    <el-button type="primary" @click="drawer = false" plain>取消</el-button>
                    <el-button type="primary" @click="saveTarget">保存</el-button>
                </div>
            </div>
        </el-drawer>

        <el-dialog
            title="绑定配合比"
            :visible.sync="showPhbDialog"
            width="90%"
            :close-on-click-modal="false"
        >
            <div class="flex-box flex-column h100p">
                <!-- 搜索表单 -->
                <div class="search-box flex-box">
                    <el-form class="flex-item" ref="phbSearchForm" :inline="true" :model="phbSearchForm">
                        <el-form-item label="配合比编号：">
                            <el-input v-model="phbSearchForm.proportionPhb" clearable
                                placeholder="请输入"
                                style="width: 160px"
                            />
                        </el-form-item>
                        <el-form-item label="强度等级：">
                            <el-select
                                v-model="phbSearchForm.proportionQddjList"
                                filterable clearable
                                multiple
                                placeholder="请选择等级强度"
                                style="width: 240px">
                                <el-option
                                    v-for="item in ddList"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="抗渗等级：">
                            <el-select
                                v-model="phbSearchForm.proportionKsdjList"
                                filterable clearable
                                multiple
                                placeholder="请选择抗渗等级"
                                style="width: 240px">
                                <el-option
                                    v-for="item in ksList"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="备注：">
                            <el-input v-model="phbSearchForm.proportionRemarks" clearable
                                placeholder="请输入"
                                style="width: 160px"
                            />
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="handlePhbFilter">搜索</el-button>
                            <el-button type="text" icon="el-icon-refresh-right" @click="resetPhbForm()">重置</el-button>
                        </el-form-item>
                    </el-form>
                </div>

                <!-- 配合比列表表格 -->
                <div class="flex-item overHide">
                    <div class="scroll-div" style="height: calc(100% - 50px);">
                        <el-table
                            :data="phbTableData"
                            :loading="phbLoading"
                            style="width: 100%;">
                            <template v-for="item in phbTableColumn">
                                <el-table-column
                                    :key="item.prop"
                                    :prop="item.prop"
                                    :label="item.label"
                                    :fixed="item.fixed"
                                    :width="item.width || ''"
                                    :formatter="item.formatter"
                                    align="center"
                                />
                            </template>
                            <el-table-column width="80" label="操作" align="center" key="handle" fixed="right" :resizable="false">
                                <template slot-scope="scope">
                                    <el-button type="text" size="small" @click="confirmSelectPhb(scope.row)">绑定</el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                    <div class="mt16 mb4">
                        <Pagination
                            :total="phbTotal"
                            :pageNum="phbPageObj.pageNum"
                            :pageSize="phbPageObj.pageSize"
                            @getData="initPhbData"
                        />
                    </div>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import moment from "moment";
import Pagination from "@/components/Pagination/index.vue";
import DrawerForm from "@/components/drawerForm.vue";

export default {
    components: {
        Pagination,
        DrawerForm
    },
    data() {
        return {
            pageObj: {
                pageNum: 1, // 页数
                pageSize: 10, // 条数
            },
            total: 1,
            
            tableData: [],
            searchForm: {
                materialsTypeList: [],
                isConfig: 0,
            },
            materialsTypeOpts: [],
            tableColumn: [
                {
                    label: '物料编号',
                    prop: 'materialNo',
                    width: '150'
                },
                {
                    label: '物料名称',
                    prop: 'materialName',
                    formatter: (row) => {
                        return `${row.materialName} / ${row.materialSpecs}`
                    },
                },
                {
                    label: "配合比",
                    prop: 'phbNo',
                },
                {
                    label: '材料类型',
                    prop: 'materialTypeName',
                    // formatter: (row,materialsTypeOpts) => {
                    //     for(let i =0;i<materialsTypeOpts.length; i++){
                    //         if(row.materialsConfigType == materialsTypeOpts[i].value){
                    //             return materialsTypeOpts[i].label;
                    //         }
                    //     }
                    // },
                },
                {
                    label: '材料名称',
                    prop: 'materialsConfigName',
                    formatter: (row) => {
                        let configName = row.materialsConfigName;
                        let sampleJudge = row.sampleJudge;
                        if(sampleJudge){
                            return configName + (sampleJudge ? `【${sampleJudge}】` : '');
                        }
                        if(configName){
                            return configName;
                        }
                        return '';
                    },
                },
                {
                    label: '材料规格',
                    prop: 'materialsConfigSpec',
                },
                {
                    label: '状态',
                    prop: 'specId',
                },
            ],
            currentData: {}, // 当前选中的物料
            formContent: [],
            editForm: {},
            drawer: false,

            specConfigOpts: [],
            specConfig_2Opts: [],

            showPhbDialog: false,

            // 配合比弹窗相关数据
            phbSearchForm: {
                proportionPhb: "",
                proportionQddjList: [],
                proportionKsdjList: [],
                proportionRemarks: ""
            },
            phbTableData: [],
            phbLoading: false,
            phbTotal: 0,
            phbPageObj: {
                pageNum: 1,
                pageSize: 10
            },

            // 强度等级选项
            ddList: [{
                label: "C10",
                value: "C10"
            },{
                label: "C15",
                value: "C15"
            },{
                label: "C20",
                value: "C20"
            },{
                label: "C25",
                value: "C25"
            },{
                label: "C30",
                value: "C30"
            },{
                label: "C35",
                value: "C35"
            },{
                label: "C40",
                value: "C40"
            },{
                label: "C45",
                value: "C45"
            },{
                label: "C50",
                value: "C50"
            },{
                label: "C55",
                value: "C55"
            }],

            // 抗渗等级选项
            ksList: [{
                label: "P6",
                value: "P6"
            },{
                label: "P8",
                value: "P8"
            },{
                label: "P10",
                value: "P10"
            },{
                label: "P12",
                value: "P12"
            }],

            // 配合比表格列定义
            phbTableColumn: [
                {
                    label: '配合比编号',
                    prop: 'proportionPhb',
                },
                {
                    label: '设计依据',
                    prop: 'proportionSjyj',
                },
                {
                    label: '强度等级',
                    prop: 'proportionQddj',
                },
                {
                    label: '龄期',
                    prop: 'proportionLq',
                },
                {
                    label: '粘聚性',
                    prop: 'proportionZjx',
                },
                {
                    label: '保水性',
                    prop: 'proportionBsx',
                },
                {
                    label: '抗渗等级',
                    prop: 'proportionKsdj',
                },
                {
                    label: '抗折强度',
                    prop: 'proportionKzqd',
                },
                {
                    label: '强氯离子',
                    prop: 'proportionQllz',
                },
                {
                    label: '设计坍落度',
                    prop: 'proportionTld',
                    formatter: (row) => {
                        return row.proportionTld?.qz ? row.proportionTld?.qz + '±' + row.proportionTld?.hz : '--'
                    },
                },
                {
                    label: '水胶比',
                    prop: 'productionSjb',
                },
                {
                    label: '容重(kg)',
                    prop: 'productionRz',
                },
                {
                    label: '砂率(%)',
                    prop: 'productionSl',
                },
                {
                    label: '创建时间',
                    prop: 'createTime',
                    formatter: (row) => {
                        return row.createTime ? moment(row.createTime).format('YYYY-MM-DD') : '--'
                    },
                    width: '130'
                },
                {
                    label: '备注',
                    prop: 'proportionRemarks',
                },
                {
                    label: '兼容说明',
                    prop: 'newRatioString',
                },
            ],
        }
    },

    mounted() {
        this.$api.getDictValue({
            dictCode: 'MASTERIAL_TYPE'
        }, this).then(res => {
            this.materialsTypeOpts = res.data.list.map(item => {
                return {
                    label: item.dictValueName,
                    value: item.dictValueCode,
                }
            })
        })

        this.initData();
    },

    methods: {
    nameColumnClass({ row, column, rowIndex }) {
      // 动态条件示例：年龄大于30则应用红色
      console.log('****',row);
      return row.isIronMark == 1 ? 'text-red' : '';
    },
        cellStyle({ row, column, rowIndex, columnIndex }) {
            if (row.isIronMark == 1) {
                return { color: '#FF4500' }
            } else {
                return { color: '#333' }
            }
        },
        handleFilter(val) {
            this.pageObj.pageNum = 1;
            this.initData();
        },
        resetForm(){
            this.searchForm = {
                materialsTypeList: [],
                isConfig: 0
            };
            this.initData();
        },
        initData(opageNum, opageSize){
            if (opageNum) this.pageObj.pageNum = opageNum;
            if (opageSize) this.pageObj.pageSize = opageSize;
                
            const params ={
                ...this.pageObj,
                params: this.searchForm
            }
            
            //获取列表
            this.tableData = [];
            this.$api.queryMaterialsPage(params, this).then(res => {
                if(res.succ){
                    this.tableData = res.data.list;
                    this.total = res.data.total;
                }else{
                    this.$message.error(res.msg || '查询失败')
                }
            })
        },

        setMaterials(row) {
            this.$api.querySelectByMaterialsType(`materialsType=${row.materialType}`, this).then(res => {
                if (res.code == 1) {
                    this.specConfigOpts = res.data.list.map(item => {
                        return {
                            label: item.materialsName + (item.sampleJudge ? `【${item.sampleJudge}】` : ''),
                            value: item,
                        }
                    })
                    this.drawer = true;
                    let configName = row.materialsConfigName;
                    let sampleJudge = row.sampleJudge;
                    let materialsNameDesc = '';

                    if(configName){
                        materialsNameDesc = configName;
                    }
                    if(sampleJudge){
                        materialsNameDesc = configName + (sampleJudge ? `【${sampleJudge}】` : '');
                    }
                    
                    this.editForm = {
                        id: row.id,
                        materialNo: row.materialNo,
                        materialName: row.materialName, // 物料类型名称
                        materialType: row.materialType, // 材料类型
                        materialTypeName: row.materialTypeName, // 材料类型名称
                        specId: row.specId,
                        materialsName: row.materialsConfigName,
                        materialsNameDesc: materialsNameDesc,
                        materialsSpec: row.materialsConfigSpec,
                    };
                    this.formContent = [
                        {
                            type: 'input',
                            label: '物料编号',
                            prop: 'materialNo',
                            placeholder: "材料编号",
                            disabled: true
                        },
                        {
                            type: 'input',
                            label: '物料名称',
                            prop: 'materialName',
                            placeholder: "物料名称",
                            disabled: true
                        },
                        {
                            type: 'select',
                            label: '材料类型',
                            prop: 'materialTypeName',
                            placeholder: "材料类型",
                            change: (item) => this.changeSelectMaterialsType(item),
                            options: this.materialsTypeOpts,
                            // disabled: true
                        },
                        {
                            type: 'select',
                            label: '材料名称',
                            prop: 'materialsNameDesc',
                            options: this.specConfigOpts,
                            placeholder: "材料名称",
                            change: (item) => this.changeSelect(item)
                        },
                        {
                            type: 'select',
                            label: '材料规格',
                            prop: 'materialsSpec',
                            options: this.specConfig_2Opts,
                            placeholder: "材料规格",
                            change: (item) => this.changeSelectSpec(item)
                        },
                    ]
                }
            })
        },

        queryMaterialsSpecOpts(materialType, materialsName, sampleJudge) {
            this.$api.queryMaterialsSpecConfigAll({
                materialsType: materialType,
                materialsName: materialsName,
                sampleJudge: sampleJudge || ""
            }, this).then(res => {
                if (res.code == 1) {
                    this.specConfig_2Opts = res.data.list.map(item => {
                        return {
                            label: item.materialsSpec || item.materialsName,
                            value: item,
                        }
                    })
                    this.$set(this.formContent[4], "options", this.specConfig_2Opts)
                }
            })
        },

        saveTarget() {
            let formData = {
                id: this.editForm.id,
                specId: this.editForm.specId,
                materialType: this.editForm.materialType,
                materialTypeName: this.editForm.materialTypeName,
            }
            this.$api.setMaterials(formData, this).then(res => {
                if (res.succ) {
                    this.$message({
                        showClose: true,
                        message: "操作成功",
                        type: "success",
                    });
                    this.drawer = false;
                    this.initData();
                }
            })
        },

        changeSelectMaterialsType(item) {
            this.$set(this.editForm, "materialsName", "");
            this.$set(this.editForm, "materialsSpec", "");
            
            if (!item) {
                this.$set(this.editForm, "materialType", '');
                this.$set(this.editForm, "materialTypeName", '');
                return;
            }
            this.$set(this.editForm, "materialType", item);

            this.$api.querySelectByMaterialsType(`materialsType=${item}`, this).then(res => {
                if (res.code == 1) {
                    this.specConfigOpts = res.data.list.map(item => {
                        return {
                            label: item.materialsName + (item.sampleJudge ? `【${item.sampleJudge}】` : ''),
                            value: item,
                        }
                    })

                    this.$set(this.formContent[3], "options", this.specConfigOpts);
                    let materialTypeName = this.materialsTypeOpts.find((mat) => mat.value == item).label;
                    this.$set(this.editForm, "materialTypeName", materialTypeName);
                }
            })
        },

        changeSelect(item) {
            let materialsName = item.materialsName + (item.sampleJudge ? `【${item.sampleJudge}】` : '');
            this.$set(this.editForm, "materialsName", materialsName);
            this.$set(this.editForm, "materialsSpec", "");

            this.queryMaterialsSpecOpts(item.materialsType, item.materialsName, item.sampleJudge);
        },
        changeSelectSpec(item) {
            this.$set(this.editForm, "specId", item.id);
            this.$set(this.editForm, "materialsSpec", item.materialsSpec || item.materialsName);
        },
        handleClose(done) {
            this.$confirm('确认关闭？')
            .then(_ => {
                if(done){
                    done();
                }
                this.drawer = false;
            })
            .catch(_ => {});
        },

        deleteMaterials(item) {
            this.$confirm('确认删除该配置吗？')
            .then(_ => {
                this.$api.deleteMaterials({
                    id: item.id
                }).then(res => {
                    if (res.code == 1) {
                        this.$message({
                            showClose: true,
                            message: "删除成功",
                            type: "success",
                        });
                        this.initData();
                    }
                })
            })
        },

        materialsBind(row) {
            this.currentData = row;
            this.showPhbDialog = true;
        },

        // 绑定配合比
        materialsBindPhbNoResp(row) {
            this.currentData = row;
            this.showPhbDialog = true;
            this.initPhbData();
        },

        // 配合比弹窗相关方法
        handlePhbFilter() {
            this.initPhbData(1);
        },

        resetPhbForm() {
            this.phbSearchForm = {
                proportionPhb: "",
                proportionQddjList: [],
                proportionKsdjList: [],
                proportionRemarks: ""
            };
            this.initPhbData(1);
        },

        initPhbData(opageNum, opageSize) {
            if (opageNum) this.phbPageObj.pageNum = opageNum;
            if (opageSize) this.phbPageObj.pageSize = opageSize;

            this.phbLoading = true;
            const params = {
                ...this.phbPageObj,
                params: {
                    ...this.phbSearchForm
                }
            };

            this.$api.queryMixProportionPage(params, this).then(res => {
                this.phbLoading = false;
                if (res.succ) {
                    this.phbTableData = res.data.list || [];
                    this.phbTotal = res.data.total || 0;
                } else {
                    this.$message.error(res.msg || '查询配合比列表失败');
                }
            }).catch(err => {
                this.phbLoading = false;
                this.$message.error('查询配合比列表失败');
            });
        },

        confirmSelectPhb(row) {
            // 绑定配合比到当前材料
            this.$api.materialsBindPhbNo({
                id: this.currentData.id,
                phbNo: row.proportionPhb
            }).then(res => {
                if (res.code == 1) {
                    this.$message({
                        showClose: true,
                        message: "绑定成功",
                        type: "success",
                    });
                    this.showPhbDialog = false;
                    this.currentData = {}
                    this.initData();
                } else {
                    this.$message.error(res.msg || '绑定失败');
                }
            });
        }
    },
}
</script>


<style scoped lang="scss">
  ::v-deep .el-button{
    padding-left: 13px;
    padding-right: 13px;
  }
  .el-form-item{
    margin-bottom: 8px;
  }
  ::v-deep .el-form--inline{
    .el-form-item{
      margin-right: 24px;
      margin-bottom: 0;
      &:last-child{
        margin: 0;
      }
    }
  }
  ::v-deep .el-table{
    .expanded,.expanded:hover{
      background-color: #FFFBD9;
      
    }
    .expanded + tr{
      background-color: #FFFBD9;
      td{
        background-color: #FFFBD9;
      }
    }
    
    .table-child-box{
      margin: 16px;
      padding: 16px;
      background: #FFFFFF;
    }
  }
  
  .content-box{
    padding: 16px;
  }
  .content{
    width: 100%;
    height: 100%;
    padding: 16px;
    background: #FFFFFF;
    border-radius: 16px;
  }
  
  .search-box{
    padding-bottom: 16px;
    line-height: 40px;
  }

  // 配合比弹窗样式
  .h100p {
    height: 100%;
  }

  .flex-box {
    display: flex;
  }

  .flex-column {
    flex-direction: column;
  }

  .flex-item {
    flex: 1;
  }

  .overHide {
    overflow: hidden;
  }

  .scroll-div {
    overflow-y: auto;
  }

  .mt16 {
    margin-top: 16px;
  }

  .mb4 {
    margin-bottom: 4px;
  }

  .text-red {
  color: #F56C6C !important; /* 红色 */
}
</style>