import { h } from 'vue'
import moment from 'moment'
export const pjColumn = [
  {
    label: '指标编号',
    prop: 'batchCode',
    type: 1
  },
  {
    label: '指标编号',
    prop: 'quickCode',
    type: 2
  },
  // {
  //   label: '物料编号',
  //   prop: 'projectNameCode',
  // },
  {
    label: '试验项目',
    prop: 'testProjectName',
  },
  {
    label: '物料类型',
    prop: 'projectCategory',
    formatter: (row,materialTypeList) => {
      for(let i =0;i<materialTypeList.length; i++){
        if(row.projectCategory == materialTypeList[i].value){
          return materialTypeList[i].label;
        }
      }
    },
  },
  {
    label: '检验类型',
    prop: 'checkType',
    formatter: (row) => {
      if(row.checkType == 0){
        return '常规'
      }else if(row.checkType == 10){
        return '月检'
      }else if(row.checkType == 20){
        return '季检'
      }else if(row.checkType == 30){
        return '半年检'
      }else if(row.checkType == 40){
        return '年检'
      }
    },
  },
  {
    label: '指标类型',
    prop: 'zblx'
  },
  // {
  //   label: '物料名称',
  //   prop: 'projectName'
  // },
  // {
  //   label: '物料规格',
  //   prop: 'projectSpecs'
  // },
  {
    label: '检测值单位',
    prop: 'checkValueUnit'
  },
  {
    label: '国际',
    prop: 'nationalStandardValue'
  },
  // {
  //   label: '合格指标',
  //   prop: 'qualifiedCheckText',
  //   type: 2
  // },
  // {
  //   label: '让步验收指标',
  //   prop: 'compromiseCheckText',
  //   type: 2
  // },
  // {
  //   label: '不合格指标',
  //   prop: 'unQualifiedCheckText',
  //   type: 1
  // },
  {
    label: '是否必检',
    prop: 'isMust',
    formatter: (row) => {
      return row.isMust ==  1 ? '是' : '否'// 0
    },
  },
  // {
  //   label: '扣除规则',
  //   prop: 'deductionPercentage',
  //   width: '150px',
  //   type: 2,
  //   formatter: (row) => {
  //     return `每${row.deductionThreshold}%，扣除${row.deductionPercentage}%`// 0
  //   },
  // },
  // {
  //   label: '批检规则',
  //   prop: 'batchRules',
  //   type: 1,
  //   formatter: (row) => {
  //     return row.batchRules ==  1 ? '车车都检' : '按方量'// 2
  //   },
  // },
  // {
  //   label: '检测数量',
  //   prop: 'batchThreshold',
  // },
  {
    label: '是否需要图片',
    prop: 'isUploadImg',
    formatter: (row) => {
      return row.isUploadImg ==  1 ? '是' : '否'// 0
    },
  },
  {
    label: '备注',
    prop: 'remark'
  },
 
]



export const equipmentColumn = [
  {
    label: "设备类别",
    prop: "equipmentCategory",
    width: 160
  },
  {
    label: "设备编号",
    prop: "equipmentNo",
    width: 160
  },
  {
    label: "设备名称",
    prop: "equipmentName",
    width: 200
  },
  {
    label: "设备型号",
    prop: "equipmentType"
  },
  {
    label: "出厂编号",
    prop: "factoryNumber"
  },
  {
    label: "准确度",
    prop: "technicalParameter"
  },
  {
    label: "生产厂家",
    prop: "manufacturer",
    width: 200
  },
  {
    label: "购置日期",
    prop: "buyTime",
    formatter: (row) => {
      if (!row.buyTime) {
        return "--";
      }
      return moment(row.buyTime).format("YYYY-MM-DD");
    }
  },
  {
    label: "存放地点",
    prop: "saveAddress"
  },
  {
    label: "保管员",
    prop: "custodyUserName"
  },
  {
    label: "设备状态",
    prop: "equipmentStatus"
  },
  {
    label: "检定周期（天）",
    prop: "verificationCycle"
  },
  {
    label: "上次检定时间",
    prop: "lastDetectionTime",
    formatter: (row) => {
      if (!row.lastDetectionTime) {
        return "--";
      }
      return moment(row.lastDetectionTime).format("YYYY-MM-DD");
    }
  },
  {
    label: "可试验项目",
    prop: "testProjectNameStr",
    width: 200
  }
];


export const humitureColumn = [
  {
    label: '记录时间',
    prop: 'recordTime',
  },{
    label: '温度',
    prop: 'temperature',
    formatter: (row) => {
      return `${row.temperature}℃`
    },
  },{
    label: '湿度',
    prop: 'humidness',
    formatter: (row) => {
      return `${row.humidness}RH%`
    },
  },{
    label: '记录人',
    prop: 'recordName',
  },{
    label: '类型',
    prop: 'typeName',
  }
]

export const userColumn = [{
  label: '用户编号',
  prop: 'userNo',
},{
  label: '用户姓名',
  prop: 'userName',
},{
  label: '手机号码',
  prop: 'userPhone',
},{
  label: '角色',
  prop: 'roleName',
},{
  label: '状态',
  prop: 'orderType',
  width: '60',
  formatter: (row) => {
    //return row.orderType == 1 ? '忙碌' : '空闲'
    console.log(h)
    let ohtml = [];
    ohtml.push(h('div', {
      style:{
        color: 'white',
        width: '40px',
        borderRadius: '4px',
        backgroundColor: row.orderType == 1 ? '#DC3290' : '#3369FF',
      },
      attrs: {}
    }, row.orderType == 1 ? '忙碌' : '空闲'))
    return h('div', null, ohtml) 
    
  },
},{
  label: '职位',
  prop: 'job',
},{
  label: '协会用户名称',
  prop: "societyUserName"
}]

export const regionalismColumn = [{
  label: '工程区域',
  prop: 'projectArea',
},{
  label: '区域组长',
  prop: 'headmanName',
},{
  label: '客服员',
  prop: 'customerServiceName',
}]



export const standardizingRecordColumn = [
{
  label: '设备名称',
  prop: 'equipmentName',
}, 
{
  label: '报告编号',
  prop: 'reportNo',
},{
  label: '校准单位',
  prop: 'calibrationUnit',
},{
  label: '校准时间',
  prop: 'calibrationTime',
},{
  label: '校准结果',
  prop: 'calibrationText',
},{
  label: '校准附件',
  prop: 'calibrationAnnex',
  formatter: (row) => {},
}]