<template>
  <div class="content-box">
    <div class="flex-box flex-column content">
      <div class="search-box flex-box">
        <el-form class="flex-item" ref="searchForm" :inline="true" :model="searchForm" :disabled="loading">
          <el-form-item label="物料类型：">
            <el-select
              v-model="searchForm.materialObj" 
              filterable clearable 
              placeholder="请选择物料类型" 
              @change="materialTypeChange"
              :value-key="'dictValueCode'"
              style="width: 180px">
              <el-option
                v-for="(item, index) in materialTypeList2"
                :key="index"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="试验项目：">
            <el-select
              v-model="searchForm.testObj" 
              filterable clearable 
              placeholder="请选择试验项目" 
              @change="testProjectChange"
              :value-key="'testCode'"
              style="width: 180px">
              <el-option
                v-for="(item, index) in testProjectList"
                :key="index"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" :disabled="loading" @click="handleFilter">搜索</el-button>
            <el-button type="text" icon="el-icon-refresh-right" :disabled="loading" @click="resetForm()">重置</el-button>
          </el-form-item>
        </el-form>
        <el-button type="primary" @click="handleSetTarget()">新增试验说明</el-button>
      </div>
      
      <div class="flex-item overHide">
        <div class="scroll-div">
          <el-table
            :data="tableData"
            v-loading="loading"
            style="width: 100%">
            <af-table-column
              v-for="item in tableColumn" 
              :key="item.prop" :prop="item.prop" 
              v-if="!item.type || item.type === checkType"
              :label="item.label" 
              :formatter="item.formatter"
              :fixed="item.fixed" 
              :width="item.width || ''"
              align="center" 
            />
            <af-table-column width="280" label="操作" align="center" key="handle" :resizable="false">
              <template slot-scope="scope">
                <el-button type="text" size="small" @click="handleSetTarget(scope.row)">修改</el-button>
                <el-button type="text" size="small" style="color: #ff0000;" @click="handleDel(scope.row)">删除</el-button>
              </template>
            </af-table-column>
          </el-table>
        </div>
      </div>
      <div class="mt16 mb4">
        <Pagination
          :total="total" 
          :pageNum="pageObj.pageNum" 
          :pageSize="pageObj.pageSize" 
          @getData="initData" 
        />
      </div>
    </div>

    <el-drawer
        title="试验说明"
        :visible.sync="showTest"
        direction="rtl"
        :before-close="handleTestClose"
    >
    <div class="flex-box flex-column h100p drawer-box">
          <div class="flex-item ofy-auto">
              <el-form label-width="160px" 
                  :model="editForm"
              >
              
              </el-form>
          </div>
          <div class="drawer-footer">
              <el-button type="primary" @click="showTest = false" plain>取消</el-button>
              <el-button type="primary" @click="saveTarget">保存</el-button>
          </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import Pagination from "@/components/Pagination/index.vue";
import DrawerForm from "@/components/drawerForm.vue";
export default {
  name:'paramsConfig',
  components: {
    Pagination,
    
    DrawerForm
  },
  data() {
    return {
      tableColumn: [
        {
          label: '编号',
          prop: 'descNo',
        },
        {
          label: '物料类型',
          prop: 'materialTypeName',
        },
        {
          label: '试验项目',
          prop: 'itemName',
        },
      ],
      addApi: 'addMaterialsTestProjectDesc',
      delApi: 'deleteMaterialsTestProjectDesc',
      updateApi: 'updateMaterialsTestProjectDesc',
      getListApi: 'queryMaterialsTestProjectDescPage',

      loading: false,
      searchForm: {
        materialType: "",
        itemCode: "",
        testObj: {},
        materialObj: {}
      },
      materialTypeList2: [],
      testProjectList: [],
      pageObj: {
        pageNum: 1, // 页数
        pageSize: 10, // 条数
      },
      total: 1,
      tableData: [],
      showTest: false,
      activeRow: {},//编辑对象
      formContent:[
        {
          type: 'select',
          label: '物料类型',
          prop: 'materialType',
          valueKey: 'dictValueCode',
          options: [],
        },
        {
          type: 'select',
          label: '试验项目',
          prop: 'itemCode',
          valueKey: 'testCode',
          options: [],
        },
        {
          type: 'html',
          label: '国标',
          prop: 'nationalStandard',
        },
        {
          type: 'html',
          label: '地标',
          prop: 'landmark',
        },
        {
          type: 'html',
          label: '试验室管理',
          prop: 'laboratoryManage',
        },
      ],
      editForm: {},
      rules: {
      },
    };
  },
  
  created() {
    this.getMaterialType();//获取物料类型
    this.initData();
    
    
  },
  methods: {
    //获取物料类型
    getMaterialType(){
      this.$api.getDictValue({
        dictCode: 'MASTERIAL_TYPE'
      },this).then(res =>{
        if(res.succ){
          this.materialTypeList2 = res.data.list.map(i => {
            return {
              label: i.dictValueName,
              value: i
            }
          })

          this.formContent[0].options = this.materialTypeList2;
        }
      });

      this.$api.getTestProject2({},this).then(res =>{
        if(res.succ){
          this.testProjectList = res.data.list.map(i => {
            return {
              label: i.testName,
              value: i
            }
          })
          this.formContent[1].options = this.testProjectList;
        }
      })
    },
    
    handleFilter() {
      console.log("handleFilter", this.searchForm)
      this.initData(1);
    },
    resetForm(){
      this.searchForm = {};
      this.initData(1);
    },
    initData(opageNum, opageSize){
      this.loading = true;
      if (opageNum) this.pageObj.pageNum = opageNum;
      if (opageSize) this.pageObj.pageSize = opageSize;
        
      const params ={
        ...this.pageObj,
        params: {
          materialType: this.searchForm.materialType,
          itemCode: this.searchForm.itemCode,
        }
      }
      //获取列表
      this.$api[this.getListApi](params, this).then(res => {
        this.loading = false;
        if(res.succ){
          this.tableData = res.data.list.map(item =>{
            item.zblx = this.getListApi === 'getTargetList' ? '批检' : '快检'
            return item;
          });
          this.total = res.data.total;
        }else{
          this.$message.error(res.msg || '查询失败')
        }
      }).catch(err => {
        this.loading = false;
      });
    },
    handleDel(row){
      this.$confirm("删除后不能恢复，确定要删除吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        //删除
        this.$api[this.delApi]({
          id: row.id
        }, this).then((res) => {
          if (res.succ) {
            this.$message({
              showClose: true,
              message: "删除成功",
              type: "success",
            });
            if(this.tableData.length == 1 && this.pageObj.pageNum > 1){
              this.pageObj.pageNum = this.pageObj.pageNum -1
            }
            this.initData();
          }
        });
      }).catch(() => {
        this.$message({
          type: "info",
          message: "已取消删除",
        });
      });
    },
   
    handleSetTarget(row){
      
    },
    saveTarget(formData){
      if(formData.id){//修改this.activeRow
        this.$api[this.updateApi](formData, this).then((res) => {
          if (res.succ) {
            this.$message({
              showClose: true,
              message: "修改成功",
              type: "success",
            });
            this.initData();
            this.handleClose();
          }
        });
      }else{
        this.$api[this.addApi](formData, this).then((res) => {
          if (res.succ) {
            this.$message({
              showClose: true,
              message: "添加成功",
              type: "success",
            });
            this.initData();
            this.handleClose();
          }
        });
      }
      
    },

    materialTypeChange(event) {
      console.log("materialTypeChange", event);
      this.searchForm.materialObj = event;
      this.searchForm.materialType = event.dictValueCode;
      this.handleFilter();
    },
    testProjectChange(event) {
      console.log("testProjectChange", event);
      this.searchForm.testObj = event;
      this.searchForm.itemCode = event.testCode;
      this.handleFilter();
    },

    handleClose() {
      this.showTest = false;
    },
    handleTestClose(done) {
      this.$confirm('确认关闭？')
        .then(_ => {
          if(done){
            done();
          }
          this.showTest = false;
        })
        .catch(_ => {});
    },
  },
};
</script>

<style scoped lang="scss">
  .multi-form-item-box{
    padding: 0 0 4px 0;
    .el-select,.el-input{
      margin-right: 20px;
    }
  }
  ::v-deep .el-button{
    padding-left: 13px;
    padding-right: 13px;
  }
  .el-form-item{
    margin-bottom: 8px;
  }
  ::v-deep .el-form--inline{
    .el-form-item{
      margin-right: 24px;
      margin-bottom: 0;
      &:last-child{
        margin: 0;
      }
    }
  }
  ::v-deep .el-table{
    .expanded,.expanded:hover{
      background-color: #FFFBD9;
      
    }
    .expanded + tr{
      background-color: #FFFBD9;
      td{
        background-color: #FFFBD9;
      }
    }
    
    .table-child-box{
      margin: 16px;
      padding: 16px;
      background: #FFFFFF;
    }
  }
  
  .content-box{
    padding: 16px;
  }
  .content{
    width: 100%;
    height: 100%;
    padding: 16px;
    background: #FFFFFF;
    border-radius: 16px;
  }
  
  .search-box{
    padding-bottom: 16px;
    line-height: 40px;
  }
</style>