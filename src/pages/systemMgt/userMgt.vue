<template>
  <div class="content-box">
    <div class="flex-box flex-column content">
      <div class="search-box flex-box">
        <el-form class="flex-item" ref="searchForm" :inline="true" :model="searchForm" :disabled="loading">
          <el-form-item label="用户姓名：">
            <el-input v-model="searchForm.userName" clearable
              placeholder="请输入用户姓名" 
              style="width: 180px" 
            />
          </el-form-item>
          
          
          <el-form-item label="手机号码：">
            <el-input v-model="searchForm.userPhone" clearable
              placeholder="请输入手机号码" 
              style="width: 180px" 
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" :disabled="loading" @click="handleFilter">搜索</el-button>
            <el-button type="text" icon="el-icon-refresh-right" :disabled="loading" @click="resetForm()">重置</el-button>
          </el-form-item>
        </el-form>
        <el-button v-if="roleId == '5'" type="primary" @click="handleSetTarget()">新增用户</el-button>
      </div>
      
      <div class="flex-item overHide">
        <div class="scroll-div">
          <el-table
            :data="tableData"
            v-loading="loading"
            style="width: 100%">
            <af-table-column
              v-for="item in tableColumn" 
              :key="item.prop" :prop="item.prop" 
              :label="item.label" 
              :formatter="item.formatter"
              :fixed="item.fixed" 
              :width="item.width || ''"
              align="center" 
            />
            <af-table-column v-if="roleId == '5'" width="200" label="操作" align="center" key="handle" :resizable="false">
              <template slot-scope="scope">
                <el-button  type="text" size="small" @click="gotoDetail(scope.row)">详情</el-button>              
                <el-button  type="text" size="small" @click="handleSetTarget(scope.row)">修改</el-button>
                <el-button  type="text" size="small" style="color: #ff0000;" @click="handleDel(scope.row)">删除</el-button>
              </template>
            </af-table-column>
          </el-table>
        </div>
      </div>
      <div class="mt16 mb4">
        <Pagination
          :total="total" 
          :pageNum="pageObj.pageNum" 
          :pageSize="pageObj.pageSize" 
          @getData="initData" 
        />
      </div>
    </div>
    
    
    <DrawerForm
      :formContent="formContent"
      ref="drawerForm"
      @saveTarget="saveTarget"
    >
      <template #customLastForm>
        <el-form-item
            :label="`签名：`"
          >
          <div class="img-box">
            <el-upload
              :action="baseUrl + '/upload/file'"
              list-type="picture-card"
              :show-file-list="false"
              :on-success="handlePicSuccess"
              :on-remove="handlePicRemove">
              <img v-if="userSignImg.filePath" :src="userSignImg.filePath" style="width: 148px; height: 148px; object-fit: cover;" >
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
          </div>
        </el-form-item>
        <el-form-item
            :label="`选择试验项目：`"
          >
          <el-checkbox-group v-model="selectedExperimentTypeList">
            <template v-for="(item, index) in userExperimentTypeList">
              <el-checkbox :label="item.name" v-model="item.name" :key="index"></el-checkbox>
            </template>
          </el-checkbox-group>
        </el-form-item>
      </template>
    </DrawerForm>
  </div>
</template>

<script>
import { userColumn as tableColumn } from "./config.js"
import getOpt from "@/common/js/getListData.js"
import Pagination from "@/components/Pagination/index.vue";
import DrawerForm from "@/components/drawerForm.vue";
export default {
  name:'userMgt',
  components: {
    Pagination,
    DrawerForm
  },
  data() {
    return {
      addApi: 'addUser',
      delApi: 'delUser',
      updateApi: 'setUser',
      getListApi: 'getUserList',
      baseUrl: process.env.NODE_ENV === 'development' ? "/api" : "/mes-platform",
      filePrefix: this.$store.state.loginStore.userInfo.filePrefix,
      userId: this.$store.state.loginStore.userInfo.userId,
      roleId: this.$store.state.loginStore.userInfo.roleId,

      loading: false,
      
      searchForm: {},
      
      
      tableColumn: tableColumn,
      pageObj: {
        pageNum: 1, // 页数
        pageSize: 10, // 条数
      },
      total: 1,
      tableData: [],
      
      activeRow: {},//编辑对象
      userSignImg: {
        fileName: "",
        filePath: "",
      },
      formContent: [
        {
          type: 'input',
          label: '用户姓名',
          prop: 'userName',
        },
        // {
        //   type: 'input',
        //   label: '身份证',
        //   prop: 'idCard',
        // },
        {
          type: 'input',
          label: '手机号码',
          prop: 'userPhone',
        },{
          type: 'select',
          label: '角色',
          prop: 'roleId',
          options: [] //this.roleList
        },{
          type: 'input',
          label: '职位',
          prop: 'job',
        },{
          type: 'input',
          label: '专业',
          prop: 'speciality',
        },{
          type: 'input',
          label: '技术职称',
          prop: 'technicalTitles',
        },{
          type: 'select',
          label: '学历',
          prop: 'educationCode',
          options: [], //this.educationList,
          handle: "educationSelected"
        },{
          type: 'input',
          label: '协会用户名称',
          prop: 'societyUserName'
        },
      ],
      rules: {},
      
      roleList:[],
      educationList: [],

      experimentTypeList: [],
      userExperimentTypeList: [],
      selectedExperimentTypeList: []
    };
  },
  
  created() {
    this.initData();
    this.method_name();

    this.$api.getDictValue({
      dictCode: 'MASTERIAL_TYPE'
    }, this).then(res => {
      this.experimentTypeList = res.data.list
      // .filter(item => item.dictValueCode != 7) // 加上混凝土
      .map((item) => {
        // if (item.dictValueCode != 7) {
          return {
            no: item.dictValueCode,
            name: item.dictValueName,
            checkboxed: false, // 默认全部未选中
          }
        // }
      });
    })

    this.$api.getDictValue({
      dictCode: 'EDUCATION_TYPE'
    }, this).then(res => {
      this.educationList = res.data.list
      .map((item) => {
        return {
          value: item.dictValueCode,
          label: item.dictValueName,
        }
      });

      this.$set(this.formContent[6], 'options', this.educationList);
    })
  },
  methods: {
    async method_name(){
      this.roleList = await getOpt.getRoleListAll(this)
      this.$set(this.formContent[2], 'options', this.roleList);
    },

    educationSelected(val) {
      let res = this.educationList.filter(item => item.value === val);
      this.activeRow.educationCode = res[0].value;
      this.activeRow.educationName = res[0].label;
    },
    
    handleFilter() {
      console.log(this.searchForm)
      this.initData(1);
    },
    resetForm(){
      this.searchForm = {};
      this.initData(1);
    },
    initData(opageNum, opageSize){
      this.loading = true;
      if (opageNum) this.pageObj.pageNum = opageNum;
      if (opageSize) this.pageObj.pageSize = opageSize;
        
      const params ={
        ...this.pageObj,
        params: this.searchForm
      }
      //获取列表
      this.$api[this.getListApi](params, this).then(res => {
        this.loading = false;
        if(res.succ){
          this.tableData = res.data.list;
          this.total = res.data.total;
        }else{
          this.$message.error(res.msg || '查询失败')
        }
      })
    },
    handleDel(row){
      this.$confirm("删除后不能恢复，确定要删除吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        //删除
        this.$api[this.delApi]({
          id: row.id
        }, this).then((res) => {
          if (res.succ) {
            this.$message({
              showClose: true,
              message: "删除成功",
              type: "success",
            });
            if(this.tableData.length == 1 && this.pageObj.pageNum > 1){
              this.pageObj.pageNum = this.pageObj.pageNum -1
            }
            this.initData();
          }
        });
      }).catch(() => {
        this.$message({
          type: "info",
          message: "已取消删除",
        });
      });
    },

    gotoDetail(row) {
      // 跳转详情
      this.$router.push({
        path: "/systemMgt/userDetail",
        query: {
          id: row.id,
        },
      });
    },
    
    handleSetTarget(row){
      let tempList = [...this.experimentTypeList];
      this.selectedExperimentTypeList = [];
      // 试验权限
      if (row && row.testProjectJson && row.testProjectJson.length > 0) {
        // 修改
        for (const userSour of row.testProjectJson) {
          for (const exSour of tempList) {
            if (exSour.no == userSour.no) {
              exSour.checkboxed = true;
              this.selectedExperimentTypeList.push(exSour.name)
            }
          }
        }
      }
      this.userExperimentTypeList = tempList;
      console.log(">>>>this.selectedExperimentTypeList>>>", this.selectedExperimentTypeList)
      console.log(">>>>this.userExperimentTypeList>>>", this.userExperimentTypeList)
      
      if (row) {
        this.activeRow = row;
        // 签名图片
        this.userSignImg = {
          fileName: row.userSignImg || "",
          filePath: row.userSignImg ? this.filePrefix + row.userSignImg : ''
        }
        this.$refs.drawerForm.initData(row);
      }else{
        this.$refs.drawerForm.initData();
      }
    },
    //图片
    handlePicSuccess(response) {
      this.userSignImg = {
        fileName: response.data.fileName,
        filePath: response.data.filePath,
      }
    },
    handlePicRemove() {
      this.userSignImg = {
        fileName: '',
        filePath: '',
      }
    },
    getName(oVal, list){
      for(let i =0;i<list.length; i++){
        if(oVal == list[i].value){
          return list[i].label;
        }
      }
    },
    saveTarget(formData){
      formData.roleName = this.getName(formData.roleId, this.roleList)
      formData.userSignImg = this.userSignImg.fileName;
      formData.educationCode = this.activeRow.educationCode;
      formData.educationName = this.activeRow.educationName;
      let temp = [];
      for (const item of this.userExperimentTypeList) {
        if (this.selectedExperimentTypeList.indexOf(item.name) > -1) {
          temp.push({
            no: item.no,
            name: item.name
          })
        }
      }
      formData.testProjectJson = temp;
      
      if(formData.id){//修改this.activeRow
        this.$api[this.updateApi](formData, this).then((res) => {
          if (res.succ) {
            this.$message({
              showClose: true,
              message: "修改成功",
              type: "success",
            });
            this.$refs.drawerForm.handleClose();
            this.initData();
          }
        });
      }else{
        
        this.$api[this.addApi](formData, this).then((res) => {
          if (res.succ) {
            this.$message({
              showClose: true,
              message: "添加成功",
              type: "success",
            });
            this.initData();
            this.$refs.drawerForm.handleClose();
          }
        });
      }
      
    },
  },
};
</script>

<style scoped lang="scss">
  .multi-form-item-box{
    padding: 0 0 4px 0;
    // display: flex;
    // justify-content: space-between;
    .el-select,.el-input{
      margin-right: 20px;
    }
  }
  ::v-deep .el-button{
    padding-left: 13px;
    padding-right: 13px;
  }
  .el-form-item{
    margin-bottom: 8px;
  }
  ::v-deep .el-form--inline{
    .el-form-item{
      margin-right: 24px;
      margin-bottom: 0;
      &:last-child{
        margin: 0;
      }
    }
  }
  ::v-deep .el-table{
    .expanded,.expanded:hover{
      background-color: #FFFBD9;
      
    }
    .expanded + tr{
      background-color: #FFFBD9;
      td{
        background-color: #FFFBD9;
      }
    }
    
    .table-child-box{
      margin: 16px;
      padding: 16px;
      background: #FFFFFF;
    }
  }
  
  .content-box{
    padding: 16px;
  }
  .content{
    width: 100%;
    height: 100%;
    padding: 16px;
    background: #FFFFFF;
    border-radius: 16px;
  }
  
  .search-box{
    padding-bottom: 16px;
    line-height: 40px;
  }
</style>