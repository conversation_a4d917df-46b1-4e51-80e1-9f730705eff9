<template>
    <div class="content-box">
        <div class="flex-box flex-column content">
            <div v-for="(item, index) in configs" :key="index">
                <template v-if="item[0].experimentType != 7">
                    <p class="content-title">{{ experimentTypeNameList[`${item[0].experimentType}`] }}</p>
                    <div class="flex-row content-row">
                        <el-switch 
                            active-color="#2367B1" 
                            :active-value="1" 
                            :inactive-value="0" 
                            v-model="item[0].isOpen"
                            @change="changeStatus0(index, item[0])"
                        ></el-switch>
                        <span style="margin-left: 24px;">快检规则：</span>
                        <el-radio-group v-model="item[0].checkRules" :disabled="item[0].isOpen == 0">
                            <el-radio :label="1">车车都检</el-radio>
                            <!-- <el-radio :label="2">按重量</el-radio>
                            <el-radio :label="3">按车数</el-radio>
                            <el-radio :label="4">按时间</el-radio> -->
                        </el-radio-group>
                        <!-- <span style="margin-left: 34px; margin-right: 6px;">每</span>
                        <el-input :disabled="item[0].isOpen == 0" v-model="item[0].checkThreshold" style="width: 140px; height: 40px;"  />
                        <span style="margin-left: 6px;">车，生成一个检验委托</span> -->
                        <!-- <div class="flex-row" style="position: absolute; right: 30px;">
                            <el-radio-group :disabled="item[0].isOpen == 0" v-model="item[0].qualifiedType">
                                <el-radio :label="1">同一厂家</el-radio>
                                <el-radio :label="2">同一供应商</el-radio>
                            </el-radio-group>
                        </div>  -->
                        
                    </div>
                    <div class="flex-row content-row">
                        <el-switch 
                            active-color="#2367B1" 
                            :active-value="1" 
                            :inactive-value="0" 
                            v-model="item[1].isOpen"
                            @change="changeStatus1(index, item[1])"
                        ></el-switch>
                        <span style="margin-left: 24px;">批检规则：</span>
                        <el-radio-group :disabled="item[1].isOpen == 0" v-model="item[1].checkRules" @change="checkRulesChange($event, item, index)">
                            <el-radio :label="1">车车都检</el-radio>
                            <el-radio :label="2">按重量</el-radio>
                            <el-radio :label="3">按车数</el-radio>
                            <el-radio :label="4">按时间</el-radio>
                        </el-radio-group>
                        <div style="margin-left: 34px; margin-right: 6px;" v-if="item[1].checkRules == 4">
                            <el-checkbox-group v-model="item[1].checkThresholdList" @change="(value) => bindCheckBox(value, item[1], index)" :disabled="item[1].isOpen == 0">
                                <el-checkbox label="1">星期一</el-checkbox>
                                <el-checkbox label="2">星期二</el-checkbox>
                                <el-checkbox label="3">星期三</el-checkbox>
                                <el-checkbox label="4">星期四</el-checkbox>
                                <el-checkbox label="5">星期五</el-checkbox>
                                <el-checkbox label="6">星期六</el-checkbox>
                                <el-checkbox label="7">星期日</el-checkbox>
                            </el-checkbox-group>
                        </div>
                        <div class="flex-row" v-if="item[1].checkRules == 2 || item[1].checkRules == 3">
                            <span style="margin-left: 34px; margin-right: 6px;">每</span>
                            <el-input :disabled="item[1].isOpen == 0" oninput="value=value.replace(/[^\d]/g,'')" v-model="item[1].checkThreshold" style="width: 140px; height: 40px;"  />
                            <span style="margin-left: 6px;">{{ item[1].checkRules == 2 ? '吨' : '车' }}，生成一个检验委托</span>
                        </div>
                        <div v-if="item[1].checkRules == 2">
                            <span style="margin-left: 24px; margin-right: 6px;">委托有效期：</span>
                            <el-input :disabled="item[1].isOpen == 0" v-model="item[1].batchDay" style="width: 60px; height: 40px;"  />
                            <span> 天，截止时间：</span>
                            <el-time-select
                                :disabled="item[1].isOpen == 0"
                                style="width: 120px;"
                                v-model="item[1].batchTime"
                                :picker-options="{
                                    start: '00:00',
                                    step: '01:00',
                                    end: '23:00'
                                }"
                                placeholder="选择时间">
                            </el-time-select>
                        </div>
                        <div class="flex-row" style="margin-left: 64px; position: absolute; right: 30px;">
                            <el-radio-group :disabled="item[1].isOpen == 0" v-model="item[1].qualifiedType">
                                <el-radio :label="1">同一厂家</el-radio>
                                <el-radio :label="2">同一供应商</el-radio>
                            </el-radio-group>
                            <!-- <el-checkbox :disabled="item[1].isOpen == 0" :true-label="1" :false-label="0" v-model="item[1].qualifiedType">限定为同一厂家</el-checkbox> -->
                        </div>
                    </div>
                </template>

                <template v-else>
                    <p class="content-title">{{ experimentTypeNameList[`${item[0].experimentType}`] }}</p>
                    <div class="flex-row content-row">
                        <el-switch 
                            active-color="#2367B1" 
                            :active-value="1" 
                            :inactive-value="0" 
                            v-model="item[0].isOpen"
                            @change="changeStatus0(index, item[0])"
                        ></el-switch>
                        <span style="margin-left: 24px;">快检规则：</span>
                        <el-radio-group v-model="item[0].checkRules" :disabled="item[0].isOpen == 0">
                            <el-radio :label="1">车车都检</el-radio>
                            <!-- <el-radio :label="2">按重量</el-radio>
                            <el-radio :label="3">按车数</el-radio> -->
                            <!-- <el-radio :label="4">按时间</el-radio> -->
                        </el-radio-group>
                        <!-- <span style="margin-left: 34px; margin-right: 6px;">每</span>
                        <el-input :disabled="item[0].isOpen == 0" v-model="item[0].checkThreshold" style="width: 140px; height: 40px;"  />
                        <span style="margin-left: 6px;">车，生成一个检验委托</span>-->
                        <!-- <div class="flex-row" style="position: absolute; right: 30px;">
                            <el-radio-group :disabled="item[0].isOpen == 0" v-model="item[0].qualifiedType">
                                <el-radio :label="1">同一厂家</el-radio>
                                <el-radio :label="2">同一供应商</el-radio>
                            </el-radio-group>
                        </div>  -->
                    </div>
                    <div class="flex-row content-row">
                        <el-switch 
                            active-color="#2367B1" 
                            :active-value="1" 
                            :inactive-value="0" 
                            v-model="item[1].isOpen"
                            @change="changeStatus1(index, item[1])"
                        ></el-switch>
                        <span style="margin-left: 24px;">{{ item[1].testName }}：</span>
                        <el-radio-group :disabled="item[1].isOpen == 0" v-model="item[1].checkRules">
                            <el-radio :label="1">车车都检</el-radio>
                            <el-radio :label="2">按方量</el-radio>
                            <el-radio :label="3">按车数</el-radio>
                            <!-- <el-radio :label="4">按时间</el-radio> -->
                        </el-radio-group>
                        <!-- <div style="margin-left: 34px; margin-right: 6px;" v-if="item[1].checkRules == 2 || item[1].checkRules == 3">
                            <el-checkbox-group v-model="item[1].checkThresholdList" @change="(value) => bindCheckBox(value, item[1], index)" :disabled="item[1].isOpen == 0">
                                <el-checkbox label="1">星期一</el-checkbox>
                                <el-checkbox label="2">星期二</el-checkbox>
                                <el-checkbox label="3">星期三</el-checkbox>
                                <el-checkbox label="4">星期四</el-checkbox>
                                <el-checkbox label="5">星期五</el-checkbox>
                                <el-checkbox label="6">星期六</el-checkbox>
                                <el-checkbox label="7">星期日</el-checkbox>
                            </el-checkbox-group>
                        </div> -->
                        <div class="flex-row" v-if="item[1].checkRules == 2 || item[1].checkRules == 3">
                            <span style="margin-left: 34px; margin-right: 6px;">每</span>
                            <el-input :disabled="item[1].isOpen == 0" oninput="value=value.replace(/[^\d]/g,'')" v-model="item[1].checkThreshold" style="width: 140px; height: 40px;"  />
                            <span style="margin-left: 6px;">{{ item[1].checkRules == 2 ? '方' : '车' }}，生成一个检验委托</span>
                        </div>
                        <div v-if="item[1].checkRules == 2">
                            <span style="margin-left: 24px; margin-right: 6px;">委托有效期：</span>
                            <el-input :disabled="item[1].isOpen == 0" v-model="item[1].batchDay" style="width: 60px; height: 40px;"  />
                            <span> 天，截止时间：</span>
                            <el-time-select
                                :disabled="item[1].isOpen == 0"
                                style="width: 120px;"
                                v-model="item[1].batchTime"
                                :picker-options="{
                                    start: '00:00',
                                    step: '01:00',
                                    end: '23:00'
                                }"
                                placeholder="选择时间">
                            </el-time-select>
                        </div>
                        <!-- <div class="flex-row" style="margin-left: 64px; position: absolute; right: 30px;">
                            <el-radio-group :disabled="item[1].isOpen == 0" v-model="item[1].qualifiedType">
                                <el-radio :label="1">同一厂家</el-radio>
                                <el-radio :label="2">同一供应商</el-radio>
                            </el-radio-group>
                        </div> -->
                    </div>

                    <!-- <div class="flex-row content-row" v-for="(subItem, index) in item" v-if="index == 2 || index == 3 || index == 4">
                        <el-switch 
                            active-color="#2367B1" 
                            :active-value="1" 
                            :inactive-value="0" 
                            v-model="subItem.isOpen"
                            @change="changeStatus1(index, subItem)"
                        ></el-switch>
                        <span style="margin-left: 24px;">{{ subItem.testName }}：</span>
                        <div style="margin-left: 34px; margin-right: 6px;">
                            <el-checkbox :disabled="subItem.isOpen == 0" v-model="subItem.yearSameProject" :false-label="0" :true-label="1">当年同一工程</el-checkbox>
                            <el-checkbox :disabled="subItem.isOpen == 0" v-model="subItem.yearSameMix" :false-label="0" :true-label="1">当年同一配比</el-checkbox>
                        </div>
                    </div> -->
                    <!-- <div class="flex-row content-row" v-for="(subItem, index) in item" v-if="index == 2 || index == 3 || index == 4">
                        <el-switch 
                            active-color="#2367B1" 
                            :active-value="1" 
                            :inactive-value="0" 
                            v-model="subItem.isOpen"
                            @change="changeStatus1(index, subItem)"
                        ></el-switch>
                        <span style="margin-left: 24px;">{{ subItem.testName }}：</span>
                        <div style="margin-left: 34px; margin-right: 6px;">
                            <el-checkbox :disabled="subItem.isOpen == 0" v-model="subItem.isProjectWithMix" :false-label="0" :true-label="1">同工程同配比</el-checkbox>
                            <el-checkbox :disabled="subItem.isOpen == 0" v-model="subItem.isYear" :false-label="0" :true-label="1">限定为当年</el-checkbox>
                        </div>
                    </div> -->
                    <template v-for="(subItem, index) in item">
                        <div class="flex-row content-row" v-if="index == 2 || index == 4">
                            <el-switch 
                                active-color="#2367B1" 
                                :active-value="1" 
                                :inactive-value="0" 
                                v-model="subItem.isOpen"
                                @change="changeStatus1(index, subItem)"
                            ></el-switch>
                            <span style="margin-left: 24px;">{{ subItem.testName }}：</span>
                            <div style="margin-left: 34px; margin-right: 6px;">
                                <el-checkbox :disabled="subItem.isOpen == 0" v-model="subItem.isProjectWithMix" :false-label="0" :true-label="1">同工程同配比</el-checkbox>
                                <el-checkbox :disabled="subItem.isOpen == 0" v-model="subItem.isYear" :false-label="0" :true-label="1">限定为当年</el-checkbox>
                            </div>
                        </div>
                        <div class="flex-row content-row" v-else-if="index == 3">
                            <el-switch 
                                active-color="#2367B1" 
                                :active-value="1" 
                                :inactive-value="0" 
                                v-model="subItem.isOpen"
                                @change="changeStatus1(index, subItem)"
                            ></el-switch>
                            <span style="margin-left: 24px;">{{ subItem.testName }}：</span>
                            <el-radio-group :disabled="subItem.isOpen == 0" v-model="subItem.checkRules">
                                <el-radio :label="1">车车都检</el-radio>
                                <el-radio :label="2">按方量</el-radio>
                                <el-radio :label="3">按车数</el-radio>
                            </el-radio-group>
                            <div class="flex-row" v-if="subItem.checkRules == 2 || subItem.checkRules == 3">
                                <span style="margin-left: 34px; margin-right: 6px;">每</span>
                                <el-input :disabled="subItem.isOpen == 0" oninput="value=value.replace(/[^\d]/g,'')" v-model="subItem.checkThreshold" style="width: 140px; height: 40px;"  />
                                <span style="margin-left: 6px;">{{ subItem.checkRules == 2 ? '方' : '车' }}，生成一个检验委托</span>
                            </div>
                            <div v-if="subItem.checkRules == 2">
                                <span style="margin-left: 24px; margin-right: 6px;">委托有效期：</span>
                                <el-input :disabled="subItem.isOpen == 0" v-model="subItem.batchDay" style="width: 60px; height: 40px;"  />
                                <span> 天，截止时间：</span>
                                <el-time-select
                                    :disabled="subItem.isOpen == 0"
                                    style="width: 120px;"
                                    v-model="subItem.batchTime"
                                    :picker-options="{
                                        start: '00:00',
                                        step: '01:00',
                                        end: '23:00'
                                    }"
                                    placeholder="选择时间">
                                </el-time-select>
                            </div>
                        </div>
                    </template>

                    <div class="flex-row content-row">
                        <el-switch 
                            active-color="#2367B1" 
                            :active-value="1" 
                            :inactive-value="0" 
                            v-model="item[5].isOpen"
                            @change="changeStatus1(index, item[5])"
                        ></el-switch>
                        <span style="margin-left: 24px;">{{ item[5].testName }}：</span>
                        <span style="margin-left: 24px; margin-right: 6px;">单个任务单超过：</span>
                        <el-input :disabled="item[5].isOpen == 0" oninput="value=value.replace(/[^\d]/g,'')" v-model="item[5].combinationPropertyThreshold" style="width: 140px; height: 40px;"  />
                        <span style="margin-left: 4px; margin-right: 6px;">方，生成一个委托</span>
                    </div>

                </template>
            </div>

            <div style="margin-left: calc(100% - 200px); margin-top: 40px;">
                <el-button type="primary" @click="cancelTarget" plain>取消</el-button>
                <el-button type="primary" @click="saveTarget">保存</el-button>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    data() {
        return {
            sourceConfigs: [],
            configs: [],
            experimentTypeNameList: {}
        }
    },

    mounted() {
        this.$api.getDictValue({
            dictCode: 'MASTERIAL_TYPE'
        }, this).then(res => {
            let experimentTypeNameList = {};
            res.data.list.forEach(item => {
                experimentTypeNameList[`${item.dictValueCode}`] = item.dictValueName
            })
            this.experimentTypeNameList = experimentTypeNameList;
            this.queryExperimentGenConfigResp();
        })
    },

    methods: {
        queryExperimentGenConfigResp() {
            this.$api.queryExperimentGenConfig({}, this).then(res => {
                if (res.code == 1) {
                    const groupedByType = res.data.list.reduce((accumulator, currentItem) => {
                        if (!accumulator[`${currentItem.experimentType}`]) {
                            accumulator[`${currentItem.experimentType}`] = [];
                        }

                        if (currentItem.checkThreshold && currentItem.checkType == 2) {
                            currentItem.checkThresholdList = (currentItem.checkThreshold + '').split(',');
                        }else{
                            currentItem.checkThresholdList = [];
                        }
                        accumulator[`${currentItem.experimentType}`].push(currentItem);
                        return accumulator;
                    }, {});
                    this.configs = groupedByType;
                    this.sourceConfigs = JSON.parse(JSON.stringify(this.configs));

                    console.log(">>>this.configs>>>", this.configs);
                }
            })
        },

        saveTarget() {
            let self = this;
            this.$confirm('确认保存吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                let configList = []
                
                Object.values(self.configs).forEach(element => {
                    // element[1] 肯定是批件
                    if (element[1].checkRules == 4) {
                        if (element[1].checkThresholdList.length > 0) {
                            element[1].checkThreshold = element[1].checkThresholdList.join(",");
                        }else{
                            element[1].checkThreshold = "";
                        }
                    } 
                    configList = configList.concat(element);
                });
                console.log(">>>configList>>>", configList);
                self.$api.updateExperimentGenConfig({
                    configList: configList
                }, self).then(res => {
                    if (res.succ) {
                        self.$message({
                            showClose: true,
                            message: "操作成功",
                            type: "success",
                        });
                        self.queryExperimentGenConfigResp();
                    }
                })
            });
        },

        cancelTarget() {
            this.configs = {};
            this.configs = JSON.parse(JSON.stringify(this.sourceConfigs));
        },

        bindCheckBox(value, item, index){
            if(item.checkThresholdList.length > 1){
                item.checkThresholdList.splice(0,1)
            }
        },

        changeStatus0(index, item) {
            
        },
        changeStatus1(index, item) {
            
        },

        checkRulesChange(val, item, index) {
            let itemList = item;
            itemList[1].checkThresholdList = [];
            itemList[1].checkThreshold = "";
            this.$set(this.configs, index, item)
        }
    }
}
</script>

<style lang="scss" scoped>
.content-box{
    padding: 24px 16px;
}
.content{
    width: 100%;
    height: 100%;
    padding: 16px;
    background: #FFFFFF;
    border-radius: 16px;
    overflow: auto;
    .content-title {
        font-weight: 600;
        font-size: 16px;
        color: #1F2329;
        margin-top: 24px;
    }
    .content-row {
        height: 40px;
        margin-top: 16px;
        // min-width: 1000px;
        justify-content: flex-start;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #1F2329;
        position: relative;
    }
}
  
</style>