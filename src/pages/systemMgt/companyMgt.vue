<template>
    <div class="content-box">
        <div class="flex-box flex-column content">
            <el-row class="flex-row row-cell">
                <div class="flex-row row-title">
                    <div>企业名称：</div>
                    <el-input :disabled="!isOpen" placeholder="请输入企业名称" v-model="formData.companyName" style="width: 240px; height: 40px;"  />
                </div>
                <div class="flex-row row-title" style="margin-left: 96px;">
                    <div>企业协会会员编号：</div>
                    <el-input :disabled="!isOpen" placeholder="请输入企业协会会员编号" v-model="formData.companyAssociationMember" style="width: 240px; height: 40px;"  />
                </div>
                <div class="flex-row row-title" style="margin-left: 30px;">
                    <div>企业备案证号：</div>
                    <el-input :disabled="!isOpen" placeholder="请输入企业备案证号" v-model="formData.companyRecord" style="width: 240px; height: 40px;"  />
                </div>
                <div class="flex-row row-title" style="margin-left: 45px;">
                    <div>配合比通知单编号：</div>
                    <el-input :disabled="!isOpen" placeholder="请输入配合比通知单编号" v-model="formData.mixProportionNo" style="width: 240px; height: 40px;"  />
                </div>
            </el-row>
            <el-row class="flex-row row-cell">
                <div class="flex-row row-title">
                    <div>配比贯标：</div>
                    <el-input :disabled="!isOpen" placeholder="请输入配比贯标" v-model="formData.proportioningIso" style="width: 240px; height: 40px;"  />
                </div>
                <div class="flex-row row-title" style="margin-left: 96px;">
                    <div>质量证明书贯标：&nbsp;&nbsp;&nbsp;</div>
                    <el-input :disabled="!isOpen" placeholder="请输入质量证明书贯标" v-model="formData.qualityCertificateIso" style="width: 240px; height: 40px;"  />
                </div>
                <div class="flex-row row-title" style="margin-left: 30px;">
                    <div>质量证明书编号：</div>
                    <el-input :disabled="!isOpen" placeholder="请输入质量证明书编号" v-model="formData.qualityCertificateNo" style="width: 240px; height: 40px;"  />
                </div>
                <div class="flex-row row-title" style="margin-left: 30px;">
                    <div>开盘鉴定台账编号：</div>
                    <el-input :disabled="!isOpen" placeholder="请输入开盘鉴定台账编号" v-model="formData.openAppraisalNo" style="width: 240px; height: 40px;"  />
                </div>
            </el-row>

            <div class="row-title" style="margin-top: 40px;">协会厂家规则：</div>
            <div>
                <div class="flex-row row-title" style="margin-top: 20px;">
                    <div>水泥：</div>
                    <el-radio-group :disabled="!isOpen" v-model="formData.snConfig">
                        <el-radio :label="1">厂家</el-radio>
                        <el-radio :label="2">供应商</el-radio>
                    </el-radio-group>
                </div>
                <div class="flex-row row-title" style="margin-top: 20px;">
                    <div>粉煤灰：</div>
                    <el-radio-group :disabled="!isOpen" v-model="formData.fmhConfig">
                        <el-radio :label="1">厂家</el-radio>
                        <el-radio :label="2">供应商</el-radio>
                    </el-radio-group>
                </div>
                <div class="flex-row row-title" style="margin-top: 20px;">
                    <div>矿渣粉：</div>
                    <el-radio-group :disabled="!isOpen" v-model="formData.kzfConfig">
                        <el-radio :label="1">厂家</el-radio>
                        <el-radio :label="2">供应商</el-radio>
                    </el-radio-group>
                </div>
                <div class="flex-row row-title" style="margin-top: 20px;">
                    <div>细骨料：</div>
                    <el-radio-group :disabled="!isOpen" v-model="formData.xglConfig">
                        <el-radio :label="1">厂家</el-radio>
                        <el-radio :label="2">供应商</el-radio>
                    </el-radio-group>
                </div>
                <div class="flex-row row-title" style="margin-top: 20px;">
                    <div>粗骨料：</div>
                    <el-radio-group :disabled="!isOpen" v-model="formData.cglConfig">
                        <el-radio :label="1">厂家</el-radio>
                        <el-radio :label="2">供应商</el-radio>
                    </el-radio-group>
                </div>
                <div class="flex-row row-title" style="margin-top: 20px;">
                    <div>外加剂1：</div>
                    <el-radio-group :disabled="!isOpen" v-model="formData.wjjConfig">
                        <el-radio :label="1">厂家</el-radio>
                        <el-radio :label="2">供应商</el-radio>
                    </el-radio-group>
                </div>
                <div class="flex-row row-title" style="margin-top: 20px;">
                    <div>外加剂2：</div>
                    <el-radio-group :disabled="!isOpen" v-model="formData.wjj2Config">
                        <el-radio :label="1">厂家</el-radio>
                        <el-radio :label="2">供应商</el-radio>
                    </el-radio-group>
                </div>
                <div class="flex-row row-title" style="margin-top: 20px;">
                    <div>外掺料1：</div>
                    <el-radio-group :disabled="!isOpen" v-model="formData.wcl1Config">
                        <el-radio :label="1">厂家</el-radio>
                        <el-radio :label="2">供应商</el-radio>
                    </el-radio-group>
                </div>
                <div class="flex-row row-title" style="margin-top: 20px;">
                    <div>外掺料2：</div>
                    <el-radio-group :disabled="!isOpen" v-model="formData.wcl2Config">
                        <el-radio :label="1">厂家</el-radio>
                        <el-radio :label="2">供应商</el-radio>
                    </el-radio-group>
                </div>
            </div>

            <div>
                <div class="row-title" style="margin-top: 40px;">氯离子台账设置：</div>
                <el-row class="flex-row row-cell">
                    <div class="flex-row row-title">
                        <div>抗氯离子台账样品编号：</div>
                        <el-input :disabled="!isOpen" placeholder="请输入抗氯离子台账样品编号" v-model="formData.kllzSampleNo" style="width: 240px; height: 40px;"  />
                    </div>
                    <div class="flex-row row-title">
                        <div>抗氯离子台账委托编号：</div>
                        <el-input :disabled="!isOpen" placeholder="请输入抗氯离子台账报告编号" v-model="formData.kllzConsignId" style="width: 240px; height: 40px;"  />
                    </div>
                    <div class="flex-row row-title">
                        <div>抗氯离子台账报告编号：</div>
                        <el-input :disabled="!isOpen" placeholder="请输入抗氯离子台账报告编号" v-model="formData.kllzReportId" style="width: 240px; height: 40px;"  />
                    </div>
                </el-row>
            </div>


            <!-- <div>
                <div class="row-title" style="margin-top: 40px;">性能报告台账设置：</div>
                <el-row class="flex-row row-cell">
                    <div class="flex-row row-title">
                        <div>性能报告台账样品编号：</div>
                        <el-input :disabled="!isOpen" placeholder="请输入性能报告台账样品编号" v-model="formData.xnbgSampleNo" style="width: 240px; height: 40px;"  />
                    </div>
                    <div class="flex-row row-title">
                        <div>性能报告台账委托编号：</div>
                        <el-input :disabled="!isOpen" placeholder="请输入性能报告台账报告编号" v-model="formData.xnbgConsignId" style="width: 240px; height: 40px;"  />
                    </div>
                    <div class="flex-row row-title">
                        <div>性能报告台账报告编号：</div>
                        <el-input :disabled="!isOpen" placeholder="请输入性能报告台账报告编号" v-model="formData.xnbgReportId" style="width: 240px; height: 40px;"  />
                    </div>
                </el-row>
            </div> -->

            <div style="margin-left: calc(100% - 200px); margin-top: 40px; position: absolute; bottom: 20px;">
                <el-button type="primary" @click="cancelTarget" plain>{{ isOpen ? '取消' : '编辑' }}</el-button>
                <el-button type="primary" @click="saveTarget">保存</el-button>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    data() {
        return {
            formData: {
                id: 1, // 写死
                companyName: "", // 企业名称
                companyAssociationMember: "", // 企业协会会员号
                proportioningIso: "", // 配比贯标
                qualityCertificateIso: "", // 质量证明书贯标
                qualityCertificateNo: "", // 初始化质量证明书编号
                companyRecord: "", // 企业备案证号
                openAppraisalNo: "", // 开盘鉴定台账编号
                mixProportionNo: "", // 配合比通知单编号
                kllzSampleNo: "", // 抗氯离子台账样品编号
                kllzConsignId: "", // 抗氯离子台账委托编号
                kllzReportId: "", // 抗氯离子台账报告编号

                snConfig: '', // 水泥配置  1-厂家 2-供应商
                fmhConfig: '', // 粉煤灰配置  1-厂家 2-供应商
                kzfConfig: '', // 矿渣粉配置  1-厂家 2-供应商
                xglConfig: '', // 细骨料配置  1-厂家 2-供应商
                cglConfig: '', // 粗骨料配置  1-厂家 2-供应商
                wjjConfig: '', // 外加剂配置  1-厂家 2-供应商
                wjj2Config: '', // 外加剂2配置  1-厂家 2-供应商
                wcl1Config: '', // 外掺料1配置 1-厂家  2-供应商
                wcl1Config: '', // 外掺料2配置 1-厂家  2-供应商
            },
            isOpen: false,
        }
    },

    mounted() {
        this.queryCompanyConfigResp();
    },

    methods: {
        
        queryCompanyConfigResp() {
            this.$api.queryCompanyConfig(`id=${this.formData.id}`, this).then(res => {
                if (res.code == 1) {
                    this.formData = res.data;
                }
            })
        },

        saveTarget() {
            let self = this;
            this.$confirm('确认保存吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                self.updateCompanyConfigResp();
            });
        },

        updateCompanyConfigResp() {
            console.log(">>this.formData>>", this.formData)
            this.$api.updateCompanyConfig(this.formData, this).then(res => {
                if (res.succ) {
                    this.$message({
                        showClose: true,
                        message: "操作成功",
                        type: "success",
                    });
                    this.cancelTarget();
                    this.queryCompanyConfigResp();
                }
            });
        },

        cancelTarget() {
            this.isOpen = !this.isOpen;
        }
    }
}
</script>

<style lang="scss" scoped>
.content-box{
    padding: 24px 16px;
}
.content{
    width: 100%;
    height: 100%;
    padding: 16px;
    background: #FFFFFF;
    border-radius: 16px;
    overflow: auto;
    align-items: flex-start;
    position: relative;
    .row-cell {
        margin-top: 16px;
        justify-content: flex-start;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #1F2329;

       
    }
}
.row-title {
    font-weight: 600;
    font-size: 16px;
    color: #1F2329;
    margin-right: 60px;
}
.row-title-left {
    margin-left: 96px;
}
</style>