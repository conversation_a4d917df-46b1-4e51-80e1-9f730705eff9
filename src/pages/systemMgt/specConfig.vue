<!--
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-06-11 22:20:34
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-06-25 22:31:56
 * @FilePath: /quality_center_web/src/pages/systemMgt/specConfig.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div class="content-box">
        <div class="flex-box flex-column content">
            <div class="search-box flex-box">
                <el-form class="flex-item" ref="searchForm" :inline="true" :model="searchForm">
                    <el-form-item label="材料类型：">
                        <el-checkbox-group v-model="searchForm.materialsTypeList"
                        @change="handleFilter()"
                        >
                            <el-checkbox v-for="item in materialsTypeOpts" :key="item.value" :label="item.value">{{item.label}}</el-checkbox>
                        </el-checkbox-group>
                    </el-form-item>
                    
                    
                    <el-form-item>
                        <el-button type="primary" @click="handleFilter">搜索</el-button>
                        <el-button type="text" icon="el-icon-refresh-right" @click="resetForm()">重置</el-button>
                    </el-form-item>
                </el-form>
                <!-- <el-button v-if="roleId == '5'" type="primary" @click="initSpecConfig">初始化协会材料</el-button> -->
                <el-button type="primary" @click="addSpecConfig">新增</el-button>
            </div>

            <div class="flex-item overHide">
                <div class="scroll-div" style="height: calc(100% - 50px);">
                    <el-table
                        :data="tableData"
                        >
                        <template v-for="item in tableColumn" >
                            <el-table-column
                                :prop="item.prop" 
                                :label="item.label" 
                                :fixed="item.fixed" 
                                :width="item.width || ''"
                                :formatter="item.prop === 'materialsType' ? (row) => item.formatter(row,materialsTypeOpts) : item.formatter"
                                align="center" 
                            />
                        </template>
                        
                        
                        <el-table-column width="220" label="操作" align="center" key="handle" :resizable="false">
                        <template slot-scope="scope">
                            <el-button type="text" size="small" @click="addSpecConfig(scope.row)">修改</el-button>
                            <el-button type="text" size="small" style="color: #ff0000;" @click="deleteSpecConfig(scope.row)">删除</el-button>
                        </template>
                        </el-table-column>
                    </el-table>
                </div>
                <div class="mt16 mb4">
                    <Pagination
                    :total="total" 
                    :pageNum="pageObj.pageNum" 
                    :pageSize="pageObj.pageSize" 
                    @getData="initData" 
                    />
                </div>
            </div>
        </div>

        <DrawerForm
            :formContent="formContent"
            ref="drawerForm"
            @saveTarget="saveTarget"
        >
        </DrawerForm>
    </div>
</template>

<script>
import Pagination from "@/components/Pagination/index.vue";
import DrawerForm from "@/components/drawerForm.vue";
export default {
    components: {
        Pagination,
        DrawerForm
    },
    data() {
        return {
            pageObj: {
                pageNum: 1, // 页数
                pageSize: 10, // 条数
            },
            total: 1,
            roleId: this.$store.state.loginStore.userInfo.roleId,
            tableData: [],
            searchForm: {
                materialsTypeList: [],
            },
            materialsTypeOpts: [],
            tableColumn: [
                {
                    label: '材料编号',
                    prop: 'materialsNo',
                    width: '150'
                },
                {
                    label: '材料类型',
                    prop: 'materialsType',
                    formatter: (row,materialsTypeOpts) => {
                        for(let i =0;i<materialsTypeOpts.length; i++){
                            if(row.materialsType == materialsTypeOpts[i].value){
                                return materialsTypeOpts[i].label;
                            }
                        }
                    },
                },
                {
                    label: '材料名称',
                    prop: 'materialsName',
                },
                {
                    label: '协会材料编号',
                    prop: 'sampleId',
                },
                {
                    label: '材料简称',
                    prop: 'materialAbbreviation',
                },
                {
                    label: '国家标准',
                    prop: 'sampleJudge',
                },
                {
                    label: '材料规格',
                    prop: 'materialsSpec',
                },
                {
                    label: '协会规格编号',
                    prop: 'specId',
                },
                {
                    label: '等级编号',
                    prop: 'gradeId',
                },
            ],
            formContent: []
        }
    },

    mounted() {
        this.$api.getDictValue({
            dictCode: 'MASTERIAL_TYPE'
        }, this).then(res => {
            this.materialsTypeOpts = res.data.list.map(item => {
                return {
                    label: item.dictValueName,
                    value: parseInt(item.dictValueCode),
                }
            })

            this.formContent = [
                {
                    type: 'input',
                    label: '材料编号',
                    prop: 'materialsNo',
                    placeholder: "材料编号",
                    disabled: true
                },
                {
                    type: 'select',
                    label: '材料类型',
                    prop: 'materialsType',
                    options: this.materialsTypeOpts,
                },
                {
                    type: 'input',
                    label: '材料名称',
                    prop: 'materialsName',
                },
                {
                    type: 'input',
                    label: '协会材料编号',
                    prop: 'sampleId',
                },
                {
                    type: 'input',
                    label: '材料简称',
                    prop: 'materialAbbreviation',
                },
                {
                    type: 'input',
                    label: '国家标准',
                    prop: 'sampleJudge',
                },
                {
                    type: 'input',
                    label: '材料规格',
                    prop: 'materialsSpec',
                },
                {
                    type: 'input',
                    label: '协会规格编号',
                    prop: 'specId',
                },
                {
                    type: 'input',
                    label: '等级编号',
                    prop: 'gradeId',
                },
            ]
        })

        this.initData();
    },

    methods: {
        handleFilter() {
            this.pageObj.pageNum = 1;
            this.pageObj.pageSize = 10;
            this.initData();
        },
        resetForm(){
            this.searchForm = {
                materialsTypeList: [],
            };
            this.handleFilter();
        },
        initData(opageNum, opageSize){
            if (opageNum) this.pageObj.pageNum = opageNum;
            if (opageSize) this.pageObj.pageSize = opageSize;
            const params ={
                ...this.pageObj,
                params: this.searchForm
            }
            
            //获取列表
            this.tableData = [];
            this.$api.queryMaterialsSpecConfigPage(params, this).then(res => {
                if(res.succ){
                    this.tableData = res.data.list;
                    this.total = res.data.total;
                }else{
                    this.$message.error(res.msg || '查询失败')
                }
            })
        },

        addSpecConfig(row) {
            if (row) {

            }else{
                row = {}
            }
            this.$refs.drawerForm.initData(row);
        },

        saveTarget(formData) {
            let api = '';
            if (formData.id) {
                // 修改
                api = 'updateMaterialsSpecConfig';
            }else{
                api = 'addMaterialsSpecConfig';
            }

            this.$api[api](formData, this).then(res => {
                if (res.succ) {
                    this.$message({
                        showClose: true,
                        message: "操作成功",
                        type: "success",
                    });
                    this.$refs.drawerForm.handleClose();
                    this.initData();
                }
            })
        },

        deleteSpecConfig(row) {
            this.$confirm('确认删除该配置吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$api.deleteMaterialsSpecConfig({id: row.id}).then(res => {
                    if (res.code == 1) {
                        this.$message({
                            showClose: true,
                            message: "操作成功",
                            type: "success",
                        });

                        this.initData();
                    }
                })
            });
        },

        initSpecConfig() {
            this.$confirm('确认初始化协会材料吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$api.syncLaboratoryMaterialsInfo().then(res => {
                    if (res.code == 1) {
                        this.$message({
                            showClose: true,
                            message: "操作成功",
                            type: "success",
                        });

                        this.initData();
                    }
                })
            });
        }
    },
}
</script>


<style scoped lang="scss">
  ::v-deep .el-button{
    padding-left: 13px;
    padding-right: 13px;
  }
  .el-form-item{
    margin-bottom: 8px;
  }
  ::v-deep .el-form--inline{
    .el-form-item{
      margin-right: 24px;
      margin-bottom: 0;
      &:last-child{
        margin: 0;
      }
    }
  }
  ::v-deep .el-table{
    .expanded,.expanded:hover{
      background-color: #FFFBD9;
      
    }
    .expanded + tr{
      background-color: #FFFBD9;
      td{
        background-color: #FFFBD9;
      }
    }
    
    .table-child-box{
      margin: 16px;
      padding: 16px;
      background: #FFFFFF;
    }
  }
  
  .content-box{
    padding: 16px;
  }
  .content{
    width: 100%;
    height: 100%;
    padding: 16px;
    background: #FFFFFF;
    border-radius: 16px;
  }
  
  .search-box{
    padding-bottom: 16px;
    line-height: 40px;
  }
</style>