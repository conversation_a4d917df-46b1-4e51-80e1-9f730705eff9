<!--
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-07-16 22:48:09
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2024-10-10 22:42:13
 * @FilePath: /quality_center_web/src/pages/supplierMgt/supplierList.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div class="content-box">
        <div class="flex-box flex-column content">
            <el-row class="row-title">
                <span class="row-title-label">基本信息</span>
            </el-row>

            <el-row>
                <div class="supplier-no">
                    {{ detailInfo.userNo }}
                    <span :class="[detailInfo.status=='1'?'supplier-name-status':'supplier-name-status2']">{{detailInfo.status=='1'?'正常':'禁用'}}</span>
                </div>
                <div class="">
                    <section class="jbz-intro">
                        <div class="display-inline">
                            <span class="font_2">用户名称：</span>
                            <span class="font_3">{{detailInfo.userName || '--'}}</span>
                        </div>
                        <div class="display-inline">
                            <span class="font_2">职位：</span>
                            <span class="font_3">{{detailInfo.job || '--'}}</span>
                        </div>
                        <div class="vertical-space_3 display-inline">
                            <span class="font_2">联系方式：</span>
                            <span class="font_3">{{detailInfo.userPhone || '--'}}</span>
                        </div>
                        <div class="vertical-space_3 display-inline">
                            <span class="font_2">角色：</span>
                            <span class="font_3">{{detailInfo.roleName || '--'}}</span>
                        </div>
                    </section>
                    <section class="supplier-ul">
                        
                    </section>
                </div>
            </el-row>

            <el-row class="row-title" style="margin-top: 30px;">
                <span class="row-title-label">证书管理</span>
                <el-button type="primary" size="mini" style="margin-left: 20px;" @click="handleCertificate">编辑</el-button>
            </el-row>
            <el-row>
                <div class="flex-row" style="justify-content: flex-start; margin-top: 10px;">
                    <span>配比：</span>
                    <div class="flex-row" v-if="certificateInfo.peibi && certificateInfo.peibi.startTime" :style="cellStyle(certificateInfo.peibi)">
                        <span>证书有效期：{{ certificateInfo.peibi.startTime }}</span>
                        <span style="margin-left: 5px; margin-right: 5px;"> 至 </span>
                        <span>{{ certificateInfo.peibi.endTime }}</span>
                    </div>
                    <div class="flex-row" v-else>无 <i style="color: #FF8D19; margin-left: 10px;" class="el-icon-warning-outline" /></div>
                </div>
                <div class="flex-row" style="justify-content: flex-start; margin-top: 10px;">
                    <span>砼操：</span>
                    <div class="flex-row" v-if="certificateInfo.tongcao && certificateInfo.tongcao.startTime" :style="cellStyle(certificateInfo.tongcao)">
                        <span>证书有效期：{{ certificateInfo.tongcao.startTime }}</span>
                        <span style="margin-left: 5px; margin-right: 5px;"> 至 </span>
                        <span>{{ certificateInfo.tongcao.endTime }}</span>
                    </div>
                    <div class="flex-row" v-else>无 <i style="color: #FF8D19; margin-left: 10px;" class="el-icon-warning-outline" /></div>
                </div>
                <div class="flex-row" style="justify-content: flex-start; margin-top: 10px;">
                    <span>水泥：</span>
                    <div class="flex-row" v-if="certificateInfo.shuini && certificateInfo.shuini.startTime" :style="cellStyle(certificateInfo.shuini)">
                        <span>证书有效期：{{ certificateInfo.shuini.startTime }}</span>
                        <span style="margin-left: 5px; margin-right: 5px;"> 至 </span>
                        <span>{{ certificateInfo.shuini.endTime }}</span>
                    </div>
                    <div class="flex-row" v-else>无 <i style="color: #FF8D19; margin-left: 10px;" class="el-icon-warning-outline" /></div>
                </div>
                <div class="flex-row" style="justify-content: flex-start; margin-top: 10px;">
                    <span>骨料：</span>
                    <div class="flex-row" v-if="certificateInfo.guliao && certificateInfo.guliao.startTime" :style="cellStyle(certificateInfo.guliao)">
                        <span>证书有效期：{{ certificateInfo.guliao.startTime }}</span>
                        <span style="margin-left: 5px; margin-right: 5px;"> 至 </span>
                        <span>{{ certificateInfo.guliao.endTime }}</span>
                    </div>
                    <div class="flex-row" v-else>无 <i style="color: #FF8D19; margin-left: 10px;" class="el-icon-warning-outline" /></div>
                </div>
                <div class="flex-row" style="justify-content: flex-start; margin-top: 10px;">
                    <span>砂浆：</span>
                    <div class="flex-row" v-if="certificateInfo.shajiang && certificateInfo.shajiang.startTime" :style="cellStyle(certificateInfo.shajiang)">
                        <span>证书有效期：{{ certificateInfo.shajiang.startTime }}</span>
                        <span style="margin-left: 5px; margin-right: 5px;"> 至 </span>
                        <span>{{ certificateInfo.shajiang.endTime }}</span>
                    </div>
                    <div class="flex-row" v-else>无 <i style="color: #FF8D19; margin-left: 10px;" class="el-icon-warning-outline" /></div>
                </div>
                <div class="flex-row" style="justify-content: flex-start; margin-top: 10px;">
                    <span>管理：</span>
                    <div class="flex-row" v-if="certificateInfo.guanli && certificateInfo.guanli.startTime" :style="cellStyle(certificateInfo.guanli)">
                        <span>证书有效期：{{ certificateInfo.guanli.startTime }}</span>
                        <span style="margin-left: 5px; margin-right: 5px;"> 至 </span>
                        <span>{{ certificateInfo.guanli.endTime }}</span>
                    </div>
                    <div class="flex-row" v-else>无 <i style="color: #FF8D19; margin-left: 10px;" class="el-icon-warning-outline" /></div>
                </div>
                <div class="flex-row" style="justify-content: flex-start; margin-top: 10px;">
                    <span>设备：</span>
                    <div class="flex-row" v-if="certificateInfo.shebei && certificateInfo.shebei.startTime" :style="cellStyle(certificateInfo.shebei)">
                        <span>证书有效期：{{ certificateInfo.shebei.startTime }}</span>
                        <span style="margin-left: 5px; margin-right: 5px;"> 至 </span>
                        <span>{{ certificateInfo.shebei.endTime }}</span>
                    </div>
                    <div class="flex-row" v-else>无 <i style="color: #FF8D19; margin-left: 10px;" class="el-icon-warning-outline" /></div>
                </div>
                <div class="flex-row" style="justify-content: flex-start; margin-top: 10px;">
                    <span>网管：</span>
                    <div class="flex-row" v-if="certificateInfo.wangguan && certificateInfo.wangguan.startTime" :style="cellStyle(certificateInfo.wangguan)">
                        <span>证书有效期：{{ certificateInfo.wangguan.startTime }}</span>
                        <span style="margin-left: 5px; margin-right: 5px;"> 至 </span>
                        <span>{{ certificateInfo.wangguan.endTime }}</span>
                    </div>
                    <div class="flex-row" v-else>无 <i style="color: #FF8D19; margin-left: 10px;" class="el-icon-warning-outline" /></div>
                </div>
                <div class="flex-row" style="justify-content: flex-start; margin-top: 10px;">
                    <span>外掺：</span>
                    <div class="flex-row" v-if="certificateInfo.waican && certificateInfo.waican.startTime" :style="cellStyle(certificateInfo.waican)">
                        <span>证书有效期：{{ certificateInfo.waican.startTime }}</span>
                        <span style="margin-left: 5px; margin-right: 5px;"> 至 </span>
                        <span>{{ certificateInfo.waican.endTime }}</span>
                    </div>
                    <div class="flex-row" v-else>无 <i style="color: #FF8D19; margin-left: 10px;" class="el-icon-warning-outline" /></div>
                </div>
                <div class="flex-row" style="justify-content: flex-start; margin-top: 10px;">
                    <span>化学：</span>
                    <div class="flex-row" v-if="certificateInfo.huaxue && certificateInfo.huaxue.startTime" :style="cellStyle(certificateInfo.huaxue)">
                        <span>证书有效期：{{ certificateInfo.huaxue.startTime }}</span>
                        <span style="margin-left: 5px; margin-right: 5px;"> 至 </span>
                        <span>{{ certificateInfo.huaxue.endTime }}</span>
                    </div>
                    <div class="flex-row" v-else>无 <i style="color: #FF8D19; margin-left: 10px;" class="el-icon-warning-outline" /></div>
                </div>
                <div class="flex-row" style="justify-content: flex-start; margin-top: 10px;">
                    <span>钢筋：</span>
                    <div class="flex-row" v-if="certificateInfo.gangjin && certificateInfo.gangjin.startTime" :style="cellStyle(certificateInfo.gangjin)">
                        <span>证书有效期：{{ certificateInfo.gangjin.startTime }}</span>
                        <span style="margin-left: 5px; margin-right: 5px;"> 至 </span>
                        <span>{{ certificateInfo.gangjin.endTime }}</span>
                    </div>
                    <div class="flex-row" v-else>无 <i style="color: #FF8D19; margin-left: 10px;" class="el-icon-warning-outline" /></div>
                </div>
                <div class="flex-row" style="justify-content: flex-start; margin-top: 10px;">
                    <span>构件：</span>
                    <div class="flex-row" v-if="certificateInfo.goujian && certificateInfo.goujian.startTime" :style="cellStyle(certificateInfo.goujian)">
                        <span>证书有效期：{{ certificateInfo.goujian.startTime }}</span>
                        <span style="margin-left: 5px; margin-right: 5px;"> 至 </span>
                        <span>{{ certificateInfo.goujian.endTime }}</span>
                    </div>
                    <div class="flex-row" v-else>无 <i style="color: #FF8D19; margin-left: 10px;" class="el-icon-warning-outline" /></div>
                </div>
            </el-row>
        </div>

        <el-drawer
            title="证书编辑"
            :visible.sync="drawer"
            direction="rtl"
            :before-close="handleClose"
        >
            <div class="flex-box flex-column h100p drawer-box">
                <div class="flex-item ofy-auto">
                    <el-form label-width="160px">
                        <el-form-item label="证书名称：">
                            <el-input 
                                v-model="editForm.certificateName" 
                                clearable
                                :placeholder="`请输入证书名称`"
                                style="width: 350px"
                            />
                        </el-form-item>
                        <el-form-item label="证书编号：">
                            <el-input 
                                v-model="editForm.certificateNo" 
                                clearable
                                :placeholder="`请输入证书编号`"
                                style="width: 350px"
                            />
                        </el-form-item>
                        <el-form-item label="发放单位：">
                            <el-input 
                                v-model="editForm.certificateCompany" 
                                clearable
                                :placeholder="`请输入发放单位`"
                                style="width: 350px"
                            />
                        </el-form-item>

                        <el-form-item
                            :label="`营业执照图片：`"
                        >
                            <div class="flex-row img-box" style="justify-content: flex-start; flex-wrap: wrap;">
                                <div v-for="(item, index) in certificateImgList" :key="item.filePath" style="position: relative;">
                                    <img style="width: 148px; height: 148px; margin-right: 10px; object-fit: cover;" :src="item.filePath" />
                                    <i class="el-icon-delete" style="position: absolute; right: 0; top: 0; padding: 10px;" @click="handleCertificateImgRemove(item, index)"></i>
                                </div>
                                
                                <el-upload
                                    :action="baseUrl + '/upload/file'"
                                    list-type="picture-card"
                                    :show-file-list="false"
                                    :file-list="certificateImgList"
                                    :on-success="handleCertificateImgSuccess">
                                    <i class="el-icon-plus avatar-uploader-icon"></i>
                                </el-upload>
                            </div>
                        </el-form-item>

                        <el-form-item
                            v-for="(el,index) in formContent"
                            :key="index"
                            :label="`${el.label}：`"
                            :required="el.required || false"
                        >
                            <el-date-picker
                                type="date"
                                v-model="el.startTime"
                                placeholder="选择日期"
                                format="yyyy-MM-dd"
                                value-format="yyyy-MM-dd" />
                            
                            <span style="margin-left: 5px; margin-right: 5px;"> 至 </span>
                            <el-date-picker
                                type="date"
                                v-model="el.endTime"
                                placeholder="选择日期"
                                format="yyyy-MM-dd"
                                value-format="yyyy-MM-dd" />
                        </el-form-item>
                    </el-form>
                </div>
                <div class="drawer-footer">
                    <el-button type="primary" @click="drawer = false" plain>取消</el-button>
                    <el-button type="primary" @click="saveTarget">保存</el-button>
                </div>
            </div>
        </el-drawer>
    </div>
  </template>
  
<script>
import { supplierMaterialsColumn as tableColumn } from "./config.js"
import Pagination from "@/components/Pagination/index.vue";
import DrawerForm from "@/components/drawerForm.vue";
export default {
    components: {
        Pagination,
        DrawerForm
    },
    data() {
        return {
            baseUrl: process.env.NODE_ENV === 'development' ? "/api" : "/mes-platform",
            filePrefix: this.$store.state.loginStore.userInfo.filePrefix,

            loading: false,
            detailInfo: {},
            formContent: [
                {
                    label: "配比",
                    prop: "peibi",
                    startTime: "",
                    endTime: ""
                },
                {
                    label: "砼操",
                    prop: "tongcao",
                    startTime: "",
                    endTime: ""
                },
                {
                    label: "水泥",
                    prop: "shuini",
                    startTime: "",
                    endTime: ""
                },
                {
                    label: "骨料",
                    prop: "guliao",
                    startTime: "",
                    endTime: ""
                },
                {
                    label: "砂浆",
                    prop: "shajiang",
                    startTime: "",
                    endTime: ""
                },
                {
                    label: "管理",
                    prop: "guanli",
                    startTime: "",
                    endTime: ""
                },
                {
                    label: "设备",
                    prop: "shebei",
                    startTime: "",
                    endTime: ""
                },
                {
                    label: "网管",
                    prop: "wangguan",
                    startTime: "",
                    endTime: ""
                },
                {
                    label: "外掺",
                    prop: "waican",
                    startTime: "",
                    endTime: ""
                },
                {
                    label: "化学",
                    prop: "huaxue",
                    startTime: "",
                    endTime: ""
                },
                {
                    label: "钢筋",
                    prop: "gangjin",
                    startTime: "",
                    endTime: ""
                },
                {
                    label: "构件",
                    prop: "goujian",
                    startTime: "",
                    endTime: ""
                }
            ],
            drawer: false,
            editForm: {
                id: "",
                certificateName: "",
                certificateNo: "",
                certificateCompany: "",
            },
            certificateImgList: [],
            certificateImg: [],
            certificateInfo: {},
        };
    },

    created() {
        this.queryDetailResp();
        this.queryUserCertificateAllResp();
    },
    methods: {
        cellStyle(row) {
            if (!row) return {color: '#333'}
            if (row.warningStatus == '1') {
                return {color: '#FF7F50'}
            } else if (row.warningStatus == '2') {
                return {color: '#FF4500'}
            } else {
                return {color: '#333'}
            }
        },
        queryDetailResp() {
            this.$api.queryUserInfo(`id=${this.$route.query.id}`, this)
            .then(res => {
                if (res.code == 1) {
                    this.detailInfo = res.data;
                }else{
                    this.$message.error(res.msg || '查询失败')
                }
            });
        },

        queryUserCertificateAllResp() {
            this.$api.queryUserCertificateAll({userId: this.$route.query.id}, this)
            .then(res => {
                if (res.code == 1) {
                    this.certificateInfo = res.data.list.length > 0 ? res.data.list[0] : {};
                    if(this.certificateInfo.certificateImg){
                        let imgList = this.certificateInfo.certificateImg.split(",");
                        imgList.map((item, index) => {
                            this.certificateImg.push(item);
                            this.certificateImgList.push({
                                fileName: item,
                                filePath: this.filePrefix + item
                            });
                        });
                        this.certificateImg = imgList;
                    }
                    this.editForm = {
                        id: this.certificateInfo.id || "",
                        certificateName: this.certificateInfo.certificateName || "",
                        certificateNo: this.certificateInfo.certificateNo || "",
                        certificateCompany: this.certificateInfo.certificateCompany || "",
                    }
                    this.formContent.map((item, index) => {
                        this.$set(item, "startTime", (this.certificateInfo[item.prop] || {}).startTime || "");
                        this.$set(item, "endTime", (this.certificateInfo[item.prop] || {}).endTime || "");
                    });
                }else{
                    this.$message.error(res.msg || '查询失败')
                }
            });
        },

        handleCertificate() {
            this.drawer = true;
        },

        saveTarget() {
            let api = this.certificateInfo.id ? "updateUserCertificate" : "addUserCertificate";
            let param = {
                userId: this.$route.query.id,
                ...this.editForm,
            }
            param["certificateImg"] = this.certificateImg.join(",");
            this.formContent.map(item => {
                param[item.prop] = {
                    startTime: item.startTime,
                    endTime: item.endTime,
                }
            });
            this.$api[api](param).then(res => {
                if (res.code == 1) {
                    this.$message({
                        showClose: true,
                        message: "操作成功",
                        type: "success"
                    });
                    this.drawer = false;
                    this.queryUserCertificateAllResp();
                }else{
                    this.$message.error(res.msg || '操作失败')
                }
            });
        },
        handleCertificateImgRemove(file, index) {
            this.certificateImgList.splice(index, 1);
            this.certificateImg.splice(index, 1);
        },
        handleCertificateImgSuccess(response) {
            console.log(">>response>>>", response);
            if (response.code == 1) {
                this.certificateImgList.push(response.data);
                this.certificateImg.push(response.data.fileName);
            }
        },

        handleClose(done) {
            this.$confirm('确认关闭？')
            .then(_ => {
                if(done){
                    done();
                }
                this.drawer = false;
            })
            .catch(_ => {});
        }
    },
};
</script>

<style scoped lang="scss">
.content-box{
    padding: 16px;
}

.content{
    width: 100%;
    height: 100%;
    padding: 16px;
    background: #FFFFFF;
    border-radius: 16px;
    overflow: auto;
}
.row-title {
  border-bottom: #DDDFE6 1px solid;
  padding-bottom: 8px;
  position: relative;
  .row-title-label {
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 16px;
    color: #1F2329;
  }
}
.dg-card-view {
  margin-top: 24px;
  width: 100%;
  .dj-card {
    width: 177px;
    height: 72px;
    border-radius: 4px;
    position: relative;
    justify-content: center;
    .dj-label {
      font-family: PingFangSC, PingFang SC;
      font-size: 14px;
      color: #6A727D;
      margin-top: 4px
    }
    .dj-value {
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      font-size: 20px;
      color: #1F2329;
    }
    .dj-img {
      width: 46px;
      height: 46px;
      position: absolute;
      right: 0;
      bottom: 0
    }
  }
}
.cs-view {
  width: '100%';
  height: 52px;
  background: #F2F6FE;
  border-radius: 4px;
  margin-top: 16px;
  padding-left: 16px;
  padding-right: 16px;
  font-size: 14px;
  color: #6A727D;
}
.chart-view {
  margin-right: 12px; 
  border: #DDDFE6 1px solid; 
  border-radius: 4px;
  height: 331px;
  margin-top: 24px;
  padding: 16px;
  .qdsjb-chart {
    width: 100%;
    height: 260px;
    margin-top: 16px;
  }
}
.sjb-view {
  width: calc((100% - 20px) / 3);
  height: 331px;
  border-radius: 4px;
  border: 1px solid #DDDFE6;
  justify-content: flex-start;

  .no-view {
    background: rgba(80,127,153,0.1);
    padding: 24px;
    width: 100%;
    text-align: center;
  }
  .value-view {
    margin-top: 38px;
    .value-label {
      font-weight: 600;
      font-size: 20px;
      color: #1F2329;
    }
    .desc-label {
      font-size: 14px;
      color: #6A727D;
    }
  }
}

.yz-form-item {
  font-weight: 400;
  font-size: 14px;
  color: #1F2021;
  justify-content: flex-start;
  padding-left: 24px;
  padding-bottom: 0px;
}
.yzset-drawer__footer {
  position: absolute;
  bottom: 30px;
  right: 30px;
}

.sub-table {
  position: relative;
  height: calc(100% - 310px);
  margin-top: 40px;
  .table-view {
    height: 100%;
  }
  .page-view {
    margin-top: 10px
  }
}
::v-deep .el-table__empty-block { // Element自带类名
  height: 360px !important;
}
::v-deep .el-row::before, .el-row::after {
  content: revert;
}
::-webkit-scrollbar {
	display:none
}

.supplier-no{
    margin-top:11px;
    font-weight: bold;
}
.supplier-name-status{
    font-size:14px;
    color:#fff;
    width: 40px;
    height: 22px;
    line-height: 22px;
    border-radius: 4px;
    background: rgba(0, 186, 108, 1);
    display: inline-block;
    text-align: center;
    margin-left:16px;
}
.supplier-name-status2{
    font-size:14px;
    color:#fff;
    width: 40px;
    height: 22px;
    line-height: 22px;
    border-radius: 4px;
    background:#9CA5B9;
    display: inline-block;
    text-align: center;
    margin-left:16px;
}
.jbz-intro {
    position:relative;
    .display-inline{
        display: inline-block;
        margin-right:64px;
        font-size:14px;
    }
    .font_1 {
        font-size: 16px;
        font-weight: bold;
        color: rgba(31, 35, 41, 1);
    }
    .font_2 {
        font-size: 14px;
        font-weight: 400;
        color: rgba(106, 114, 125, 1);
    }
    .font_3 {
        font-size: 14px;
        font-weight: 400;
        color: rgba(31, 35, 41, 1);
    }
    .vertical-space_1 {
        margin-top: 11px;
    }
    .vertical-space_2 {
        margin-top: 22px;
    }
    .vertical-space_3 {
        margin-top: 15px;
    }
}

.supplier-ul{
    .supplier-flex{
        display: inline-block;
        justify-content: space-around;
        // width:60%;
        margin-top:40px;
        margin-bottom:14px;
    }
    .supplier-flex-li{
        height:40px;
        line-height:40px;
        border-right:1px solid #ddd;
        margin:10px;
        margin-right:24px;
        padding-right:34px;
        display: inline-block;
        &:last-child{
            border-right:0px;
        }
        .supplier-flex-num{
            font-size:26px;
            text-align: center;
            line-height:35px;
            margin-top:-15px;
            font-weight: bold;
        }
        .supplier-flex-count{
            font-size:14px;
            color:rgba(106,114,125,1);
            text-align: center;
        }
    }
}
</style>