<template>
  <div class="content-box">
    <div class="flex-box flex-column content">
      <div class="search-box flex-box">
        <el-form class="flex-item" ref="searchForm" :inline="true" :model="searchForm" :disabled="loading">
          <el-form-item label="物料类型：">
            <el-select
              v-model="searchForm.projectCategory" 
              filterable clearable 
              placeholder="请选择物料类型" 
              @change="materialTypeChange"
              style="width: 180px">
              <el-option
                v-for="item in materialTypeList2"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="物料名称：">
            <el-select
              v-model="searchForm.projectName" 
              filterable clearable 
              placeholder="请选择物料名称" 
              @change="materialsNameChange"
              style="width: 180px">
              <el-option
                v-for="item in materialsNameList"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          
          <el-form-item label="物料规格：">
            <el-select
              v-model="searchForm.projectSpecs" 
              filterable clearable 
              placeholder="请选择物料规格" 
              style="width: 180px">
              <el-option
                v-for="item in materialsSpecsList"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item> -->
          <el-form-item label="指标类型：">
            <el-radio-group v-model="checkType" @change="checkTypeChange">
              <!-- 这里批检 快检的值反了，但不影响功能，因为是两个接口  没像后端传值 -->
              <el-radio :label="2">快检</el-radio>
              <el-radio :label="1">批检</el-radio>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" :disabled="loading" @click="handleFilter">搜索</el-button>
            <el-button type="text" icon="el-icon-refresh-right" :disabled="loading" @click="resetForm()">重置</el-button>
          </el-form-item>
        </el-form>
        <el-button type="primary" @click="handleSetTarget()">新增{{checkType == 1 ? '批检' : '快检'}}指标</el-button>
        <el-button type="primary" @click="importEquipmentExcelClick()">
          <el-upload
              v-show="false"
              ref="uploadImport"
              :action="baseUrl + '/devops/importCheckBatchConfigExcel'"
              :show-file-list="false"
              :auto-upload="true"
              :before-upload="importClickBefore"
              :on-success="importClickSuccess">
          </el-upload>
          导入批检指标
        </el-button>
        <el-button type="primary" @click="downloadTemplate()">下载模板</el-button>
      </div>
      
      <div class="flex-item overHide">
        <div class="scroll-div">
          <el-table
            :data="tableData"
            v-loading="loading"
            style="width: 100%">
            <af-table-column
              v-for="item in tableColumn" 
              :key="item.prop" :prop="item.prop" 
              v-if="!item.type || item.type === checkType"
              :label="item.label" 
              :formatter="item.prop === 'projectCategory' ? (row) => item.formatter(row,materialTypeList2) : item.formatter"
              :fixed="item.fixed" 
              :width="item.width || ''"
              align="center" 
            />
            <af-table-column width="280" label="操作" align="center" key="handle" :resizable="false">
              <template slot-scope="scope">
                <el-button type="text" size="small" @click="handleUpdateMust(scope.row)"> {{scope.row.isMust === 1 ? '取消必检' : '设为必检'}}</el-button>
                <el-button type="text" size="small" @click="handleSetTarget(scope.row)">修改</el-button>
                <el-button type="text" size="small" style="color: #ff0000;" @click="handleDel(scope.row)">删除</el-button>
              </template>
            </af-table-column>
          </el-table>
        </div>
      </div>
      <div class="mt16 mb4">
        <Pagination
          :total="total" 
          :pageNum="pageObj.pageNum" 
          :pageSize="pageObj.pageSize" 
          @getData="initData" 
        />
      </div>
    </div>
    
    
    <DrawerForm
      :formContent="formContent"
      :name="checkType == 1 ? '批检' : '快检'"
      ref="drawerForm"
      @saveTarget="saveTarget"
    >
      <template v-slot:qualified="{editForm}">
        <div class="multi-form-item-box">
          <el-select
            v-model="editForm.qualifiedCheckOneType" 
            filterable clearable 
            placeholder="请选择" 
            style="width: 130px">
            <el-option
              v-for="item in equalityTypeList"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
          <el-input v-model="editForm.qualifiedCheckOneValue" clearable
            placeholder="请输入" 
            type="number"
            style="width: 200px" 
          />
        </div>
        <div class="multi-form-item-box">
          <el-select
            v-model="editForm.qualifiedCheckTwoType" 
            filterable clearable 
            placeholder="请选择" 
            style="width: 130px">
            <el-option
              v-for="item in equalityTypeList"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
          <el-input v-model="editForm.qualifiedCheckTwoValue" clearable
            placeholder="请输入" 
            type="number"
            style="width: 200px" 
          />
        </div>
      </template>
      
      <template v-slot:unQualified="{editForm}">
        <div class="multi-form-item-box">
          <el-select
            v-model="editForm.unQualifiedCheckOneType" 
            filterable clearable 
            placeholder="请选择" 
            style="width: 130px">
            <el-option
              v-for="item in equalityTypeList"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
          <el-input v-model="editForm.unQualifiedCheckOneValue" clearable
            placeholder="请输入" 
            type="number"
            style="width: 200px" 
          />
        </div>
        <div class="multi-form-item-box">
          <el-select
            v-model="editForm.unQualifiedCheckTwoType" 
            filterable clearable 
            placeholder="请选择" 
            style="width: 130px">
            <el-option
              v-for="item in equalityTypeList"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
          <el-input v-model="editForm.unQualifiedCheckTwoValue" clearable
            placeholder="请输入" 
            type="number"
            style="width: 200px" 
          />
        </div>
      </template>
      
      
      <template v-slot:compromise="{editForm}">
        <div class="multi-form-item-box">
          <el-select
            v-model="editForm.compromiseCheckOneType" 
            filterable clearable 
            placeholder="请选择" 
            style="width: 130px">
            <el-option
              v-for="item in equalityTypeList"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
          <el-input v-model="editForm.compromiseCheckOneValue" clearable
            placeholder="请输入" 
            type="number"
            style="width: 200px" 
          />
        </div>
        <div class="multi-form-item-box">
          <el-select
            v-model="editForm.compromiseCheckTwoType" 
            filterable clearable 
            placeholder="请选择" 
            style="width: 130px">
            <el-option
              v-for="item in equalityTypeList"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
          <el-input v-model="editForm.compromiseCheckTwoValue" clearable
            placeholder="请输入" 
            type="number"
            style="width: 200px" 
          />
        </div>
      </template>
      
      <template v-slot:deduction="{editForm}">
        <div class="multi-form-item-box">
          <span>每</span>
          <el-input v-model="editForm.deductionThreshold" clearable
            placeholder="数字" 
            type="number"
            style="width: 120px; margin: 0;" 
          />
          <span>%，扣除</span>
          <el-input v-model="editForm.deductionPercentage" clearable
            placeholder="数字" 
            type="number"
            style="width: 120px; margin: 0;" 
          />
          <span>%</span>
        </div>
      </template>

      <!-- <template v-slot:selectDialog="{editForm, el}">
        <el-input
          :disabled="true"
          v-model="editForm[el.prop]" 
          clearable
          :placeholder="el.placeholder || `请选择${el.label}`"
          :style="{'width': el.width ? el.width : 350 + 'px'}"
        >
          
        </el-input>
      </template> -->
      <template v-slot:selectDialog>
        <el-input
          readonly
          clearable
          :placeholder="`请选择`"
          :style="{'width': 350 + 'px'}"
          :value="selectEquipmentName"
          @click.native="selectEquipment"
        >
          
        </el-input>
      </template>
    </DrawerForm>

    <!-- 仪器设备选择 -->
    <el-dialog
      title="选择仪器设备"
      width="1000px"
      :visible.sync="equipmentTableShow"
      class="claim-dialog-box"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div class="flex-box flex-column h100p drawer-box">
        <div class="flex-item">
          <el-table
            ref="multipleTable"
            :data="equipmentOption"
            tooltip-effect="dark"
            style="width: 100%; height: 600px; overflow-y: scroll;"
            @selection-change="handleSelectionChange"
          >
            <el-table-column
              type="selection"
              width="55">
            </el-table-column>
            <el-table-column
              prop="equipmentNo"
              label="设备编号"
              width="120">
            </el-table-column>
            <el-table-column
              prop="equipmentName"
              label="设备名称"
              width="120">
            </el-table-column>
            <el-table-column
              prop="equipmentType"
              label="设备类型"
              width="120">
            </el-table-column>
            <el-table-column
              prop="technicalParameter"
              label="技术参数"
              width="120">
            </el-table-column>
            <el-table-column
              prop="equipmentStatus"
              label="设备状态"
              width="120">
            </el-table-column>
            <el-table-column
              prop="custodyUserName"
              label="保管人"
              show-overflow-tooltip>
            </el-table-column>
          </el-table>
        </div>
        <div class="drawer-footer">
           <el-button type="primary" @click="equipmentTableShow = false"plain>取消</el-button>
           <el-button type="primary" @click="saveEquipmentTable()">保存</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { pjColumn as tableColumn } from "./config.js"
import Pagination from "@/components/Pagination/index.vue";
import DrawerForm from "@/components/drawerForm.vue";
import getOpt from "@/common/js/getListData.js"
export default {
  name:'paramsConfig',
  components: {
    Pagination,
    
    DrawerForm
  },
  data() {
    return {
      addApi: 'addTarget',
      delApi: 'delTarget',
      updateApi: 'setTarget',
      getListApi: 'getTargetList',
      updateMust: 'updateMust',
      
      checkType: 1,
      loading: false,
      // 设置时间选择
      pickerOptions: {
        disabledDate(time) {
          let deadline = Date.now() - 60 * 60 * 1000;
          return time.getTime() > deadline //
        },
      },
      searchForm: {},
      materialTypeList2: [],
      materialsNameList: [],
      materialsSpecsList: [],
      testProjectList: [],
      
      projectSpecsList: [
        {
          label: '车车都检',
          value: 1,
        },{
          label: '按重量',
          value: 2,
        }
      ],
      equalityTypeList: [
        {
          label: '大于',
          value: 1,
        },{
          label: '大于等于',
          value: 2,
        },{
          label: '等于',
          value: 3,
        },{
          label: '不等于',
          value: 4,
        },{
          label: '小于',
          value: 5,
        },{
          label: '小于等于',
          value: 6,
        }
        // ,{
        //   label: '检测方法',
        //   value: 'experimentGist',
        // },{
        //   label: '评定依据',
        //   value: 'judgeGist',
        // }
      ],
      
      tableColumn: tableColumn,
      pageObj: {
        pageNum: 1, // 页数
        pageSize: 10, // 条数
      },
      total: 1,
      tableData: [],
      
      activeRow: {},//编辑对象
      formContent:[],
      
      formContentPublic: [
        {
          type: 'select',
          label: '物料类型',
          prop: 'dictValueCode',
          //value: '',
          //placeholder: 'select',
          //width: '',
          handle: 'materialTypeChange',
          options: [],
        },
        {
          type: 'select',
          label: '物料名称',
          prop: 'projectName',
          handle: 'materialsNameChange',
          isHide: true,
          options: [],
        },
        {
          type: 'select',
          label: '物料规格',
          prop: 'projectSpecs',
          isHide: true,
          options: [],
        },
        {
          type: 'select',
          label: '试验项目',
          prop: 'testProjectCode',
          options: [],
        },
        
        {
          type: 'select',
          label: '检验类型',
          prop: 'checkType',
          options: [{
            label: '常规',
            value: 0
          },{
            label: '月检',
            value: 10
          },{
            label: '季检',
            value: 20
          },{
            label: '半年检',
            value: 30
          },{
            label: '年检',
            value: 40
          }],
        },
        {
          label: '检测值单位',
          type: 'input',
          prop: 'checkValueUnit',
        },
        {
          type: 'input',
          label: '国标值',
          prop: 'nationalStandardValue',
        },
        {
          type: 'input',
          label: '检测方法',
          prop: 'experimentGist',
        },
        {
          type: 'input',
          label: '评定依据',
          prop: 'judgeGist',
        },
        {
          label: '检测方法',
          type: 'input',
          prop: 'checkMethod',
        },
        {
          label: '仪器设备',
          type: 'selectDialog',
          prop: 'equipmentNo',
        },
        // {
        //   label: '合格标准',
        // }
      ],
      rules: {
        // rname: [{ required: true, message: "请输入姓名", trigger: "blur" }],
        // age: [
        //   { required: true, message: "年龄不能为空", trigger: "blur" },
        //   {
        //     type: "number",
        //     message: "年龄必须为数字值",
        //     trigger: "blur",
        //   },
        // ],
        // email: [
        //   { required: true, message: "请输入邮箱地址", trigger: "blur" },
        //   {
        //     type: "email",
        //     message: "请输入正确的邮箱地址",
        //     trigger: "blur",
        //   },
        // ],
        // school: [{ required: true, message: "请输入毕业学校", trigger: "blur" }],
        // phone: [{ required: true, message: "请输入电话", trigger: "blur" }],
        // gender: [{ required: true, message: "请输入性别", trigger: "blur" }],
        // education: [{ required: true, message: "请输入学历", trigger: "blur" }],
        // graduateDate: [{ required: true, message: "请输入毕业日期", trigger: "blur" }],
        // source: [{ required: true, message: "请输入渠道来源", trigger: "blur" }],
        // isDeliverType: [{ required: true, message: "请输入投递状态", trigger: "blur" }],
        // resumeDig: [{ required: true, message: "请输入简历挖掘", trigger: "blur" }],
        // principals: [{ required: true, message: "请输入负责人", trigger: "blur" }],
        // applySectors: [{ required: true, message: "请输入应聘部门", trigger: "blur" }],
        // applyStations: [{ required: true, message: "请输入应聘岗位", trigger: "blur" }],
        // interfacingDate: [{ required: true, message: "请输入沟通日期", trigger: "blur" }],
      },

      selectEquipmentList: [],
      selectEquipmentName: "",
      equipmentTableShow: false,
      equipmentOption: [],
    };
  },
  
  created() {
    this.getMaterialType();//获取物料类型
    this.initData();
    
    
  },
  methods: {
    //获取物料类型
    getMaterialType(){
      this.$api.getDictValue({
        dictCode: 'MASTERIAL_TYPE'
      },this).then(res =>{
        if(res.succ){
          this.materialTypeList2 = res.data.list.map(i => {
            return {
              label: i.dictValueName,
              dictCode: i.dictCode,
              id: i.id,
              correlationCode: i.correlationCode,
              value: i.dictValueCode
            }
          })

          this.formContentPublic[0].options = res.data.list.map(i => {
            return {
              label: i.dictValueName,
              value: i.dictValueCode
            }
          });
          this.formContent = [
            ...this.formContentPublic,
            // {
            //   label: '不合格标准',
            // },
            // {
            //   type: 'radio',
            //   label: '批检规则',
            //   prop: 'batchRules',
            //   options: [{
            //     label: '车车都检',
            //     value: 1
            //   },{
            //     label: '按方量',
            //     value: 2
            //   }],
            // },
            // {
            //   type: 'number',
            //   label: '检测数量',
            //   prop: 'batchThreshold',
            // },
            {
              type: 'radio',
              label: '上传检验图片',
              prop: 'isUploadImg',
              options: [{
                label: '是',
                value: 1
              },{
                label: '否',
                value: 0
              }],
            },
            {
              type: 'radio',
              label: '是否必检',
              prop: 'isMust',
              options: [{
                label: '是',
                value: 1
              },{
                label: '否',
                value: 0
              }],
            },
          ]
        }
      })
    },
    
    getNameDictCode(oVal, list){
      for(let i =0;i<list.length; i++){
        if(oVal == list[i].value){
          return list[i].dictValueCode;
        }
      }
    },
    
    //获取物料名称+项目名称
    materialTypeChange(val){//formContent改动时要注意这里索引[1]
      console.log(val,this.formContent);
      // 7 代表混凝土
      if(val == '7'){
        this.$set(this.formContent[1], 'isHide', true)
        this.$set(this.formContent[2], 'isHide', true)
        if(this.checkType === 2){
          this.$set(this.formContent[8], 'options',  [{
              label: '车车都检',
              value: 1
            },{
              label: '按方量',//按方数(m³)',
              value: 2
            },{
              label: '按小票数(张)',
              value: 3
            }]
          )
        }
        
      }else{
        // 全部都认为是隐藏，禅道 4568
        this.$set(this.formContent[1], 'isHide', true)
        this.$set(this.formContent[2], 'isHide', true)
        if(this.checkType === 2){
          this.$set(this.formContent[8], 'options', [{
              label: '车车都检',
              value: 1
            },{
              label: '按重量',
              value: 2
            },{
              label: '按车数',
              value: 3
            },{
              label: '按时间',
              value: 4
            }]
          )
        }
      }
      this.$refs.drawerForm.editForm.projectName = "";
      this.$refs.drawerForm.editForm.projectSpecs = "";
      this.$refs.drawerForm.editForm.testProjectCode = "";
      this.$refs.drawerForm.editForm.testProjectName = "";

      // const oval = this.getNameDictCode(val, this.materialTypeList2)
      this.$api.getMaterialsNameByType(`materialType=${val}`, this).then(res =>{
        if(res.succ){
          this.materialsNameList = res.data.list.map(i => {
            return {
              label: i,
              value: i
            }
          })
          this.formContentPublic[1].options = this.materialsNameList
        }
      })

      let curr = this.materialTypeList2.filter(item => item.value == val);
      let testType = "";
      if (curr.length > 0) {
        testType = curr[0].correlationCode
      }
      let params = {
        testType: testType,
        //isQuick: 1,//this.checkType == 2 ? 1 : 0 ,//1-是 0-不是  
        //isBatch: this.checkType == 1 ? 1 : 0,//1-是  0-不是
      }
      if(this.checkType == 2){
        params.isQuick = 1;
      }
      this.$api.getTestProject2(params,this).then(res =>{
        if(res.succ){
          this.testProjectList = res.data.list.map(i => {
            return {
              label: i.testName,
              value: i.testCode
            }
          })
          this.formContentPublic[3].options = this.testProjectList
        }
      })
    },
    //获取物料规格
    materialsNameChange(val){
      console.log(val);
      this.$api.getMaterialsSpecs(`materialName=${val}`,this).then(res =>{
        if(res.succ){
          this.materialsSpecsList = res.data.list.map(i => {
            return {
              label: i,
              value: i
            }
          })
          this.formContentPublic[2].options = this.materialsSpecsList
        }
      })
    },
    
    checkTypeChange(val){
      console.log(val)
      console.log(this.checkType)
      if(val === 1){
        this.addApi= 'addTarget';
        this.delApi= 'delTarget';
        this.updateApi= 'setTarget';
        this.getListApi= 'getTargetList';
        this.updateMust= 'updateMust';
        this.formContent = [
          ...this.formContentPublic,
          // {
          //   label: '不合格标准',
          // },
          // {
          //   type: 'radio',
          //   label: '批检规则',
          //   prop: 'batchRules',
          //   options: [{
          //     label: '车车都检',
          //     value: 1
          //   },{
          //     label: '按重量',
          //     value: 2
          //   }],
          // },
          // {
          //   type: 'number',
          //   label: '检测数量',
          //   prop: 'batchThreshold',
          // },
          {
            type: 'radio',
            label: '上传检验图片',
            prop: 'isUploadImg',
            options: [{
              label: '是',
              value: 1
            },{
              label: '否',
              value: 0
            }],
          },
          {
            type: 'radio',
            label: '是否必检',
            prop: 'isMust',
            options: [{
              label: '是',
              value: 1
            },{
              label: '否',
              value: 0
            }],
          },
        ]
      }else{
        this.addApi= 'addcheckQuick';
        this.delApi= 'delcheckQuick';
        this.updateApi= 'setCheckQuick';
        this.getListApi= 'getcheckQuickList';
        this.updateMust= 'updateQuickMust';
        this.formContent = [
          ...this.formContentPublic,
          
          // {
          //   label: '让步指标',
          // },
          // {
          //   label: '扣除规则',
          // },
          {
            type: 'radio',
            label: '快检规则',
            prop: 'quickRules',
            options: [{
              label: '车车都检',
              value: 1
            },{
              label: '按方量',//按方数(m³)',
              value: 2
            },{
              label: '按小票数(张)',
              value: 3
            }],
          },
          {
            type: 'number',
            label: '检测数量',
            prop: 'quickThreshold',
          },
          
          
          {
            type: 'radio',
            label: '上传检验图片',
            prop: 'isUploadImg',
            options: [{
              label: '是',
              value: 1
            },{
              label: '否',
              value: 0
            }],
          },
          {
            type: 'radio',
            label: '是否必检',
            prop: 'isMust',
            options: [{
              label: '是',
              value: 1
            },{
              label: '否',
              value: 0
            }],
          },
        ]
      }
      this.initData(1);
    },
    handleFilter() {
      console.log(this.searchForm)
      this.initData(1);
    },
    resetForm(){
      this.searchForm = {};
      this.initData(1);
    },
    initData(opageNum, opageSize){
      this.loading = true;
      if (opageNum) this.pageObj.pageNum = opageNum;
      if (opageSize) this.pageObj.pageSize = opageSize;
        
      const params ={
        ...this.pageObj,
        params: this.searchForm
      }
      //获取列表
      this.$api[this.getListApi](params, this).then(res => {
        this.loading = false;
        if(res.succ){
          this.tableData = res.data.list.map(item =>{
            item.zblx = this.getListApi === 'getTargetList' ? '批检' : '快检'
            return item;
          });
          this.total = res.data.total;
        }else{
          this.$message.error(res.msg || '查询失败')
        }
      })
    },
    handleDel(row){
      this.$confirm("删除后不能恢复，确定要删除吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        //删除
        this.$api[this.delApi]({
          id: row.id
        }, this).then((res) => {
          if (res.succ) {
            this.$message({
              showClose: true,
              message: "删除成功",
              type: "success",
            });
            if(this.tableData.length == 1 && this.pageObj.pageNum > 1){
              this.pageObj.pageNum = this.pageObj.pageNum -1
            }
            this.initData();
          }
        });
      }).catch(() => {
        this.$message({
          type: "info",
          message: "已取消删除",
        });
      });
    },
    //必检
    handleUpdateMust(row){
      this.$api[this.updateMust]({
        isMust: row.isMust ^ 1,
        id: row.id
      },this).then((res) => {
        if (res.succ) {
          this.$message({
            showClose: true,
            message: "修改成功",
            type: "success",
          });
          this.initData();
        }
      });
    },
    handleSetTarget(row){
      console.log(">>handleSetTarget>>>>", row);
      this.formContentPublic[3].options = [];
      //this.activeRow = row;
      if(row){
        let curr = this.materialTypeList2.filter(item => item.value == row.projectCategory);
        if (curr.length > 0) {
          row.dictValueCode = curr[0].value;
        }
        
        this.materialTypeChange(row.dictValueCode)
      }
      if(!row){
        if(this.checkType == 1){
          row = {
            isUploadImg: 1,
            batchRules: 2,
            isMust: 1,
          }
        }else{
          row = {
            isUploadImg: 1,
            quickRules: 2,
            isMust: 1
          }
        }
      }
      this.selectEquipmentName = row.equipmentNo || "";

      this.$refs.drawerForm.initData(row);
    },
    getName(oVal, list){
      for(let i =0;i<list.length; i++){
        if(oVal == list[i].value){
          return list[i].label;
        }
      }
    },
    getNameId(oVal, list){
      for(let i =0;i<list.length; i++){
        if(oVal == list[i].value){
          return list[i].id;
        }
      }
    },
    saveTarget(formData){
      //项目名字
      let temp = this.testProjectList.find((el) => el.value == formData.testProjectCode) || {};
      formData.testProjectName = temp["label"] || "";
        
      if(formData.id){//修改this.activeRow
        formData.projectCategory = this.$refs.drawerForm.editForm.dictValueCode;
        this.$api[this.updateApi](formData, this).then((res) => {
          if (res.succ) {
            this.$message({
              showClose: true,
              message: "修改成功",
              type: "success",
            });
            this.$refs.drawerForm.handleClose();
            this.initData();
          }
        });
      }else{
        formData.projectCategory = this.$refs.drawerForm.editForm.dictValueCode;
        
        this.$api[this.addApi](formData, this).then((res) => {
          if (res.succ) {
            this.$message({
              showClose: true,
              message: "添加成功",
              type: "success",
            });
            this.initData();
            this.$refs.drawerForm.handleClose();
          }
        });
      }
      
    },

    //仪器设备
    selectEquipment(){
      this.getequipmentOption();
    },

    handleSelectionChange(val) {
      this.selectEquipmentList = val.map(item => item.equipmentNo);
      this.selectEquipmentName = this.selectEquipmentList.join(';');
    },
    saveEquipmentTable(){
      this.$refs.drawerForm.editForm.equipmentNo = this.selectEquipmentName;
      this.equipmentTableShow = false;
    },
    getequipmentOption(){
      this.$api.getEquipmentAll({}).then(res =>{
        if(res.data.list){
          this.equipmentOption =  res.data.list;
        }
        let name = this.selectEquipmentName + "";
        this.$nextTick(()=>{
          for(let i = 0; i< this.equipmentOption.length; i++){
            if(name.indexOf(this.equipmentOption[i].equipmentNo) > -1){
              this.$refs.multipleTable.toggleRowSelection(this.equipmentOption[i], true);
            }
          }
        })
      });

      this.equipmentTableShow = true;
    },

     importEquipmentExcelClick() {
      this.$refs.uploadImport.$refs['upload-inner'].handleClick()
    },

    importClickBefore(file, fileList) {
      return new Promise((resolve, reject) => {
        this.$confirm('确认导入数据吗？')
        .then(_ => {
          resolve(true);
        })
        .catch(_ => {
          reject(false);
        });
      })
    },

    importClickSuccess(response, file, fileList) {
      if (response.code == 1) {
        this.$message({
          message: "导入成功",
          type: "success",
        });
      }else{
        this.$message.error('导入失败，请重试')
      }
    },

    downloadTemplate() {
      this.$api.downloadEquipmentTemplate('', this).then(res => {
        if(res.succ){
          const url = res.data.data || '';
          window.open(url, '_blank');
        }else{
          this.$message.error(res.msg || '导出失败')
        }
      });
    }
  },
};
</script>

<style scoped lang="scss">
  .multi-form-item-box{
    padding: 0 0 4px 0;
    // display: flex;
    // justify-content: space-between;
    .el-select,.el-input{
      margin-right: 20px;
    }
  }
  ::v-deep .el-button{
    padding-left: 13px;
    padding-right: 13px;
  }
  .el-form-item{
    margin-bottom: 8px;
  }
  ::v-deep .el-form--inline{
    .el-form-item{
      margin-right: 24px;
      margin-bottom: 0;
      &:last-child{
        margin: 0;
      }
    }
  }
  ::v-deep .el-table{
    .expanded,.expanded:hover{
      background-color: #FFFBD9;
      
    }
    .expanded + tr{
      background-color: #FFFBD9;
      td{
        background-color: #FFFBD9;
      }
    }
    
    .table-child-box{
      margin: 16px;
      padding: 16px;
      background: #FFFFFF;
    }
  }
  
  .content-box{
    padding: 16px;
  }
  .content{
    width: 100%;
    height: 100%;
    padding: 16px;
    background: #FFFFFF;
    border-radius: 16px;
  }
  
  .search-box{
    padding-bottom: 16px;
    line-height: 40px;
  }
</style>