<!--
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-06-11 22:20:34
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2024-11-27 00:09:41
 * @FilePath: /quality_center_web/src/pages/systemMgt/specConfig.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div class="content-box">
        <div class="flex-box flex-column content">
            <div class="search-box flex-box">
                <!-- <el-form class="flex-item" ref="searchForm" :inline="true" :model="searchForm">
                    <el-form-item label="材料类型：">
                        <el-checkbox-group v-model="searchForm.materialsTypeList"
                        @change="initData"
                        >
                            <el-checkbox v-for="item in materialsTypeOpts" :key="item.value" :label="item.value">{{item.label}}</el-checkbox>
                        </el-checkbox-group>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="handleFilter">搜索</el-button>
                        <el-button type="text" icon="el-icon-refresh-right" @click="resetForm()">重置</el-button>
                    </el-form-item>
                </el-form> -->
                <el-button style="margin-left: calc(100% - 70px);" type="primary" @click="addDrawerForm()">新增</el-button>
            </div>

            <div class="flex-item overHide">
                <div class="scroll-div" style="height: calc(100% - 50px);">
                    <el-table
                        :data="tableData"
                        >
                        <template v-for="item in tableColumn">
                            <el-table-column
                                v-if="item.prop != 'specId'"
                                :prop="item.prop" 
                                :label="item.label" 
                                :fixed="item.fixed" 
                                :width="item.width || ''"
                                :formatter="item.prop === 'materialsType' ? (row) => item.formatter(row,materialsTypeOpts) : item.formatter"
                                align="center" 
                            />
                        </template>
                        
                        
                        <el-table-column width="220" label="操作" align="center" key="handle" :resizable="false">
                            <template slot-scope="scope">
                                <el-button type="text" size="small" @click="addDrawerForm(scope.row)">修改</el-button>
                                <el-button type="text" size="small" @click="previewAttachmentsTarget(scope.row)">附件预览</el-button>
                                <el-button type="text" size="small" style="color: #ff0000;"
                                    @click="deleteSupplierCompanyMaterials(scope.row)">删除</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
                <div class="mt16 mb4">
                    <Pagination
                    :total="total" 
                    :pageNum="pageObj.pageNum" 
                    :pageSize="pageObj.pageSize" 
                    @getData="initData" 
                    />
                </div>
            </div>
        </div>

        <el-drawer
            title="外委托"
            :visible.sync="drawer"
            direction="rtl"
            :before-close="handleDrawerClose"
        >
            <div class="flex-box flex-column h100p drawer-box">
                <div class="flex-item ofy-auto">
                    <el-form label-width="160px" 
                        :model="editForm"
                    >
                        <el-form-item
                            v-for="(el,index) in formContent"
                            :key="index"
                            :label="`${el.label}：`"
                            :required="el.required || false"
                        >
                            <el-select
                                v-if="el.type === 'select'"
                                v-model="editForm[el.prop]"
                                @change="el['change']"
                                filterable clearable 
                                :value-key="el.valueKey"
                                :placeholder="'请选择' + el.placeholder"
                                style="width: 350px"
                            >
                                <el-option
                                    v-for="item in el.options"
                                    :key="item.label"
                                    :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                            <el-input 
                                v-else-if="el.type === 'input'"
                                v-model="editForm[el.prop]" 
                                :disabled="el.disabled"
                                clearable
                                :placeholder="el.placeholder || `请输入${el.label}`"
                                style="width: 350px"
                            />
                            <el-date-picker
                                v-else-if="el.type === 'date' || el.type === 'datetime'"
                                :type="el.type"
                                v-model="editForm[el.prop]"
                                placeholder="选择日期/时间"
                                :format="el.format"
                                :value-format="el.format"
                                :style="{'width': el.width ? el.width : 350 + 'px'}"
                            >
                            </el-date-picker>
                        </el-form-item>

                        <el-form-item
                            :label="`附件：`"
                        >
                            <div class="img-box">
                                <el-upload
                                :action="baseUrl + '/upload/file'"
                                :file-list="attachmentsImgList"
                                :on-success="handleAttachmentsImgSuccess"
                                :on-remove="handleAttachmentsImgRemove">
                                <i class="el-icon-plus avatar-uploader-icon" style="color: #424E73;">点击添加附件</i>
                                </el-upload>
                            </div>
                        </el-form-item>
                    </el-form>
                </div>
                <div class="drawer-footer">
                    <el-button type="primary" @click="drawer = false" plain>取消</el-button>
                    <el-button type="primary" @click="saveTarget">保存</el-button>
                </div>
            </div>
        </el-drawer>
    
        <!-- 弹出一个模态框显示所有附件-->
        <el-dialog
                title="附件"
                :visible.sync="attachmentsImgVisible"
                width="40%"
                :before-close="handleClose"
                :close-on-click-modal="false"
                :close-on-press-escape="false"
                >
                <div class="img-box">
                    <div v-for="(item, index) in attachmentsImgList" :key="item.filePath" style="position: relative; margin-bottom: 10px;">
                        <a :href="item.url" target="_blank">
                            <i class="el-icon-document" style="margin-right: 6px;" />
                            <span>{{ item.name }}</span>
                        </a>
                    </div>
                </div>
        </el-dialog>
    </div>
</template>

<script>
import Pagination from "@/components/Pagination/index.vue";
import DrawerForm from "@/components/drawerForm.vue";
export default {
    components: {
        Pagination,
        DrawerForm
    },
    data() {
        return {

      supplierCompanyOpts: [],
      factoryOpts: [],
            pageObj: {
                pageNum: 1, // 页数
                pageSize: 10, // 条数
            },
            total: 1,
            baseUrl: process.env.NODE_ENV === 'development' ? "/api" : "/mes-platform",
            filePrefix: this.$store.state.loginStore.userInfo.filePrefix,
            
            tableData: [],
            searchForm: {
                // materialsTypeList: [],
            },
            materialsTypeOpts: [],
            tableColumn: this.$store.state.loginStore.tieBiaoFlag ? [
                {
                    label: '委托编号',
                    prop: 'outerNo',
                    width: '150'
                },
                {
                    label: '报告编号',
                    prop: 'reportNo',
                },
                {
                    label: '材料类型',
                    prop: 'materialsType',
                    formatter: (row,materialsTypeOpts) => {
                        for(let i =0;i<materialsTypeOpts.length; i++){
                            if(row.materialsType == materialsTypeOpts[i].value){
                                return materialsTypeOpts[i].label;
                            }
                        }
                    },
                },
                {
                    label: '材料名称',
                    prop: 'materialsName',
                    width: '150'
                },
                {
                    label: '材料规格',
                    prop: 'materialsSpec',
                },

                {
                    label: '试验项目',
                    prop: 'testProjectName',
                },
                {
                    label: '检测阈值',
                    prop: 'checkThreshold',
                },

                // 下面是铁标新增字段
                // {
                //     label: '供应商名称',
                //     prop: 'supplierCompanyName',
                //     width: '200'
                // },
                // {
                //     label: '厂家名称',
                //     prop: 'checkThreshold',
                //     width: '200'
                // },
                // {
                //     label: '质保书编号',
                //     prop: 'checkThreshold',
                //     width: '100'
                // },
                // 上面是铁标新增字段
                {
                    label: '检测结论',
                    prop: 'checkResult',
                },
                {
                    label: '检测单位',
                    prop: 'checkCompany',
                },
                {
                    label: '报告时间',
                    prop: 'reportTime',
                    width: '180'
                },
                {
                    label: '批准时间',
                    prop: 'approveTime',
                    width: '180'
                },
            ] : [
                {
                    label: '委托编号',
                    prop: 'outerNo',
                    width: '150'
                },
                {
                    label: '报告编号',
                    prop: 'reportNo',
                },
                {
                    label: '材料类型',
                    prop: 'materialsType',
                    formatter: (row,materialsTypeOpts) => {
                        for(let i =0;i<materialsTypeOpts.length; i++){
                            if(row.materialsType == materialsTypeOpts[i].value){
                                return materialsTypeOpts[i].label;
                            }
                        }
                    },
                },
                {
                    label: '材料名称',
                    prop: 'materialsName',
                    width: '150'
                },
                {
                    label: '材料规格',
                    prop: 'materialsSpec',
                },
                {
                    label: '试验项目',
                    prop: 'testProjectName',
                },
                {
                    label: '检测阈值',
                    prop: 'checkThreshold',
                },
                {
                    label: '检测结论',
                    prop: 'checkResult',
                },
                {
                    label: '检测单位',
                    prop: 'checkCompany',
                },
                {
                    label: '报告时间',
                    prop: 'reportTime',
                    width: '180'
                },
                {
                    label: '批准时间',
                    prop: 'approveTime',
                    width: '180'
                },
            ],
            formContent: [],
            editForm: {},
            drawer: false,

            specConfigOpts: [],
            specConfig_2Opts: [],
            testProjectOpts: [],
            
            attachmentsImgList: [],
            attachments: [],
            attachmentsImgVisible: false,
        }
    },

    mounted() {
        this.$api.getDictValue({
            dictCode: 'MASTERIAL_TYPE'
        }, this).then(res => {
            this.materialsTypeOpts = res.data.list.map(item => {
                return {
                    label: item.dictValueName,
                    value: parseInt(item.dictValueCode),
                }
            })
            this.formContent = [
                {
                    type: 'input',
                    label: '委托编号',
                    prop: 'outerNo',
                    disabled: true,
                    placeholder: "委托编号"
                },
                {
                    type: 'input',
                    label: '报告编号',
                    prop: 'reportNo',
                    placeholder: "报告编号"
                },
                {
                    type: 'select',
                    label: '材料类型',
                    prop: 'materialsType',
                    placeholder: "材料类型",
                    options: this.materialsTypeOpts,
                    change: (item) => this.setMaterials(item)
                },
                {
                    type: 'select',
                    label: '材料名称',
                    prop: 'materialsName',
                    options: this.specConfigOpts,
                    valueKey: "materialsName",
                    placeholder: "材料名称",
                    change: (item) => this.changeSelect(item)
                },
                {
                    type: 'select',
                    label: '材料规格',
                    prop: 'materialsSpec',
                    options: this.specConfig_2Opts,
                    valueKey: "materialsSpec",
                    placeholder: "材料规格",
                    change: (item) => this.changeSelectSpec(item)
                },
                
        // {
        //   type: 'select',
        //   label: '供应商',
        //   prop: 'supplierCompanyName',
        //   options: this.supplierCompanyOpts,
        //   placeholder: "供应商",
        //   valueKey: "supplierName",
        //             change: (item) => this.changeSelectSupplierCompany(item)
        // },
        // {
        //   type: 'input',
        //   label: '供应商简称',
        //   prop: 'supplyCompanyCalled',
        //   placeholder: "供应商简称",
        //   disabled: true,
        // },
        // {
        //   type: 'select',
        //   label: '生产厂家',
        //   prop: 'factory',
        //   options: this.factoryOpts,
        //   placeholder: "生产厂家",
        //   valueKey: "manufacturers",
        //             change: (item) => this.changeSelectFactory(item)
        // },
        // {
        //   type: 'input',
        //   label: '生产厂家简称',
        //   prop: 'factoryCalled',
        //   placeholder: "生产厂家简称",
        //   disabled: true,
        // },
                {
                    type: 'select',
                    label: '试验项目',
                    prop: 'testProjectName',
                    placeholder: "试验项目",
                    valueKey: "testCode",
                    options: this.testProjectOpts,
                    change: (item) => this.changeTestProject(item)
                },
                {
                    type: 'input',
                    label: '检测阈值',
                    prop: 'checkThreshold',
                    placeholder: "检测阈值"
                },
                {
                    type: 'input',
                    label: '检测结论',
                    prop: 'checkResult',
                    placeholder: "检测结论"
                },
                {
                    type: 'input',
                    label: '检测单位',
                    prop: 'checkCompany',
                    placeholder: "检测单位"
                },
                {
                    type: 'datetime',
                    label: '报告时间',
                    prop: 'reportTime',
                    placeholder: "报告时间",
                    format: 'yyyy-MM-dd HH:mm:ss'
                },
                {
                    type: 'datetime',
                    label: '批准时间',
                    prop: 'approveTime',
                    placeholder: "批准时间",
                    format: 'yyyy-MM-dd HH:mm:ss'
                },
            ];
        })

        this.initData();
    },

    methods: {

    // 选择供应商
    changeSelectSupplierCompany(item) {
        console.log('选择供应商：',item);
      this.$set(this.editForm, "supplierCompanyName", item.supplierName);
      this.$set(this.editForm, "supplyCompanyCalled", item.supplierAbbreviation);
      this.$set(this.editForm, "factory", undefined);
      this.$set(this.editForm, "factoryCalled", undefined);
      
      // 获取厂家信息
      this.$api.querySupplierCompanyMaterialsFactoryList(`supplierCompanyId=${item.id}&materialsName=${this.editForm.materialsName}&materialsSpec=${this.editForm.materialsSpec}`, this).then(res => {
          if (res.code == 1) {
              this.factoryOpts = res.data.list.map(item => {
                  return {
                      label: item.manufacturers,
                      value: item,
                  }
              })
              this.$set(this.formContent[7], "options", this.factoryOpts)
          }
      })

      this.changeSelectFactory({});
    },


    changeSelectFactory(item) {
      this.$set(this.editForm, "factory", item.manufacturers || "");
      this.$set(this.editForm, "factoryCalled", item.manufacturersCalled || "");

    },


        handleFilter() {
            this.initData();
        },
        resetForm(){
            this.searchForm = {
                // materialsTypeList: [],
            };
            this.initData();
        },
        initData(opageNum, opageSize){
            if (opageNum) this.pageObj.pageNum = opageNum;
            if (opageSize) this.pageObj.pageSize = opageSize;
                
            const params ={
                ...this.pageObj,
                params: this.searchForm
            }
            
            //获取列表
            this.tableData = [];
            this.$api.queryExperimentOuterPage(params, this).then(res => {
                if(res.succ){
                    this.tableData = res.data.list;
                    this.total = res.data.total;
                }else{
                    this.$message.error(res.msg || '查询失败')
                }
            })
        },
        addDrawerForm(row) {
            this.attachments = [];
            this.attachmentsImgList = [];
            if (row) {
                this.editForm = {
                    ...row,
                    materialsName: row.materialsName + (row.sampleJudge ? `【${row.sampleJudge}】` : ''),
                };
                if (row.attachments) {
                    row.attachments.split(",").map((item, index) => {
                        this.attachmentsImgList.push({
                            name: item,
                            url: this.filePrefix + item
                        })
                    });
                    this.attachments = row.attachments.split(",");
                }
                this.$api.querySelectByMaterialsType(`materialsType=${row.materialsType}`, this).then(res => {
                    if (res.code == 1) {
                        this.specConfigOpts = res.data.list.map(item => {
                            return {
                                label: item.materialsName + (item.sampleJudge ? `【${item.sampleJudge}】` : ''),
                                value: item,
                            }
                        })
                        this.$set(this.formContent[3], "options", this.specConfigOpts);
                    }
                })
            }else{
                this.editForm = {};
            }
            this.drawer = true;
        },

        saveTarget() {
            let api = this.editForm.id ? "updateExperimentOuter" : "addExperimentOuter";
            this.editForm.materialsName = undefined;
            this.editForm.materialsSpec = undefined;
            this.editForm.attachments = this.attachments.join(",");

            this.$api[api](this.editForm, this).then(res => {
                if (res.succ) {
                    this.$message({
                        showClose: true,
                        message: "操作成功",
                        type: "success",
                    });
                    this.drawer = false;
                    this.initData();
                }
            })
        },

        deleteSupplierCompanyMaterials(row) {
            this.$confirm("删除后不能恢复，确定要删除吗？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                //删除
                this.$api.deleteExperimentOuter({id: row.id}, this).then(res => {
                    if (res.succ) {
                        this.$message({
                            showClose: true,
                            message: "操作成功",
                            type: "success",
                        });
                        this.initData();
                    }
                })
            });
        },

        setMaterials(row) {
            this.$api.querySelectByMaterialsType(`materialsType=${row}`, this).then(res => {
                if (res.code == 1) {
                    this.specConfigOpts = res.data.list.map(item => {
                        return {
                            label: item.materialsName + (item.sampleJudge ? `【${item.sampleJudge}】` : ''),
                            value: item.materialsType + '$$$' + item.materialsName + '$$$' + item.sampleJudge,
                        }
                    })
                    this.$set(this.formContent[3], "options", this.specConfigOpts);
                    this.$set(this.editForm, "materialsName", undefined);
                    this.$set(this.editForm, "materialsSpec", undefined);
                    this.$set(this.editForm, "specId", undefined);

      this.$set(this.editForm, "supplierCompanyName", undefined);
      this.$set(this.editForm, "supplyCompanyCalled", undefined);
      this.$set(this.editForm, "factory", undefined);
      this.$set(this.editForm, "factoryCalled", undefined);
                }
            })

            this.$api.queryTestProjectInfo(`materialsType=${row}`).then(res => {
                if (res.code == 1) {
                    this.testProjectOpts = res.data.list.map(item => {
                        return {
                            label: item.testName,
                            value: item,
                        }
                    })
                    this.$set(this.formContent[5], "options", this.testProjectOpts);

                    this.$set(this.editForm, "testProjectName", "");
                    this.$set(this.editForm, "testProjectCode", "");
                }
            })
        },
        changeTestProject(item) {
            this.$set(this.editForm, "testProjectName", item.testName);
            this.$set(this.editForm, "testProjectCode", item.testCode);
        },
        changeSelect(item) {


            console.log('选择的材料名称：', item);
        let clmcList =  item.split('$$$');
            if(clmcList.length >= 2){

            this.queryMaterialsSpecOpts(clmcList[0], clmcList[1]);

            }
        },
        queryMaterialsSpecOpts(materialType, materialsName) {
            this.$api.queryMaterialsSpecConfigAll({
                materialsType: materialType,
                materialsName: materialsName
            }, this).then(res => {
                if (res.code == 1) {
                    this.specConfig_2Opts = res.data.list.map(item => {
                        return {
                            label: item.materialsSpec || item.materialsName,
                            value: item.materialsName + '$$$' + item.materialsSpec + '$$$' + item.id,
                        }
                    })
                    this.$set(this.formContent[4], "options", this.specConfig_2Opts);
                    this.$set(this.editForm, "materialsSpec", undefined);
                    this.$set(this.editForm, "specId", undefined);

      this.$set(this.editForm, "supplierCompanyName", undefined);
      this.$set(this.editForm, "supplyCompanyCalled", undefined);
      this.$set(this.editForm, "factory", undefined);
      this.$set(this.editForm, "factoryCalled", undefined);
                }
            })
        },
        changeSelectSpec(item) {
            const itemList = item.split('$$$');
            if(itemList.length >= 3){
            this.$set(this.editForm, "specId", itemList[2]);
            // materialsName
      this.$set(this.editForm, "materialsName", itemList[0]);
      this.$set(this.editForm, "materialsSpec", itemList[1]);

      this.$set(this.editForm, "supplierCompanyName", undefined);
      this.$set(this.editForm, "supplyCompanyCalled", undefined);
      this.$set(this.editForm, "factory", undefined);
      this.$set(this.editForm, "factoryCalled", undefined);


      // 获取供应商信息
      this.$api.querySupplierCompnayMaterialsList(`materialsName=${itemList[0]}&materialsSpec=${itemList[1]}`, this).then(res => {
          if (res.code == 1) {
              this.supplierCompanyOpts = res.data.list.map(item => {
                return {
                  label: item.supplierName,
                  value: item,
                }
              })
              this.$set(this.formContent[5], "options", this.supplierCompanyOpts);
          }
      })

            }
        },
        handleAttachmentsImgRemove(file, fileList) {
            this.attachmentsImgList = fileList;
            this.attachments = [];
            fileList.map(item => {
                this.attachments.push(item.name);
            });
        },
        handleAttachmentsImgSuccess(response) {
            if (response.code == 1) {
                this.attachmentsImgList.push({
                    name: response.data.fileName,
                    url: response.data.filePath,
                });
                this.attachments.push(response.data.fileName);
            }
        },
        previewAttachmentsTarget(row) {
            this.attachments = [];
            this.attachmentsImgList = [];
            if (row.attachments) {
                row.attachments.split(",").map((item, index) => {
                    this.attachmentsImgList.push({
                        name: item,
                        url: this.filePrefix + item
                    })
                });
                this.attachments = row.attachments.split(",");
            }
            this.attachmentsImgVisible = true;
        },
        handleClose(done) {
            this.$confirm('确认关闭？')
            .then(_ => {
                if(done){
                    done();
                }
                this.attachmentsImgVisible = false;
            })
            .catch(_ => {});
        },

        handleDrawerClose(done) {
            this.$confirm('确认关闭？')
            .then(_ => {
                if(done){
                    done();
                }
                this.attachmentsImgVisible = false;
                this.attachments = [];
                this.attachmentsImgList = [];
            })
            .catch(_ => {});
        }
    },
}
</script>


<style scoped lang="scss">
  ::v-deep .el-button{
    padding-left: 13px;
    padding-right: 13px;
  }
  .el-form-item{
    margin-bottom: 8px;
  }
  ::v-deep .el-form--inline{
    .el-form-item{
      margin-right: 24px;
      margin-bottom: 0;
      &:last-child{
        margin: 0;
      }
    }
  }
  ::v-deep .el-table{
    .expanded,.expanded:hover{
      background-color: #FFFBD9;
      
    }
    .expanded + tr{
      background-color: #FFFBD9;
      td{
        background-color: #FFFBD9;
      }
    }
    
    .table-child-box{
      margin: 16px;
      padding: 16px;
      background: #FFFFFF;
    }
  }
  
  .content-box{
    padding: 16px;
  }
  .content{
    width: 100%;
    height: 100%;
    padding: 16px;
    background: #FFFFFF;
    border-radius: 16px;
  }
  
  .search-box{
    padding-bottom: 16px;
    line-height: 40px;
  }
</style>