<template>
	<section class="firstpage-container common-container" ref="firstpagecontainer">
        <!-- 内容区域 -->
        <section class="data-content"> <!--  v-bar="{preventParentScroll: true, scrollThrottle: 30}" -->
            <el-row class="scroll-div">
                <el-row class="top-analysis">
                    <el-row style="height: 42px;">概览统计</el-row>
                    <el-row class="ta-content">
                        <el-col style="width: 11%;">
                            <el-row class="ta-number">{{ topAnalysisData.supplyCount }}</el-row>
                            <el-row>供应商(个)</el-row>
                        </el-col>
                        <el-col style="width: 11%;">
                            <el-row class="ta-number">{{ topAnalysisData.logisticCount }}</el-row>
                            <el-row>物流商(个)</el-row>
                        </el-col>
                        <el-col style="width: 11%;">
                            <el-row class="ta-number">{{ topAnalysisData.projectCount }}</el-row>
                            <el-row>供货物料(个)</el-row>
                        </el-col>
                        <el-col style="width: 11%;">
                            <el-row class="ta-number">{{ topAnalysisData.vehicleCount }}</el-row>
                            <el-row>车辆总数(台)</el-row>
                        </el-col>
                        <el-col style="width: 11%;">
                            <el-row class="ta-number">{{ topAnalysisData.driverCount }}</el-row>
                            <el-row>司机总数(人)</el-row>
                        </el-col>
                        <el-col style="width: 11%;">
                            <el-row class="ta-number">{{ topAnalysisData.planCount }}</el-row>
                            <el-row>供货计划总数(个)</el-row>
                        </el-col>
                        <el-col style="width: 11%;">
                            <el-row class="ta-number">{{ topAnalysisData.taskCount }}</el-row>
                            <el-row>供货任务总数(个)</el-row>
                        </el-col>
                        <el-col style="width: 11%;">
                            <el-row class="ta-number">{{ topAnalysisData.waybillCount }}</el-row>
                            <el-row>运单总数(个)</el-row>
                        </el-col>
                        <el-col style="width: 11%;">
                            <el-row class="ta-number">{{ topAnalysisData.signQuantity }}</el-row>
                            <el-row>供货总数(吨)</el-row>
                        </el-col>
                    </el-row>
                </el-row>
                <el-row class="chart-analysis row-layout">
                    <el-row>供货分析</el-row>
                    <el-row class="ca-row">
                        <el-col class="ca-region">
                            <el-row>今日供货：</el-row>
                            <el-row class="row-inner-curve">
                                <ul class="ca-ul">
                                    <li class="ca-li ca-li-1">
                                        <el-row class="ta-number">{{ todaySupplyAnalysisData.cement.finishCount + '/' + todaySupplyAnalysisData.cement.totalCount }}</el-row>
                                        <el-row>水泥供货量(吨)</el-row>
                                    </li>
                                    <li class="ca-li ca-li-2">
                                        <el-row class="ta-number">{{ todaySupplyAnalysisData.coarse.finishCount + '/' + todaySupplyAnalysisData.coarse.totalCount }}</el-row>
                                        <el-row>粗骨料供货量(吨)</el-row>
                                    </li>
                                    <li class="ca-li ca-li-3">
                                        <el-row class="ta-number">{{ todaySupplyAnalysisData.fine.finishCount + '/' + todaySupplyAnalysisData.fine.totalCount }}</el-row>
                                        <el-row>细骨料供货量(吨)</el-row>
                                    </li>
                                    <li class="ca-li ca-li-4">
                                        <el-row class="ta-number">{{ todaySupplyAnalysisData.fly.finishCount + '/' + todaySupplyAnalysisData.fly.totalCount }}</el-row>
                                        <el-row>粉煤灰供货量(吨)</el-row>
                                    </li>
                                    <li class="ca-li ca-li-5">
                                        <el-row class="ta-number">{{ todaySupplyAnalysisData.slag.finishCount + '/' + todaySupplyAnalysisData.slag.totalCount }}</el-row>
                                        <el-row>矿渣粉供货量(吨)</el-row>
                                    </li>
                                    <li class="ca-li ca-li-6">
                                        <el-row class="ta-number">{{ todaySupplyAnalysisData.admixture.finishCount + '/' + todaySupplyAnalysisData.admixture.totalCount }}</el-row>
                                        <el-row>外加剂供货量(吨)</el-row>
                                    </li>
                                </ul>
                            </el-row>
                        </el-col>
                        <el-col class="ca-region" style="margin-left: 16px;">
                            <el-row>今日供货</el-row>
                            <el-row class="row-inner-curve" id="todaySupplyChart">
                            </el-row>
                        </el-col>
                    </el-row>
                    <el-row class="ca-row">
                        <el-col class="ca-region">
                            <el-row>近七日供货单趋势</el-row>
                            <el-row class="row-inner-curve" id="sevenDaySupplyChart" style="padding-left: 10px; padding-right: 20px;">
                            </el-row>
                        </el-col>
                        <el-col class="ca-region" style="margin-left: 16px;">
                            <el-row>近七日供货数量趋势</el-row>
                            <el-row class="row-inner-curve" id="sevenDaySupplyAmountChart" style="padding-left: 10px; padding-right: 20px;">
                            </el-row>
                        </el-col>
                    </el-row>
                </el-row>
                <el-row class="dynamic-region row-layout">
                    <el-row>供货任务分析</el-row>
                    <el-col style="width: calc(75.66% - 8px)">
                        <el-row class="capital-analysis">
                            <el-row>供货任务</el-row>
                            <hr style="border: 1px solid rgba(232, 232, 232, 1);" />
                            <el-row class="ca-row">
                                <el-col style="width: 20%;">
                                    <el-row class="ca-number">{{ supplyTaskAnalysisData.waitDeterminedCount }}</el-row>
                                    <el-row>待确认(个)</el-row>
                                </el-col>
                                <el-col style="width: 20%;">
                                    <el-row class="ca-number">{{ supplyTaskAnalysisData.noCarCount }}</el-row>
                                    <el-row>未派车(个)</el-row>
                                </el-col>
                                <el-col style="width: 20%;">
                                    <el-row class="ca-number">{{ supplyTaskAnalysisData.hasCarCount }}</el-row>
                                    <el-row>已派车(个)</el-row>
                                </el-col>
                                <el-col style="width: 20%;">
                                    <el-row class="ca-number">{{ supplyTaskAnalysisData.onTheWayCount }}</el-row>
                                    <el-row>发货中(个)</el-row>
                                </el-col>
                                <el-col style="width: 20%;">
                                    <el-row class="ca-number">{{ supplyTaskAnalysisData.finishCount }}</el-row>
                                    <el-row>已完成(个)</el-row>
                                </el-col>
                            </el-row>
                            <el-row class="ca-row">
                                <el-col style="width: 20%;">
                                    <el-row class="ca-number">{{ supplyTaskAnalysisData.waitDeterminedAmount }}</el-row>
                                    <el-row>待确认数量(吨)</el-row>
                                </el-col>
                                <el-col style="width: 20%;">
                                    <el-row class="ca-number">{{ supplyTaskAnalysisData.noCarAmount }}</el-row>
                                    <el-row>未派车数量(吨)</el-row>
                                </el-col>
                                <el-col style="width: 20%;">
                                    <el-row class="ca-number">{{ supplyTaskAnalysisData.hasCarAmount }}</el-row>
                                    <el-row>已派车数量(吨)</el-row>
                                </el-col>
                                <el-col style="width: 20%;">
                                    <el-row class="ca-number">{{ supplyTaskAnalysisData.onTheWayAmount }}</el-row>
                                    <el-row>发货中数量(吨)</el-row>
                                </el-col>
                                <el-col style="width: 20%;">
                                    <el-row class="ca-number">{{ supplyTaskAnalysisData.finishAmount }}</el-row>
                                    <el-row>已完成数量(吨)</el-row>
                                </el-col>
                            </el-row>
                        </el-row>
                        <el-row class="approval-analysis">
                            <el-row>运单</el-row>
                            <hr style="border: 1px solid rgba(232, 232, 232, 1);" />
                            <el-row class="ca-row">
                                <el-col style="width: 20%;">
                                    <el-row class="ca-number">{{ wayBillAnalysisData.reservationCount }}</el-row>
                                    <el-row>已预约(个)</el-row>
                                </el-col>
                                <el-col style="width: 20%;">
                                    <el-row class="ca-number">{{ wayBillAnalysisData.setOutCount }}</el-row>
                                    <el-row>已出发(个)</el-row>
                                </el-col>
                                <el-col style="width: 20%;">
                                    <el-row class="ca-number">{{ wayBillAnalysisData.lineUpCount }}</el-row>
                                    <el-row>排队中(个)</el-row>
                                </el-col>
                                <el-col style="width: 20%;">
                                    <el-row class="ca-number">{{ wayBillAnalysisData.signCount }}</el-row>
                                    <el-row>已签收(个)</el-row>
                                </el-col>
                                <el-col style="width: 20%;">
                                    <el-row class="ca-number">{{ wayBillAnalysisData.abnormalSignCount }}</el-row>
                                    <el-row>异常签收(个)</el-row>
                                </el-col>
                            </el-row>
                            <el-row class="row-inner-rate" id="fhzbRate"></el-row>
                            <el-row class="row-inner-rate" id="qszbRate"></el-row>
                            <el-row class="row-inner-rate" id="ycQszbRate"></el-row>
                            <el-row class="drr-total">
                                <el-col :span="8">
                                    <el-row class="drr-number">{{ wayBillAnalysisData.deliverAmount }}</el-row>
                                    <el-row>发货数量(吨)</el-row>
                                </el-col>
                                <el-col :span="8">
                                    <el-row class="drr-number">{{ wayBillAnalysisData.signAmount }}</el-row>
                                    <el-row>签收数量(吨)</el-row>
                                </el-col>
                                <el-col :span="8">
                                    <el-row class="drr-number">{{ wayBillAnalysisData.abnormalSignAmount }}</el-row>
                                    <el-row>异常签收数量(吨)</el-row>
                                </el-col>
                                
                            </el-row>
                            <el-row style="text-align: center; top: -25%;">
                                <el-col :span="8">发货占比分析</el-col>
                                <el-col :span="8">签收占比分析</el-col>
                                <el-col :span="8">异常签收占比分析</el-col>
                            </el-row>
                        </el-row>
                    </el-col>
                    <el-col class="dr-region">
                        <el-row class="row-inner-rate" id="supplyTaskRate"></el-row>
                        <el-row class="drr-total">
                            <el-row class="drr-number">{{ supplyTaskAnalysisData.totalPlanAmount }}</el-row>
                            <el-row>供货数量(吨)</el-row>
                        </el-row>
                    </el-col>
                </el-row>
            </el-row>
        </section>
	</section>
</template>
<script>
	import { getTopAnalysis } from '../api/api';
	import { getTodaySupplyAnalysis, getSevenDaySupplyAnalysis, getSevenDaySupplyAmountAnalysis } from '../api/api';
	import { getSupplyTaskAnalysis, getWayBillAnalysis } from '../api/api';
    
	import util from '../common/js/util';

	export default {
		components: {
			
		},
		data() {
			return {
                // 概览统计
				topAnalysisData: {},

                // 今日供货
                todaySupplyAnalysisData: {
                    "cement": {
                        "finishCount": 0.0,
                        "totalCount": 0.0
                    },
                    "coarse": {
                        "finishCount": 0.0,
                        "totalCount": 0.0
                    },
                    "fine": {
                        "finishCount": 0.0,
                        "totalCount": 0.0
                    },
                    "fly": {
                        "finishCount": 0.0,
                        "totalCount": 0.0
                    },
                    "slag": {
                        "finishCount": 0.0,
                        "totalCount": 0.0
                    },
                    "admixture": {
                        "finishCount": 0.0,
                        "totalCount": 0.0
                    }
                },
                // 今日供货柱状图
				todaySupplyAnalysisXData: [],
				todaySupplyAnalysisYData: [],
                todaySupplyChart: null,
                // 近七日供货单趋势
				sevenDaySupplyAnalysisDate: [],
				sevenDaySupplyAnalysisNum: [],
                sevenDaySupplyChart: null,
                // 近七日供货数量趋势
				sevenDaySupplyAmountAnalysisDate: [],
				sevenDaySupplyAmountAnalysisNum: [],
                sevenDaySupplyAmountChart: null,

                // 供货任务分析
                supplyTaskAnalysisData: [],
                // 供货任务分析环形图
                supplyTaskRateData: [],
                supplyTaskRateData: [],
                supplyTaskRate: null,
                
                // 运单
                wayBillAnalysisData: [],
                // 发货占比分析
                fhzbRateData: [],
                fhzbRate: null,
                // 签收占比分析
                qszbRateData: [],
                qszbRate: null,
                // 异常签收占比分析
                ycQszbRateData: [],
                ycQszbRate: null,
			}
		},
        // 页面销毁前执行
        beforeDestroy() {
            /* 页面组件销毁的时候，移除绑定的监听resize事件，
            否则的话，多渲染几次容易导致内存泄漏和额外CPU或GPU占用 */
            window.removeEventListener("resize", () => {
                this.todaySupplyChart.resize();
                this.sevenDaySupplyChart.resize();
                this.sevenDaySupplyAmountChart.resize();
                // 环形图
                if(!!this.supplyTaskRate) { this.supplyTaskRate.resize(); }
                if(!!this.fhzbRate) { this.fhzbRate.resize(); }
                if(!!this.qszbRate) { this.qszbRate.resize(); }
                if(!!this.ycQszbRate) { this.ycQszbRate.resize(); }
            });
        },
		methods: {
            // 获取概览统计
			getTopAnalysisData: function () {
				let _this = this;
				// 执行操作
				let para = '';
				getTopAnalysis(para, _this)
				.then(res=>res)
				.then(data => {
					let { msg, code } = data;
					if (code !== 1) {
						_this.$message({
							message: msg,
							type: 'error'
						});
					} else {
						data = data.data;
						_this.topAnalysisData = data;
					}
				});
			},

            // 获取今日供货
			getTodaySupplyAnalysisData: function () {
				let _this = this;
				// 执行操作
				let para = '';
				getTodaySupplyAnalysis(para, _this)
				.then(res=>res)
				.then(data => {
					let { msg, code } = data;
					if (code !== 1) {
						_this.$message({
							message: msg,
							type: 'error'
						});
					} else {
						data = data.data;
						_this.todaySupplyAnalysisData = data;
                        // 获取X、Y轴数据
                        _this.todaySupplyAnalysisXData = [ '水泥', '粗骨料', '细骨料', '粉煤灰', '矿渣粉', '外加剂' ];
                        _this.todaySupplyAnalysisYData = [ _this.todaySupplyAnalysisData.cement.finishCount, 
                            _this.todaySupplyAnalysisData.coarse.finishCount, 
                            _this.todaySupplyAnalysisData.fine.finishCount,
                            _this.todaySupplyAnalysisData.fly.finishCount,
                            _this.todaySupplyAnalysisData.slag.finishCount,
                            _this.todaySupplyAnalysisData.admixture.finishCount];

                        // 绘制今日供货柱状图
                        _this.drawTodaySupplyChart();
					}
				});
			},

            // 绘制今日供货柱状图
            drawTodaySupplyChart: function () {
                let _this = this;
                // 今日供货柱状图
                if(_this.todaySupplyChart == null) {
                    _this.todaySupplyChart = _this.$echarts.init(document.getElementById('todaySupplyChart'));
                }
                // 绘制图表
                _this.todaySupplyChart.setOption({
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'shadow'
                        }
                    },
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '3%',
                        containLabel: true
                    },
                    xAxis: [{
                        type: 'category',
                        data: _this.todaySupplyAnalysisXData,
                        axisTick: {
                            alignWithLabel: true
                        }
                    }],
                    yAxis: [{
                        type: 'value',
                        name: '吨        ',
                        nameLocation: "end",
                    }],
                    series: [{
                        name: '供货量：',
                        type: 'bar',
                        barWidth: '11px',
                        data: _this.todaySupplyAnalysisYData,
                        itemStyle: {
                            normal: {
                                color: '#3370FF',
                                barBorderRadius: 6
                            },
                        }
                    }]
                });
            }, 

            // 获取近七日供货单趋势
			getSevenDaySupplyAnalysisData: function () {
				let _this = this;
				// 执行操作
				let para = '';
				getSevenDaySupplyAnalysis(para, _this)
				.then(res=>res)
				.then(data => {
					let { msg, code } = data;
					if (code !== 1) {
						_this.$message({
							message: msg,
							type: 'error'
						});
					} else {
						data = data.data;
                        if(!!data && data.length > 0) {
                            data.forEach(a => {
                                _this.sevenDaySupplyAnalysisDate = util.array.addElement(_this.sevenDaySupplyAnalysisDate, util.formatDate.format(new Date(a.days), 'd') + '号');
                                _this.sevenDaySupplyAnalysisNum = util.array.addElement(_this.sevenDaySupplyAnalysisNum, a.xCount);
                            });
                        }

                        // 绘制近七日供货单趋势折线图
                        _this.drawSevenDaySupplyChart();
					}
				});
			},

            // 近七日供货数量趋势
			getSevenDaySupplyAmountAnalysisData: function () {
				let _this = this;
				// 执行操作
				let para = '';
				getSevenDaySupplyAmountAnalysis(para, _this)
				.then(res=>res)
				.then(data => {
					let { msg, code } = data;
					if (code !== 1) {
						_this.$message({
							message: msg,
							type: 'error'
						});
					} else {
						data = data.data;
                        if(!!data && data.length > 0) {
                            data.forEach(a => {
                                _this.sevenDaySupplyAmountAnalysisDate = util.array.addElement(_this.sevenDaySupplyAmountAnalysisDate, util.formatDate.format(new Date(a.days), 'd') + '号');
                                _this.sevenDaySupplyAmountAnalysisNum = util.array.addElement(_this.sevenDaySupplyAmountAnalysisNum, a.xCount);
                            });
                        }
                        
                        // 绘制近七日供货数量趋势图
                        _this.drawSevenDaySupplyAmountChart();
					}
				});
			},

            // 绘制近七日供货单趋势
            drawSevenDaySupplyChart: function () {
                let _this = this;
                // 近七日供货单趋势图
                if(_this.sevenDaySupplyChart == null) {
                    _this.sevenDaySupplyChart = _this.$echarts.init(document.getElementById('sevenDaySupplyChart'));
                }
                // 绘制图表
                _this.sevenDaySupplyChart.setOption({
                    title: {
                        text: '',
                        textStyle:{
                            color: 'rgba(39, 39, 39, 1)',     // 字体颜色
                            fontWeight: '400',    // 粗细
                            fontSize: 14          //大小
                        },
                    },
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'cross',
                            label: {
                                backgroundColor: '#1A82D1'
                            }
                        }
                    },
                    toolbox: {
                        feature: {
                            saveAsImage: {
                                show: false, // 控制保存按钮显示隐藏
                            }
                        }
                    },
                    grid: {
                        left: '2%',
                        right: '2%',
                        bottom: '10%',
                        containLabel: true
                    },
                    xAxis: [{
                        type: 'category',
                        //boundaryGap: false,
                        data: _this.sevenDaySupplyAnalysisDate,
                        name: '',
                        nameLocation: "end",
                    }],
                    yAxis: [{
                        type: 'value',
                        name: '个       ',
                        nameLocation: "end",
                    }],
                    series: [{
                        name: '供货单：',
                        type: 'line',
                        color: '#1A82D1',
                        areaStyle: {
                            color: {
                                type: 'linear',
                                x: 0,
                                y: 0,
                                x2: 0,
                                y2: 1,
                                colorStops: [{
                                    offset: 0, color: '#3370FF' // 0% 处的颜色
                                }, {
                                    offset: 1, color: 'white' // 100% 处的颜色
                                }],
                                global: false // 缺省为 false
                            }
                        },
                        emphasis: {
                            focus: 'series'
                        },
                        data: _this.sevenDaySupplyAnalysisNum
                    }]
                });
            },

            // 绘制近七日供货数量趋势折线图
            drawSevenDaySupplyAmountChart: function () {
                let _this = this;
                // 近七日供货数量曲线
                if(_this.sevenDaySupplyAmountChart == null) {
                    _this.sevenDaySupplyAmountChart = _this.$echarts.init(document.getElementById('sevenDaySupplyAmountChart'));
                }
                // 绘制图表
                _this.sevenDaySupplyAmountChart.setOption({
                    title: {
                        text: '',
                        textStyle:{
                            color: '#94969F',     // 字体颜色
                            fontWeight: '400',    // 粗细
                            fontSize: 12          //大小
                        },
                    },
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'cross',
                            label: {
                                backgroundColor: '#FF8A00'
                            }
                        }
                    },
                    toolbox: {
                        feature: {
                            saveAsImage: {
                                show: false, // 控制保存按钮显示隐藏
                            }
                        }
                    },
                    grid: {
                        left: '2%',
                        right: '2%',
                        bottom: '10%',
                        containLabel: true
                    },
                    xAxis: [{
                        type: 'category',
                        //boundaryGap: false,
                        data: _this.sevenDaySupplyAmountAnalysisDate,
                        name: '',
                        nameLocation: "end",
                    }],
                    yAxis: [{
                        type: 'value',
                        name: '吨        ',
                        nameLocation: "end",
                    }],
                    series: [{
                        name: '供货量：',
                        type: 'line',
                        color: '#FF8A00',
                        areaStyle: {
                            color: {
                                type: 'linear',
                                x: 0,
                                y: 0,
                                x2: 0,
                                y2: 1,
                                colorStops: [{
                                    offset: 0, color: '#FF7A00' // 0% 处的颜色
                                }, {
                                    offset: 1, color: 'white' // 100% 处的颜色
                                }],
                                global: false // 缺省为 false
                            }
                        },
                        emphasis: {
                            focus: 'series'
                        },
                        data: _this.sevenDaySupplyAmountAnalysisNum
                    }]
                });
            },

            // 供货任务分析
			getSupplyTaskAnalysisData: function () {
				let _this = this;
				// 执行操作
				let para = '';
				getSupplyTaskAnalysis(para, _this)
				.then(res=>res)
				.then(data => {
					let { msg, code } = data;
					if (code !== 1) {
						_this.$message({
							message: msg,
							type: 'error'
						});
					} else {
						data = data.data;
                        _this.supplyTaskAnalysisData = data;

                        let temp1 = { value: data.waitDeterminedRatio, name: '待确认 ' + data.waitDeterminedRatio + '%' };
                        _this.supplyTaskRateData = util.array.addElement(_this.supplyTaskRateData, temp1);
                        let temp2 = { value: data.noCarRatio, name: '未派车 ' + data.noCarRatio + '%' };
                        _this.supplyTaskRateData = util.array.addElement(_this.supplyTaskRateData, temp2);
                        let temp3 = { value: data.hasCarRatio, name: '已派车 ' + data.hasCarRatio + '%' };
                        _this.supplyTaskRateData = util.array.addElement(_this.supplyTaskRateData, temp3);
                        let temp4 = { value: data.onTheWayRatio, name: '发货中 ' + data.onTheWayRatio + '%' };
                        _this.supplyTaskRateData = util.array.addElement(_this.supplyTaskRateData, temp4);
                        let temp5 = { value: data.finishRatio, name: '已完成 ' + data.finishRatio + '%' };
                        _this.supplyTaskRateData = util.array.addElement(_this.supplyTaskRateData, temp5);

                        // 绘制供货任务分析环形图
                        _this.drawSupplyTaskRate();
					}
				});
			},
            // 绘制供货任务分析环形图
            drawSupplyTaskRate: function () {
                let _this = this;
                // 供货任务分析环形图
                if (_this.supplyTaskRate == null) { // 如果不存在，就进行初始化。
                    _this.supplyTaskRate = _this.$echarts.init(document.getElementById('supplyTaskRate'));
                }
                // 绘制图表
                _this.supplyTaskRate.setOption({
                    tooltip: {
                        formatter: function (params) { // 格式化鼠标移上去显示内容样式
                            return params.name;
                        },
                    },
                    legend: {
                        textStyle: {
                            fontSize: 14, //图例字体大小
                            fontWeight: 400, 
                        },
                        //图例大小
                        itemHeight: 10,
                        bottom: 1,  //图例距离顶的距离
                        x: 'left',  //图例左右居中
                    },
                    series: [{
                        name: '供货任务分析',
                        type: 'pie',
                        radius: ['60%', '80%'],
                        avoidLabelOverlap: true,
                        label: {
                            show: false,
                            position: 'center'
                        },
                        labelLine: {
                            show: false
                        },
                        data: _this.supplyTaskRateData
                    }]
                });
            },

            // 运单
			getWayBillAnalysisData: function () {
				let _this = this;
				// 执行操作
				let para = '';
				getWayBillAnalysis(para, _this)
				.then(res=>res)
				.then(data => {
					let { msg, code } = data;
					if (code !== 1) {
						_this.$message({
							message: msg,
							type: 'error'
						});
					} else {
						data = data.data;
                        _this.wayBillAnalysisData = data;
                        // 发货占比分析
                        let temp1 = { value: data.deliverRatio, itemStyle: { color: '#477cff'}, name: '发货数量 ' + data.deliverAmount };
                        _this.fhzbRateData = util.array.addElement(_this.fhzbRateData, temp1);
                        let temp11 = { value: 100 - parseFloat(data.deliverRatio), itemStyle: { color: '#EEEFF2'}, name: '' };
                        _this.fhzbRateData = util.array.addElement(_this.fhzbRateData, temp11);

                        // 签收占比分析
                        let temp2 = { value: data.signRatio, itemStyle: { color: '#477cff'}, name: '签收数量 ' + data.signAmount };
                        _this.qszbRateData = util.array.addElement(_this.qszbRateData, temp2);
                        let temp21 = { value: 100 - parseFloat(data.signRatio), itemStyle: { color: '#EEEFF2'}, name: '' };
                        _this.qszbRateData = util.array.addElement(_this.qszbRateData, temp21);

                        // 异常签收占比分析
                        let temp3 = { value: data.abnormalSignRatio, itemStyle: { color: '#477cff'}, name: '异常签收数量 ' + data.abnormalSignAmount };
                        _this.ycQszbRateData = util.array.addElement(_this.ycQszbRateData, temp3);
                        let temp31 = { value: 100 - parseFloat(data.abnormalSignRatio), itemStyle: { color: '#EEEFF2'}, name: '' };
                        _this.ycQszbRateData = util.array.addElement(_this.ycQszbRateData, temp31);

                        // 绘制发货占比分析环形图
                        _this.drawFhzb();
                        // 绘制签收占比分析环形图
                        _this.drawQszb();
                        // 绘制异常签收占比分析环形图
                        _this.drawYcQszb();
					}
				});
			},
            // 绘制发货占比分析环形图
            drawFhzb: function () {
                let _this = this;
                // 开发商总览环形图
                if (_this.fhzbRate == null) { // 如果不存在，就进行初始化。
                    _this.fhzbRate = _this.$echarts.init(document.getElementById('fhzbRate'));
                }
                // 绘制图表
                _this.fhzbRate.setOption({
                    tooltip: {
                        formatter: function (params) { // 格式化鼠标移上去显示内容样式
                            return params.name;
                        },
                    },
                    legend: {
                        show: false,
                        textStyle: {
                            fontSize: 10, //图例字体大小
                        },
                        //图例大小
                        itemHeight: 10,
                        top: '80%',  //图例距离顶的距离
                        x: 'center',  //图例左右居中
                    },
                    series: [{
                        name: '开发商总览',
                        type: 'pie',
                        radius: ['50%', '65%'],
                        avoidLabelOverlap: false,
                        label: {
                            show: false,
                            position: 'center'
                        },
                        labelLine: {
                            show: false
                        },
                        data: _this.fhzbRateData
                    }]
                });
            },
            // 绘制签收占比分析环形图
            drawQszb: function () {
                let _this = this;
                // 签收占比分析环形图
                if (_this.qszbRate == null) { // 如果不存在，就进行初始化。
                    _this.qszbRate = _this.$echarts.init(document.getElementById('qszbRate'));
                }
                // 绘制图表
                _this.qszbRate.setOption({
                    tooltip: {
                        formatter: function (params) { // 格式化鼠标移上去显示内容样式
                            return params.name;
                        },
                    },
                    legend: {
                        show: false,
                        textStyle: {
                            fontSize: 10, //图例字体大小
                        },
                        //图例大小
                        itemHeight: 10,
                        top: '80%',  //图例距离顶的距离
                        x: 'center',  //图例左右居中
                    },
                    series: [{
                        name: '签收占比分析',
                        type: 'pie',
                        radius: ['50%', '65%'],
                        avoidLabelOverlap: false,
                        label: {
                            show: false,
                            position: 'center'
                        },
                        labelLine: {
                            show: false
                        },
                        data: _this.qszbRateData
                    }]
                });
            },
            // 绘制异常签收占比分析环形图
            drawYcQszb: function () {
                let _this = this;
                // 异常签收占比分析环形图
                if (_this.ycQszbRate == null) { // 如果不存在，就进行初始化。
                    _this.ycQszbRate = _this.$echarts.init(document.getElementById('ycQszbRate'));
                }
                // 绘制图表
                _this.ycQszbRate.setOption({
                    tooltip: {
                        formatter: function (params) { // 格式化鼠标移上去显示内容样式
                            return params.name;
                        },
                    },
                    legend: {
                        show: false,
                        textStyle: {
                            fontSize: 10, //图例字体大小
                        },
                        //图例大小
                        itemHeight: 10,
                        top: '80%',  //图例距离顶的距离
                        x: 'center',  //图例左右居中
                    },
                    series: [{
                        name: '异常签收占比分析',
                        type: 'pie',
                        radius: ['50%', '65%'],
                        avoidLabelOverlap: false,
                        label: {
                            show: false,
                            position: 'center'
                        },
                        labelLine: {
                            show: false
                        },
                        data: _this.ycQszbRateData
                    }]
                });
            },

            // 图标大小适应方法
            chartResize: function () {
                let _this = this;
                // 在页面初始化加载的时候绑定页面resize事件监听。补充resize事件：resize事件是在浏览器窗口大小改变时，会触发。
                // 如当用户调整窗口大小，或者最大化、最小化、恢复窗口大小显示时触发 resize 事件。
                // 我们一般使用这个事件去做窗口大小与对应元素的大小适配
                window.addEventListener("resize", () => {
                    // 执行echarts自带的resize方法，即可做到让echarts图表自适应
                    _this.todaySupplyChart.resize();
                    _this.sevenDaySupplyChart.resize();
                    _this.sevenDaySupplyAmountChart.resize();
                    // 环形图
                    if(!!_this.fhzbRate) { _this.fhzbRate.resize(); }
                    if(!!_this.qszbRate) { _this.qszbRate.resize(); }
                    if(!!_this.ycQszbRate) { _this.ycQszbRate.resize(); }
                    if(!!_this.supplyTaskRate) { _this.supplyTaskRate.resize(); }
                    // 如果有多个echarts，就在这里执行多个echarts实例的resize方法,不过一般要做组件化开发，即一个.vue文件只会放置一个echarts实例
                    /*
                    this.myChart1.resize();
                    this.myChart2.resize();
                    ......
                    */
                });
            },

            // 初始化调用
            initCall: function () {
                let _this = this;
                // 获取数据
                _this.getTopAnalysisData();
                // 今日供货
                _this.getTodaySupplyAnalysisData();
                // 近七日供货单趋势
                _this.getSevenDaySupplyAnalysisData();
                // 近七日供货数量趋势
                _this.getSevenDaySupplyAmountAnalysisData();
                // 供货任务分析
                _this.getSupplyTaskAnalysisData();
                // 运单
                _this.getWayBillAnalysisData();

                _this.chartResize();
            },
		},

		// 创建VUE实例后的钩子
		created: function () {

		},
		// 挂载到DOM后的钩子
		mounted: function () {
			let _this = this;
            // 初始化调用
            //_this.initCall();
		}
	};
</script>

<style lang="scss">
.firstpage-container {
    width: calc(100% + 18px);
	height: calc(100vh - 90px);
    .data-content {
        height: 100%;
        padding: 16px;
        overflow-x: auto;
        opacity: 1;
        border-top-left-radius: 16px;
        background: rgba(243, 245, 246, 1);
        font-family: PingFangSC-Medium, PingFang SC;
        .scroll-div {
            width: 100%;
            height: 100%;
            .row-layout {
                margin-top: 16px;
            }
            .top-analysis {
                width: 100%;
                height: 142px;
                background: #FFFFFF;
                border-radius: 16px;
                font-size: 16px;
                font-weight: 500;
                color: rgba(31, 35, 41, 1);
                padding: 16px;
                .ta-content {
                    height: 100px; 
                    line-height: 32px; 
                    text-align: center;
                    font-size: 14px;
                    font-weight: 400;
                    letter-spacing: 0px;
                    color: rgba(106, 114, 125, 1);
                    vertical-align: top;
                    .ta-number {
                        font-size: 22px;
                        font-weight: 600;
                        letter-spacing: 0px;
                        color: rgba(31, 35, 41, 1);
                        vertical-align: top;
                    }
                }
            }
            .chart-analysis {
                width: 100%;
                height: 690px;
                background: #FFFFFF;
                border-radius: 16px;
                font-size: 16px;
                font-weight: 500;
                color: rgba(31, 35, 41, 1);
                padding: 16px;
                .ca-row {
                    width: 100%;
                    height: calc(50% - 8px);
                    .ca-region {
                        width: calc(50% - 11px);
                        height: 100%;
                        background: #FFFFFF;
                        border-radius: 8px;
                        padding: 16px 0px;
                        font-size: 14px;
                        font-weight: 600;
                        letter-spacing: 0px;
                        color: rgba(31, 35, 41, 1);
                        vertical-align: top;
                        .ca-ul {
                            list-style: none;
                            .ca-li {
                                display: inline-block;
                                width: calc(33.33% - 10px);
                                min-height: 78px;
                                border-radius: 8px;
                                margin-top: 16px;
                                margin-left: 8px;
                                text-align: center;
                                padding-top: 5px;
                                line-height: 40px;
                                font-size: 14px;
                                font-weight: 400;
                                letter-spacing: 0px;
                                color: rgba(255, 255, 255, 1);
                                vertical-align: top;
                                .ta-number {
                                    font-size: 22px;
                                    font-weight: 600;
                                }
                            }
                            .ca-li-1 {
                                background: rgba(51, 112, 255, 1);
                            }
                            .ca-li-2 {
                                background: rgba(0, 165, 93, 1);
                            }
                            .ca-li-3 {
                                background: rgba(0, 160, 235, 1);
                            }
                            .ca-li-4 {
                                background: rgba(143, 102, 193, 1);
                            }
                            .ca-li-5 {
                                background: rgba(32, 176, 165, 1);
                            }
                            .ca-li-6 {
                                background: rgba(233, 138, 51, 1);
                            }
                        }
                        .row-inner-curve {
                            width: 100%;
                            height: 100%;
                        }
                    }
                }
            }
            .dynamic-region {
                width: 100%;
                height: 620px;
                background: #FFFFFF;
                border-radius: 16px;
                font-size: 16px;
                font-weight: 500;
                color: rgba(31, 35, 41, 1);
                padding: 16px;
                .capital-analysis {
                    width: 100%;
                    height: 200px;
                    background: #FFFFFF;
                    border-radius: 16px;
                    font-size: 14px;
                    font-weight: 500;
                    letter-spacing: 0px;
                    color: rgba(31, 35, 41, 1);
                    text-align: left;
                    vertical-align: top;
                    padding: 16px 0px;
                    .ca-row {
                        font-size: 14px;
                        font-weight: 400;
                        letter-spacing: 0px;
                        color: rgba(106, 114, 125, 1);
                        text-align: center;
                        vertical-align: top;
                        margin-top: 10px;
                        line-height: 30px;
                        .ca-number {
                            font-size: 22px;
                            font-weight: 600;
                            letter-spacing: 0px;
                            color: rgba(31, 35, 41, 1);
                            vertical-align: top;
                        }
                    }
                }
                .approval-analysis {
                    width: 100%;
                    height: 360px;
                    background: #FFFFFF;
                    border-radius: 16px;
                    font-size: 14px;
                    font-weight: 500;
                    letter-spacing: 0px;
                    color: rgba(31, 35, 41, 1);
                    text-align: left;
                    vertical-align: top;
                    padding: 16px 0px;
                    .ca-row {
                        font-size: 14px;
                        font-weight: 400;
                        letter-spacing: 0px;
                        color: rgba(106, 114, 125, 1);
                        text-align: center;
                        vertical-align: top;
                        margin-top: 20px;
                        line-height: 30px;
                        .ca-number {
                            font-size: 22px;
                            font-weight: 600;
                            letter-spacing: 0px;
                            color: rgba(31, 35, 41, 1);
                            vertical-align: top;
                        }
                    }
                    .row-inner-rate {
                        display: inline-block;
                        width: 33.33%;
                        height: 65%;
                    }
                    .drr-total {
                        position: relative;
                        top: -45%;
                        font-size: 14px;
                        font-weight: 400;
                        letter-spacing: 0px;
                        color: rgba(106, 114, 125, 1);
                        text-align: center;
                        vertical-align: top;
                        .drr-number {
                            font-size: 22px;
                            font-weight: 600;
                            color: rgba(31, 35, 41, 1);
                            margin-bottom: 5px;
                        }
                    }
                }
                .dr-region {
                    width: calc(24.33% - 8px);
                    height: 580px;
                    background: #FFFFFF;
                    border-radius: 16px;
                    margin-left: 16px;
                    text-align: center;
                    .row-inner-rate {
                        display: inline-block;
                        width: 80%;
                        height: 80%;
                    }
                    .drr-total {
                        position: relative;
                        top: -45%;
                        font-size: 14px;
                        font-weight: 400;
                        letter-spacing: 0px;
                        color: rgba(106, 114, 125, 1);
                        text-align: center;
                        vertical-align: top;
                        .drr-number {
                            font-size: 22px;
                            font-weight: 600;
                            color: rgba(31, 35, 41, 1);
                            margin-bottom: 5px;
                        }
                    }
                }
            }
        }
    }
}
</style>