/*
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2022-10-15 00:31:43
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2024-09-08 19:06:09
 * @FilePath: /truck-scales-web/src/main.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import Vue from 'vue';
import App from './App.vue';
import VueRouter from 'vue-router';
import ElementUI from 'element-ui';
import Store from './store';
// 需要按需引入，先引入vue并引入element-ui
import AFTableColumn from 'af-table-column'
import api from './api/api.js';
Vue.prototype.$api = api;
import routes from './router/index';
// 引入echarts
import * as echarts from 'echarts';
// 滚动条
import Vuebar from 'vuebar';
// 调用Baidu地图组件
import BaiduMap from 'vue-baidu-map';
// 瀑布流插件-vue-masonry
import { VueMasonryPlugin } from 'vue-masonry';
// 引入打印组件
import Print from 'vue-print-nb'

import EventBus from './utils/EventBus.js';
Vue.prototype.$bus = EventBus;

import util from './common/js/util.js';
Vue.prototype.$util = util;

// 默认主题
//import 'element-ui/lib/theme-chalk/index.css';
import './assets/css/element-variables.scss'

import infiniteScroll from 'vue-infinite-scroll'
Vue.use(infiniteScroll)

Vue.directive('decimal', {
  bind(el, binding, vnode) {
    // 输入框值变化时处理
    // el.addEventListener('input', (e) => {
    //   console.log(e,binding)
    //   if (!e.isTrusted) return
    //   let timer;
    //   let lim = binding.arg;
    //   let value = e.target.value
    //   if(timer) clearTimeout(timer);
    //   timer = setTimeout(()=>{
    //     if (lim && value) {
    //       const reg = new RegExp(`^(\\d+|\\d{1,}\\.\\d{0,${lim}})$`)
    //       if (!reg.test(value)) {
    //         let factor = Math.pow(10, lim);
    //         e.target.value = Math.trunc(value * factor) / factor;
    //       }
    //     }
    //   },200)
    // });
  },
  // componentUpdated(el, binding, vnode) {
  //   const value = binding.value;
  //   if (value !== binding.oldValue) {
  //     el.saving = true;
  //     el.value = parseFloat(value).toFixed(binding.value || 2);
  //     el.saving = false;
  //   }
  // }
});


Vue.directive('manual-update', {
  bind(el, binding, vnode) {
    el.onkeyup = function(e) {
      e.target.dispatchEvent(new Event("input"))
    }
  },
  // unbind(el) {
  //   el.oninput = null
  // }
})


import Viewer from 'v-viewer';
import 'viewerjs/dist/viewer.css';
Vue.use(Viewer);

Vue.config.productionTip = false;
// 注册组件
Vue.use(ElementUI, {
    size: 'small'
});
Vue.use(VueRouter);
Vue.use(AFTableColumn);
Vue.use(Vuebar);
Vue.use(BaiduMap, {
    // ak 是在百度地图开发者平台申请的密钥 详见 http://lbsyun.baidu.com/apiconsole/key */
    ak: 'OGCiU48G6hQL4FrAGvSTyFoi0DtGnCKW'
});
Vue.use(VueMasonryPlugin);
Vue.use(Print);

Vue.prototype.$store = Store;
Vue.prototype.$echarts = echarts;

// 解决 NavigationDuplicated 报错
const originalPush = VueRouter.prototype.push
VueRouter.prototype.push = function push(location) {
    return originalPush.call(this, location).catch(err => err);
};

// 加载路由
const router = new VueRouter({
    /*存在三种模式：
   * Hash: 使用URL的hash值来作为路由。支持所有浏览器。
   * History: 依赖 HTML5 History API 和服务器配置。参考官网中HTML5 History模式
   * Abstract： 支持所有javascript运行模式。如果发现没有浏览器的API，路由会自动强制进入这个模式。
    * 
    * 默认使用的是hash模式（前端路由，单页应用标配），URL中带有#号。修改成history模式，URL中的#号就被去除了。
    * 
    * 通过history api，丢掉了丑陋的#，但是它也有个毛病：
   * 不怕前进，不怕后退，就怕刷新，f5，（如果后端没有准备的话），因为刷新是实实在在地去请求服务器的。
   * 在hash模式下，前端路由修改的是#中的信息，而浏览器请求时是不带它的，所以没有问题。但是在history下，可以自由的修改path，当刷新时，如果服务器中没有相应的响应或者资源，会刷出一个404。
    */
    mode: 'history',
    base: '/mes-web/', // 
    routes
});

//使用钩子函数对路由进行权限跳转
router.beforeEach((to, from, next) => {
    // 处理浏览器tab页显示
    let name = to.meta.title;
    document.title = name + '-砼学';
    const token = localStorage.getItem('bbToken');
    if (!token && to.path !== '/login' && to.path !== '/') {
        next('/login');
    } else {
        if (to.path === '/login') {            
            const matched = from.matched[0];
            if (matched && matched.instances.default && matched.instances.default.$vnode) {
                const vnode = matched.instances.default.$vnode;
                if (vnode.data.keepAlive) {
                    const parent = vnode.parent;
                    if (parent && parent.componentInstance && parent.componentInstance.cache) {
                        const key =
                            vnode.key == null
                                ? vnode.componentOptions.Ctor.cid +
                                (vnode.componentOptions.tag ? `::${vnode.componentOptions.tag}` : '')
                                : vnode.key;
                        const cache = parent.componentInstance.cache;
                        const keys = parent.componentInstance.keys;
                        if (cache[key]) {
                            if (keys.length) {
                                const index = keys.indexOf(key);
                                if (index > -1) {
                                    keys.splice(index, 1);
                                }
                            }
                            delete cache[key];
                        }
                    }
                }
            }
        }

        next();
    }
});

new Vue({
    router,
    render: h => h(App)
}).$mount('#app');
