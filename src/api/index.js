import axios from 'axios';
import { Loading, Message } from 'element-ui';
import Utils from '../common/js/util'

const baseUrl = process.env.NODE_ENV === 'development' ? "/api" : "/mes-platform"

var token = undefined

var getToken = () => {
    token = localStorage.getItem('bbToken');
    
    return token
}

export const postResponse = (url, parma, _this, bloading = true, isCustomTip) => {
    return new Promise((resolve, reject) => {
        let loadingInstance;
        if(bloading) {
            loadingInstance = Loading.service({
                text: '拼命加载中...',
                background: 'rgba(255, 255, 255, 0.8)',
            });
        }
        axios.defaults.baseURL = baseUrl
        axios.defaults.headers = {
            'Content-Type': 'application/json;charset=UTF-8',
            "X-DataBus-Request-From": "Mes_PinKong_DatabusAgent",
            "X-DataBus-Request-To": "ZhongShan_SanHe_MPlatform",
            'Authorization': getToken()
        }

        //console.log(`${axios.defaults.baseURL}${url}接口入参：>>>>>>>>`, JSON.stringify(parma))
        axios.post(url, parma).then(res => {
            if(bloading) {
                loadingInstance.close();
            }
            //console.log(`${axios.defaults.baseURL}${url}接口出参：>>>>>>>>`, JSON.stringify(res.data))
            // NO-LOGIN=100400:当前会话未登录 
            // KICK_OUT=100401:异地登录，系统强制退出！ 
            // TOKEN_TIMEOUT=100402:登录已过期，请重新登录 
            // BE_REPLACED=100403:已被顶下线，请重新登录 
            // INVALID_TOKEN=100404:登录已过期，请重新登录 
            // NOT_TOKEN=100405:未提供token
            if ((res.status + '') == '401' || (res.data.code >= 100400 && res.data.code <= 100405)) { // 登录失效
                Utils.userData.clearLocalStorage();
                _this.$router.push('/login');
            }else{
              
              if(res.data.code === -1){
                if(!isCustomTip){
                  Message({
                      message: res.data.msg,
                      type: 'error'
                  });
                }
                reject(res.data)
              }else{
                resolve(res.data)
              }
            }
        }).catch(err => {
            if(bloading) {
                loadingInstance.close();
            }
            //console.log(`${axios.defaults.baseURL}${url}接口异常：>>>>>>>>`, err)
            // 提示信息
            Message.closeAll();
            // 登录失效
            if (err.response.data.code == -100 || err.response.data.code == 401) { // 登录失效
                Utils.userData.clearLocalStorage();
                _this.$router.push('/login');
                Message({
                    message: '登录已失效，请重新登陆',
                    type: 'warning'
                });
            }else{
              if(!isCustomTip){
                Message({
                    message: "网络异常，请重试！",
                    type: 'warning'
                });
              }
            }
            reject(err)
        })
    })
};

export const getResponse = (url, parma, _this, bloading = true) => {
    return new Promise((resolve, reject) => {
        let loadingInstance;
        if(bloading) {
            loadingInstance = Loading.service({
                text: '拼命加载中...',
                background: 'rgba(255, 255, 255, 0.8)',
            });
        }
        axios.defaults.baseURL = baseUrl
        axios.defaults.headers = {
            'Content-Type': 'application/json;charset=UTF-8',
            "X-DataBus-Request-From": "Mes_PinKong_DatabusAgent",
            "X-DataBus-Request-To": "ZhongShan_SanHe_MPlatform",
            'Authorization': getToken()
        }

        //console.log(`${axios.defaults.baseURL}${url}接口入参：>>>>>>>>`, JSON.stringify(parma))

        if(!!parma) {
            url += '?' + parma;
        }
        axios.get(url).then(res => {
            if(bloading) {
                loadingInstance.close();
            }
            //console.log(`${axios.defaults.baseURL}${url}接口出参：>>>>>>>>`, JSON.stringify(res.data))
            // NO-LOGIN=100400:当前会话未登录 
            // KICK_OUT=100401:异地登录，系统强制退出！ 
            // TOKEN_TIMEOUT=100402:登录已过期，请重新登录 
            // BE_REPLACED=100403:已被顶下线，请重新登录 
            // INVALID_TOKEN=100404:登录已过期，请重新登录 
            // NOT_TOKEN=100405:未提供token
            if ((res.status + '') == '401' || (res.data.code >= 100400 && res.data.code <= 100405)) { // 登录失效
                Utils.userData.clearLocalStorage();
                _this.$router.push('/login');
            }else{
                resolve(res.data)
            }
        }).catch(err => {
            if(bloading) {
                loadingInstance.close();
            }
            //console.log(`${axios.defaults.baseURL}${url}接口异常：>>>>>>>>`, err.toString())
            // 提示信息
            Message.closeAll();
            // 登录失效
            if (err.response.data.code == -100 || err.response.data.code == 401) { // 登录失效
                Utils.userData.clearLocalStorage();
                _this.$router.push('/login');
                Message({
                    message: '登录已失效，请重新登陆',
                    type: 'warning'
                });
            }else{
                Message({
                    message: "网络异常，请重试！",
                    type: 'warning'
                });
            }
            reject(err)
        })
    })
};

export const uploadResponse = (url, parma, _this, bloading = true) => {
    return new Promise((resolve, reject) => {
        let loadingInstance = Loading.service({});
        axios.defaults.baseURL = baseUrl
        let headers = {
            'Content-Type': 'multipart/form-data',
            "X-DataBus-Request-From": "Mes_PinKong_DatabusAgent",
            "X-DataBus-Request-To": "ZhongShan_SanHe_MPlatform",
            "Authorization": getToken()
        }
        console.log(`${axios.defaults.baseURL}${url}接口入参：>>>>>>>>`, JSON.stringify(parma))
        axios.post(url, parma, {headers: headers}).then(res => {
            loadingInstance.close();
            console.log(`${axios.defaults.baseURL}${url}接口出参：>>>>>>>>`, JSON.stringify(res.data))
            // NO-LOGIN=100400:当前会话未登录 
            // KICK_OUT=100401:异地登录，系统强制退出！ 
            // TOKEN_TIMEOUT=100402:登录已过期，请重新登录 
            // BE_REPLACED=100403:已被顶下线，请重新登录 
            // INVALID_TOKEN=100404:登录已过期，请重新登录 
            // NOT_TOKEN=100405:未提供token
            if ((res.status + '') == '401' || (res.data.code >= 100400 && res.data.code <= 100405)) { // 登录失效
                Utils.userData.clearLocalStorage();
                _this.$router.push('/login');
            }else{
                resolve(res.data)
            }
        }).catch(err => {
            loadingInstance.close();
            console.log(`${axios.defaults.baseURL}${url}接口异常：>>>>>>>>`, err.toString())
            // 提示信息
            Message.closeAll();
            // 登录失效
            if (err.response.data.code == -100 || err.response.data.code == 401) { // 登录失效
                Utils.userData.clearLocalStorage();
                _this.$router.push('/login');
                Message({
                    message: '登录已失效，请重新登陆',
                    type: 'warning'
                });
            }else{
                Message({
                    message: "网络异常，请重试！",
                    type: 'warning'
                });
            }
            reject(err)
        })
    })
};

export const uploadMultiResponse = (url, formData, _this, bloading = true) => {
    return new Promise((resolve, reject) => {
        if(bloading) {
            uni.showLoading()
        }

        console.log(`${baseURL}${url}文件上传：>>>>>>>>`, JSON.stringify(formData))

        axios.post(
            `${baseURL}${url}`, 
            formData, 
            {
                headers: {
                    'Content-Type': 'multipart/form-data',
                    "X-DataBus-Request-From": "Mes_PinKong_DatabusAgent",
                    "X-DataBus-Request-To": "ZhongShan_SanHe_MPlatform",
                }
            }
        ).then((res)=>{
            console.log(`${baseURL}${url}接口出参：>>>>>>>>`, JSON.stringify(res.data));
            // NO-LOGIN=100400:当前会话未登录 
            // KICK_OUT=100401:异地登录，系统强制退出！ 
            // TOKEN_TIMEOUT=100402:登录已过期，请重新登录 
            // BE_REPLACED=100403:已被顶下线，请重新登录 
            // INVALID_TOKEN=100404:登录已过期，请重新登录 
            // NOT_TOKEN=100405:未提供token
            if ((res.status + '') == '401' || (res.data.code >= 100400 && res.data.code <= 100405)) { // 登录失效
                Utils.userData.clearLocalStorage();
                _this.$router.push('/login');
            }else{
                resolve(res.data)
            }
        }).catch((err)=>{
            console.log(`${baseURL}${url}接口错误：>>>>>>>>`, err);
            // 提示信息
            Message.closeAll();
            // 登录失效
            if (err.response.data.code == -100 || err.response.data.code == 401) { // 登录失效
                Utils.userData.clearLocalStorage();
                _this.$router.push('/login');
                Message({
                    message: '登录已失效，请重新登陆',
                    type: 'warning'
                });
            }else{
                Message({
                    message: "网络异常，请重试！",
                    type: 'warning'
                });
            }
            reject(err)
        }).finally(() => {
            
        })
    })
};

/************ 独立看板移动端 *************/

export const postResponseMobile = (url, parma) => {
    return new Promise((resolve, reject) => {
        axios.defaults.baseURL = baseUrl
        axios.defaults.headers = {
            'Content-Type': 'application/json;charset=UTF-8',
            "X-DataBus-Request-From": "Mes_PinKong_DatabusAgent",
            "X-DataBus-Request-To": "ZhongShan_SanHe_MPlatform",
        }

        console.log(`${axios.defaults.baseURL}${url}接口入参：>>>>>>>>`, JSON.stringify(parma))
        axios.post(url, parma).then(res => {
            console.log(`${axios.defaults.baseURL}${url}接口出参：>>>>>>>>`, JSON.stringify(res.data))
            resolve(res.data)
        }).catch(err => {
            console.log(`${axios.defaults.baseURL}${url}接口异常：>>>>>>>>`, err.toString())
            reject(err)
        })
    })
};

export const getResponseMobile = (url, parma) => {
    return new Promise((resolve, reject) => {

        axios.defaults.baseURL = baseUrl
        axios.defaults.headers = {
            'Content-Type': 'application/json;charset=UTF-8',
            "X-DataBus-Request-From": "Mes_PinKong_DatabusAgent",
            "X-DataBus-Request-To": "ZhongShan_SanHe_MPlatform",
        }

        console.log(`${axios.defaults.baseURL}${url}接口入参：>>>>>>>>`, JSON.stringify(parma))

        if(!!parma) {
            url += '?' + parma;
        }
        axios.get(url).then(res => {
            console.log(`${axios.defaults.baseURL}${url}接口出参：>>>>>>>>`, JSON.stringify(res.data))
            resolve(res.data)
        }).catch(err => {
            console.log(`${axios.defaults.baseURL}${url}接口异常：>>>>>>>>`, err.toString())
            reject(err)
        })
    })
};

/************ 运输计划移动端 *************/

export const postYSResponseMobile = (url, parma) => {
    return new Promise((resolve, reject) => {
        // let baseUrl = 'http://180.168.218.122:17864'
        // let baseUrl = 'http://databus.siweisoft.cn:5001'
        let baseUrl = 'http://127.0.0.1:8094'
        axios.defaults.baseURL = baseUrl
        axios.defaults.headers = {
            'Content-Type': 'application/json;charset=UTF-8',
            'X-DataBus-Request-From':'Wechat_APP_Api',
            'X-DataBus-Request-To':'Bengbu_Huaiyuan_tmes'
        }

        console.log(`${axios.defaults.baseURL}${url}接口入参：>>>>>>>>`, JSON.stringify(parma))
        axios.post(url, parma).then(res => {
            console.log(`${axios.defaults.baseURL}${url}接口出参：>>>>>>>>`, JSON.stringify(res.data))
            resolve(res.data)
        }).catch(err => {
            console.log(`${axios.defaults.baseURL}${url}接口异常：>>>>>>>>`, err.toString())
            reject(err)
        })
    })
};

export const getYSResponseMobile = (url, parma) => {
    return new Promise((resolve, reject) => {
        // let baseUrl = 'http://180.168.218.122:17864'
        // let baseUrl = 'http://databus.siweisoft.cn:5001'
        let baseUrl = 'http://127.0.0.1:8094'
        axios.defaults.baseURL = baseUrl
        axios.defaults.headers = {
            'Content-Type': 'application/json;charset=UTF-8',
            'X-DataBus-Request-From':'Wechat_APP_Api',
            'X-DataBus-Request-To':'Bengbu_Huaiyuan_tmes'
        }

        console.log(`${axios.defaults.baseURL}${url}接口入参：>>>>>>>>`, JSON.stringify(parma))

        if(!!parma) {
            url += '?' + parma;
        }
        axios.get(url).then(res => {
            console.log(`${axios.defaults.baseURL}${url}接口出参：>>>>>>>>`, JSON.stringify(res.data))
            resolve(res.data)
        }).catch(err => {
            console.log(`${axios.defaults.baseURL}${url}接口异常：>>>>>>>>`, err.toString())
            reject(err)
        })
    })
};


export default {
	postResponse,
    getResponse,
    uploadResponse,
    uploadMultiResponse,
    token,
    postResponseMobile,
    getResponseMobile,
    postYSResponseMobile,
    getYSResponseMobile,
}