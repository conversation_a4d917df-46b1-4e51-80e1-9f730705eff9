import http from './index'
import { Loading, Message } from 'element-ui';
/**
 * 这里统一处理项目中所有的 api 接口
 */
// api列表
export default {

  // 铁标开关
  requestLoginCode: (params, _this) => http.postResponse('/auth/login', params, _this),
  // 根据手机号和验证码登录
  getIsIronMarkStatus: (_this) => http.getResponse('/systemParam/getIsIronMarkStatus',null, _this),
  //退出登录
  requestLogout: (params, _this) => http.postResponse('/auth/logout', params, _this),
  //获取验证码
  requestCode: (params, _this) => http.postResponse('/auth/code', params, _this),
  getPrefixPath: (params, _this) => http.getResponse('/upload/getPrefixPath', params, _this),
  
  //获取评定依据
  getAccordingTo: (params, _this) => new Promise((resolve, reject) => {
    if (params.testProjectCode === 'quick' || params.testProjectCode === "CEMENT_PARAM_KJ") {
      return resolve();
    }
    http.postResponse('/testProjectSpecConfig/getAll', params, _this).then(res => {
      try {
        if(res.data.list.length > 0){
          // 不处理
        }else{
          // Message({
          //   message: "结论标准未配置",
          //   type: 'warning'
          // });
          console.warn(params, "结论标准未配置");
        }
      } catch (error) {
        // Message({
        //   message: "结论标准未配置",
        //   type: 'warning'
        // });
        console.warn(params, "结论标准未配置");
      }
      resolve(res);
    });
  }),
  
  
  //获取单个字典属性
  getDictValue: (params, _this) => http.postResponse('/dictValue/getDictValue', params, _this),
  //获取合同
  getContractList: (params, _this) => http.postResponse('/tglContract/getAll', params, _this),
  getContractPageList: (params, _this) => http.postResponse('/tglContract/page', params, _this),
  
  
  // 根据试验台账id获取供货任务信息
  getTaskInfoMaterial: (params, _this) => http.getResponse('/tcWaybill/selectByExperimentId', params, _this),
  // 采购单接口
  getTcWaybill: (params, _this) => http.getResponse('/experimentSupplyTask/selectByExperimentId', params, _this),
  // 修改批次
  setBatchApi: (params, _this) => http.postResponse('/experiment/updateBatchByIds', params, _this),
  
  // 根据任务单号查询打印绑定的试验委托
  getExperimentByFrwdh: (params, _this) => http.getResponse('/erpRwdextra/selectExperimentByFrwdh', params, _this),
  
  // 根据任务单号查询打印绑定的试验委托
  getExperimentUpdateByFrwdh: (params, _this) => http.getResponse('/rwdextraUpdateLog/selectExperimentByFrwdh', params, _this),


  

  // 手动生成资料
  genPrintInfo: (params, _this) => http.getResponse('/erpRwdextra/genPrintInfo', params, _this),
  
  // 上传协会数据
  uploadAssociation: (params, _this) => http.postResponse('/uploadAssociation/upload', params, _this),
  // 下载协会数据
  downloadAssociation: (params, _this) => http.postResponse('/uploadAssociation/download', params, _this),
  // 保存协会相关信息
  submitShxhSynchronizedata: (params, _this) => http.postResponse('/experiment/submitShxhSynchronizedata', params, _this),
  
  
  //混凝土多个标签的信息
  //根据试验台账id获取对应快检和批检信息	
  getExperimentDetail: (params, _this) => http.postResponse('/experimentDetail/getAll', params, _this),
  //修改试验台账json	
  setExperimentDetail: (params, _this) => http.postResponse('/experimentDetail/update', params, _this, false, true),
  //获取快检和批检信息  具体list
  getExperimentDetail2: (params, _this) => http.postResponse('/experimentSampleSmoke/getAll', params, _this),
  getExperimentDetail3: (params, _this) => http.getResponse('/experimentSampleSmoke/getKsDetail', params, _this),
  //修改试验台账json 具体list
  setExperimentDetail2: (params, _this) => http.postResponse('/experimentSampleSmoke/updateList', params, _this),
  setExperimentDetail3: (params, _this) => http.postResponse('/experimentSampleSmoke/updateKsDetail', params, _this),
  
  //导出批检指标配置模版文件
  downloadCheckBatchConfigTemplate: (params, _this) => http.getResponse('/devops/exportCheckBatchConfigExcel', params, _this),
  
  //混凝土试验台账 相关
  //修改
  setExperiment: (params, _this) => http.postResponse('/experiment/update', params, _this),
  //删除
  delExperiment: (params, _this) => http.postResponse('/experiment/delete', params, _this),
  //新增
  addExperiment: (params, _this) => http.postResponse('/experiment/create', params, _this),
  //原材料新建委托
  addMaterialsExperiment: (params, _this) => http.postResponse('/experiment/createMaterialsExperiment', params, _this),
  //获取所有-分页	
  getExperimentList: (params, _this) => http.postResponse('/experiment/page', params, _this),
  // 生成实验报告
  experimentGenerateReport: (params, _this) => http.postResponse('/experiment/generateReport', params, _this),
  // 根据试验类型查询指标配置
  getCheckConfig: (params, _this) => http.postResponse('/checkBatchConfig/getCheckConfig', params, _this),
  
  getExperimentListDetail: (params, _this) => http.getResponse('/experimentCheckRecord/selectByExperimentId', params, _this),

  // /experiment/viewNewTask
  experimentViewNewTask: (params, _this) => http.postResponse('/experiment/viewNewTask', params, _this),
  
  //获取所有-bu 分页
  getExperimentListAll: (params, _this) => http.postResponse('/experiment/getAll', params, _this),
  //获取所有个数
  getExperimentAllCount: (params, _this) => http.postResponse('/experiment/getAllCount', params, _this, false),
  
  //根据id获取
  getExperimentById: (params, _this) => http.getResponse('/experiment/loadById', params, _this),
  //获取试验台账对应的小票信息
  getReceiptInfo: (params, _this) => http.getResponse('/experimentItemorder/selectByExperimentId', params, _this),
  //接收	
  setExperimentAccept: (params, _this) => http.postResponse('/experiment/accept', params, _this),
  //完成	
  setExperimentFinish: (params, _this) => http.postResponse('/experiment/finish', params, _this),
  //拒绝	
  setExperimentRefuse: (params, _this) => http.postResponse('/experiment/refuse', params, _this),
  
  //根据试验台账id获取对应任务信息
  getTaskInfo: (params, _this) => http.getResponse('/experimentTask/selectByExperimentId', params, _this),
  getTaskInfo2: (params, _this) => http.getResponse('/erpRwdextra/selectByExperimentId', params, _this),
  getTaskInfo3: (params, _this) => http.postResponse('/erpRwdextra/selectByExperimentIdPage', params, _this),
 
  //获取所有快检或批检试验项目名称	
  // setExperimentProject: (params, _this) => http.postResponse('/testProjectInfo/getAll', params, _this),
  
  //绑定运单
  getBindPage: (params, _this) => http.postResponse('/tcWaybill/bindPage', params, _this),
  bindExperimentWaybill: (params, _this) => http.postResponse('/experiment/bindExperimentWaybill', params, _this),
  //绑定小票
  getBindPageXP: (params, _this) => http.postResponse('/erpItemorder/bindPage', params, _this),
  bindExperimentItemOrder: (params, _this) => http.postResponse('/experiment/bindExperimentItemOrder', params, _this),

  //运单列表
  getTCWaybillList: (params, _this) => http.postResponse('/tcWaybill/page', params, _this),
  // 批量生成委托
  addTCWaybillExperimentBatch: (params, _this) => http.postResponse('/tcWaybill/batchGenerateExperiment', params, _this),
  
  
  //样品管理
    //取样
    setSampling: (params, _this) => http.postResponse('/experimentSampleTake/sample', params, _this),
    //修改
    setSample: (params, _this) => http.postResponse('/experimentSampleTake/update', params, _this),
    //删除
    delSample: (params, _this) => http.postResponse('/experimentSampleTake/delete', params, _this),
    //新增
    addSample: (params, _this) => http.postResponse('/experimentSampleTake/create', params, _this),
    //获取所有-分页	
    getSampleList: (params, _this) => http.postResponse('/experimentSampleTake/page', params, _this),
    setSampleExclusive: (params, _this) => http.postResponse('/experimentSampleTake/insertOrUpdateByExperimentId', params, _this),
    getSampleExclusive: (params, _this) => http.getResponse('/experimentSampleTake/selectByExperimentId', params, _this),
    
    //抽样
    //修改
    setSampleMore: (params, _this) => http.postResponse('/experimentSampleSmoke/update', params, _this),
    //删除
    delSampleMore: (params, _this) => http.postResponse('/experimentSampleSmoke/delete', params, _this),
    //新增
    addSampleMore: (params, _this) => http.postResponse('/experimentSampleSmoke/create', params, _this),
    //获取所有-分页	
    getSampleMoreList: (params, _this) => http.postResponse('/experimentSampleSmoke/page', params, _this),
    
    //获取仪器设备数据
    getEquipmentData: (params, _this) => http.getResponse('/equipment/getEquipmentInfo', params, _this),
    //获取最新温湿度
    getHumitureData: (params, _this) => http.postResponse('/temperatureAndHumidity/getLastInfo', params, _this),
  //混凝土试验台账 相关 结束
  
  // 上传工程信息到协会
  uploadLaboratory: (params, _this) => http.postResponse('/erpProject/uploadLaboratory', params, _this),
  
  // 上传客户到协会
  uploadCustomer: (params, _this) => http.postResponse('/erpCustomer/uploadLaboratory', params, _this),
  
/**
 * 品控中心  开始----------
 */
  //新增含水率	
  moistureContentCreate: (params, _this) => http.postResponse('/aggregateWater/create', params, _this),
  //修改含水率	
  setMoistureContent: (params, _this) => http.postResponse('/aggregateWater/update', params, _this),
  //删除含水率	
  delMoistureContent: (params, _this) => http.postResponse('/aggregateWater/delete', params, _this),
  //获取所有含水率-分页
  getMoistureContent: (params, _this) => http.postResponse('/aggregateWater/page', params, _this),
  //根据id获取含水率	
  getMoistureContentDetail: (params, _this) => http.getResponse('/aggregateWater/loadById', params, _this),
/**
 * 品控中心  结束
 */
/**
 * 工单服务  开始
 */
  //修改
  setWorkOrder: (params, _this) => http.postResponse('/order/update', params, _this),
  //删除
  delWorkOrder: (params, _this) => http.postResponse('/order/delete', params, _this),
  //作废
  cancelWorkOrder: (params, _this) => http.postResponse('/order/cancel', params, _this),
  // 撤销上传
  revokeUpload: (params, _this) => http.getResponse('/experiment/revokeUpload', params, _this),
  //新增
  addWorkOrder: (params, _this) => http.postResponse('/order/create', params, _this),
  //获取所有-分页	
  getWorkOrderList: (params, _this) => http.postResponse('/order/page', params, _this),
    //获取所有工单不分页	
  getWorkOrderAllList: (params, _this) => http.postResponse('/order/getAll', params, _this),

  //获取所有任务单不分页	
  getTglTrwdAllList: (params, _this) => http.postResponse('/erpRwdextra/getAll', params, _this),
  //获取所有任务单分页	
  getTglTrwdPageList: (params, _this) => http.postResponse('/erpRwdextra/page', params, _this),
  // 获取所有erp 任务单分页 
  getErpRwdextraPageList: (params, _this) => http.postResponse('/erpRwdextra/page', params, _this),
  // 导出任务单 
  exportRwdextraPageList: (params, _this) => http.postResponse('/erpRwdextra/exportRwdextra', params, _this),
  // 批量修改任务单
  updateBatchDetail: (params, _this) => http.postResponse('/tglTrwd/updateBatchDetail', params, _this),
  // erp 批量修改任务单
  updateBatchErpRwdextraDetail: (params, _this) => http.postResponse('/erpRwdextra/updateBatchDetail', params, _this),

  // 批量上传任务单
  uploadBatchErpRwdextra: (params, _this) => http.postResponse('/erpRwdextra/uploadBatch', params, _this),


  // 获取生产记录列表
  getErpItemorderPageList: (params, _this) => http.postResponse('/erpItemorder/page', params, _this),
  
  // 获取任务单详情
  getTglTrwdById: (params, _this) => http.getResponse('/tglTrwd/loadById', params, _this),
  // erp 根据任务单id查询数据
  getErpRwdextraByFrwdh: (params, _this) => http.getResponse('/erpRwdextra/loadByFrwdh', params, _this),

  // 根据试验委托id和任务id删除关联关系 （小票）
  deleteByExperimentIdAndTaskId: (params, _this) => http.postResponse('/experimentTask/deleteByExperimentIdAndTaskId', params, _this),
  // 删除试验委托和小票关系
  deleteByExperimentIdAndItemorderId: (params, _this) => http.postResponse('/experimentItemorder/deleteByExperimentIdAndItemorderId', params, _this),
  // 删除试验委托和运单关系
  deleteByExperimentIdAndWaybillId: (params, _this) => http.postResponse('/experimentWaybill/deleteByExperimentIdAndWaybillId', params, _this),
  // 删除关联关系根据试验台账id和任务单号
  deleteByExperimentIdAndSupplyTaskId: (params, _this) => http.postResponse('/experimentSupplyTask/deleteByExperimentIdAndSupplyTaskId', params, _this),


  // 获取生产记录列表
  getTglItemOrderPage: (params, _this) => http.postResponse('/tglItemOrder/page', params, _this),
  
  //根据工程id获取
  getWorkOrderById: (params, _this) => http.getResponse('/order/loadById', params, _this),
  
  //转派
  setWorkOrderTransfer: (params, _this) => http.postResponse('/order/transfer', params, _this),
  //接收	
  setWorkOrderAccept: (params, _this) => http.postResponse('/order/accept', params, _this),
  //完成	
  setWorkOrderFinish: (params, _this) => http.postResponse('/order/finish', params, _this),
  //拒绝	
  setWorkOrderRefuse: (params, _this) => http.postResponse('/order/refuse', params, _this),
  //工单动态
  getOrderDynamic: (params, _this) => http.getResponse('/orderDynamic/getListByOrderId', params, _this),
  
/**
 * 工单服务  结束
 */


/**
 * 工程服务  开始
 */
  //修改
  setEngineering: (params, _this) => http.postResponse('/erpProject/update', params, _this),
  //修改
  setEngAddress: (params, _this) => http.postResponse('/project/updateAddress', params, _this),
  
  //删除
  delEngineering: (params, _this) => http.postResponse('/project/delete', params, _this),
  //新增
  addEngineering: (params, _this) => http.postResponse('/project/create', params, _this),
  //获取所有-分页	
  getEngineeringList: (params, _this) => http.postResponse('/project/page', params, _this),
  getEngineeringListAll: (params, _this) => http.postResponse('/erpProject/getAll', params, _this),
  //根据工程id获取
  getEngineeringById: (params, _this) => http.getResponse('/project/loadById', params, _this),

  // 新的工程列表接口 /erpProject/page
  queryNewEngineeringList: (params, _this) => http.postResponse('/erpProject/page', params, _this),
  // 新的工程详情接口 /erpProject/loadById
  getNewEngineeringById: (params, _this) => http.getResponse('/erpProject/loadById', params, _this),
/**
 * 工程服务  结束
 */


/**
 * 批检/快检参数配置  开始---------------
 */
  //根据物料类型获取所有物料名称 可以不传	
  getMaterialsNameByType: (param, _this) => http.getResponse('/materials/getMaterialsNameByType', param, _this),
  //根据物料名称获取对应的物料规格	
  getMaterialsSpecs: (param, _this) => http.getResponse('/materials/getMaterialsSpecs', param, _this),
  //根据物料类型获取对应的试验项目	
  getTestProject: (param, _this) => http.getResponse('/materials/getTestProject', param, _this),
  getTestProject2: (param, _this) => http.postResponse('/testProjectInfo/getAll', param, _this),

  // 根据物料类型和物料名称获取所有物料信息
  queryMaterialsAll: (param, _this) => http.postResponse('/materials/getAll', param, _this),
  
  
  
  //修改必检	
  updateMust: (params, _this) => http.postResponse('/checkBatchConfig/updateMust', params, _this),
  //修改批检指标
  setTarget: (params, _this) => http.postResponse('/checkBatchConfig/update', params, _this),
  //删除批检指标
  delTarget: (params, _this) => http.postResponse('/checkBatchConfig/delete', params, _this),
  //新增批检指标
  addTarget: (params, _this) => http.postResponse('/checkBatchConfig/create', params, _this),
  //根据id查询批检指标	
  getTargetDel: (params, _this) => http.getResponse('/checkBatchConfig/loadById', params, _this),
  //获取所有快检指标-分页	
  getTargetList: (params, _this) => http.postResponse('/checkBatchConfig/page', params, _this),
  //快检
  
  //修改必检
  updateQuickMust: (params, _this) => http.postResponse('/checkQuickConfig/updateMust', params, _this),
  //修改
  setCheckQuick: (params, _this) => http.postResponse('/checkQuickConfig/update', params, _this),
  //删除
  delcheckQuick: (params, _this) => http.postResponse('/checkQuickConfig/delete', params, _this),
  //新增
  addcheckQuick: (params, _this) => http.postResponse('/checkQuickConfig/create', params, _this),
  //获取所有快检指标-分页	
  getcheckQuickList: (params, _this) => http.postResponse('/checkQuickConfig/page', params, _this),
  
/**
 * 批检/快检参数  结束
 */
/**
 * 设备管理  开始
 */
  //修改
  setEquipment: (params, _this) => http.postResponse('/equipment/update', params, _this),
  //删除
  delEquipment: (params, _this) => http.postResponse('/equipment/delete', params, _this),
  //新增
  addEquipment: (params, _this) => http.postResponse('/equipment/create', params, _this),
  //获取所有快检指标-分页	
  getEquipmentList: (params, _this) => http.postResponse('/equipment/page', params, _this),
  getEquipmentAll: (params, _this) => http.postResponse('/equipment/getAll', params, _this),
  // 批量修改周期
  updateBatchCycle: (params, _this) => http.postResponse('/equipment/updateList', params, _this),
  // 设备详情
  getEquipmentDetail: (params, _this) => http.getResponse('/equipment/loadById', params, _this),
  // 导出仪器设备模版文件
  downloadEquipmentTemplate: (params, _this) => http.getResponse('/devops/exportEquipmentExcel', params, _this),
  // 获取对应试验类型的设备信息
  getEquipmentInfoByExperimentType: (params, _this) => http.getResponse('/equipment/getEquipmentInfoByExperimentType', params, _this),
  
/**
 * 设备管理  结束
 */
/**
 * 用户管理  开始
 */
  //修改
  setUser: (params, _this) => http.postResponse('/user/update', params, _this),
  //删除
  delUser: (params, _this) => http.postResponse('/user/delete', params, _this),
  //新增
  addUser: (params, _this) => http.postResponse('/user/create', params, _this),
  //获取所有快检指标-分页	
  getUserList: (params, _this) => http.postResponse('/user/page', params, _this),
  getUserListAll: (params, _this) => http.postResponse('/user/getAll', params, _this),
  
  getRoleList: (params, _this) => http.postResponse('/role/getAll', params, _this),
  
  updatePassword: (params, _this) => http.postResponse('/user/updatePassword', params, _this),
  // 查询用户信息
  queryUserInfo: (params, _this) => http.getResponse('/user/loadById', params, _this),
  // 查询用户证书所有数据
  queryUserCertificateAll: (params, _this) => http.postResponse('/userCertificate/getAll', params, _this),
  // 新增用户证书
  addUserCertificate: (params, _this) => http.postResponse('/userCertificate/create', params, _this),
  // 修改用户证书
  updateUserCertificate: (params, _this) => http.postResponse('/userCertificate/update', params, _this),  
  
/**
 * 用户管理  结束
 */

/**
 * 温湿度管理  开始
 */
  //修改
  setHumiture: (params, _this) => http.postResponse('/temperatureAndHumidity/update', params, _this),
  //删除
  delhumiture: (params, _this) => http.postResponse('/temperatureAndHumidity/delete', params, _this),
  //新增
  addhumiture: (params, _this) => http.postResponse('/temperatureAndHumidity/create', params, _this),
  //获取所有快检指标-分页	
  gethumitureList: (params, _this) => http.postResponse('/temperatureAndHumidity/page', params, _this),
/**
 * 温湿度管理  结束
 */
/**
 * 工程区域划分  开始
 */
  //修改
  setRegion: (params, _this) => http.postResponse('/projectArea/update', params, _this),
  //删除
  delRegion: (params, _this) => http.postResponse('/projectArea/delete', params, _this),
  //新增
  addRegion: (params, _this) => http.postResponse('/projectArea/create', params, _this),
  //获取所有快检指标-分页	
  getRegionList: (params, _this) => http.postResponse('/projectArea/page', params, _this),
/**
 * 工程区域划分  结束
 */



  uploadFile: (params, _this) => http.uploadResponse('/upload/upload_file', params, _this),


  /**
   * 基准配合比
   */
  // 列表查询
  queryMixProportionPage: (params, _this) => http.postResponse('/mixProportion/page', params, _this),
  // 获取配合比全部数据
  queryMixProportionAll: (params, _this) => http.postResponse('/mixProportion/getAll', params, _this),
  // 配合比详情
  queryMixProportionDetail: (params, _this) => http.getResponse('/mixProportion/loadById', params, _this),
  // 根据基准配合比id 分页查询适配记录
  queryAdaptationPage: (params, _this) => http.postResponse('/adaptation/page', params, _this),
  // 修改配合比
  mixProportionUpdate: (params, _this) => http.postResponse('/mixProportion/update', params, _this),
  //新增配合比
  mixProportionCreate: (params, _this) => http.postResponse('/mixProportion/create', params, _this),
  // 配合比修改记录
  mixProportionUpdateLogGetAll: (params, _this) => http.postResponse('/mixProportionUpdateLog/getAll', params, _this),
  // 根据任务单号 查询erp那边的配合比修改历史
  queryMixProportionHistory: (params, _this) => http.getResponse('/erpPhbhistory/selectByFrwd', params, _this),
  // 获取所有客户
  getErpCustomerAll: (params, _this) => http.postResponse('/erpCustomer/getAll', params, _this),
  // 导出配合比 
  exportMixProportion: (params, _this) => http.getResponse('/mixProportion/exportProportion', params, _this),
  // 新增配合比验证记录
  addMixProportionVerifyRecord: (params, _this) => http.postResponse('/mixProportionVerifyRecord/create', params, _this),
  // 获取所有配合比验证记录-分页
  getMixProportionVerifyRecordList: (params, _this) => http.postResponse('/mixProportionVerifyRecord/page', params, _this),
  // 修改配合比验证记录
  updateMixProportionVerifyRecord: (params, _this) => http.postResponse('/mixProportionVerifyRecord/update', params, _this),
  // 删除配合比验证记录
  deleteMixProportionVerifyRecord: (params, _this) => http.postResponse('/mixProportionVerifyRecord/delete', params, _this),
  // 根据物料类型查询对应生产厂家
  getFactoryListByMaterialsType: (params, _this) => http.getResponse('/supplierCompanyMaterials/getFactoryListByMaterialsType', params, _this),
  // 批量修改任务单
  updateBatchPrintDetailRwdextra: (params, _this) => http.postResponse('/erpRwdextra/updateBatchPrintDetailRwdextra', params, _this),
  // 增补资料批量修改
  rwUpdateBatchPrintDetailRwdextra: (params, _this) => http.postResponse('/rwdextraUpdateLog/updateBatchPrintDetailRwdextra', params, _this),

  // 导入配合比
  // frontEndImportProportion: (params, _this) => http.getResponse('/mixProportion/frontEndImportProportion', params, _this),
/**
 * 设备校准记录表相关共
 *
*/
  //修改
  setCalibrationRecord: (params, _this) => http.postResponse('/calibrationRecord/update', params, _this),
  //删除
  delCalibrationRecord: (params, _this) => http.postResponse('/calibrationRecord/delete', params, _this),
  //新增
  addCalibrationRecord: (params, _this) => http.postResponse('/calibrationRecord/create', params, _this),
  //获取所有-分页	
  getCalibrationRecordList: (params, _this) => http.postResponse('/calibrationRecord/page', params, _this),
  getCalibrationRecordAll: (params, _this) => http.postResponse('/calibrationRecord/getAll', params, _this),

  getCalibrationRecordById: (params, _this) => http.getResponse('/calibrationRecord/loadById', params, _this),
  
  // 获取设备打印记录 
  getEquipmentPrintInfo: (params, _this) => http.postResponse('/experimentPrint/getEquipmentPrintInfo', params, _this),
  
  /**
   * 材料及规格设置
   */
  // 查询材料及规格分页
  queryMaterialsSpecConfigPage: (params, _this) => http.postResponse('/materialsSpecConfig/page', params, _this),
  queryMaterialsSpecConfigAll: (params, _this) => http.postResponse('/materialsSpecConfig/getAll', params, _this),
  querySelectByMaterialsType: (params, _this) => http.getResponse('/materialsSpecConfig/selectByMaterialsType', params, _this),
  setMaterialsInfo: (params, _this) => http.postResponse('/erpRwdextra/setMaterialsInfo', params, _this),
  getMaterialsInfo: (params, _this) => http.postResponse('/erpRwdextra/getMaterialsInfo', params, _this),
  
  // 新增材料及规格分页
  addMaterialsSpecConfig: (params, _this) => http.postResponse('/materialsSpecConfig/create', params, _this),
  // 修改材料及规格分页
  updateMaterialsSpecConfig: (params, _this) => http.postResponse('/materialsSpecConfig/update', params, _this),
  // 删除材料及规格分页
  deleteMaterialsSpecConfig: (params, _this) => http.postResponse('/materialsSpecConfig/delete', params, _this),
  // 分页获取物料信息
  queryMaterialsPage: (params, _this) => http.postResponse('/materials/page', params, _this),
  // 设置材料及规格配置信息
  setMaterials: (params, _this) => http.postResponse('/materials/config', params, _this),
  // 设置材料及规格配置信息
  deleteMaterials: (params, _this) => http.postResponse('/materials/delete', params, _this),
  // 初始化协会材料
  syncLaboratoryMaterialsInfo: (params, _this) => http.getResponse('/syncLaboratory/materialsInfo', params, _this),
  // 初始化协会工程
  syncLaboratoryContractInfo: (params, _this) => http.getResponse('/syncLaboratory/contractInfo', params, _this),
  // 初始化协会客户
  syncLaboratoryCustomInfo: (params, _this) => http.getResponse('/syncLaboratory/customInfo', params, _this),

  // 获取所有委托规则设置-不分页
  queryExperimentGenConfig: (params, _this) => http.postResponse('/experimentGenConfig/getAll', params, _this),
  // 修改委托规则设置（多个的）
  updateExperimentGenConfig: (params, _this) => http.postResponse('/experimentGenConfig/updateList', params, _this),

  // 根据材料id获取对应材料所有的供应商信息
  querySupplierCompnayMaterialsList: (params, _this) => http.getResponse('/supplierCompnay/selectBySupplierCompanyList', params, _this),
  // 根据材料id和供应商id获取供应商物料的生产厂家
  querySupplierCompanyMaterialsFactoryList: (params, _this) => http.getResponse('/supplierCompanyMaterials/getFactoryList', params, _this),
  // 物料规则设置配合比 /materials/bindPhbNo
  materialsBindPhbNo: (params, _this) => http.postResponse('/materials/bindPhbNo', params, _this),

  /**
   * 供应商管理
   */
  //修改
  setSupplierCompnay: (params, _this) => http.postResponse('/supplierCompnay/update', params, _this),
  //删除
  delSupplierCompnay: (params, _this) => http.postResponse('/supplierCompnay/delete', params, _this),
  //新增
  addSupplierCompnay: (params, _this) => http.postResponse('/supplierCompnay/create', params, _this),
  //获取所有快检指标-分页	
  getSupplierCompnayList: (params, _this) => http.postResponse('/supplierCompnay/page', params, _this),
  // 
  getSupplierCompnayListAll: (params, _this) => http.postResponse('/supplierCompnay/getAll', params, _this),
  // 根据id 查询供应商信息
  querySupplierCompnayDetail: (params, _this) => http.getResponse('/supplierCompnay/loadById', params, _this),
  // 所有供应商物料分页
  querySupplierCompanyMaterialsListPage: (params, _this) => http.postResponse('/supplierCompanyMaterials/page', params, _this),
  // 所有供应商物料
  querySupplierCompanyMaterialsAll: (params, _this) => http.postResponse('/supplierCompanyMaterials/getAll', params, _this),
  //新增供应商物料
  addSupplierCompanyMaterials: (params, _this) => http.postResponse('/supplierCompanyMaterials/create', params, _this),
  // 修改供应商物料
  updateSupplierCompanyMaterials: (params, _this) => http.postResponse('/supplierCompanyMaterials/update', params, _this),
  // 删除供应商物料
  deleteSupplierCompanyMaterials: (params, _this) => http.postResponse('/supplierCompanyMaterials/delete', params, _this),
  // 供应商物料统计
  getSupplierCompanyMaterialsStatistics: (params, _this) => http.postResponse('/supplierCompanyMaterials/getStatistics', params, _this),


  // 预警设置
  queryWarningAll: (params, _this) => http.postResponse('/warning/getAll', params, _this),
  updateWarning: (params, _this) => http.postResponse('/warning/updateList', params, _this),

  // 受检台账
  addExperimentTested: (params, _this) => http.postResponse('/experimentTested/create', params, _this),
  updateExperimentTested: (params, _this) => http.postResponse('/experimentTested/update', params, _this),
  deleteExperimentTested: (params, _this) => http.postResponse('/experimentTested/delete', params, _this),
  queryExperimentTestedPage: (params, _this) => http.postResponse('/experimentTested/page', params, _this),

  // 外委托
  addExperimentOuter: (params, _this) => http.postResponse('/experimentOuter/create', params, _this),
  updateExperimentOuter: (params, _this) => http.postResponse('/experimentOuter/update', params, _this),
  deleteExperimentOuter: (params, _this) => http.postResponse('/experimentOuter/delete', params, _this),
  queryExperimentOuterPage: (params, _this) => http.postResponse('/experimentOuter/page', params, _this),

  // 根据物料类型查询试验项目配置
  queryTestProjectInfo: (params, _this) => http.getResponse('/testProjectInfo/selectByMaterialType', params, _this),
  
  // 查询未读消息
  queryExperimentMessage: (params, _this) => http.postResponse('/experimentMessage/getAll', params, _this, false),
  // 消息已读 /experimentMessage/read
  updateExperimentMessage: (params, _this) => http.postResponse('/experimentMessage/read', params, _this, false),

  // 打印
  queryExperimentPrintInfo: (params, _this) => http.postResponse('/experimentPrint/getExperimentPrintInfo', params, _this),
  // 获取ERP任务单打印数据
  queryErpRwdPrintInfo: (params, _this) => http.postResponse('/experimentPrint/getErpRwdPrintInfo', params, _this),
  // 根据任务单修改记录获取ERP任务单打印数据
  getErpRwdPrintInfoToUpdateLog: (params, _this) => http.postResponse('/experimentPrint/getErpRwdPrintInfoToUpdateLog', params, _this),
  // 根据id获取ERP任务单打印记录
  getExperimentPrintRecord: (params, _this) => http.getResponse('/rwdextraPrintRecord/loadById', params, _this),
  // 根据配合比查询打印的数据
  queryPrintInfoByMixIds: (params, _this) => http.postResponse('/experimentPrint/getPrintInfoByMixIds', params, _this),

  // 新增打印记录
  addExperimentPrintRecord: (params, _this) => http.postResponse('/experimentPrintRecord/create', params, _this),
  // 新增ERP任务单打印记录相关
  addErpRwdPrintRecord: (params, _this) => http.postResponse('/rwdextraPrintRecord/create', params, _this),
  // 获取所有ERP任务单打印记录-不分页
  queryErpRwdPrintRecord: (params, _this) => http.postResponse('/rwdextraPrintRecord/getAll', params, _this),
  // 获取所有任务单的修改记录
  queryErpRwdUpdateLog: (params, _this) => http.postResponse('/rwdextraUpdateLog/getAll', params, _this),
  // 增补资料列表
  queryErpRwdUpdatePage: (params, _this) => http.postResponse('/rwdextraUpdateLog/page', params, _this),
  // 增补资料查询接口
  queryErpRwdUpdateLogById: (params, _this) => http.getResponse('/rwdextraUpdateLog/loadRwdextraById', params, _this),
  // 删除修改记录
  deleteErpRwdUpdateLog: (params, _this) => http.postResponse('/rwdextraUpdateLog/delete', params, _this),
  // 客户管理
  // 获取客户分页数据
  queryCustomerPage: (params, _this) => http.postResponse('/erpCustomer/page', params, _this),
  // 修改客户信息
  updateErpCustomer: (params, _this) => http.postResponse('/erpCustomer/update', params, _this),

  // 获取企业信息配置
  queryCompanyConfig: (params, _this) => http.getResponse('/companyConfig/loadById', params, _this),
  // 修改企业信息配置
  updateCompanyConfig: (params, _this) => http.postResponse('/companyConfig/update', params, _this),

  // 获取推送设置
  getAllSystemParam: (params, _this) => http.getResponse('/systemParam/getAllSystemParam', params, _this),
  // 修改推送设置
  updateSystemParamList: (params, _this) => http.postResponse('/systemParam/updateSystemParamList', params, _this),

  // 新增试验项目说明
  addMaterialsTestProjectDesc: (params, _this) => http.postResponse('/materialsTestProjectDesc/create', params, _this),
  // 修改试验项目说明 
  updateMaterialsTestProjectDesc: (params, _this) => http.postResponse('/materialsTestProjectDesc/update', params, _this),
  // 删除试验项目说明
  deleteMaterialsTestProjectDesc: (params, _this) => http.postResponse('/materialsTestProjectDesc/delete', params, _this),
  // 获取所有试验项目说明-分页
  queryMaterialsTestProjectDescPage: (params, _this) => http.postResponse('/materialsTestProjectDesc/page', params, _this),
  // 根据itemCode获取试验说明
  queryMaterialsTestProjectDescByItemCode: (params, _this) => http.getResponse('/materialsTestProjectDesc/loadByItemCode', params, _this),

  // 分页获取开盘鉴定
  queryOpenAppraisalPage: (params, _this) => http.postResponse('/openAppraisal/page', params, _this),
  // 获取开盘鉴定绑定的抗压台账
  queryOpenAppraisalById: (params, _this) => http.getResponse('/openAppraisal/loadKyExperimentExById', params, _this),
  // 根据物料类型获取所有物料名称
  queryMaterialsNameList: (params, _this) => http.getResponse('/supplierCompanyMaterials/getMaterialsNameList', params, _this),
  // 根据物料类型+物料名称获取所有物料规格
  queryMaterialsSpecList: (params, _this) => http.getResponse('/supplierCompanyMaterials/getMaterialsSpecList', params, _this),
  // 根据物料类型+物料名称+物料规格获取所有生产厂家 
  queryMaterialsFactoryList: (params, _this) => http.getResponse('/supplierCompanyMaterials/getMaterialsFactoryList', params, _this),

  // 根据主键id获取ERP任务单打印修改信息
  queryPrintByFrwdh: (params, _this) => http.getResponse('/erpRwdextra/getPrintByFrwdh', params, _this),
  // 根据主键id获取ERP任务单打印修改信息
  updateBatchPrintDetail: (params, _this) => http.postResponse('/erpRwdextra/updateBatchPrintDetail', params, _this),
  // 根据任务单修改信息生成报告
  generateReport: (params, _this) => http.postResponse('/erpRwdextra/generateReport', params, _this),

  // 根据物料名称和物料规格获取所有的供应商信息
  queryMaterialSupplierCompanyList: (params, _this) => http.getResponse('/supplierCompnay/selectByMaterialSupplierCompanyList', params, _this),

  // 工程绑定物料
  configErpProject: (params, _this) => http.postResponse('/erpProject/configErpProject', params, _this),

  // 根据物料类型查询对应供货商
  querySupplierCompanyListByType: (params, _this) => http.getResponse('/supplierCompnay/selectBySupplierCompanyListByType', params, _this),
}






// // 上传多张图片
// export const uploadMultiFile = (params, loginType, bloading=true) => http.uploadMultiResponse('/upload/upload_file_list', params.formData, _this);
// // 上传图片
// export const uploadFile = (params, _this) => http.uploadResponse('/upload/upload_file', params, _this);
// // 导入、导出
// // 导出用户信息
// export const exportUser = (params, _this) => http.postResponse('/export/user', params, _this);
// // 下载用户excel模板
// export const downloadUserTemplate = (params, _this) => http.postResponse('/downloadTemplate/user', params, _this);
// // 导入用户
// export const importUser = (params, _this) => http.postResponse('/import/user', params, _this);
// export const getAllMixers = (params, _this) => http.getYSResponseMobile('/api/GetAllMixers', params, _this);
// // 根据id获取批检记录详情
// export const getCheckBatchRecordDetail = (params, _this) => http.getResponse('/checkBatchRecord/getDetail', params, _this);