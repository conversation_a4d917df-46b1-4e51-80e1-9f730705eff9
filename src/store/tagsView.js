const state = {
  visitedViews: [],
  addMixProportionFullpath: '',
  experimentBoardFullpath: '',
  materialBoardFullpath: '',
}

const mutations = {
  ADD_VISITED_VIEW: (state, view) => {
    console.log("ADD_VISITED_VIEW>>", view)
    //if (state.visitedViews.some(v => v.path === view.path)) return
    if (view.meta.fixed) return;
    if (state.visitedViews.some(v => v.name === view.name && v.path === view.path)) return;
    
    const {fullPath,
          hash,
          meta,
          name,
          params,
          path,
          query
        } = view;
    state.visitedViews.push({
      fullPath,
      hash,
      meta,
      name,
      params,
      path,
      query
    })
    
  },

  DEL_VISITED_VIEW: (state, view) => {
    for (const [i, v] of state.visitedViews.entries()) {
      if (v.name === view.name && v.path === view.path) {
        state.visitedViews.splice(i, 1)
        break
      }
    }
  },
  DEL_ALL_VISITED_VIEWS: state => {
    // keep affix tags
    const affixTags = state.visitedViews.filter(tag => tag.meta.affix)
    state.visitedViews = affixTags
  },

  SAVE_ADD_MIX_FULL_PATH(state, fullpath) {
    state.addMixProportionFullpath = fullpath
  },
  SAVE_EXPERIENT_BOARD_FULL_PATH(state, fullpath) {
    state.experimentBoardFullpath = fullpath
  },
  SAVE_MATERIAL_BOARD_FULL_PATH(state, fullpath) {
    state.materialBoardFullpath = fullpath
  }
}

const actions = {

  addVisitedView({
    commit
  }, view) {
    commit('ADD_VISITED_VIEW', view)
  },

  delVisitedView({
    commit,
    state
  }, view) {
    return new Promise(resolve => {
      commit('DEL_VISITED_VIEW', view)
      resolve(state.visitedViews)
    })
  },


  delAllVisitedViews({
    commit,
    state
  }) {
    return new Promise(resolve => {
      commit('DEL_ALL_VISITED_VIEWS')
      resolve([...state.visitedViews])
    })
  },

  saveAddMixProportionFullpath({commit}, fullpath) {
    commit('SAVE_ADD_MIX_FULL_PATH', fullpath)
  },
  saveExperimentBoardFullpath({commit}, fullpath) {
    commit('SAVE_EXPERIENT_BOARD_FULL_PATH', fullpath)
  },
  saveMaterialBoardFullpath({commit}, fullpath) {
    commit('SAVE_MATERIAL_BOARD_FULL_PATH', fullpath)
  }

}

const getters = {
  visitedViews: state => state.visitedViews,
  addMixProportionFullpath: state => state.addMixProportionFullpath,
  experimentBoardFullpath: state => state.experimentBoardFullpath,
  materialBoardFullpath: state => state.materialBoardFullpath
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}