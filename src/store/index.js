/*
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2022-10-24 21:50:08
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2023-08-21 23:27:25
 * @FilePath: /truck-scales-web/src/store/index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import Vue from 'vue'
import Vuex from 'vuex'
import persistedState from 'vuex-persistedstate'

import loginStore from './loginStore';
import tagsView from './tagsView';

Vue.use(Vuex)

const store = new Vuex.Store({
    modules: {
        loginStore,
        tagsView,
        
    },
    plugins: [
        persistedState(),
        persistedState({ storage: window.sessionStorage })
    ]
})

export default store;
