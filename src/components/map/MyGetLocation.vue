<template>
    <div>
        <baidu-map
            style="display: flex; flex-direction: column-reverse;" 
            id="allmap"
            @ready="mapReady"
            @click="getLocation"
            :scroll-wheel-zoom="true"
            @set-localtion = "setLocation">
            <!-- <div style="display: flex; justify-content: right; margin-bottom: 5px;">
                <bm-auto-complete v-model="searchJingwei" :sugStyle="{zIndex: 999999}">
                    <el-input v-model="searchJingwei" style="width: 300px; margin-right: 15px" size="small" placeholder="请输入定位地址..." @keyup.enter.native="getBaiduMapPoint"></el-input>
                </bm-auto-complete>
                <el-button type="primary" @click="getBaiduMapPoint" size="small">搜索</el-button>
                <el-button type="primary" @click="infoWindowClose" size="small">清除</el-button>
            </div> -->
            <div style="display: flex; justify-content: right; margin-bottom: 5px;">
                <span style="width: 85px; margin-right: 13px; text-align: right; line-height: 26px;">
                    {{ labelTitle }}
                </span>
                <bm-auto-complete v-model="address" :sugStyle="{zIndex: bmAutoZindex}" style="width: calc(100% - 100px);">
                    <el-input v-model="address" style="width: 100%; margin-right: 15px" size="mini" placeholder="请输入单位地址..." @keyup.enter.native="getBaiduMapPoint"></el-input>
                </bm-auto-complete>
            </div>
            <bm-map-type :map-types="['BMAP_NORMAL_MAP', 'BMAP_HYBRID_MAP']" anchor="BMAP_ANCHOR_TOP_LEFT"></bm-map-type>
            <bm-marker v-if="infoWindowShow" :position="{lng: longitude, lat: latitude}">
                <bm-label content="" :labelStyle="{color: 'red', fontSize : '24px'}" :offset="{width: 8, height: 30}"/>
            </bm-marker>
            <!-- <bm-info-window :position="{lng: longitude, lat: latitude}" :show="infoWindowShow" @clickclose="infoWindowClose">
                <p>经度:{{ this.longitude }}</p>
                <p>纬度:{{ this.latitude }}</p>
            </bm-info-window> -->
        </baidu-map>
    </div>
</template>

<script>
export default {
    props: {
        lngVal: {
            type: Number | String,
            default: ''
        },
        latVal: {
            type: Number | String,
            default: ''
        },
        searchJingwei: {
            type: String,
            default: ''
        },
        labelTitle: {
            type: String,
            default: '单位地址'
        }
    },
    data() {
        return {
            // searchJingwei: '',
            address: '',
            infoWindowShow: false,
            longitude: '',
            latitude: '',
            point: '',
            bmAutoZindex: 999999
        }
    },
    methods: {
        // 地图初始化
        mapReady({ BMap, map }) {
            // 选择一个经纬度作为中心点
            this.point = new BMap.Point(121.506673, 31.243691);
            map.centerAndZoom(this.point, 12);
            this.BMap = BMap;
            this.map = map;
        },
        // 点击获取经纬度
        getLocation: function(e) {
            let that = this
            that.longitude = e.point.lng;
            that.latitude = e.point.lat;
            that.infoWindowShow = true;
            // 创建地理编码实例      
            let myGeo = new this.BMap.Geocoder();
            // 根据坐标得到地址描述    
            myGeo.getLocation(new BMap.Point(that.longitude, that.latitude), function(result){      
                if (result){      
                    // 与父组件通信要加上这句
                    that.$emit("locationSelectedChange", that.longitude, that.latitude,result.address);  
                }      
            });
            
        },
        // 设置经纬度
        setLocation: function(address, lng, lat) {
            this.address = address;
            this.longitude = lng;
            this.latitude = lat;
            if(!!lng && !!lat) {
                this.infoWindowShow = true;
                // 设置位置
                this.point = new BMap.Point(lng, lat);
                this.map.centerAndZoom(this.point, 12);
            } else {
                this.infoWindowShow = false;
                this.point = new BMap.Point(121.506673, 31.243691);
                this.map.centerAndZoom(this.point, 12);
            }

            setTimeout(function () {
                this.address = address;
            }, 1000);
        },
        getBaiduMapPoint: function() {
            let that = this;
            let myGeo = new this.BMap.Geocoder();
            myGeo.getPoint(that.address, function(point) {
                if(point) {
                    that.map.centerAndZoom(point,12);
                    that.longitude = point.lng;
                    that.latitude = point.lat;
                    that.infoWindowShow = true;
                    // 与父组件通信要加上这句
                    that.$emit("locationSelectedChange", point.lng, point.lat,that.address);
                }
            });
        },
        // 信息窗口关闭
        infoWindowClose: function() {
            this.latitude = '';
            this.longitude = '';
            this.address = '';
            this.infoWindowShow = false;
            // 与父组件通信要加上这句
            this.$emit("locationSelectedChange", '', '', '');
        },
    },
    // 创建VUE实例后的钩子
    created: function() {

    },
}
</script>

<style lang="scss" scoped>
#allmap{
    height: 300px;
    width: 100%;
    // margin: 10px 0;
}
</style>