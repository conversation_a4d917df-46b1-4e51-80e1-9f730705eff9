<template>
  <el-dialog
    v-loading="loading"
    title=""
    :visible.sync="detailVisible"
    class="claim-dialog-box"
    :before-close="handleClose"
  > 
    <div v-if="type != '7'" class="flex-row" style="margin-top: -42px;  position: absolute;">
      <el-input
        style="width: 280px;"
        size="mini"
        v-model="params.keyword"
        placeholder="搜索：输入车辆、车号、厂家、供应商名称"
      ></el-input>
      <!-- 搜索按钮 -->
      <el-button style="margin-left: 30px;" size="mini" type="primary" @click="onLoad(1)">搜索</el-button>
    </div>
    <div v-if="type == '7'" class="flex-row" style="margin-top: -42px;  position: absolute;">
      <div style="width: 200px; color: #999999;">任务单号：</div>
      <el-input
        style="margin-right: 20px;"
        size="mini"
        v-model="params.frwno"
        placeholder="搜索：请输入任务单号"
      ></el-input>
      <div style="width: 200px; color: #999999;">小票编号：</div>
      <el-input
        style=""
        size="mini"
        v-model="params.itemorderno"
        placeholder="搜索：请输入小票编号"
      ></el-input>

      <!-- 搜索按钮 -->
       <el-button style="margin-left: 30px;" size="mini" type="primary" @click="onLoad(1)">搜索</el-button>
    </div>
    <el-table
      :data="tableData"
       @selection-change="handleSelectionChange"
      style="width: 100%">
      <el-table-column
        type="selection"
        width="55">
      </el-table-column>
      <af-table-column
        v-for="item in tableColumn" 
        :key="item.prop" :prop="item.prop" 
        :label="item.label" 
        :formatter="item.formatter"
        :fixed="item.fixed" 
        :width="item.width || ''"
        align="center" 
      />
      
    </el-table>
    <Pagination
      :total="total" 
      :pageNum="pageObj.pageNum" 
      :pageSize="pageObj.pageSize" 
      @getData="onLoad" 
    />
    

    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose" size="small">取 消</el-button>
      <el-button type="primary" @click="bindFun" size="small">绑 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  import Pagination from "@/components/Pagination/index.vue";
export default {
  name: "",
  components: { Pagination,},
  props: {
    tableColumn: {
      type: Array,
      required: true,
    },
    // rowData: {
    //   type: [Object, Array],
    //   required: true,
    // },
  },
  data() {
    return {
      loading: false,
      pageObj: {
        pageNum: 1, // 页数
        pageSize: 10, // 条数
      },
      total: 1,
      tableData: [],
      detailVisible: false,
      getDataUrl: '',
      params:{
        keyword: '',
        itemorderno: "",
        frwno: "",
      },
      multipleSelection: [],
      experimentId: '',
      type: '',
    };
  },
  watch: {},
  computed: {},
  created() {},
  methods: {
    initData(getDataUrl,params, eId, type = 1) {
      this.detailVisible = true;
      this.type = type
      this.experimentId = eId;
      this.getDataUrl = getDataUrl;
      this.params = params;
      this.onLoad();
    },
    onLoad(opageNum, opageSize){
      this.loading = true;
      if (opageNum) this.pageObj.pageNum = opageNum;
      if (opageSize) this.pageObj.pageSize = opageSize;
        
      const params ={
        params: this.params,
        ...this.pageObj,
      }
      //获取列表
      this.$api[this.getDataUrl](params, this).then(res => {
        this.loading = false;
        if(res.succ){
          this.tableData = res.data.list;
          this.total = res.data.total;
        }else{
          this.$message.error(res.msg || '查询失败')
        }
      })
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    bindFun(){
      if(this.type == '7'){
        this.$api.bindExperimentItemOrder({
          experimentId: this.experimentId,
          itemOrderIds: this.multipleSelection.map(item => item.itid)
        }, this).then(res => {
          this.loading = false;
          if(res.succ){
            this.$message.success('绑定成功');
            this.detailVisible = false;
            this.$parent.getTaskInfo();
            this.$parent.getExperimentById();
          }else{
            this.$message.error(res.msg || '查询失败')
          }
        })
      }else{
        this.$api.bindExperimentWaybill({
          experimentId: this.experimentId,
          waybillIds: this.multipleSelection.map(item => item.id)
        }, this).then(res => {
          this.loading = false;
          if(res.succ){
            this.$message.success('绑定成功');
            this.detailVisible = false;
            this.$parent.getPurchaseInfo();
            this.$parent.queryLeftList();
            this.$parent.getExperimentById();
          }else{
            this.$message.error(res.msg || '查询失败')
          }
        })
      }
     
      
    },
    handleClose() {
      //关闭弹簧
      this.detailVisible = false;
    },
    handleSetTarget(row){
      this.$emit('setData', row);
      this.handleClose()
    },
    confirmClaim(){
      
    }
  },
};
</script>

<style lang="scss" scoped>

.handle-btn {
  display: inline-block;
  margin-top: 2px;
}
::v-deep .el-dialog{
  width: 1200px;
}
</style>
