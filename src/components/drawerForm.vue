<template>
  <el-drawer
    :title="editForm.id ? '编辑' + name : '新增' + name"
    :visible.sync="drawer"
    direction="rtl"
    :before-close="handleBeforeClose">
    <div class="flex-box flex-column h100p drawer-box">
      <div class="flex-item ofy-auto">
          <!-- :rules="rules" -->
        <el-form ref="editForm" label-width="160px" 
          :model="editForm"
          :rules="rules"
          :show-message="false"
        >
          <slot name="customForm"></slot>
          <el-form-item
            v-for="(el,index) in formContent"
            v-show="!el.isHide"
            :key="index"
            :label="`${el.label}：`"
            :required="el.required || false"
            :prop="el.prop"
          >
            <el-select
              v-if="el.type === 'select' && el.handle"
              v-model="editForm[el.prop]" 
              :disabled="el.disabled"
              :multiple="el.multiple"
              @change="val => $parent[el.handle](val)"
              filterable clearable 
              :value-key="el.valueKey"
              :placeholder="el.placeholder || `请选择${el.label}`" 
              :style="{'width': el.width ? el.width : 350 + 'px'}"
            >
              <el-option
                v-for="(item, index) in el.options"
                :key="index + (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1)"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
            <el-select
              v-else-if="el.type === 'select' && !el.handle"
              v-model="editForm[el.prop]" 
              :disabled="el.disabled"
              :multiple="el.multiple"
              filterable clearable 
              :value-key="el.valueKey"
              @change="(v) => defaultSelectHandle(v, el.prop)"
              :placeholder="el.placeholder || `请选择${el.label}`" 
              :style="{'width': el.width ? el.width : 350 + 'px'}"
            >
              <el-option
                v-for="(item, index) in el.options"
                :key="index + (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1)"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
            <el-input 
              v-else-if="el.type === 'input'"
              v-model="editForm[el.prop]" 
              :disabled="el.disabled"
              clearable
              :placeholder="el.placeholder || `请输入${el.label}`"
              :style="{'width': el.width ? el.width : 350 + 'px'}"
            />
            <el-input
              v-else-if="el.type === 'number'"
              :type="el.type"
              :disabled="el.disabled"
              v-model="editForm[el.prop]" 
              clearable
              :placeholder="el.placeholder || `请输入${el.label}`"
              :style="{'width': el.width ? el.width : 350 + 'px'}"
            >
              <template v-if="el.slot" :slot="el.slot">{{el.slotVal}}</template>
            </el-input>
            <el-radio-group
              v-else-if="el.type === 'radio'"
              v-model="editForm[el.prop]"
            >
              <el-radio v-for="item in el.options" 
                :key="item.value" :label="item.value"
              >{{item.label}}</el-radio>
            </el-radio-group>
            
            <el-checkbox-group
              v-else-if="el.type === 'checkbox'"
              v-model="editForm[el.prop]"
            >
              <el-checkbox v-for="item in el.options"
                :name="el.prop"
                :key="el.props ? item[el.props.value] : item.value" 
                :label="el.vModel ? JSON.stringify(item) : el.props ? item[el.props.value] : item.value"
              >{{el.props ? item[el.props.label] : item.label}}</el-checkbox>
            </el-checkbox-group>
            
            <el-date-picker
              v-else-if="el.type === 'date' || el.type === 'datetime'"
              :type="el.type"
              v-model="editForm[el.prop]"
              placeholder="选择日期/时间"
              :format="el.format"
              :value-format="el.valueFormat"
              :style="{'width': el.width ? el.width : 350 + 'px'}"
            >
            </el-date-picker>
            
            
            
            <template v-else-if="el.label === '合格标准'">
              <slot name="qualified" v-bind:editForm="editForm">
              </slot>
            </template>
            <template v-else-if="el.label === '不合格标准'">
              <slot name="unQualified" v-bind:editForm="editForm">
              </slot>
            </template>
            <template v-else-if="el.label === '让步指标'">
              <slot name="compromise" v-bind:editForm="editForm">
              </slot>
            </template>
            <template v-else-if="el.label === '扣除规则'">
              <slot name="deduction" v-bind:editForm="editForm">
              </slot>
            </template>

            <template v-if="el.type === 'selectDialog'">
              <slot name="selectDialog" v-bind:editForm="{editForm, el}">
              </slot>
            </template>
          </el-form-item>
          <slot name="customLastForm"></slot>
        </el-form>
      </div>
      <div class="drawer-footer">
         <el-button type="primary" @click="handleBeforeClose()" plain>取消</el-button>
         <el-button type="primary" @click="saveForm('editForm')">保存</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
  export default {
    components: {},
    props:{
      formContent: {
        type: Array,
        required: true,
      },
      name: {
        type: String,
        default: '',
      },
      rules: {
        type: Object,
        default: () => {
        },
      },
    },
    data() {
      return {
        drawer: false,
        editForm: {
          testProjectJson: [],
          projectCategory: '4',
        },
        activeRow: {},//编辑对象
      };
    },
    created() {
      
    },
    methods: {
      setEditFormValue(type,val){
        this.$set(this.editForm, type, val);
      },
      initData(row){
        console.log(row)
        this.drawer = true;
        this.activeRow = row;
        if(row){
          this.editForm = JSON.parse(JSON.stringify(row))
        }else{
          let obj = {}
          this.formContent.forEach(i => {
            obj[i.prop] = i.type === 'checkbox' ? [] : undefined;
          });
          this.editForm = obj;
        }
      },
      saveForm(formName){
        this.$refs[formName].validate((valid) => {
          if (valid) {
           this.$emit('saveTarget', this.editForm)
          } else {
            return false;
          }
        });
      },
      selectChange(val){
        console.log(val)
      },
      defaultSelectHandle(val, prop) {
        this.$set(this.editForm, prop, val);
        this.$forceUpdate();
      },
      handleClose(done) {
        if(done){
          done();
        }
        this.drawer = false;
        this.$emit("drawerClose");
      },

      handleBeforeClose(done) {
        this.$confirm('确认关闭？')
          .then(_ => {
            if(done){
              done();
            }
            this.drawer = false;
            this.$emit("drawerClose");
          })
          .catch(_ => {});
      }
    },
  };
  
</script>

<style>
  .ofy-auto{
    overflow-y: auto;
  }
  .drawer-footer{
    border-top: 1px solid #DDDFE6;
  }
</style>