<template>
  <el-dialog
    title=""
    :visible.sync="detailVisible"
    class="claim-dialog-box"
    :before-close="handleClose"
  >
    <!-- <el-input
      @input="onLoad(1)"
      style="width: 280px;margin-top: -42px;  position: absolute;"
      size="mini"
      v-model="keyword"
      placeholder="搜索：输入车辆、车号、厂家、供应商名称"
    ></el-input> -->
    <el-table
      v-loading="loadingTD"
      :data="tableData"
      style="width: 100%">
      <af-table-column
        v-for="item in tableColumn" 
        :key="item.prop" :prop="item.prop" 
        :label="item.label" 
        :formatter="item.formatter"
        :fixed="item.fixed" 
        :width="item.width || ''"
        align="center" 
      />
      <af-table-column fixed="right" width="150" label="操作" align="center" key="handle" :resizable="false">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="handleSetTarget(scope.row)">选择</el-button>
        </template>
      </af-table-column>
    </el-table>
    <Pagination
      :total="total" 
      :pageNum="pageObj.pageNum" 
      :pageSize="pageObj.pageSize" 
      @getData="initData" 
    />
    

    <!-- <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose" size="small">取 消</el-button>
      <el-button type="primary" @click="handleClose" size="small">确 定</el-button>
    </span> -->
  </el-dialog>
</template>

<script>
  import Pagination from "@/components/Pagination/index.vue";
export default {
  name: "",
  components: { Pagination,},
  props: {
    tableColumn: {
      type: Array,
      required: true,
    },
    // rowData: {
    //   type: [Object, Array],
    //   required: true,
    // },
  },
  data() {
    return {
      loadingTD: false,
      pageObj: {
        pageNum: 1, // 页数
        pageSize: 10, // 条数
      },
      total: 1,
      tableData: [],
      detailVisible: false,
      getDataUrl: '',
      params:{
      },
      keyword: ''
    };
  },
  watch: {},
  computed: {},
  created() {},
  methods: {
    initData(getDataUrl,params, isShowDio) {
      //初始化
      if(isShowDio != 'init'){
        this.detailVisible = true;
      }
      this.getDataUrl = getDataUrl;
      this.params = params;
      this.onLoad();
    },
    onLoad(opageNum, opageSize){
      this.loadingTD = true;
      if (opageNum) this.pageObj.pageNum = opageNum;
      if (opageSize) this.pageObj.pageSize = opageSize;
        
      const params ={
        ...this.pageObj,
      }
      // params.keyword = this.params
      //获取列表
      this.$api[this.getDataUrl](this.params, this).then(res => {
        this.loadingTD = false;
        if(res.succ){
          if(!this.$parent.searchForm.itemorderNo && res.data && res.data[0]){
            this.$emit('setData', res.data[0]);
          }
          this.tableData = res.data;
        }else{
          this.$message.error(res.msg || '查询失败')
        }
      })
    },
    handleClose() {
      //关闭弹簧
      this.detailVisible = false;
    },
    handleSetTarget(row){
      this.$emit('setData', row);
      this.handleClose()
    },
    confirmClaim(){
      
    }
  },
};
</script>

<style lang="scss" scoped>

.handle-btn {
  display: inline-block;
  margin-top: 2px;
}
::v-deep .el-dialog{
  width: 1200px;
}
</style>
