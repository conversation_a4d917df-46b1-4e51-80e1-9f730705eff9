<template>
  <div class="nav">
      <router-link
        v-for="(tag, index) in fixViews"
        :key="tag.path + tag.name"
        :class="isActive(tag) ? 'active' : ''"
        :to="{ path: tag.path, query: tag.query, fullPath: tag.fullPath }"
        tag="span"
      >{{ tag.meta.title }}</router-link>
      
      <router-link
        v-for="(tag, index) in visitedViews"
        :key="tag.path + tag.name"
        :class="isActive(tag) ? 'active' : ''"
        :to="{ path: tag.path, query: tag.query, fullPath: tag.fullPath }"
        tag="span"
      >{{ tag.meta.title }}<i 
        @click.prevent.stop="closeSelectedTag(tag)" v-if="!tag.meta.fixed"
        class="el-icon-close"></i>
      </router-link>
  </div>
</template>

<script>
  import routes from "@/router/index.js"
  export default {
    components: {},
    data() {
      return {
        fixViews: [
          {
            path: "/qualityControl/experimentBoard",
            name: "experimentBoard",
            meta: {
              title: "混凝土看板",
              fixed: true,
            }
          },
          {
            path: "/qualityControl/materialBoard",
            name: "materialBoard",
            meta: {
              title: "原材料看板",
              fixed: true,
            }
          },
        ]
      };
    },
    computed: {
      visitedViews() {
        console.log(this.$store.state.tagsView.visitedViews)
        return this.$store.state.tagsView.visitedViews;
      },
    },
    watch: {
      $route() {
        this.addTags();
      },
    },
    created: function() {
      //this.delAllViev();
      // if(this.visitedViews < 5){
      //   this.navAllRoutes(routes)
      // }
      this.addTags();
    },
    beforeDestroy () {
      
    },
    methods: {
      navAllRoutes(rObj, baseUrl){
        for(let i = 0; i<rObj.length; i++){
          if (rObj[i].name) {
            if(baseUrl){
              rObj[i].path = baseUrl+ '/' +rObj[i].path
            }
            this.$store.dispatch("tagsView/addVisitedView", rObj[i]);
          }
          if(rObj[i].children){
            this.navAllRoutes(rObj[i].children, rObj[i].path)
          }
        }
      },
      delAllViev(){
        this.$store.dispatch("tagsView/delAllVisitedViews").then(() => {
          this.$router.push(this.fixViews[0].path);
        });
      },
      isActive(route) {
        return route.name === this.$route.name && route.path === this.$route.path;
      },
      addTags() {
        const { name, path } = this.$route;
        if (this.visitedViews.some(v => v.path === path && v.name === name)){
          return false
        }else if (name) {
          this.$store.dispatch("tagsView/addVisitedView", this.$route);
        }
      },
      closeSelectedTag(view) {
        this.$store.dispatch("tagsView/delVisitedView", view).then(visitedViews => {
          console.log(visitedViews)
          if (this.isActive(view)) {
            this.toLastView(visitedViews, view);
          }
        });
      },
      toLastView(visitedViews, view) {
        console.log(visitedViews)
        const latestView = visitedViews.slice(-1)[0];
        console.log("latestView", latestView);
        if (latestView) {
          this.$router.push(latestView);
        } else {
          this.$router.push(this.fixViews[0].path); 
        }
      },
      
    },
    
  };
</script>

<style scoped lang="scss">
  .nav{
    height: 48px;
    padding: 8px 16px;
    width: 100%;
    overflow-x: auto;
    white-space: nowrap;
    overflow-y: hidden;
    
    
    span{
      display: inline-block;
      cursor: pointer;
      min-width: 110px;
      height: 32px;
      line-height: 32px;
      background: #2A264F;
      border-radius: 2px;
      padding: 0 8px;
      color: #A8A4C7;
      margin-right: 4px;
      font-weight: 500;
      &.active{
        background: #E5E2FF;
        color: #2A264F;
      }
      i{
        float: right;
        margin-top: 9px;
        color: #817DA8;
        padding-left: 8px;
      }
    }
  }
</style>