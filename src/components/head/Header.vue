<template>
  <div class="head-top flex-box">
    <div class="head-left clearfloat">
      <div class="fl-img fl"><img src="@/assets/images/logo.png" alt="" />砼学</div>
      <div class="menu-father fl">
        <div class="menu-scope">
          <span class="menu-icon"><img src="@/assets/images/icon_list.png" alt="" /></span>
        </div>
        <div class="menu-box flex-box">
          <div class="menu-con" v-for="item in menuList" :key="item.moduleTitle">
            <span>{{item.moduleTitle}}</span>
            <router-link
              v-for="(tag, index) in item.children"
              :key="tag.path"
              :class="isActive(tag) ? 'active' : ''"
              :to="{ path: '/' + tag.path}"
              tag="span"
            >{{ tag.title }}
            </router-link>
          </div>
        </div>
      </div>
    </div>
    <!-- <ul class="sys-info flex-item cbo">
      <li class="fl" v-for="item in userInfo.testProjectJson" :key="item.name">{{item.name}}：<span>{{item.no}}</span></li>
    </ul> -->

    <div class="head-right" >
      <el-popover
        placement="bottom"
        width="200"
        trigger="click">
        <div slot="reference" @click="showLoginMenu = true">
          <div class="user-box">
            <img v-if="userInfo.avatar" :src="userInfo.avatar.startsWith('http') ? userInfo.avatar : filePrefix + userInfo.avatar" alt="" />
            <img v-else src="@/assets/images/define-user-icon.png" alt="" />
            <span>{{userInfo.userName}}，{{userInfo.job}}</span>
          </div>
        </div>

        <div class="login-menu">
          <div class="login-menu-btn" @click="showChangePswHandle">修改密码</div>
          <div class="login-menu-btn" @click="loginOutClick">退出登录</div>
        </div>
      </el-popover>
    </div>

    <el-drawer
      class="pwd-drawer"
      title="修改密码"
      destory-on-close
      @close="pwdHandleClose"
      :visible.sync="showChangePsw">
      
        <div v-if="showChangePsw">
            <el-form :model="pswFormData" :rules="pswRules" ref="pswFormData" label-width="120px" class="form-data" size="mini">
            <el-form-item label="原密码" prop="oldPassword">
              <el-input v-model="pswFormData.oldPassword" placeholder="请输入原密码" maxlength="20" type="password" style="width: 200px;"></el-input>
            </el-form-item>
            <el-form-item label="新密码" prop="password">
              <el-input v-model="pswFormData.password" placeholder="请输入新密码" maxlength="20" type="password" style="width: 200px;"></el-input>
            </el-form-item>
            <el-form-item label="再次确认密码" prop="aginPassword">
              <el-input v-model="pswFormData.aginPassword" placeholder="请再次输入新密码" maxlength="20" type="password" style="width: 200px;"></el-input>
            </el-form-item>
          </el-form>
          <el-row style="float: right; margin-right: 100px;">
              <el-button size="small" @click="showChangePsw = false" class="hzpbtn-close">取消</el-button>
              <el-button size="small" type="primary" @click="updateChangePsw('pswFormData')">确 定</el-button>
          </el-row>
        </div>
    </el-drawer>
    
  </div>
</template>

<script>
import Utils from '../../common/js/util'
  export default {
    components: {},
    computed: {
      userInfo() {
        return this.$store.state.loginStore.userInfo;
      },
    },
    data() {
      return {
        filePrefix: this.$store.state.loginStore.userInfo.filePrefix,
        sysInfoVal:{
          asdf: '1123'
        },
        sysInfo: [],
        showLoginMenu: false,
        showChangePsw: false,
        menuList: [
          {
            moduleTitle: "品控中心",
            cPath: '',
            children: [
            {
              path: 'qualityControl/materialBoard',
              title: '原材料看板'
            },{
              path: 'qualityControl/panelList',
              title: '原材料台账列表'
            },{
              path: 'qualityControl/experimentBoard',
              title: '混凝土看板'
            },{
              path: 'qualityControl/concretePanelList',
              title: '混凝土台账列表'
            },{
              path: 'systemMgt/experimentTested',
              title: "受检台账"
            },{
              path: 'systemMgt/experimentOuter',
              title: "外委托台账"
            },
            // {
            //   path: 'qualityControl/moistureContentMgt',
            //   title: '含水率管理'
            // },
            // {
            //   path: 'mixProportion/list',
            //   title: '基准配合比'
            // },
            // {
            //   path: 'qualityControl/taskList',
            //   title: '任务单列表'
            // },
          ]},
          {
          moduleTitle: "生产管理",
          cPath: '',
          children: [
            {
              path: 'systemMgt/supplierList',
              title: "供应商管理"
            },
            {
              path: 'systemMgt/materialsList',
              title: "物料管理"
            },
            {
              path: 'systemMgt/tcWaybillList',
              title: "运单管理"
            },
            {
              path: 'systemMgt/erpCustomerList',
              title: '客户管理'
            },
            {
              path: 'engineeringService/engineeringList',
              title: '工程列表'
            },
            {
              path: 'mixProportion/list',
              title: '基准配合比'
            },
            // {
            //   path: 'mixProportion/openAppraisal',
            //   title: '开盘鉴定记录'
            // },
            {
              path: 'qualityControl/taskList',
              title: '生产任务'
            },
            // {
            //   path: 'systemMgt/regionalismConfig',
            //   title: '工程区域设置'
            // },
            // {
            //   path: 'engineeringService/workOrderList',
            //   title: '工单列表'
            // },
          ]},
          {
            moduleTitle: "售后管理",
            cPath: '',
            children: [{
              path: 'qualityControl/addMaterList',
              title: '增补资料记录'
            },
            {
              path: 'mixProportion/openAppraisal',
              title: '开盘鉴定资料'
            },
            {
              path: 'systemMgt/regionalismConfig',
              title: '工程区域设置'
            },
            {
              path: 'engineeringService/workOrderList',
              title: '工单列表'
            },
          ]
          },
          {
            moduleTitle: "基础信息",
            cPath: '',
            children: [{
              path: 'systemMgt/specConfig',
              title: '材料及规格设置'
            },{
              path: 'systemMgt/materialsConfig',
              title: '物料规则设置'
            },{
              path: 'systemMgt/experimentGenConfig',
              title: "委托规则设置"
            },{
              path: 'systemMgt/paramsConfig',
              title: '批检/快检参数配置'
            },
            // {
            //   path: 'systemMgt/testProjectDesc',
            //   title: '试验说明配置'
            // },
            {
              path: 'systemMgt/equipmentConfig',
              title: '设备管理'
            },
            {
              path: 'systemMgt/humitureMgt',
              title: '温湿度管理'
            }]
          },
          {
            moduleTitle: "系统服务",
            cPath: '',
            children: [
            // {
            //   path: 'systemMgt/standardizingRecord',
            //   title: '设备校检管理'
            // },
            {
              path: 'systemMgt/userMgt',
              title: '用户管理'
            },
            {
              path: 'systemMgt/systemMgt',
              title: "预警设置"
            },{
              path: 'systemMgt/companyMgt',
              title: "企业设置"
            },{
              path: 'systemMgt/pushMgt',
              title: "推送设置"
            }]
          },
          // {
          //   moduleTitle: "其它",
          //   cPath: '',
          //   children: []
          // }
        ],
        
        updatePasswordApi: 'updatePassword',
        pswFormData: {
          oldPassword: '',
          password: '',
          aginPassword: '',
        },
        pswRules: {
          oldPassword: [
						{ required: true, message: '请输入原密码', trigger: 'blur' }
					],
					password: [
						{ required: true, message: '请输入新密码', trigger: 'blur' }
					],
          aginPassword: [
						{ required: true, message: '请再次输入新密码', trigger: 'blur' }
					],
        }
      };
    },
    created: function() {
      console.log(">>>this.userInfo>>>>", this.userInfo)
      // if (this.userInfo && this.userInfo.userPhone === "13701637629") {
      //   this.$set(this.menuList[2], 'children', [
      //       {
      //         path: 'systemMgt/paramsConfig',
      //         title: '批检/快检参数配置'
      //       },
      //       {
      //         path: 'systemMgt/equipmentConfig',
      //         title: '设备管理'
      //       },{
      //         path: 'systemMgt/humitureMgt',
      //         title: '温湿度管理'
      //       },{
      //         path: 'systemMgt/userMgt',
      //         title: '用户管理'
      //       },{
      //         path: 'systemMgt/regionalismConfig',
      //         title: '工程区域划分'
      //       }
      //   ])
      // }
    },
    methods: {
      isActive(route) {
        return route.path === this.$route.path;
      },

      loginOutClick() {
        this.$confirm('确认退出吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          Utils.userData.clearLocalStorage();
          this.$router.push('/login');
        });
      },

      updateChangePsw() {
        if (this.pswFormData.aginPassword != this.pswFormData.password) {
          this.$message({
              showClose: true,
              message: "两次输入的新密码不一致",
              type: "info",
            });
          return;
        }
        this.$confirm('确认修改密码吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.updateChangePswResp();
        });
      },

      updateChangePswResp() {
        this.$api[this.updatePasswordApi]({...this.pswFormData, id: this.userInfo.userId}, this).then((res) => {
          if (res.succ) {
            this.$message({
              showClose: true,
              message: "修改成功，请重新登陆",
              type: "success",
            });
            this.showChangePsw = false;
            setTimeout(() => {
              Utils.userData.clearLocalStorage();
              this.$router.push('/login');
            }, 500);
          }
        });
      },

      showChangePswHandle() {
        this.showChangePsw = true;
        this.pswFormData = {
          oldPassword: '',
          password: '',
          aginPassword: '',
        }
      },
      pwdHandleClose(done) {
        this.$confirm('确认关闭？')
          .then(_ => {
            if(done){
              done();
            }
            this.showChangePsw = false;
          })
          .catch(_ => {});
      }
    },
  };
</script>

<style scoped lang="scss">
  .head-top{
    height: 50px;
    align-items: center;
    padding: 0 16px 0 0;
    border-bottom: 1px solid rgba(255, 255, 255,.2);
    width: 100%;
    .head-left{
      padding-left: 8px;
      .fl-img{
        font-size: 18px;
        font-family: SourceHanSansCN, SourceHanSansCN;
        font-weight: bold;
        color: #FFFFFF;
        line-height: 30px;
        height: 30px;
        padding-right: 16px;
        overflow: hidden;
        margin-top: 10px;
        img{
          height: 32px;
          margin-top: -1px;
          margin-right: 9px;
        }
      }
      .menu-father{
        height: 49px;
        position: relative;
        .menu-scope{
          margin-top: 10px;
          height: 30px;
          padding: 0 14px;
          border-right: 1px solid #6F6692;
          border-left: 1px solid #6F6692;
        }
        .menu-icon{
          width: 30px;
          cursor: pointer;
          height: 30px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: #65569C;
          border-radius: 4px;
        }
        
        
        &:hover{
          .menu-box{
            display: flex;
          }
        }
      }
    }
    .sys-info{
      height: 22px;
      line-height: 22px;
      li{
        padding: 0 16px;
        border-right: 1px solid #6F6692;
        color: #FFFFFF;
        letter-spacing: 1px;
        span{
          font-size: 16px;
          font-weight: 600;
          color: #FFFFFF;
          letter-spacing: 1px;
        }
        &:last-child{
          border: none;
        }
      }
    }
    
    .head-right{
      justify-content: flex-end;
      align-items: flex-end;
      display: flex;
      min-width: 200px;
      flex: 1;
      .user-box{
        height: 34px;
        line-height: 34px;
        img{
          width: 34px;
          height: 34px;
          border-radius: 50%;
          vertical-align: middle;
          margin-top: -2px;
        }
        span{
          font-size: 16px;
          color: #fff;
          margin-left: 8px;
        }
      }
    }
  }
  
  
  
  .menu-box{
    display: none;
    position: absolute;
    top: 48px;
    left: -84px;
    padding: 16px 0;
    z-index: 4000;
    background: #302C45;
    box-shadow: 0px 0px 15px 0px rgba(0,0,0,0.2);
    border-radius: 8px;
    .menu-con{
      padding: 0 8px;
      border-right: 1px solid #4D466F;
      &:last-child{
        border: none;
      }
      
      span{
        display: block;
        min-width: 134px;
        height: 40px;
        line-height: 40px;
        padding: 0 8px;
        font-size: 14px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #E8E6F4;
        border-radius: 4px;
        cursor: pointer;
        white-space: normal;
        &.active,&:hover{
          background: #433E5D;
        }
        &:first-child{
          cursor: inherit;
          font-size: 16px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 600;
          color: #FFFFFF;
          line-height: 22px;
          margin-bottom: 10px;
          height: 22px;
          &:hover{
            background: none;
          }
        }
      }
    }
  }
.login-menu {
  .login-menu-btn {
    padding-top: 10px;
    padding-bottom: 10px;
    border-bottom: #f1f1f1 1px solid;
    cursor: pointer;
  }
}
::v-deep .pwd-drawer .el-drawer {
  width: 400px !important;
}
</style>