<template>
  <el-dialog
    v-loading="loading"
    title=""
    :visible.sync="detailVisible"
    class="claim-dialog-box"
    :before-close="handleClose"
  >
    <slot name="search"></slot>
    <el-table
      :data="tableData"
      style="width: 100%">
      <af-table-column
        v-for="item in tableColumn" 
        :key="item.prop" :prop="item.prop" 
        :label="item.label" 
        :formatter="item.formatter"
        :fixed="item.fixed" 
        :width="item.width || ''"
        show-overflow-tooltip
        align="center" 
      />
      <af-table-column width="150" label="操作" align="center" key="handle" :resizable="false">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="handleSetTarget(scope.row)">选择</el-button>
        </template>
      </af-table-column>
    </el-table>
    <Pagination
      :total="total" 
      :pageNum="pageObj.pageNum" 
      :pageSize="pageObj.pageSize" 
      layout="prev, pager, next"
      @getData="onLoad" 
    />
    

    <!-- <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose" size="small">取 消</el-button>
      <el-button type="primary" @click="handleClose" size="small">确 定</el-button>
    </span> -->
  </el-dialog>
</template>

<script>
import Pagination from "@/components/Pagination/index.vue";
export default {
  name: "",
  components: { Pagination,},
  props: {
    tableColumn: {
      type: Array,
      required: true,
    },
  },
  data() {
    return {
      loading: false,
      pageObj: {
        pageNum: 1, // 页数
        pageSize: 10, // 条数
      },
      total: 1,
      tableData: [],
      detailVisible: false,
      getDataUrl: '',
      params:{},
    };
  },
  watch: {},
  computed: {},
  created() {},
  methods: {
    initData(getDataUrl,params) {
      //初始化
      this.detailVisible = true;
      this.getDataUrl = getDataUrl;
      this.params = params;
      this.onLoad();
    },
    onLoad(opageNum, opageSize){
      this.loading = true;
      if (opageNum) this.pageObj.pageNum = opageNum;
      if (opageSize) this.pageObj.pageSize = opageSize;
      const params ={
        ...this.pageObj,
        params: this.params
      }
      //获取列表
      this.$api[this.getDataUrl](params, this).then(res => {
        this.loading = false;
        if(res.succ){
          this.tableData = res.data.list;
          this.total = res.data.total;
        }else{
          this.$message.error(res.msg || '查询失败')
        }
      })
    },
    tableDataSearch(params) {
      this.params = params;
      this.onLoad(1, this.pageObj.pageSize || 10, );
    },
    tableDataSearchReset(params) {
      this.params = params;
      this.onLoad(1, this.pageObj.pageSize || 10, );
    },
    handleClose() {
      //关闭弹簧
      this.detailVisible = false;
    },
    handleSetTarget(row){
      this.$emit('setData', row);
      this.handleClose()
    },
    confirmClaim(){
      
    }
  },
};
</script>

<style lang="scss" scoped>
.claim-dialog-box {
  overflow: auto;
}
::v-deep .el-dialog {
  width: 85vw;
}
.handle-btn {
  display: inline-block;
  margin-top: 2px;
}

</style>
