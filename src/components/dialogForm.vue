<template>
  <el-dialog
    :title="dialogTitle ? dialogTitle : (editForm.id ? '编辑' : '新增')"
    width="700px"
    :visible.sync="drawer"
    class="claim-dialog-box"
    :before-close="handleClose"
  >
    <div class="flex-box flex-column h100p drawer-box">
      <div class="flex-item ofy-auto">
          <!-- :rules="rules" -->
        <el-form ref="editForm" label-width="160px" 
          :model="editForm"
        >
          <el-form-item
            v-for="(el,index) in formContent"
            :key="index"
            :label="`${el.label}：`"
          >
            <el-select
              v-if="el.type === 'select' && el.handle"
              v-model="editForm[el.prop]" 
              :disabled="el.disabled"
              @change="val => $parent[el.handle](val)"
              filterable clearable 
              :placeholder="el.placeholder || `请选择${el.label}`" 
              :style="{'width': el.width ? el.width : 350 + 'px'}"
              :value-key="el.valueKey"
            >
              <el-option
                v-for="(item, index) in el.options"
                :key="item.value.toString() + '_' + index"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
            <el-select
              v-else-if="el.type === 'select' && !el.handle"
              v-model="editForm[el.prop]" 
              :disabled="el.disabled"
              filterable clearable 
              :placeholder="el.placeholder || `请选择${el.label}`" 
              :style="{'width': el.width ? el.width : 350 + 'px'}"
              :value-key="el.valueKey"
            >
              <el-option
                v-for="(item, index) in el.options"
                :key="item.value.toString() + '_' + index"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
            <el-input 
              v-else-if="el.type === 'input'"
              v-model="editForm[el.prop]" 
              :disabled="el.disabled"
              clearable
              :placeholder="el.placeholder || `请输入${el.label}`"
              :style="{'width': el.width ? el.width : 350 + 'px'}"
            />
            <el-input
              v-else-if="el.type === 'number'"
              :type="el.type"
              :disabled="el.disabled"
              v-model="editForm[el.prop]" 
              clearable
              :placeholder="el.placeholder || `请输入${el.label}`"
              :style="{'width': el.width ? el.width : 350 + 'px'}"
              v-manual-update
            >
              <template v-if="el.slot" :slot="el.slot">{{el.slotVal}}</template>
            </el-input>
            <el-radio-group
              v-else-if="el.type === 'radio'"
              v-model="editForm[el.prop]"
            >
              <el-radio v-for="item in el.options" 
                :key="item.value" :label="item.value"
              >{{item.label}}</el-radio>
            </el-radio-group>
            <el-checkbox-group
              v-else-if="el.type === 'checkbox'"
              v-model="editForm[el.prop]"
            >
              <el-checkbox v-for="item in el.options"
                :name="el.prop"
                :key="item.value" :label="item.value">{{item.label}}</el-checkbox>
            </el-checkbox-group>
            
            <el-date-picker
              v-else-if="el.type === 'date' || el.type === 'datetime'"
              :type="el.type"
              v-model="editForm[el.prop]"
              placeholder="选择日期/时间"
              :format="el.format"
              :value-format="el.valueFormat"
              :style="{'width': el.width ? el.width : 350 + 'px'}"
            >
            </el-date-picker>
            
            
            
            <template v-else-if="el.label === '合格标准'">
              <slot name="qualified" v-bind:editForm="editForm">
              </slot>
            </template>
            <template v-else-if="el.label === '不合格标准'">
              <slot name="unQualified" v-bind:editForm="editForm">
              </slot>
            </template>
            <template v-else-if="el.label === '让步指标'">
              <slot name="compromise" v-bind:editForm="editForm">
              </slot>
            </template>
            <template v-else-if="el.label === '扣除规则'">
              <slot name="deduction" v-bind:editForm="editForm">
              </slot>
            </template>
          </el-form-item>
          
          <el-form-item v-if="!hiddenImg" label="抽样图片：">
              <div class="img-box" style="margin-top: 10px;">
                <el-upload
                  :action="baseUrl + '/upload/file'"
                  list-type="picture-card"
                  :file-list="editForm.img"
                  :on-success="ksdjHandlePicSuccess"
                  :on-remove="ksdjHandlePicRemove">
                  <i class="el-icon-plus"></i>
                </el-upload>
              </div>
          </el-form-item>
        </el-form>
      </div>
      <div class="drawer-footer">
         <el-button type="primary" @click="handleClose()"plain>取消</el-button>
         <el-button type="primary" @click="saveForm('editForm')">保存</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
  export default {
    components: {},
    props:{
      formContent: {
        type: Array,
        required: true,
      },
      hiddenImg: {
        type: Boolean,
        default: false
      },
      dialogTitle: {
        type: String,
        default: ""
      }
    },
    data() {
      return {
        drawer: false,
        editForm: {
          testProjectJson: [],
          projectCategory: '4'
        },
        baseUrl: process.env.NODE_ENV === 'development' ? "/api" : "/mes-platform",
        activeRow: {},//编辑对象
      };
    },
    created() {
      
    },
    methods: {
      //图片C
      ksdjHandlePicSuccess(response, file, fileList) {
        this.editForm.img = fileList.map(item =>{
          if(item.response){
            return item.response.data.filePath;
          }else{
            return item.url
          }
        })
      },
      ksdjHandlePicRemove(file, fileList) {
        this.editForm.img = fileList.map(item =>{
          if(item.response){
            return item.response.data.filePath;
          }else{
            return item.url
          }
        })
      },
      
      setEditFormValue(type,val){
        // this.editForm[type] = val;
        this.$set(this.editForm, type, val);
      },
      initData(row){
        console.log(row)
        this.drawer = true;
        this.activeRow = row;
        if(row){
          this.editForm = JSON.parse(JSON.stringify(row))
        }else{
          let obj = {}
          this.formContent.forEach(i => {
            obj[i.prop] = i.type === 'checkbox' ? [] : undefined;
          });
          this.editForm = obj;
        }
      },
      saveForm(formName){
        this.$emit('saveTarget', this.editForm)
        // this.$refs[formName].validate((valid) => {
        //   if (valid) {
            
        //   } else {
        //     return false;
        //   }
        // });
      },
      selectChange(val){
        console.log(val)
      },
      handleClose(done) {
        this.editForm.img = [];
        console.log(this.editForm.img)
        this.editForm = {
          testProjectJson: [],
          projectCategory: '4'
        }
        
        if(done){
          
          done();
        }else{
          this.drawer = false;
        }
        // this.$confirm('确认关闭？')
        //   .then(_ => {
        //     done();
        //   })
        //   .catch(_ => {});
      }
    },
  };
  
</script>

<style scoped>
  .ofy-auto{
    overflow-y: auto;
  }
  .drawer-footer{
    text-align: center;
  }
  
</style>