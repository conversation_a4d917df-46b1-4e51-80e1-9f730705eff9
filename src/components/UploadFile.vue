<template>
	<el-form :inline="true" style="display: inline;">
		<el-form-item>
			<a href="javascript:;" class="file" v-loading="loading">
				<el-image class="image-btn" :src="fileUrl" fit="cover"></el-image>
    			<input id="file" type="file" style="cursor:pointer;" @change='uploadFile($event)' accept="image/gif, image/jpg, image/jpeg, image/png, image/bmp" />
			</a>
		</el-form-item>
	</el-form>
</template>

<script>
var MaxMSize = 10;                              // 图片最大M数
export default {
	props:["imgUrl", "targetType", "targetNo"],  //props用来接收父组件传过来的数据
	data(){
		return{
			loading: false,

			addImg: require('@/assets/images/btn_add_img.png'),   // 新增图片地址
			fileUrl: require('@/assets/images/btn_add_img.png'),  // 对象地址
			btnText: '上传图片',  // 按钮文字
		}
	},
	methods: {
		uploadFile(e) {
            var file = e.target.files[0];
			if (!file) return;
			// 清空选中文件
			let f = document.getElementById('file');
			f.value = '';
			
			let _this = this;
			// 限制图片最大10M
			let isLimit = file.size / 1024 / 1024 > MaxMSize;
			if(isLimit) {
				this.$message({
					message: '图片过大，请选择大小在 ' + MaxMSize + 'M 内的图片！',
					type: 'error'
				});
				return;
			}

            let param = new FormData()  // 创建form对象
            param.append('file', file, file.name)  // 通过append向form对象添加数据
            param.append('targetType', _this.targetType)  // 通过append向form对象添加数据
            param.append('targetNo', _this.targetNo)      // 通过append向form对象添加数据

            this.loading = true;
            this.$api.uploadFile(param, _this).then(res => {
                _this.loading = false;
                if (res.code !== 1) {
                    _this.$message({
                        message: res.msg || '上传失败，请重试',
                        type: 'warning'
                    });
                }else{
                    // _this.fileUrl = res.data.path;
					_this.$emit("getFileUrl", res.data.path, res.data.filePath);
                }
            }).catch(err => {
                _this.loading = false;
            })
        },
		// 改变文件路径
		changeFileUrl: function (url) {
			if(!url) {
				this.fileUrl = this.addImg;
			} else {
				this.fileUrl = url;
			}
			// this.btnText = txt;
		},
	},
	// 创建VUE实例后的钩子
	created: function () {

	},
	// 挂载到DOM后的钩子
	mounted: function () {

	}
}
</script>

<style lang="scss" scoped>
.file {
    display: inline-block;
    position: relative;
    width: 120px;
    height: 120px;
    line-height: 120px;
    color: #FFF;
    font-size: 18px;
    text-align: center;
    text-indent: 0;
    text-decoration: none;
    overflow: hidden;
	.image-btn {
		width: 100%; 
		height: 100%; 
		z-index: 1;
		position: absolute;
		right: 0;
		top: 0;
        border-radius: 5px;
	}
}
.file input {
    position: absolute;
    font-size: 0px;
	width: 100%; 
	height: 100%; 
    right: 0;
    top: 0;
    opacity: 0;
	z-index: 2
}
</style>