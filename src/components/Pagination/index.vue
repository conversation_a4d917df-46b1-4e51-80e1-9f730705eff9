<template>
      <!-- :hide-on-single-page="true" -->
    <el-pagination 
      @size-change="handleSizeChange" 
      @current-change="handleCurrentChange" 
      :current-page="pageNum" 
      :page-size="pageSize" 
      :total="total"
      :page-sizes="pageSizes" 
      :layout="layout" 
    >
    </el-pagination>
  </div>
</template>

<script>
export default {
  name: 'Pagination',
  props: {
    pageNum: {
      type: Number,
      default: 1
    },
    pageSize: {
      type: Number,
      default: 10
    },
    total: {
      required: true,
      type: Number
    },
    pageSizes: {
      type: Array,
      default() {
        return [5, 10, 20, 30, 50, 100]
      }
    },
    layout: {
      type: String,
      default: 'total, sizes, prev, pager, next, jumper'
    },
    showBackGround: {
      type: Boolean,
      default: false
    },
  },
  computed: {},
  methods: {
    handleSizeChange(val) {
      this.$emit('getData',1,val);//获取数据
    },
    handleCurrentChange(val) {
      this.$emit('getData',val);//获取数据
    }
  }
}
</script>

<style scoped lang="scss">
.el-pagination{
  text-align: center;
}
</style>
