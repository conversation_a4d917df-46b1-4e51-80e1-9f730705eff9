<!--
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2022-09-13 23:31:24
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2024-10-17 22:08:19
 * @FilePath: /funds_approve_admin/src/App.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div id="app">
    <keep-alive v-if="isLoggedIn">
      <router-view v-if="$route.meta.keepAlive" :key="$route.fullPath" />
    </keep-alive>
    <router-view v-if="!$route.meta.keepAlive" :key="$route.fullPath" />
  </div>
</template>

<script>

export default {
  name: 'App',
  components: {
    
  },
  data() {
    return {
      isLoggedIn: false,
    }
  },

  watch: {
    $route(to, from) {
      let token = localStorage.getItem("bbToken") || "";
      if (token) {
        this.isLoggedIn = true;
      } else {
        this.isLoggedIn = false;
      }
    }
  },
}
</script>

<style lang="scss">
@import "./assets/css/main.css";
@import "./assets/fonts/font.css";
@page {
  size: auto;
  margin: 20mm;
  margin-bottom: 2mm;
}

@page portrait {
  size: portrait;
  margin-top: 2mm;
}

@page landscape {
  size: landscape;
  margin-top: 8mm;
  margin-bottom: 8mm;
}

@media print {
  body, html,div{
    height: auto!important;
    margin-top: 2mm;
    font-family: MES-Song;
    font-weight: 400;
    font-size: 20px;
  } 
  .portrait {
    page: portrait;
  }

  .landscape {
    page: landscape;
    // transform: rotate(270deg);
    transform-origin: 50% 50%;
    overflow: visible;
    position: relative;
  }

  .portrait, .landscape {
    page-break-after: always;
    text-align: center;
  }
}
</style>
