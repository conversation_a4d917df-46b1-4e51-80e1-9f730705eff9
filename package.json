{"name": "funds_approve_admin", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"af-table-column": "^1.0.3", "axios": "^0.18.0", "babel-polyfill": "^6.26.0", "echarts": "^5.3.0", "element-ui": "^2.11.0", "moment": "^2.29.1", "qs": "^6.9.6", "v-viewer": "^1.6.4", "vant": "^2.12.49", "vue": "^2.6.10", "vue-baidu-map": "^0.21.22", "vue-infinite-scroll": "^2.0.2", "vue-masonry": "^0.16.0", "vue-print-nb": "^1.7.5", "vue-router": "^3.0.7", "vuebar": "^0.0.20", "vuex": "^3.4.0", "vuex-persistedstate": "^4.1.0"}, "devDependencies": {"@babel/plugin-proposal-optional-chaining": "^7.21.0", "@vue/cli-plugin-babel": "^3.9.0", "@vue/cli-service": "^3.9.0", "less": "^3.0.4", "less-loader": "^5.0.0", "sass": "^1.54.8", "sass-loader": "^8.0.0", "vue-template-compiler": "^2.6.10"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}